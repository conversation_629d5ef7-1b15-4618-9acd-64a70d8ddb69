{"file_info": {"path": "data/bvh/walk_forward.bvh", "size_mb": 2.7470855712890625, "frame_count": 4692, "frame_time": 0.008333, "fps": 120.00480019200768, "duration": 39.098436, "total_channels": 78}, "joints": [{"name": "Hips", "type": "ROOT", "offset": [0.0, 0.0, 0.0], "channels": "XYZXYZ", "parent_name": null}, {"name": "LeftUpLeg", "type": "JOINT", "offset": [0.0364953, 0.0, 0.0], "channels": "XYZ", "parent_name": "Hips"}, {"name": "LeftLeg", "type": "JOINT", "offset": [0.0, -0.157058, 0.0], "channels": "XYZ", "parent_name": "LeftUpLeg"}, {"name": "LeftFoot", "type": "JOINT", "offset": [0.0, -0.1541867, 0.0], "channels": "XYZ", "parent_name": "LeftLeg"}, {"name": "LeftToeBase", "type": "JOINT", "offset": [0.0, -0.015354300000000001, 0.0573033], "channels": "XYZ", "parent_name": "LeftFoot"}, {"name": "LeftToeBase_end", "type": "End Site", "offset": [0.0, 0.0, 0.0295275], "channels": "", "parent_name": "LeftToeBase"}, {"name": "RightUpLeg", "type": "JOINT", "offset": [-0.0364953, 0.0, 0.0], "channels": "XYZ", "parent_name": "Hips"}, {"name": "RightLeg", "type": "JOINT", "offset": [0.0, -0.157058, 0.0], "channels": "XYZ", "parent_name": "RightUpLeg"}, {"name": "RightFoot", "type": "JOINT", "offset": [0.0, -0.1541867, 0.0], "channels": "XYZ", "parent_name": "RightLeg"}, {"name": "RightToeBase", "type": "JOINT", "offset": [0.0, -0.015354300000000001, 0.0573033], "channels": "XYZ", "parent_name": "RightFoot"}, {"name": "RightToeBase_end", "type": "End Site", "offset": [0.0, 0.0, 0.0295275], "channels": "", "parent_name": "RightToeBase"}, {"name": "Spine", "type": "JOINT", "offset": [0.0, 0.00039370000000000003, 0.0], "channels": "XYZ", "parent_name": "Hips"}, {"name": "Spine1", "type": "JOINT", "offset": [0.0, 0.10248290000000002, 0.0], "channels": "XYZ", "parent_name": "Spine"}, {"name": "Neck", "type": "JOINT", "offset": [0.0, 0.07826870000000001, 0.0], "channels": "XYZ", "parent_name": "Spine1"}, {"name": "Head", "type": "JOINT", "offset": [0.0, 0.0690715, 0.0], "channels": "XYZ", "parent_name": "Neck"}, {"name": "Head_end", "type": "End Site", "offset": [0.0, 0.045275499999999996, 0.0], "channels": "", "parent_name": "Head"}, {"name": "LeftShoulder", "type": "JOINT", "offset": [0.0, 0.07826870000000001, 0.0], "channels": "XYZ", "parent_name": "Spine1"}, {"name": "LeftArm", "type": "JOINT", "offset": [0.0671018, -2.0000000000000002e-07, 0.0], "channels": "XYZ", "parent_name": "LeftShoulder"}, {"name": "LeftForeArm", "type": "JOINT", "offset": [0.10944190000000001, -4.0000000000000003e-07, 0.0], "channels": "XYZ", "parent_name": "LeftArm"}, {"name": "LeftHand", "type": "JOINT", "offset": [0.085201, -3.0000000000000004e-07, 0.0], "channels": "XYZ", "parent_name": "LeftForeArm"}, {"name": "LeftHandThumb", "type": "JOINT", "offset": [0.0, 0.0, 0.0], "channels": "XYZ", "parent_name": "LeftHand"}, {"name": "LeftHandThumb_end", "type": "End Site", "offset": [0.0, 0.0, 0.03937], "channels": "", "parent_name": "LeftHandThumb"}, {"name": "L_Wrist_End", "type": "JOINT", "offset": [0.03937, -1.0000000000000001e-07, 0.0], "channels": "XYZ", "parent_name": "LeftHand"}, {"name": "RightShoulder", "type": "JOINT", "offset": [0.0, 0.07826870000000001, 0.0], "channels": "XYZ", "parent_name": "Spine1"}, {"name": "RightArm", "type": "JOINT", "offset": [-0.0671018, -2.0000000000000002e-07, 0.0], "channels": "XYZ", "parent_name": "RightShoulder"}, {"name": "RightForeArm", "type": "JOINT", "offset": [-0.10944190000000001, -4.0000000000000003e-07, 0.0], "channels": "XYZ", "parent_name": "RightArm"}, {"name": "RightHand", "type": "JOINT", "offset": [-0.085201, -3.0000000000000004e-07, 0.0], "channels": "XYZ", "parent_name": "RightForeArm"}, {"name": "RightHandThumb", "type": "JOINT", "offset": [0.0, 0.0, 0.0], "channels": "XYZ", "parent_name": "RightHand"}, {"name": "RightHandThumb_end", "type": "End Site", "offset": [0.0, 0.0, 0.03937], "channels": "", "parent_name": "RightHandThumb"}, {"name": "R_Wrist_End", "type": "JOINT", "offset": [-0.0539369, -2.0000000000000002e-07, 0.0], "channels": "XYZ", "parent_name": "RightHand"}], "motion_data_info": {"shape": [4692, 78], "dtype": "float64"}}