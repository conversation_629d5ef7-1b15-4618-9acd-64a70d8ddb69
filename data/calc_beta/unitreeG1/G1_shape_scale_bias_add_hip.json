{"shape": [[-0.2292329967021942, -0.5345755815505981, -0.11299766600131989, 0.32358986139297485, 0.4381858706474304, -0.6292024850845337, -0.11596640944480896, -0.7132154107093811, -0.5570217370986938, -0.5115605592727661]], "scale": [0.7966322898864746], "bias": [[-0.04168921336531639, 0.05891435220837593, 0.0073625147342681885], [-0.03751105099311802, 0.035264592384919524, -0.00920586256165723], [-0.005924998435841607, 0.04552948680066038, -0.02083619435110617], [0.0042019790667591084, 0.02354016360186506, 0.0011031112130489278], [-0.04321715283690734, -0.060418836772441864, 0.006468355727483208], [-0.03563633629896213, -0.03398528299294412, -0.004722580048017747], [-0.0009134638350427843, -0.04566417094611097, -0.016749301593576245], [0.0018734903083339953, -0.02445392515801359, 0.0006843826971897496], [-0.009110169525825712, 0.0008589287173072502, 0.0006641027697175117], [0.0312354809272759, -0.016265445125379474, 0.01107584558986002], [0.02079568855626478, -0.027101780912131668, 0.0077356000949240665], [-0.004821295012797717, -0.00036792055480877606, 0.0013721463602382336], [0.028833200375461224, 0.013786294234075458, 0.017668886902280623], [0.02217616010292066, 0.02922238006278932, 0.013568923325055807]], "root_bias": [0.04571729525923729, 0.0, -0.06819041073322296], "joint_correspondence": {"leg_l2_link": "L_Hip", "leg_l4_link_knee": "<PERSON><PERSON><PERSON><PERSON>", "leg_l6_link": "<PERSON><PERSON><PERSON><PERSON>", "foot_l1_link": "L_Toe", "leg_r2_link": "R_Hip", "leg_r4_link_knee": "<PERSON><PERSON><PERSON><PERSON>", "leg_r6_link": "<PERSON><PERSON><PERSON><PERSON>", "foot_r1_link": "<PERSON>_<PERSON>e", "arm_l2_link": "<PERSON>_<PERSON><PERSON>", "arm_l4_link": "L_Elbow", "arm_l7_link": "L_<PERSON>rist", "arm_r2_link": "<PERSON>_<PERSON><PERSON>", "arm_r4_link": "<PERSON>_<PERSON><PERSON>", "arm_r7_link": "<PERSON>_<PERSON><PERSON>"}, "optimization_params": {"joint_loss_weight": 1.0, "beta_reg_weight": 0.001, "scale_reg_weight": 0.001, "symmetry_loss_weight": 0.1, "learning_rate": 0.005, "iterations": 1500}, "final_loss": 0.03660215437412262, "coordinate_system": {"bias": "local", "description": "每个bias值都在其对应T1机器人关节的局部坐标系下表示。使用T1机器人的关节变换矩阵将全局坐标系下的偏差转换到局部坐标系。"}, "metadata": {"timestamp": "2025-06-18 17:56:25", "task_name": "unitreeG1_retarget", "device": "cpu"}}