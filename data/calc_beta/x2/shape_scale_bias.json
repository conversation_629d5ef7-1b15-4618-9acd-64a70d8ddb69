{"shape": [[-1.9419676065444946, -2.67797589302063, -7.113190650939941, -0.005266797263175249, 10.41457462310791, -3.1382594108581543, -0.0043266937136650085, -2.2732248306274414, -3.776691436767578, 4.139739036560059]], "scale": [0.9334933757781982], "bias": [[-0.024353557266294956, 0.037631306797266006, 0.01390010118484497], [-0.0014238553121685982, 0.0426015742123127, 0.015211254358291626], [0.005999593995511532, 0.011484246701002121, 0.004197753965854645], [-0.020438178442418575, -0.03488611802458763, 0.008148103952407837], [0.0020101619884371758, -0.04151713475584984, 0.00786539912223816], [0.00446633156388998, -0.013721179217100143, -0.005231402814388275], [-0.020589124411344528, 0.004262754645171896, -0.002103615576520834], [0.008028000560702298, -0.00818225512098805, 0.018539665759707783], [-0.0014990626642623975, 0.004125851659925628, -0.006890177964390898], [-0.010227702558040619, -0.0003555956743390709, -0.0026862807815226653], [0.025950181297957897, -0.0057843443669738925, 0.01238784190744302], [0.003035874777086632, 0.0007673171471040963, -0.00046217465323367124]], "root_bias": [0.04101702198386192, 0.0, -0.08406990021467209], "joint_correspondence": {"leg_l4_link": "<PERSON><PERSON><PERSON><PERSON>", "leg_l6_link": "<PERSON><PERSON><PERSON><PERSON>", "foot_l1_link": "L_Toe", "leg_r4_link": "<PERSON><PERSON><PERSON><PERSON>", "leg_r6_link": "<PERSON><PERSON><PERSON><PERSON>", "foot_r1_link": "<PERSON>_<PERSON>e", "left_arm_link02": "<PERSON>_<PERSON><PERSON>", "left_arm_link04": "L_Elbow", "left_hand_link": "L_<PERSON>rist", "right_arm_link02": "<PERSON>_<PERSON><PERSON>", "right_arm_link04": "<PERSON>_<PERSON><PERSON>", "right_hand_link": "<PERSON>_<PERSON><PERSON>"}, "optimization_params": {"joint_loss_weight": 1.0, "beta_reg_weight": 0.001, "scale_reg_weight": 0.001, "symmetry_loss_weight": 10.0, "learning_rate": 0.005, "iterations": 1500}, "final_loss": 0.04644312640797778, "coordinate_system": {"bias": "local", "description": "每个bias值都在其对应T1机器人关节的局部坐标系下表示。使用T1机器人的关节变换矩阵将全局坐标系下的偏差转换到局部坐标系。"}, "metadata": {"timestamp": "2025-04-21 21:17:12", "task_name": "x2_retarget", "device": "cpu"}}