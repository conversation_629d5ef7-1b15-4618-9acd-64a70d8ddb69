{"shape": [[-0.15307477116584778, -0.8384014368057251, -1.0008745193481445, -0.1835389882326126, 0.5653411746025085, -0.3203769028186798, -0.0487200990319252, -0.18360723555088043, -0.41202589869499207, 0.2819713354110718]], "scale": [0.6991421580314636], "bias": [[-0.03914357163012028, 0.03298715502023697, 0.030012842267751694], [-0.008001135662198067, 0.04224073464865796, 0.00925164483487606], [0.009722566232085228, 0.023303891968680546, 0.032641745172441006], [-0.03785696066915989, -0.03133861720561981, 0.03402673825621605], [-0.007471678778529167, -0.04193142379517667, 0.013655295595526695], [0.007378583773970604, -0.023125383508158848, 0.03200868424028158], [-0.004748553037643433, 0.027319155633449554, -0.02563902735710144], [0.015384148207260293, 0.055396389216184616, -0.01710684273995458], [0.015505581495119256, 0.057366784662008286, -0.02360735510148107], [-0.001111149787902832, -0.027527041733264923, -0.02516081929206848], [0.015004149970365685, -0.058865468949079514, -0.016031574948206534], [0.0176523619859945, -0.05714055523276329, -0.0216757175759226]], "root_bias": [0.09561990946531296, 0.0, -0.06275562942028046], "joint_correspondence": {"leg_l4_link": "<PERSON><PERSON><PERSON><PERSON>", "leg_l6_link": "<PERSON><PERSON><PERSON><PERSON>", "foot_l1_link": "L_Toe", "leg_r4_link": "<PERSON><PERSON><PERSON><PERSON>", "leg_r6_link": "<PERSON><PERSON><PERSON><PERSON>", "foot_r1_link": "<PERSON>_<PERSON>e", "left_arm_link02": "<PERSON>_<PERSON><PERSON>", "left_arm_link04": "L_Elbow", "left_hand_link": "L_<PERSON>rist", "right_arm_link02": "<PERSON>_<PERSON><PERSON>", "right_arm_link04": "<PERSON>_<PERSON><PERSON>", "right_hand_link": "<PERSON>_<PERSON><PERSON>"}, "optimization_params": {"joint_loss_weight": 1.0, "beta_reg_weight": 0.001, "scale_reg_weight": 0.001, "symmetry_loss_weight": 0.1, "learning_rate": 0.005, "iterations": 1500}, "final_loss": 0.0032359370961785316, "coordinate_system": {"bias": "local", "description": "每个bias值都在其对应T1机器人关节的局部坐标系下表示。使用T1机器人的关节变换矩阵将全局坐标系下的偏差转换到局部坐标系。"}, "metadata": {"timestamp": "2025-06-13 18:43:12", "task_name": "T1_retarget", "device": "cpu"}}