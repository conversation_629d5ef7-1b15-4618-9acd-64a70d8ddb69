{"shape": [[-0.18609130382537842, -0.8982647657394409, -1.1547614336013794, -0.19743883609771729, 0.6357327103614807, -0.341300368309021, -0.07035224884748459, -0.1939336359500885, -0.42024847865104675, 0.2907317876815796]], "scale": [0.7035736441612244], "bias": [[-0.03985041566193104, 0.03262437880039215, 0.031227704137563705], [-0.008548064157366753, 0.04187344337697141, 0.011249115690588951], [0.008837005123496056, 0.022853809845400974, 0.034786916337907314], [-0.03857431747019291, -0.030957810580730438, 0.03518777713179588], [-0.008022347465157509, -0.0415440755605232, 0.015575399622321129], [0.006471684202551842, -0.02264342035050504, 0.0340603357180953], [-0.005312394350767136, 0.026351355016231537, -0.026064753532409668], [0.014968033280922097, 0.05346659943461418, -0.017478328688517203], [0.015042136755539101, 0.05448424443602562, -0.024039697392359366], [-0.0015932321548461914, -0.02654125541448593, -0.02558240294456482], [0.014630257484270257, -0.05687658116221428, -0.016380589945688834], [0.01719573452766053, -0.0542089007794857, -0.022070657952204337]], "root_bias": [0.0964178740978241, 0.0, -0.06301204860210419], "joint_correspondence": {"leg_l4_link": "<PERSON><PERSON><PERSON><PERSON>", "leg_l6_link": "<PERSON><PERSON><PERSON><PERSON>", "foot_l1_link": "L_Toe", "leg_r4_link": "<PERSON><PERSON><PERSON><PERSON>", "leg_r6_link": "<PERSON><PERSON><PERSON><PERSON>", "foot_r1_link": "<PERSON>_<PERSON>e", "left_arm_link02": "<PERSON>_<PERSON><PERSON>", "left_arm_link04": "L_Elbow", "left_hand_link": "L_<PERSON>rist", "right_arm_link02": "<PERSON>_<PERSON><PERSON>", "right_arm_link04": "<PERSON>_<PERSON><PERSON>", "right_hand_link": "<PERSON>_<PERSON><PERSON>"}, "optimization_params": {"joint_loss_weight": 1.0, "beta_reg_weight": 0.001, "scale_reg_weight": 0.001, "symmetry_loss_weight": 0.1, "learning_rate": 0.005, "iterations": 1500}, "final_loss": 0.0035202070139348507, "coordinate_system": {"bias": "local", "description": "每个bias值都在其对应T1机器人关节的局部坐标系下表示。使用T1机器人的关节变换矩阵将全局坐标系下的偏差转换到局部坐标系。"}, "metadata": {"timestamp": "2025-06-13 18:29:20", "task_name": "cdroid_retarget", "device": "cpu"}}