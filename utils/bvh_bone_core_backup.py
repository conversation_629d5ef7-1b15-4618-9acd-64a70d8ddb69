import os
import sys
import os.path as osp
import numpy as np
import torch
import open3d as o3d
import joblib
import torch.nn.functional as F
from torch.optim.lr_scheduler import StepLR
from torch.autograd import Variable
import mujoco
import mujoco.viewer
import time
import threading
import queue
from dataclasses import dataclass
from typing import List, Tuple, Dict, Optional
import json
from datetime import datetime
import pickle
from humanoid.utils.config_register import ConfigFactory
from humanoid.utils.humanoid_batch_registry import humanoid_batch_register
from humanoid.utils.torch_humanoid_batch import Humanoid_Batch
from humanoid.retarget_motion.base.motion_lib_cfg import MotionLibCfg
from scipy.spatial.transform import Rotation as sRot

from utils.bvh_parser import BVHParser
from utils.bvh_skeleton import BVHSkeleton


@dataclass
class BVHOptimizationParams:
    """BVH骨骼优化参数类"""
    def __init__(self):
        self.joint_loss_weight: float = 1.0        # 关节损失权重，默认值1.0
        self.offset_reg_weight: float = 0.001      # 偏移量正则化权重，默认值0.001
        self.symmetry_loss_weight: float = 0.1     # 对称性损失权重，默认值0.1
        self.bone_length_weight: float = 0.02      # 骨骼长度保持损失权重，默认值0.02
        self.endpoint_loss_weight: float = 0.5     # 末端位置损失权重，默认值0.5
        self.learning_rate: float = 0.005          # 学习率，默认值0.005
        self.iterations: int = 1500                # 迭代次数，默认值1500
        self.scale: float = 1.0                    # 整体缩放因子，默认值1.0


class BVHBoneOptimizer:
    """BVH骨骼优化器"""
    
    def __init__(self, motion_lib_cfg, bvh_parser: BVHParser, device=None):
        """初始化BVH骨骼优化器
        
        Args:
            motion_lib_cfg: 运动库配置
            bvh_parser: BVH解析器实例
            device: 计算设备，默认使用CPU
        """
        self.motion_lib_cfg = motion_lib_cfg
        self.bvh_parser = bvh_parser
        self.device = device if device is not None else torch.device("cpu")
        
        # 确保BVH文件已解析
        if not self.bvh_parser.joints:
            self.bvh_parser.parse()
        
        # 初始化优化参数
        self.params = BVHOptimizationParams()
        
        # 加载 MuJoCo 模型
        self.model = mujoco.MjModel.from_xml_path(motion_lib_cfg.mjcf_file)
        self.data = mujoco.MjData(self.model)
        
        # 初始化所需变量
        self.initialize_variables()
        
        # 初始化BVH根节点位置的优化变量
        self.bvh_root_trans = torch.zeros(2, device=self.device, requires_grad=True)  # 只优化 x 和 z 方向
        
        # 初始化缩放因子作为可优化参数
        self.scale = torch.nn.Parameter(torch.tensor(self.params.scale, device=self.device, dtype=torch.float32), requires_grad=True)

    def initialize_variables(self):
        """初始化所需变量"""
        # 初始化关节选择和索引
        self.joint_names = self.motion_lib_cfg.joint_names
        
        # 获取BVH关节名称列表
        self.bvh_joint_names = self.bvh_parser.get_joint_names()  #用于后续验证key-points是否都有效
        
        # 从配置文件中获取BVH关键点对应关系
        if not hasattr(self.motion_lib_cfg, 'bvh_joint_correspondence'):
            error_msg = """
                错误: 配置文件中未找到 bvh_joint_correspondence 配置项。

                请按照以下步骤操作:
                1. 在您的配置文件中添加 bvh_joint_correspondence 字典
                2. 字典的键应该是机器人的关节名称
                3. 字典的值应该是对应的BVH关节名称

                示例配置:
                bvh_joint_correspondence = {
                    'base_link': 'Hips',
                    'leg_l4_link': 'LeftKnee',
                    'leg_l6_link': 'LeftAnkle',
                    'foot_l1_link': 'LeftFoot',
                    'leg_r4_link': 'RightKnee',
                    'leg_r6_link': 'RightAnkle',
                    'foot_r1_link': 'RightFoot',
                    'left_arm_link02': 'LeftShoulder',
                    'left_arm_link04': 'LeftElbow',
                    'right_arm_link02': 'RightShoulder',
                    'right_arm_link04': 'RightElbow'
                }

                请确保:
                1. 所有机器人关节名称都在 self.joint_names 中存在
                2. 所有BVH关节名称都在 bvh_parser.get_joint_names() 中存在
                3. 关节对应关系合理且完整
                """
            raise ValueError(error_msg)
        
        # 使用配置文件中的对应关系
        self.robot_joint_pick = list(self.motion_lib_cfg.bvh_joint_correspondence.keys())
        self.bvh_joint_pick = list(self.motion_lib_cfg.bvh_joint_correspondence.values())
        
        # 验证所有选中的BVH关键点是否有效
        invalid_joints = [joint for joint in self.bvh_joint_pick if joint not in self.bvh_joint_names]
        if invalid_joints:
            error_msg = f"""
                错误: 以下BVH关键点无效: {invalid_joints}

                有效的BVH关节名称包括:
                {self.bvh_joint_names}

                请检查您的 bvh_joint_correspondence 配置，确保所有BVH关节名称都正确。
                """
            raise ValueError(error_msg)
        
        # 验证所有机器人关节名称是否有效
        invalid_robot_joints = [joint for joint in self.robot_joint_pick if joint not in self.joint_names]
        if invalid_robot_joints:
            error_msg = f"""
                错误: 以下机器人关节名称无效: {invalid_robot_joints}

                有效的机器人关节名称包括:
                {self.joint_names}

                请检查您的 bvh_joint_correspondence 配置，确保所有机器人关节名称都正确。
                """
            raise ValueError(error_msg)
        
        self.robot_joint_names_augment = self.joint_names
        self.robot_joint_pick_idx = [self.robot_joint_names_augment.index(j) for j in self.robot_joint_pick]
        self.bvh_joint_pick_idx = [self.bvh_joint_names.index(j) for j in self.bvh_joint_pick]
        
        # 初始化 Humanoid Forward Kinematics
        self.Humanoid_fk = Humanoid_Batch(self.motion_lib_cfg.mjcf_file, self.motion_lib_cfg.extend_node_dict, device=self.device)
        
        # 初始化 dof_pos 关节角度
        self.dof_pos = torch.zeros((1, self.Humanoid_fk.joints_axis.shape[1]), device=self.device)
        if self.motion_lib_cfg.dof_pos is not None:
            self.dof_pos = self.motion_lib_cfg.dof_pos.to(self.device)  # T-pose
        else:
            # 确保dof_pos是T-pose（所有关节角度为0）
            self.dof_pos = torch.zeros((1, self.Humanoid_fk.joints_axis.shape[1]), device=self.device)
        
        # 计算姿态角度
        self.calculate_pose_angles()
        
        # 初始化BVH偏移量优化变量
        self.initialize_bvh_variables()

    def calculate_pose_angles(self):
        """计算各个部位的姿态角度（参考SMPL优化器）"""
        # 获取关节轴 (形状为 [1, num_dofs, 3])
        axes = self.Humanoid_fk.joints_axis
        # 获取关节角度 (形状 [1, num_dofs])
        angles = self.dof_pos
        # 计算所有非根关节的轴角 (利用广播机制)
        # angles [1, num_dofs] * axes [1, num_dofs, 3] -> joint_aa [1, num_dofs, 3]
        joint_aa = angles.unsqueeze(-1) * axes
        
        # 调整根旋转的形状以进行拼接 (形状 [1, 3] -> [1, 1, 3])
        root_rot = torch.zeros(1, 1, 3, device=self.device)
        
        # 拼接根旋转和关节轴角
        self.pose_aa_gq = torch.cat([root_rot, joint_aa], dim=1).to(self.device)
        
        self.root_trans_gq = torch.zeros((1, 1, 3), device=self.device)

    def initialize_bvh_variables(self):
        """初始化BVH相关变量"""
        # 创建BVH骨架解析器
        self.bvh_skeleton = BVHSkeleton(self.bvh_parser)
        
        # 将骨架数据移动到指定设备
        self.bvh_skeleton.to_device(self.device)
        
        # 获取原始偏移量
        self.original_offsets = {}
        self.optimizable_offsets = {}
        
        # 使用BVH骨架解析器获取关节信息
        for joint_name in self.bvh_skeleton.get_joint_names():
            original_offset = self.bvh_skeleton.get_joint_offset(joint_name)
            self.original_offsets[joint_name] = original_offset
            
            # 创建可优化的偏移量
            optimizable_offset = torch.nn.Parameter(original_offset.clone(), requires_grad=True)
            self.optimizable_offsets[joint_name] = optimizable_offset
        
        # 获取根节点偏移量（类似SMPL的root_trans_offset）
        root_joint_name = self.bvh_skeleton.get_root_joint()
        if root_joint_name:
            self.root_trans_offset = self.bvh_skeleton.get_root_offset()
        else:
            # 如果没有找到根节点，使用零向量
            self.root_trans_offset = torch.zeros(3, device=self.device, dtype=torch.float32)

    def get_robot_positions(self):
        """获取机器人关节位置（参考SMPL优化器）"""
        fk_return = self.Humanoid_fk.fk_batch(self.pose_aa_gq[None, ], torch.zeros((1, 1, 3), device=self.device), return_full=False)
        robot_positions = fk_return.global_translation[:,:,self.robot_joint_pick_idx]
        
        # 选择指定的关节位置
        selected_positions = robot_positions.squeeze(0)  # [num_picked_joints, 3]
        
        return selected_positions

    def get_bvh_joints(self, optimized_offsets=None):
        """获取BVH关节位置
        
        Args:
            optimized_offsets: 优化后的偏移量字典，如果为None则使用原始偏移量
        """
        if optimized_offsets is None:
            # 使用原始偏移量
            offsets_dict = self.original_offsets
        else:
            # 使用优化后的偏移量
            offsets_dict = optimized_offsets
        
        # 对于T-pose优化，我们直接使用偏移量作为关节位置
        # 这是合理的，因为我们在优化骨骼结构本身
        bvh_positions = []
        
        # 确保我们为所有BVH关节构建位置数组
        for joint_name in self.bvh_skeleton.get_joint_names():
            if joint_name in offsets_dict:
                # 使用优化后的偏移量
                offset = offsets_dict[joint_name].detach().cpu().numpy()
            else:
                # 如果找不到关节，使用零位置
                offset = np.array([0.0, 0.0, 0.0])
            
            bvh_positions.append(offset)
        
        bvh_positions = np.array(bvh_positions)
        
        # 转换为torch张量
        bvh_positions = torch.tensor(bvh_positions, device=self.device, dtype=torch.float32)
        
        # 应用缩放因子
        bvh_positions *= self.scale
        
        # 应用根节点变换
        root_trans = torch.zeros(3, device=self.device)
        root_trans[0] = self.bvh_root_trans[0]  # x方向
        root_trans[2] = self.bvh_root_trans[1]  # z方向
        bvh_positions += root_trans
        
        return bvh_positions

    def get_selected_bvh_joints(self, optimized_offsets=None):
        """获取选择后的BVH关节位置（用于与机器人关节比较）"""
        full_bvh_positions = self.get_bvh_joints(optimized_offsets)
        
        # 选择指定的关节位置
        # 确保索引在有效范围内
        valid_indices = [idx for idx in self.bvh_joint_pick_idx if idx < len(full_bvh_positions)]
        if len(valid_indices) != len(self.bvh_joint_pick_idx):
            print(f"警告: 部分关节索引超出范围，跳过无效索引")
            print(f"原始索引: {self.bvh_joint_pick_idx}")
            print(f"有效索引: {valid_indices}")
        
        selected_positions = full_bvh_positions[valid_indices, :]
        
        return selected_positions

    def compute_bone_length_loss(self, bvh_positions):
        """计算骨骼长度保持损失"""
        bone_length_loss = torch.tensor(0.0, device=self.device, dtype=torch.float32)
        
        # 使用BVH骨架解析器获取关节信息
        for joint_name in self.bvh_skeleton.get_joint_names():
            parent_name = self.bvh_skeleton.get_parent(joint_name)
            if parent_name is not None and parent_name in self.bvh_skeleton.get_joint_names():
                # 获取父节点索引
                parent_idx = self.bvh_skeleton.get_joint_names().index(parent_name)
                joint_idx = self.bvh_skeleton.get_joint_names().index(joint_name)
                
                # 计算当前骨骼长度
                current_length = torch.norm(bvh_positions[joint_idx] - bvh_positions[parent_idx])
                
                # 计算原始骨骼长度
                original_offset = self.original_offsets[joint_name]
                original_length = torch.norm(original_offset)
                
                # 计算长度差异损失
                length_diff = torch.abs(current_length - original_length)
                bone_length_loss += length_diff
        
        return bone_length_loss

    def compute_symmetry_loss(self, bvh_positions, params):
        """计算对称性损失"""
        symmetry_loss = torch.tensor(0.0, device=self.device, dtype=torch.float32)
        
        # 使用BVH骨架解析器获取对称关节对
        symmetric_pairs = self.bvh_skeleton.get_symmetric_joints()
        
        for left_joint, right_joint in symmetric_pairs:
            left_idx = self.bvh_skeleton.get_joint_names().index(left_joint)
            right_idx = self.bvh_skeleton.get_joint_names().index(right_joint)
            
            # 计算对称关节位置差异
            left_pos = bvh_positions[left_idx]
            right_pos = bvh_positions[right_idx]
            
            # 对称性约束：左右关节的y坐标应该相反
            symmetry_diff = torch.abs(left_pos[1] + right_pos[1])  # y坐标应该相反
            symmetry_loss += symmetry_diff
        
        return symmetry_loss

    def compute_losses(self, robot_positions, bvh_positions, optimized_offsets, params):
        """计算所有损失"""
        loss_dict = {}
        
        # 获取选择后的BVH关节位置（用于与机器人关节比较）
        selected_bvh_positions = self.get_selected_bvh_joints(optimized_offsets)
        
        # 1. 关节位置损失
        joint_loss = torch.mean(torch.norm(robot_positions - selected_bvh_positions, dim=-1))
        loss_dict['joint_loss'] = joint_loss
        
        # 2. 偏移量正则化损失
        offset_reg_loss = torch.tensor(0.0, device=self.device, dtype=torch.float32)
        for joint_name, optimized_offset in optimized_offsets.items():
            original_offset = self.original_offsets[joint_name]
            offset_diff = torch.norm(optimized_offset - original_offset)
            offset_reg_loss += offset_diff
        loss_dict['offset_reg_loss'] = offset_reg_loss
        
        # 3. 骨骼长度保持损失
        bone_length_loss = self.compute_bone_length_loss(bvh_positions)
        loss_dict['bone_length_loss'] = bone_length_loss
        
        # 4. 对称性损失
        symmetry_loss = self.compute_symmetry_loss(bvh_positions, params)
        loss_dict['symmetry_loss'] = symmetry_loss
        
        # 5. 末端位置损失（可选）
        endpoint_loss = torch.tensor(0.0, device=self.device, dtype=torch.float32)
        # 这里可以添加末端位置的特定约束
        loss_dict['endpoint_loss'] = endpoint_loss
        
        return loss_dict

    def compute_total_loss(self, loss_dict, symmetry_loss):
        """计算总损失"""
        total_loss = (
            self.params.joint_loss_weight * loss_dict['joint_loss'] +
            self.params.offset_reg_weight * loss_dict['offset_reg_loss'] +
            self.params.bone_length_weight * loss_dict['bone_length_loss'] +
            self.params.symmetry_loss_weight * symmetry_loss +
            self.params.endpoint_loss_weight * loss_dict['endpoint_loss']
        )
        return total_loss

    def save_optimization_result(self, optimized_offsets, robot_positions, bvh_positions, task_name, loss=None, save_path=None):
        """保存优化结果"""
        if save_path is None:
            task_prefix = task_name.split('_')[0]
            save_dir = os.path.join("data", "calc_bvh", task_prefix)
            os.makedirs(save_dir, exist_ok=True)
            save_path = os.path.join(save_dir, "bvh_optimization_result.json")
        
        # 转换偏移量为numpy数组
        offsets_dict = {}
        for joint_name, offset_tensor in optimized_offsets.items():
            offsets_dict[joint_name] = offset_tensor.detach().cpu().numpy().tolist()
        
        # 准备保存数据
        result_data = {
            'task_name': task_name,
            'optimized_offsets': offsets_dict,
            'robot_positions': robot_positions.detach().cpu().numpy().tolist(),
            'bvh_positions': bvh_positions.detach().cpu().numpy().tolist(),
            'robot_joint_names': self.robot_joint_pick,
            'bvh_joint_names': self.bvh_joint_pick,
            'optimization_params': {
                'joint_loss_weight': self.params.joint_loss_weight,
                'offset_reg_weight': self.params.offset_reg_weight,
                'symmetry_loss_weight': self.params.symmetry_loss_weight,
                'bone_length_weight': self.params.bone_length_weight,
                'endpoint_loss_weight': self.params.endpoint_loss_weight,
                'learning_rate': self.params.learning_rate,
                'scale': self.scale.item(),
            },
            'timestamp': datetime.now().isoformat(),
        }
        
        if loss is not None:
            result_data['final_loss'] = float(loss)
        
        # 保存到文件
        with open(save_path, 'w') as f:
            json.dump(result_data, f, indent=2)
        
        print(f"优化结果已保存到: {save_path}")
        return save_path

    def save_config(self, config_path=None):
        """保存配置"""
        if config_path is None:
            task_prefix = self.motion_lib_cfg.__class__.__name__.replace('RetargetMotionLibCfg', '')
            config_dir = os.path.join("configs", "bvh")
            os.makedirs(config_dir, exist_ok=True)
            config_path = os.path.join(config_dir, f"{task_prefix}_bvh_config.json")
        
        config_data = {
            'bvh_joint_correspondence': self.motion_lib_cfg.bvh_joint_correspondence,
            'optimization_params': {
                'joint_loss_weight': self.params.joint_loss_weight,
                'offset_reg_weight': self.params.offset_reg_weight,
                'symmetry_loss_weight': self.params.symmetry_loss_weight,
                'bone_length_weight': self.params.bone_length_weight,
                'endpoint_loss_weight': self.params.endpoint_loss_weight,
                'learning_rate': self.params.learning_rate,
                'iterations': self.params.iterations,
            }
        }
        
        with open(config_path, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        print(f"配置已保存到: {config_path}")
        return config_path


class BVHVisualizer:
    """BVH可视化器"""
    
    def __init__(self, optimizer):
        """初始化BVH可视化器
        
        Args:
            optimizer: BVHBoneOptimizer实例
        """
        self.optimizer = optimizer
        self.mujoco_viewer = None
        self.visualization_thread = None
        self.stop_visualization_flag = False
        self.update_queue = queue.Queue()
        
        # 初始化Open3D可视化
        self.init_open3d_visualization()
        
        # 初始化MuJoCo查看器
        self.init_mujoco_viewer()
    
    def init_open3d_visualization(self):
        """初始化Open3D可视化"""
        self.vis = o3d.visualization.Visualizer()
        self.vis.create_window("BVH骨骼优化可视化", width=1200, height=800)
        
        # 创建坐标系
        self.coordinate_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.1)
        self.vis.add_geometry(self.coordinate_frame)
        
        # 初始化机器人骨架可视化
        self.robot_skeleton = o3d.geometry.LineSet()
        self.robot_points = []
        self.robot_lines = []
        
        # 初始化BVH骨架可视化
        self.bvh_skeleton = o3d.geometry.LineSet()
        self.bvh_points = []
        self.bvh_lines = []
        
        # 添加到可视化器
        self.vis.add_geometry(self.robot_skeleton)
        self.vis.add_geometry(self.bvh_skeleton)
    
    def init_mujoco_viewer(self):
        """初始化MuJoCo查看器"""
        try:
            import mujoco
            # 检查是否有可用的MuJoCo模型
            if hasattr(self.optimizer, 'motion_lib_cfg') and hasattr(self.optimizer.motion_lib_cfg, 'model'):
                # 创建 viewer
                self.mujoco_viewer = mujoco.viewer.launch_passive(
                    self.optimizer.motion_lib_cfg.model, 
                    self.optimizer.motion_lib_cfg.data
                )
                # 设置相机参数
                self.mujoco_viewer.cam.distance = 3.0
                self.mujoco_viewer.cam.azimuth = 45
                self.mujoco_viewer.cam.elevation = -20
                
                # 设置渲染选项 - 启用透明度
                self.mujoco_viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_TRANSPARENT] = 1
                
                print("已创建持久性 MuJoCo 查看器窗口")
            else:
                print("警告：没有可用的MuJoCo模型，跳过MuJoCo查看器初始化")
        except ImportError:
            print("警告：mujoco模块未安装，跳过MuJoCo查看器初始化")
        except Exception as e:
            print(f"创建 MuJoCo 查看器时出错: {e}")
            self.mujoco_viewer = None
    
    def start_visualization_thread(self):
        """启动可视化线程"""
        self.visualization_thread = threading.Thread(target=self.run_visualization)
        self.visualization_thread.daemon = True
        self.visualization_thread.start()
    
    def run_visualization(self):
        """运行可视化循环"""
        while not self.stop_visualization_flag:
            try:
                # 尝试从队列获取更新数据
                update_data = self.update_queue.get(timeout=0.1)
                self.update_visualization(update_data)
            except queue.Empty:
                continue
            except Exception as e:
                print(f"可视化更新错误: {e}")
        
        # 关闭可视化窗口
        self.vis.destroy_window()
    
    def update_visualization(self, update_data):
        """更新可视化
        
        Args:
            update_data: 包含更新数据的字典
        """
        robot_positions = update_data.get('robot_positions')
        bvh_positions = update_data.get('bvh_positions')
        iteration = update_data.get('iteration', 0)
        
        if robot_positions is not None and bvh_positions is not None:
            # 更新机器人骨架
            self.update_robot_skeleton(robot_positions)
            
            # 更新BVH骨架
            self.update_bvh_skeleton(bvh_positions)
            
            # 更新窗口标题
            self.vis.get_window_name = lambda: f"BVH骨骼优化可视化 - 迭代 {iteration}"
            
            # 刷新显示
            self.vis.poll_events()
            self.vis.update_renderer()
    
    def update_robot_skeleton(self, robot_positions):
        """更新机器人骨架可视化"""
        # 这里需要根据机器人的骨架结构来构建线条
        # 简化实现：只显示关节点
        points = []
        lines = []
        
        # 添加关节点
        for i, pos in enumerate(robot_positions):
            points.append(pos.cpu().numpy())
        
        # 构建线条（这里需要根据机器人的实际骨架结构）
        # 示例：连接相邻的关节
        for i in range(len(points) - 1):
            lines.append([i, i + 1])
        
        # 更新LineSet
        self.robot_skeleton.points = o3d.utility.Vector3dVector(points)
        self.robot_skeleton.lines = o3d.utility.Vector2iVector(lines)
        self.robot_skeleton.paint_uniform_color([1, 0, 0])  # 红色表示机器人
    
    def update_bvh_skeleton(self, bvh_positions):
        """更新BVH骨架可视化"""
        # 类似机器人骨架的更新
        points = []
        lines = []
        
        # 添加关节点
        for i, pos in enumerate(bvh_positions):
            points.append(pos.cpu().numpy())
        
        # 构建线条
        for i in range(len(points) - 1):
            lines.append([i, i + 1])
        
        # 更新LineSet
        self.bvh_skeleton.points = o3d.utility.Vector3dVector(points)
        self.bvh_skeleton.lines = o3d.utility.Vector2iVector(lines)
        self.bvh_skeleton.paint_uniform_color([0, 1, 0])  # 绿色表示BVH
    
    def update_visualization_data(self, robot_positions, bvh_positions, iteration=0):
        """更新可视化数据"""
        update_data = {
            'robot_positions': robot_positions,
            'bvh_positions': bvh_positions,
            'iteration': iteration
        }
        
        try:
            self.update_queue.put_nowait(update_data)
        except queue.Full:
            # 如果队列满了，清空队列并放入新数据
            while not self.update_queue.empty():
                try:
                    self.update_queue.get_nowait()
                except queue.Empty:
                    break
            self.update_queue.put(update_data)
        
        # 同时更新MuJoCo查看器
        self.update_mujoco_viewer(robot_positions, bvh_positions, iteration)
    
    def update_mujoco_viewer(self, robot_positions, bvh_positions, iteration=0):
        """更新MuJoCo查看器内容"""
        if self.mujoco_viewer is None:
            return
        
        try:
            import mujoco
            # 检查查看器是否还在运行
            if not hasattr(self.mujoco_viewer, 'is_running') or not self.mujoco_viewer.is_running():
                return
            
            # 这里可以添加更新MuJoCo模型状态的代码
            # 例如更新关节角度、位置等
            
            # 更新查看器
            self.mujoco_viewer.sync()
            
        except Exception as e:
            print(f"更新MuJoCo查看器时出错: {e}")
    
    def stop_visualization(self):
        """停止可视化"""
        self.stop_visualization_flag = True
        if self.visualization_thread:
            self.visualization_thread.join() 