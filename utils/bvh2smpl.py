'''
Remove one spine joint from the preprocessed bvh file, add two hand joints, and change the order of the joints. 
Save it as a .npy array with dimensions (1+24, 3), where 1 represents the offset of the root joint, 
24 represents 24 joints, and 3 represents the x, y, z coordinates for each joint.
'''
import numpy as np
from scipy.spatial.transform import Rotation as R
import pybullet as p

from scripts.bvh2smpl import bvh_utils

'''
Pelvis(0)
|-- L_Hip(1)
    |-- L_<PERSON>nee(4)
        |-- <PERSON>_<PERSON>kle(7)
            |-- L_<PERSON>(10)
|-- R_Hip(2)
    |-- R_<PERSON>nee(5)
        |-- R_<PERSON>kle(8)
            |-- R_<PERSON>(11)
|-- Spine1(3)
    |-- Spine2(6)
        |-- Spine3(9)
            |-- Neck(12)
                |-- Head(15)
            |-- L_Collar(13)
                |-- <PERSON>_Shoulder(16)
                |-- L_<PERSON><PERSON>(18)
                    |-- L_<PERSON>rist(20)
                        |-- <PERSON>_<PERSON>(22)
            |-- R_<PERSON>lar(14)
                |-- <PERSON><PERSON><PERSON><PERSON>(17)
                |-- <PERSON>_<PERSON><PERSON>(19)
                    |-- <PERSON>_<PERSON><PERSON>(21)
                        |-- <PERSON><PERSON><PERSON>(23)

Mapping from src bvh skeletal structure to smpl skeletal structure, for example: in the smpl skeletal structure idx==3 is Spine1, 
in the table below we find that idx==3 corresponds to 1, so in the src skeletal structure idx==1 is Spine1 (chest1). 
Additionally, the last two numbers are duplicates, indicating that the src bvh skeleton lacks two hand nodes.
'''



JOINT_MAP = {
    # 'BVH joint name': 'SMPLX joint index'
    'Hips': 0,
    'LeftUpLeg': 1,
    'RightUpLeg': 2,
    'Spine': 3,
    'LeftLeg': 4,
    'RightLeg': 5,
    'Spine1': 6,
    'LeftFoot': 7,
    'RightFoot': 8,
    'Spine2': 9,
    'LeftToe': 10,
    'RightToe': 11,
    'Neck': 12,
    'LeftShoulder': 13,
    'RightShoulder': 14,
    'Head': 15,
    'LeftArm': 16,
    'RightArm': 17,
    'LeftForeArm': 18,
    'RightForeArm': 19,
    'LeftHand': 20,
    'RightHand': 21,
}

# cur_path = os.path.dirname(os.path.abspath(__file__))
# bvh_path = cur_path+"/../data/bvh_data/retargeted_data/"
# retargeted_data_path = cur_path+"/../../data/bvh_data/retargeted_data/"
bvh_path = "data/bvh_data/retargeted_data/"
retargeted_data_path = "data/raw_data/amass/kongfu/test/"
bvh_files=["fightAndSports1_subject1.bvh"]

joint_spheres_GQ = []
joint_spheres_Smpl = []

### 用于可视化的函数 ###
    
def create_joint_visualization(joint_positions, type):
    RgbaColor = [0, 0, 0, 0]
    if type == 'Robot':
        RgbaColor = [1, 0, 0, 1]
    elif type == 'Smpl':
        RgbaColor = [0, 1, 0, 1]
        for sphere_id in joint_spheres_Smpl:
            p.removeBody(sphere_id)
                     
    # 创建所有关节点的视觉形状
    visual_shapes = []
    if type == 'Robot':
        for joint_pos in joint_positions:
            visual_shapes = p.createVisualShape(shapeType=p.GEOM_SPHERE, radius=0.02, rgbaColor=RgbaColor)
            sphere_id = p.createMultiBody(baseMass=0, baseVisualShapeIndex=visual_shapes, basePosition=joint_pos)
            joint_spheres_GQ.append(sphere_id)
        return joint_spheres_GQ
    if type == 'Smpl':
        for joint_pos in joint_positions:
            visual_shapes = p.createVisualShape(shapeType=p.GEOM_SPHERE, radius=0.02, rgbaColor=RgbaColor)
            sphere_id = p.createMultiBody(baseMass=0, baseVisualShapeIndex=visual_shapes, basePosition=joint_pos)
            joint_spheres_Smpl.append(sphere_id)
        return joint_spheres_Smpl

def yaw_matrix(rotation_matrix: np.ndarray):
    """
    Returns a quaternion that keeps only the yaw rotation of the given rotation matrix
    """
    quat_yaw = R.from_matrix(rotation_matrix).as_euler('XYZ')
    quat_yaw[...,:2] = 0.0
    return R.from_euler('XYZ', quat_yaw, degrees=False).as_matrix()

def euclidean_distance(p1, p2):
    """
    使用 numpy 计算二维空间中两点之间的欧氏距离
    :param p1: 点1，格式为 [x1, y1]
    :param p2: 点2，格式为 [x2, y2]
    :return: 欧氏距离
    """
    return np.linalg.norm(p2 - p1)

for i in range(len(bvh_files)):
    bvh= bvh_utils.BVHMotion(bvh_path + bvh_files[i], order="ZYX")
    joint_names = bvh.joint_name
    print(joint_names)
    idx_mapping = [joint_names.index(joint) for joint in JOINT_MAP.keys()]
    print(idx_mapping)
    positions = bvh.joint_position[:,0]/100   # cm转m
    retargeted_pos = bvh.joint_position
    retargeted_pos = retargeted_pos[:, idx_mapping, :]
    # retargeted_root_pos = retargeted_root_pos[:, :, [2, 0, 1]] # 原来坐标系的X朝上
    retargeted_pos = retargeted_pos / 100
    retargeted_pos = retargeted_pos[:, :, [2,0,1]]  # 原来坐标系的X朝上
    retargeted_pos[:,[0],[0, 1]] -= retargeted_pos[[0],[0],[0, 1]]  # 以第一帧为参考帧，将所有帧的位置都减去第一帧的位置，使其以第一帧为原点
    retargeted_root_pos = retargeted_pos[:, 0, :]

    # 根据pos推测的
    swap_transpose = np.array([[0.0, 1.0, 0.0], [0.0, 0.0, 1.0], [1.0, 0.0, 0.0]])  # 旋转角坐标系转换
    swap_transpose_inv = np.array([[0.0, 0.0, 1.0], [1.0, 0.0, 0.0], [0.0, 1.0, 0.0]])

    joint_rotation_mat = R.from_quat(bvh.joint_rotation.reshape(-1, 4)).as_matrix().reshape(bvh.motion_length, -1, 3, 3)
    joint_rotation_mat = joint_rotation_mat[:, idx_mapping, :, :]
    retargeted_joint_rot_mat = joint_rotation_mat
    retargeted_joint_rot_mat[:, [0]] = swap_transpose_inv @ retargeted_joint_rot_mat[:, [0]] @ swap_transpose
    feet_indices = [list(JOINT_MAP.keys()).index(name) for name in ["LeftFoot", "RightFoot"]]
    feet_orientations = retargeted_joint_rot_mat[:, feet_indices]
    root_orientation = retargeted_joint_rot_mat[0, 0]

    rel_rotation = feet_orientations @ yaw_matrix(root_orientation.T)

    retargeted_joint_rot_mat[:, feet_indices[0]] = retargeted_joint_rot_mat[:, feet_indices[0]] @ rel_rotation[:,0]
    retargeted_joint_rot_mat[:, feet_indices[1]] = retargeted_joint_rot_mat[:, feet_indices[1]] @ rel_rotation[:,1]
    retargeted_joint_rot_vec = R.from_matrix(retargeted_joint_rot_mat.reshape(-1, 3,3)).as_rotvec().reshape(bvh.motion_length, -1, 3)
    retargeted_joint_rot_vec = retargeted_joint_rot_vec.reshape(bvh.motion_length, -1)
    # retargeted_joint_rot_vec[:,36:] = 0
    # 全局姿态（Global Orientation）：3 个参数。
    # 身体姿态（Body Pose）：21 个关节 × 3 = 63个参数。
    # 总共66个，smpl还有左手右手参数，总共165个,现在我们直接不管，等以后重定向手的时候再说
    gender = np.array('neutral')

    # 计算腿长
    leg_length = 0
    leg_length += np.linalg.norm(retargeted_pos[0, 4, :])
    leg_length += np.linalg.norm(retargeted_pos[0, 7, :])

    np.savez(retargeted_data_path + bvh_files[i].split(".")[0] +"-retargeted.npz",
             mocap_frame_rate = 30,
             betas=np.ones(16),
             gender=gender,
             poses=retargeted_joint_rot_vec,
             trans=retargeted_root_pos,
             leg_length = leg_length,)
    # print(retargeted_data)
    # print(f"{bvh_files[i].split('.')[0]} retargeted data saved, shape: {retargeted_data.shape}")
    print(f"{bvh_files[i].split('.')[0]} retargeted data saved")


#     anim = read_bvh(bvh_path+bvh_files[i], order="zyx")
#     quats, positions = quat_fk(anim.quats, anim.pos, anim.parents)
#     positions = positions[:, :, [2, 0, 1]]
#     # self.positions *= self.wbik_params.template_scale
#     swap_transpose = np.array([[0.0, 0.0, 1.0], [1.0, 0.0, 0.0], [0.0, 1.0, 0.0]])
#     mats = ((quat2mat(quats.reshape(-1, 4)))[:, [2, 0, 1], :] @ swap_transpose).reshape(-1, 22, 3, 3)
#
#     # Remove global XY offset
#     positions[:, :, :2] -= positions[0, 0, :2]
#     joint_names = anim.bones
#     parents = anim.parents
#     idx_mapping = [joint_names.index(joint) for joint in JOINT_MAP.keys()]
#
#     # Realign feet with the torso along the z axis
#     feet_indices = [list(JOINT_MAP.keys()).index(name) for name in ["LeftFoot", "RightFoot"]]
#
#     feet_orientations = mats[0, feet_indices]
#     root_orientation = mats[0, 0]
#     rel_rotation = feet_orientations @ yaw_matrix(root_orientation.T)
#     mats[:, feet_indices[0]] = mats[:, feet_indices[0]] @ rel_rotation[0]
#     mats[:, feet_indices[1]] = mats[:, feet_indices[1]] @ rel_rotation[1]
#
#     positions /= 100.0
#
#     physicsClient = p.connect(p.GUI)
#     p.setAdditionalSearchPath(pybullet_data.getDataPath())
#     p.setGravity(0, 0, -9.8)
#     p.setRealTimeSimulation(0)
#
#     for j in range(511,755):
#         create_joint_visualization(positions[j],'Smpl')  # 可视化机器人关节
#         time.sleep(0.033)
# p.disconnect()
