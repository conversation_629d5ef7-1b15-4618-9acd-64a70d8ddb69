import mujoco
import mujoco.viewer
import numpy as np
import time

class MuJoCoVisualizer:
    """MuJoCo模型可视化器，支持T-pose和动画控制"""
    
    def __init__(self, model_path):
        """初始化可视化器"""
        self.model_path = model_path
        self.model = mujoco.MjModel.from_xml_path(model_path)
        self.data = mujoco.MjData(self.model)
        
        # 控制变量
        self.is_paused = True
        self.show_tpose = True
        self.simulation_speed = 1.0
        
        # 设置T-pose姿态
        self.setup_tpose()
        
    def setup_tpose(self):
        """设置T-pose姿态"""
        # 重置所有关节到零位置
        self.data.qpos[:] = 0
        
        # 设置特定关节到T-pose位置
        joint_tpose_angles = {
            # 手臂张开
            'LeftArm_joint': [0, 0, 90],  # 左臂向侧面张开
            'RightArm_joint': [0, 0, -90],  # 右臂向侧面张开
            
            # 前臂稍微弯曲
            'LeftForeArm_joint': [0, -15, 0],
            'RightForeArm_joint': [0, -15, 0],
            
            # 手掌自然下垂
            'LeftHand_joint': [0, 30, 0],
            'RightHand_joint': [0, 30, 0],
            
            # 腿部自然站立
            'LeftUpLeg_joint': [0, 0, 0],
            'RightUpLeg_joint': [0, 0, 0],
            
            # 脚部平放
            'LeftFoot_joint': [0, -90, 0],
            'RightFoot_joint': [0, -90, 0],
            
            # 脊椎直立
            'Spine_joint': [0, 0, 0],
            'Spine1_joint': [0, 0, 0],
            
            # 头部正视前方
            'Neck_joint': [0, 0, 0],
            'Head_joint': [0, 0, 0],
        }
        
        # 应用T-pose角度
        for joint_name, angles in joint_tpose_angles.items():
            joint_id = mujoco.mj_name2id(self.model, mujoco.mjtObj.mjOBJ_JOINT, joint_name)
            if joint_id >= 0:
                joint_addr = self.model.jnt_qposadr[joint_id]
                joint_type = self.model.jnt_type[joint_id]
                
                if joint_type == mujoco.mjtJoint.mjJNT_BALL:
                    # 球关节：将欧拉角转换为四元数
                    euler_rad = np.radians(angles)
                    quat = self.euler_to_quaternion(euler_rad)
                    self.data.qpos[joint_addr:joint_addr+4] = quat
                elif joint_type == mujoco.mjtJoint.mjJNT_HINGE:
                    # 铰链关节：只使用第一个角度
                    self.data.qpos[joint_addr] = np.radians(angles[0])
        
        # 前向运动学计算
        mujoco.mj_forward(self.model, self.data)
    
    def euler_to_quaternion(self, euler):
        """将欧拉角转换为四元数 (ZYX顺序)"""
        roll, pitch, yaw = euler
        
        cy = np.cos(yaw * 0.5)
        sy = np.sin(yaw * 0.5)
        cp = np.cos(pitch * 0.5)
        sp = np.sin(pitch * 0.5)
        cr = np.cos(roll * 0.5)
        sr = np.sin(roll * 0.5)
        
        w = cr * cp * cy + sr * sp * sy
        x = sr * cp * cy - cr * sp * sy
        y = cr * sp * cy + sr * cp * sy
        z = cr * cp * sy - sr * sp * cy
        
        return [w, x, y, z]
    
    def print_controls(self):
        """打印控制说明"""
        print("\n=== MuJoCo 可视化控制说明 ===")
        print("键盘控制:")
        print("  空格键    - 暂停/继续动画")
        print("  T         - 切换T-pose模式")
        print("  R         - 重置到初始姿态")
        print("  +/-       - 调整动画速度")
        print("  ESC       - 退出")
        print("\n鼠标控制:")
        print("  左键拖拽  - 旋转视角")
        print("  右键拖拽  - 移动视角")
        print("  滚轮      - 缩放")
        print("\n当前状态:")
        print(f"  模型文件: {self.model_path}")
        print(f"  关节数量: {self.model.njnt}")
        print(f"  几何体数量: {self.model.ngeom}")
        print("=" * 40)
    
    def handle_keypress(self, viewer, key):
        """处理键盘输入"""
        if key == ord(' '):  # 空格键 - 暂停/继续
            self.is_paused = not self.is_paused
            print(f"动画 {'暂停' if self.is_paused else '继续'}")
            
        elif key == ord('t') or key == ord('T'):  # T键 - T-pose模式
            self.show_tpose = not self.show_tpose
            if self.show_tpose:
                self.setup_tpose()
                print("切换到T-pose模式")
            else:
                print("切换到动画模式")
                
        elif key == ord('r') or key == ord('R'):  # R键 - 重置
            self.setup_tpose()
            print("重置到T-pose")
            
        elif key == ord('+') or key == ord('='):  # +键 - 加速
            self.simulation_speed = min(self.simulation_speed * 1.2, 5.0)
            print(f"动画速度: {self.simulation_speed:.1f}x")
            
        elif key == ord('-'):  # -键 - 减速
            self.simulation_speed = max(self.simulation_speed / 1.2, 0.1)
            print(f"动画速度: {self.simulation_speed:.1f}x")
    
    def run_visualization(self):
        """运行可视化"""
        self.print_controls()
        
        with mujoco.viewer.launch_passive(self.model, self.data) as viewer:
            # 设置相机位置
            viewer.cam.distance = 3.0
            viewer.cam.elevation = -20
            viewer.cam.azimuth = 45
            
            start_time = time.time()
            frame_count = 0
            
            while viewer.is_running():
                current_time = time.time()
                
                # 处理键盘输入（如果有的话）
                # 注意：mujoco viewer的键盘处理可能需要不同的方法
                
                if not self.is_paused and not self.show_tpose:
                    # 如果不是暂停状态且不是T-pose模式，则运行仿真
                    for _ in range(int(self.simulation_speed)):
                        mujoco.mj_step(self.model, self.data)
                elif self.show_tpose:
                    # T-pose模式：保持静态姿态
                    mujoco.mj_forward(self.model, self.data)
                
                # 同步显示
                viewer.sync()
                
                # 控制帧率
                frame_count += 1
                if frame_count % 60 == 0:  # 每60帧打印一次信息
                    elapsed = current_time - start_time
                    fps = frame_count / elapsed
                    print(f"FPS: {fps:.1f}, 模式: {'T-pose' if self.show_tpose else '动画' if not self.is_paused else '暂停'}")
                
                # 简单的延时控制
                time.sleep(0.01)


def main():
    """主函数"""
    import sys
    
    # 默认模型路径
    default_models = [
        "resources/robots/unitreeG1/mjcf/unitree_G1_retarget.xml"
    ]
    
    model_path = None
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        model_path = sys.argv[1]
    else:
        # 尝试找到可用的模型文件
        for path in default_models:
            try:
                test_model = mujoco.MjModel.from_xml_path(path)
                model_path = path
                print(f"使用默认模型: {path}")
                break
            except:
                continue
    
    if model_path is None:
        print("错误: 没有找到可用的模型文件")
        print("用法: python vis_mjcf.py <model_path.xml>")
        print("或确保以下文件之一存在:")
        for path in default_models:
            print(f"  - {path}")
        sys.exit(1)
    
    try:
        # 创建可视化器并运行
        visualizer = MuJoCoVisualizer(model_path)
        visualizer.run_visualization()
        
    except FileNotFoundError:
        print(f"错误: 找不到模型文件 '{model_path}'")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()