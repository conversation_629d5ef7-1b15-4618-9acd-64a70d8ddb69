import os
import sys
import os.path as osp
import numpy as np
import torch
import open3d as o3d
from smplx import SMPL
import joblib
import torch.nn.functional as F
from torch.optim.lr_scheduler import StepLR
from torch.autograd import Variable
import mujoco
import mujoco.viewer
import time
import threading
import queue
from dataclasses import dataclass
from typing import List, Tu<PERSON>, Dict, Optional
import json
from datetime import datetime
import pickle
from humanoid.utils.config_register import ConfigFactory
from humanoid.utils.humanoid_batch_registry import humanoid_batch_register
from humanoid.smpllib.smpl_parser import SMPL_Parser
# from humanoid.utils.torch_humanoid_batch import TorchHumanoidBatch
from humanoid.retarget_motion.base.motion_lib_cfg import MotionLibCfg

from scipy.spatial.transform import Rotation as sRot
from humanoid.smpllib.smpl_parser import (
    SMPL_Parser,
    SMPLH_Parser,
    SMPLX_Parser, 
    SMPL_BONE_ORDER_NAMES
)
from humanoid.utils.torch_humanoid_batch import Humanoid_Batch
from humanoid.retarget_motion.base.motion_lib_cfg import MotionLibCfg

# 修改SMPL_PARENTS字典，只保留实际存在的关节连接
SMPL_PARENTS = {
    1: 0,   # L_Hip -> Pelvis
    2: 0,   # R_Hip -> Pelvis
    3: 0,   # Spine1 -> Pelvis
    4: 1,   # L_Knee -> L_Hip
    5: 2,   # R_Knee -> R_Hip
    6: 3,   # Spine2 -> Spine1
    7: 4,   # L_Ankle -> L_Knee
    8: 5,   # R_Ankle -> R_Knee
    9: 6,   # Spine3 -> Spine2
    10: 7,  # L_Toe -> L_Ankle
    11: 8,  # R_Toe -> R_Ankle
    12: 9,  # Neck -> Spine3
    13: 9,  # L_Collar -> Spine3
    14: 9,  # R_Collar -> Spine3
    15: 12, # Head -> Neck
    16: 13, # L_Shoulder -> L_Collar
    17: 14, # R_Shoulder -> R_Collar
    18: 16, # L_Elbow -> L_Shoulder
    19: 17, # R_Elbow -> R_Shoulder
    20: 18, # L_Wrist -> L_Elbow
    21: 19, # R_Wrist -> R_Elbow
    22: 20, # L_Hand -> L_Wrist
    23: 21  # R_Hand -> R_Wrist
}

# 定义左右对称的骨骼对
SYMMETRIC_BONE_PAIRS = [
    # 腿部对称骨骼对 (左腿 - 右腿)
    ((1, 4), (2, 5)),    # 髋关节到膝盖
    ((4, 7), (5, 8)),    # 膝盖到脚踝
    ((7, 10), (8, 11)),  # 脚踝到脚趾
    
    # 手臂对称骨骼对 (左臂 - 右臂)
    ((13, 16), (14, 17)), # 锁骨到肩膀
    ((16, 18), (17, 19)), # 肩膀到肘部
    ((18, 20), (19, 21)), # 肘部到手腕
    ((20, 22), (21, 23))  # 手腕到手掌
]

@dataclass
class OptimizationParams:
    """优化参数类"""
    def __init__(self):
        self.joint_loss_weight: float = 1.0        # 关节损失权重，默认值1.0
        self.beta_reg_weight: float = 0.001        # beta正则化权重，默认值0.001
        self.scale_reg_weight: float = 0.001       # scale正则化权重，默认值0.001
        self.symmetry_loss_weight: float = 0.1     # 对称性损失权重，默认值0.1
        self.joint_angle_weight: float = 0.02      # 关节角度约束损失权重，默认值0.02
        self.bone_length_weight: float = 0.02      # 骨骼长度保持损失权重，默认值0.02
        self.endpoint_loss_weight: float = 0.5     # 末端位置损失权重，默认值0.5
        self.learning_rate: float = 0.005          # 学习率，默认值0.005
        self.iterations: int = 1500                # 迭代次数，默认值1500

class SMPLBetaOptimizer:
    def __init__(self, motion_lib_cfg, device=None):
        """初始化SMPL Beta优化器
        
        Args:
            motion_lib_cfg: 运动库配置
            device: 计算设备，默认使用CPU
        """
        self.motion_lib_cfg = motion_lib_cfg
        self.device = device if device is not None else torch.device("cpu")
        
        # 初始化优化参数
        self.params = OptimizationParams()
        
        # 加载 MuJoCo 模型
        self.model = mujoco.MjModel.from_xml_path(motion_lib_cfg.mjcf_file)
        self.data = mujoco.MjData(self.model)
        
        # 初始化 SMPL 模型
        self.smpl_model = SMPL(model_path='data/smpl/SMPL_NEUTRAL.pkl', gender='neutral').to(self.device)
        
        # 初始化所需变量
        self.initialize_variables()
        
        # 初始化SMPL根节点位置的优化变量，x 和 z 方向
        self.smpl_root_trans = torch.zeros(2, device=self.device, requires_grad=True)  # 只优化 x 和 z 方向

    def initialize_variables(self):
        """初始化所需变量"""
        # 初始化关节选择和索引
        self.joint_names = self.motion_lib_cfg.joint_names
        
        # 从配置文件中获取SMPL关键点对应关系
        if not hasattr(self.motion_lib_cfg, 'smpl_joint_correspondence'):
            error_msg = """
错误: 配置文件中未找到 smpl_joint_correspondence 配置项。

请按照以下步骤操作:
1. 在您的配置文件中添加 smpl_joint_correspondence 字典
2. 字典的键应该是T1机器人的关节名称
3. 字典的值应该是对应的SMPL关节名称

示例配置:
smpl_joint_correspondence = {
    'base_link': 'Pelvis',
    'leg_l4_link': 'L_Knee',
    'leg_l6_link': 'L_Ankle',
    'foot_l1_link': 'L_Toe',
    'leg_r4_link': 'R_Knee',
    'leg_r6_link': 'R_Ankle',
    'foot_r1_link': 'R_Toe',
    'left_arm_link02': 'L_Shoulder',
    'left_arm_link04': 'L_Elbow',
    'right_arm_link02': 'R_Shoulder',
    'right_arm_link04': 'R_Elbow'
}

请确保:
1. 所有T1关节名称都在 self.joint_names 中存在
2. 所有SMPL关节名称都在 SMPL_BONE_ORDER_NAMES 中存在
3. 关节对应关系合理且完整
"""
            raise ValueError(error_msg)
        
        # 使用配置文件中的对应关系
        self.T1_joint_pick = list(self.motion_lib_cfg.smpl_joint_correspondence.keys())
        self.smpl_joint_pick = list(self.motion_lib_cfg.smpl_joint_correspondence.values())
        
        # 验证所有选中的SMPL关键点是否有效
        invalid_joints = [joint for joint in self.smpl_joint_pick if joint not in SMPL_BONE_ORDER_NAMES]
        if invalid_joints:
            error_msg = f"""
错误: 以下SMPL关键点无效: {invalid_joints}

有效的SMPL关节名称包括:
{SMPL_BONE_ORDER_NAMES}

请检查您的 smpl_joint_correspondence 配置，确保所有SMPL关节名称都正确。
"""
            raise ValueError(error_msg)
        
        # 验证所有T1关节名称是否有效
        invalid_t1_joints = [joint for joint in self.T1_joint_pick if joint not in self.joint_names]
        if invalid_t1_joints:
            error_msg = f"""
错误: 以下T1关节名称无效: {invalid_t1_joints}

有效的T1关节名称包括:
{self.joint_names}

请检查您的 smpl_joint_correspondence 配置，确保所有T1关节名称都正确。
"""
            raise ValueError(error_msg)
        
        self.T1_joint_names_augment = self.joint_names
        self.T1_joint_pick_idx = [self.T1_joint_names_augment.index(j) for j in self.T1_joint_pick]
        self.smpl_joint_pick_idx = [SMPL_BONE_ORDER_NAMES.index(j) for j in self.smpl_joint_pick]
        
        # 初始化 Humanoid Forward Kinematics
        self.Humanoid_fk = Humanoid_Batch(self.motion_lib_cfg.mjcf_file, self.motion_lib_cfg.extend_node_dict)
        
        # 初始化 dof_pos
        self.dof_pos = torch.zeros((1, self.Humanoid_fk.joints_axis.shape[1]))
        if self.motion_lib_cfg.dof_pos is not None:
            self.dof_pos = self.motion_lib_cfg.dof_pos.to(self.device)
        
        # # 初始化肢体索引
        # self.limb_index = {}
        # for key in self.motion_lib_cfg.limb_names:
        #     limb_name = self.motion_lib_cfg.limb_names[key]
        #     self.limb_index[key] = [self.motion_lib_cfg.pos_names.index(i) for i in limb_name if i in self.motion_lib_cfg.pos_names]
        
        # 计算姿态角度
        self.calculate_pose_angles()
        
        # 初始化 SMPL 相关变量
        self.initialize_smpl_variables()

    def calculate_pose_angles(self):
        """计算各个部位的姿态角度"""
        # waist_pose_aa = self.Humanoid_fk.joints_axis[:, self.limb_index["waist"]] * self.dof_pos[:, self.limb_index["waist"],None]
        # leg_l_pose_aa = self.Humanoid_fk.joints_axis[:, self.limb_index["leg_l"]] * self.dof_pos[:, self.limb_index["leg_l"],None]
        # leg_r_pose_aa = self.Humanoid_fk.joints_axis[:, self.limb_index["leg_r"]] * self.dof_pos[:, self.limb_index["leg_r"],None]
        # arm_l_pose_aa = self.Humanoid_fk.joints_axis[:, self.limb_index["arm_l"]] * self.dof_pos[:, self.limb_index["arm_l"],None]
        # arm_r_pose_aa = self.Humanoid_fk.joints_axis[:, self.limb_index["arm_r"]] * self.dof_pos[:, self.limb_index["arm_r"],None]
        
        # self.pose_aa_gq = torch.cat([torch.zeros((1, 1, 3)),
        #                             waist_pose_aa,
        #                             leg_l_pose_aa,
        #                             leg_r_pose_aa,
        #                             arm_l_pose_aa,
        #                             arm_r_pose_aa],
        #                             axis=1).to(self.device)
        
        # --- 简化：一次性计算所有关节的轴角 ---
        # 获取关节轴 (假设形状为 [num_dofs, 3])
        axes = self.Humanoid_fk.joints_axis
        # 获取关节角度 (形状 [1, num_frames, num_dofs, 1])
        angles = self.dof_pos
        # 计算所有非根关节的轴角 (利用广播机制)
        # axes reshaped to [1, 1, num_dofs, 3]
        # angles [1, num_frames, num_dofs, 1] * axes [1, 1, num_dofs, 3] -> joint_aa [1, num_frames, num_dofs, 3]
        # --- 修正：使用 unsqueeze 来匹配广播 --- #
        joint_aa = angles.unsqueeze(-1) * axes # axes: [1, num_dofs, 3] -> [1, 1, num_dofs, 3]
        # --- 结束修正 --- #

        # 调整根旋转的形状以进行拼接 (形状 [num_frames, 3] -> [1, num_frames, 1, 3])
        root_rot = torch.zeros(1, 1, 3)

        # 拼接根旋转和关节轴角
        self.pose_aa_gq = torch.cat([root_rot, joint_aa], dim=1).to(self.device)
        # --- 结束简化 ---
        
        self.root_trans_gq = torch.zeros((1, 1, 3))

    def initialize_smpl_variables(self):
        """初始化 SMPL 相关变量"""
        # 准备 SMPL 默认姿态
        pose_aa_stand = np.zeros((1, 72))
        rotvec = sRot.from_quat([0.5, 0.5, 0.5, 0.5]).as_rotvec()
        pose_aa_stand[:, :3] = rotvec
        pose_aa_stand = pose_aa_stand.reshape(-1, 24, 3)
        self.pose_aa_stand = torch.from_numpy(pose_aa_stand.reshape(-1, 72))
        
        # 初始化 SMPL parser
        self.smpl_parser_n = SMPL_Parser(model_path="data/smpl", gender="neutral")
        
        # 初始化其他 SMPL 变量
        self.trans = torch.zeros([1, 3])
        self.beta = torch.zeros([1, 10])
        Smpl_verts, Smpl_joints = self.smpl_parser_n.get_joints_verts(self.pose_aa_stand, self.beta, self.trans)
        self.root_trans_offset = Smpl_joints[:, 0]

    def get_T1_positions(self):
        """获取T1关节位置"""
        fk_return = self.Humanoid_fk.fk_batch(self.pose_aa_gq[None, ], torch.zeros((1, 1, 3)), return_full=False)
        T1_positions = fk_return.global_translation[:,:,self.T1_joint_pick_idx]
        
        # 对所有关节点应用root_bias
        T1_positions = T1_positions
        
        return T1_positions
    
    def get_smpl_joints(self, shape_new, scale):
        """获取SMPL关节位置和顶点
        
        Args:
            shape_new: SMPL形状参数
            scale: 缩放参数
            
        Returns:
            Smpl_verts: SMPL顶点坐标 (形状: [1, num_vertices, 3])
            Smpl_joints: SMPL关节坐标 (形状: [1, num_joints, 3])
            smpl_positions: 选定的SMPL关节位置 (形状: [num_picked_joints, 3])
        """
        # 计算SMPL关节位置
        Smpl_verts, Smpl_joints = self.smpl_parser_n.get_joints_verts(self.pose_aa_stand, shape_new, self.trans[0:1])
        
        # 1. 先将根节点移动到原点
        root_pos = Smpl_joints[:, 0:1]  # 获取根节点位置 [1, 1, 3]
        Smpl_joints = Smpl_joints - root_pos  # 所有关节点减去根节点位置
        Smpl_verts = Smpl_verts - root_pos    # 所有顶点减去根节点位置
        
        # 2. 应用缩放
        Smpl_joints_scaled = Smpl_joints * scale
        Smpl_verts_scaled = Smpl_verts * scale
        
        # 3. 应用优化变量的偏置
        # 将 x 和 z 方向的偏置转换为 3D 向量，y 方向固定为 0
        root_trans_3d = torch.zeros((1, 1, 3), device=self.smpl_root_trans.device)
        root_trans_3d[0, 0, 0] = self.smpl_root_trans[0]  # x 方向
        root_trans_3d[0, 0, 2] = self.smpl_root_trans[1]  # z 方向
        
        Smpl_joints_scaled = Smpl_joints_scaled + root_trans_3d
        Smpl_verts_scaled = Smpl_verts_scaled + root_trans_3d
        
        # 获取选定的关节位置
        smpl_positions = Smpl_joints_scaled[0, self.smpl_joint_pick_idx]

        return Smpl_verts_scaled, Smpl_joints_scaled, smpl_positions
    
    def compute_joint_angle_loss(self, smpl_positions):
        """计算关节角度损失
        
        Args:
            smpl_positions: SMPL模型关节位置，形状为 [batch_size, num_joints, 3]
            
        Returns:
            torch.Tensor: 关节角度损失
        """
        # 确保输入张量维度正确
        if smpl_positions.dim() == 2:
            smpl_positions = smpl_positions.unsqueeze(0)  # 添加batch维度
        
        # 计算相邻关节之间的向量
        joint_vectors = smpl_positions[:, 1:] - smpl_positions[:, :-1]
        
        # 计算相邻向量之间的余弦值
        dot_products = torch.sum(joint_vectors[:, :-1] * joint_vectors[:, 1:], dim=-1)
        vector_norms = torch.norm(joint_vectors[:, :-1], dim=-1) * torch.norm(joint_vectors[:, 1:], dim=-1)
        cos_angles = dot_products / (vector_norms + 1e-6)  # 添加小量避免除零
        
        # 限制余弦值在[-1, 1]范围内
        cos_angles = torch.clamp(cos_angles, -1.0, 1.0)
        
        # 计算角度损失：当余弦值大于0.9（约25.8度）时产生损失
        angle_loss = torch.mean(torch.relu(cos_angles - 0.9))
        
        return angle_loss

    def compute_bone_length_loss(self, smpl_positions):
        """计算骨骼长度损失
        
        Args:
            smpl_positions: SMPL模型关节位置，形状为 [batch_size, num_joints, 3]
            
        Returns:
            torch.Tensor: 骨骼长度损失
        """
        # 确保输入张量维度正确
        if smpl_positions.dim() == 2:
            smpl_positions = smpl_positions.unsqueeze(0)  # 添加batch维度
        
        # 计算相邻关节之间的距离
        bone_lengths = torch.norm(smpl_positions[:, 1:] - smpl_positions[:, :-1], dim=-1)
        
        # 计算骨骼长度变化率
        length_ratios = bone_lengths[:, 1:] / (bone_lengths[:, :-1] + 1e-6)
        
        # 当长度变化超过10%时产生损失
        length_loss = torch.mean(torch.relu(torch.abs(length_ratios - 1.0) - 0.1))
        
        return length_loss

    def compute_losses(self, T1_positions, smpl_positions, shape_new, scale, params):
        """计算所有损失
        
        Args:
            T1_positions: T1机器人关节位置
            smpl_positions: SMPL模型关节位置
            shape_new: SMPL形状参数
            scale: 缩放参数
            params: 优化参数
            
        Returns:
            dict: 包含各项损失的字典
        """
        # 确保输入张量维度正确
        if T1_positions.dim() == 2:
            T1_positions = T1_positions.unsqueeze(0)
        if smpl_positions.dim() == 2:
            smpl_positions = smpl_positions.unsqueeze(0)
        
        # 计算关节位置损失
        joint_loss = torch.mean(torch.sum((T1_positions - smpl_positions) ** 2, dim=-1)) * params.joint_loss_weight
        
        # 计算beta正则化损失
        beta_reg_loss = torch.mean(shape_new ** 2) * params.beta_reg_weight
        
        # 计算scale正则化损失
        scale_reg_loss = ((scale - 0.656856) ** 2) * params.scale_reg_weight
        
        # 计算关节角度损失
        joint_angle_loss = self.compute_joint_angle_loss(smpl_positions) * params.joint_angle_weight
        
        # 计算骨骼长度损失
        bone_length_loss = self.compute_bone_length_loss(smpl_positions) * params.bone_length_weight
        
        return {
            'joint_loss': joint_loss,
            'beta_reg_loss': beta_reg_loss,
            'scale_reg_loss': scale_reg_loss,
            'joint_angle_loss': joint_angle_loss,
            'bone_length_loss': bone_length_loss
        }

    def compute_symmetry_loss(self, Smpl_joints, params):
        """计算骨骼对称性损失
        
        Args:
            Smpl_joints: SMPL关节坐标
            params: 优化参数
            
        Returns:
            symmetry_loss: 对称性损失
        """
        symmetry_loss = torch.tensor(0.0, device=self.device)
        
        for (left_start, left_end), (right_start, right_end) in SYMMETRIC_BONE_PAIRS:
            # 计算左侧骨骼向量
            left_bone_vec = Smpl_joints[0, left_end] - Smpl_joints[0, left_start]
            
            # 计算右侧骨骼向量
            right_bone_vec = Smpl_joints[0, right_end] - Smpl_joints[0, right_start]
            
            # 将右侧骨骼向量关于xz平面对称（翻转y坐标）
            right_bone_vec_mirrored = right_bone_vec.clone()
            right_bone_vec_mirrored[1] = -right_bone_vec_mirrored[1]  # y坐标取反
            
            # 计算对称后的向量差异（考虑方向）
            vec_diff = left_bone_vec - right_bone_vec_mirrored
            
            # 计算向量差异的平方范数
            symmetry_loss += torch.sum(vec_diff * vec_diff)
        
        return symmetry_loss * params.symmetry_loss_weight
    
    def compute_total_loss(self, loss_dict, symmetry_loss):
        """计算总损失
        
        Args:
            loss_dict: 包含各种损失的字典
            symmetry_loss: 对称性损失
            
        Returns:
            total_loss: 总损失
        """
        total_loss = sum(loss_dict.values()) + symmetry_loss
        return total_loss
    
    def save_optimization_result(self, shape_new, scale, T1_positions, smpl_positions, task_name, loss=None, save_path=None):
        """保存优化结果
        
        Args:
            shape_new: 优化后的SMPL形状参数
            scale: 优化后的缩放参数
            T1_positions: T1关节位置
            smpl_positions: SMPL关节位置
            task_name: 任务名称
            loss: 最终损失值，可选
            save_path: 保存路径（含文件名），可选，默认为None
        """
        # 获取 T1 机器人的变换矩阵
        fk_return = self.Humanoid_fk.fk_batch(self.pose_aa_gq[None, ], torch.zeros((1, 1, 3)), return_full=False)
        T1_transforms = fk_return.global_rotation_mat[0, 0].cpu().numpy()  # 使用 global_rotation_mat 而不是 global_transformation
        
        # 使用motion_lib_cfg.smpl_joint_correspondence中指定的关节来计算bias
        T1_joint_pick_bias = list(self.motion_lib_cfg.smpl_joint_correspondence.keys())
        T1_joint_pick_bias_idx = [self.T1_joint_pick.index(j) for j in T1_joint_pick_bias]
        
        # 计算每个关节的偏置（在局部坐标系下）
        bias = []
        for i in range(len(T1_joint_pick_bias_idx)):
            t1_idx = T1_joint_pick_bias_idx[i]
            t1_transform = T1_transforms[t1_idx]
            t1_R = t1_transform[:3, :3]  # 提取旋转矩阵部分
            diff = (T1_positions[0, 0, t1_idx] - smpl_positions[i]).cpu().numpy()
            local_diff = t1_R.T @ diff  # 将偏差转换到T1关节的局部坐标系
            bias.append(local_diff.tolist())
        
        smpl_root_trans = self.smpl_root_trans.detach().cpu().numpy()
        root_bias = np.zeros(3)
        root_bias[0] = smpl_root_trans[0]  # x 方向
        root_bias[2] = smpl_root_trans[1]  # z 方向
        # 准备保存的数据
        save_data = {
            "shape": shape_new.cpu().numpy().tolist(),
            "scale": scale.cpu().numpy().tolist(),
            "bias": bias,
            "root_bias": root_bias.tolist(),
            "joint_correspondence": self.motion_lib_cfg.smpl_joint_correspondence,
            "optimization_params": {
                "joint_loss_weight": self.params.joint_loss_weight,
                "beta_reg_weight": self.params.beta_reg_weight,
                "scale_reg_weight": self.params.scale_reg_weight,
                "symmetry_loss_weight": self.params.symmetry_loss_weight,
                "learning_rate": self.params.learning_rate,
                "iterations": self.params.iterations
            },
            "final_loss": float(loss) if loss is not None else None,
            "coordinate_system": {
                "bias": "local",
                "description": "每个bias值都在其对应T1机器人关节的局部坐标系下表示。使用T1机器人的关节变换矩阵将全局坐标系下的偏差转换到局部坐标系。"
            },
            "metadata": {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "task_name": task_name,
                "device": str(self.device)
            }
        }
        save_dir = os.path.join("data/calc_beta", task_name.split('_')[0])
        # 确定保存路径
        if save_path is None:
            file_name = "shape_scale_bias.json"
            save_path = os.path.join(save_dir, file_name)
        else:
            save_path = os.path.join(save_dir, save_path)
        # 确保目录存在
        os.makedirs(save_dir, exist_ok=True)
        # 保存为JSON文件，使用UTF-8编码
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=4, ensure_ascii=False)
        return save_path

    def save_config(self, config_path=None):
        """保存当前配置
        
        Args:
            config_path: 配置保存路径，可选，默认为None
            
        Returns:
            str: 保存的配置文件路径
        """
        # 准备保存的数据
        config_data = {
            "joint_loss_weight": self.params.joint_loss_weight,
            "beta_reg_weight": self.params.beta_reg_weight,
            "scale_reg_weight": self.params.scale_reg_weight,
            "symmetry_loss_weight": self.params.symmetry_loss_weight,
            "joint_angle_weight": self.params.joint_angle_weight,
            "bone_length_weight": self.params.bone_length_weight,
            "endpoint_loss_weight": self.params.endpoint_loss_weight,
            "learning_rate": self.params.learning_rate,
            "iterations": self.params.iterations,
            "metadata": {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "device": str(self.device)
            }
        }
        
        # 确定保存路径
        if config_path is None:
            config_path = "configs/default_beta_config.json"
        
        # 确保目录存在
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        # 保存为JSON文件，使用UTF-8编码
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=4, ensure_ascii=False)
        
        return config_path

class SMPLVisualizer:
    """SMPL可视化工具类"""
    
    def __init__(self, optimizer):
        """初始化可视化工具
        
        Args:
            optimizer: SMPLBetaOptimizer实例
        """
        self.optimizer = optimizer
        self.device = optimizer.device
        
        # MuJoCo查看器
        self.viewer = None
        
        # Open3D可视化相关变量
        self.mesh_queue = queue.Queue(maxsize=1)  # 用于线程间通信的队列
        self.vis_thread = None
        self.stop_vis_thread = threading.Event()
        self.o3d_vis_ready = threading.Event()  # 标记可视化窗口是否准备好
        self.vis = None # 初始化 vis 属性
        
    def init_mujoco_viewer(self):
        """初始化MuJoCo查看器"""
        if self.viewer is not None:
            return
            
        try:
            # 创建 viewer
            self.viewer = mujoco.viewer.launch_passive(self.optimizer.model, self.optimizer.data)
            # 设置相机参数
            self.viewer.cam.distance = 3.0
            self.viewer.cam.azimuth = 45
            self.viewer.cam.elevation = -20
            
            # 设置渲染选项 - 启用透明度
            self.viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_TRANSPARENT] = 1
            
            print("已创建持久性 MuJoCo 查看器窗口")
        except Exception as e:
            print(f"创建 MuJoCo 查看器时出错: {e}")
            self.viewer = None
    
    def start_visualization_thread(self):
        """启动Open3D可视化线程"""
        try:
            # 创建并启动线程
            self.vis_thread = threading.Thread(target=self.run_visualization, daemon=True)
            self.vis_thread.start()
            
            # 等待可视化窗口准备好
            if not self.o3d_vis_ready.wait(timeout=5.0):
                print("警告：Open3D可视化窗口启动超时")
            else:
                print("Open3D可视化线程已成功启动")
        except Exception as e:
            print(f"启动可视化线程时出错: {e}")
    
    def run_visualization(self):
        """Open3D可视化线程的主函数"""
        try:
            # 创建Open3D可视化窗口
            self.vis = o3d.visualization.Visualizer()
            self.vis.create_window(window_name="SMPL Mesh Visualization", width=1024, height=768)
            
            # 设置渲染选项
            render_option = self.vis.get_render_option()
            render_option.background_color = np.array([0.2, 0.2, 0.2])
            render_option.point_size = 5.0
            render_option.line_width = 2.0
            render_option.mesh_show_back_face = True
            
            # 添加坐标系
            coordinate_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.2)
            self.vis.add_geometry(coordinate_frame)
            
            # 初始化一个空网格
            # 确保 SMPL 模型已加载且 faces 可用
            if self.optimizer.smpl_model is None or self.optimizer.smpl_model.faces is None:
                 print("错误：SMPL 模型未正确初始化或 faces 不可用。")
                 return
            faces = self.optimizer.smpl_model.faces.astype(np.int32)
            # 使用 SMPL 模型获取顶点数量
            num_vertices = self.optimizer.smpl_model.v_template.shape[0]
            vertices = np.zeros((num_vertices, 3)) # 使用正确的顶点数量
            
            mesh = o3d.geometry.TriangleMesh()
            mesh.vertices = o3d.utility.Vector3dVector(vertices)
            mesh.triangles = o3d.utility.Vector3iVector(faces)
            mesh.compute_vertex_normals()
            mesh.paint_uniform_color([0.6, 0.6, 0.8])
            
            self.vis.add_geometry(mesh)
            
            # 设置初始视角
            view_control = self.vis.get_view_control()
            
            # 标记窗口已准备好
            self.o3d_vis_ready.set()
            
            # 运行可视化循环
            last_update_time = time.time()
            first_update = True
            
            while not self.stop_vis_thread.is_set():
                update_occurred = False # 标记本次循环是否有更新
                try:
                    if not self.mesh_queue.empty():
                        # 获取最新的网格数据
                        new_vertices = self.mesh_queue.get(block=False)
                        
                        # 验证顶点数据
                        if not isinstance(new_vertices, np.ndarray) or new_vertices.ndim != 2 or new_vertices.shape[1] != 3:
                            print(f"警告：从队列接收到无效的顶点数据，形状: {new_vertices.shape if isinstance(new_vertices, np.ndarray) else type(new_vertices)}")
                            continue # 跳过本次更新

                        # 检查顶点数量是否匹配
                        if new_vertices.shape[0] != len(mesh.vertices):
                             print(f"警告：接收到的顶点数量 ({new_vertices.shape[0]}) 与网格顶点数量 ({len(mesh.vertices)}) 不匹配。")
                             # 尝试重新创建网格（如果面片数量不变）
                             if len(mesh.triangles) == len(self.optimizer.smpl_model.faces):
                                 mesh.vertices = o3d.utility.Vector3dVector(new_vertices)
                                 mesh.compute_vertex_normals()
                                 self.vis.update_geometry(mesh)
                                 update_occurred = True
                             else:
                                 print("错误：面片数量也发生变化，无法更新网格。")
                                 continue
                        else:
                            # 更新网格顶点
                            mesh.vertices = o3d.utility.Vector3dVector(new_vertices)
                            mesh.compute_vertex_normals()
                            self.vis.update_geometry(mesh)
                            update_occurred = True
                        
                        # 如果是第一次更新，调整视角以适应模型
                        if first_update and update_occurred:
                            try:
                                # 计算模型边界
                                min_bound = np.min(new_vertices, axis=0)
                                max_bound = np.max(new_vertices, axis=0)
                                center = (min_bound + max_bound) / 2
                                size = np.linalg.norm(max_bound - min_bound)
                                
                                # 设置新的视角
                                view_control.set_front([0, -1, 0]) # 调整观察方向
                                view_control.set_lookat(center)    # 看向模型中心
                                view_control.set_up([0, 0, 1])     # 保持z轴向上
                                view_control.set_zoom(2.0)         # 调整缩放 (新值，更近)

                                first_update = False
                            except Exception as e:
                                print(f"调整初始视图时出错: {e}")
                        
                        # 重置上次更新时间
                        last_update_time = time.time()
                        
                except queue.Empty:
                    pass
                except Exception as e:
                     print(f"处理网格更新时出错: {e}")
                     import traceback
                     traceback.print_exc()

                # 更新可视化窗口事件
                if not self.vis.poll_events():
                    print("Open3D 窗口已关闭。")
                    break # 如果窗口关闭，退出循环
                
                # 更新渲染器
                self.vis.update_renderer()

                # 短暂休眠，避免CPU占用过高
                time.sleep(0.01)
            
            # 循环结束后关闭窗口
            print("正在关闭 Open3D 窗口...")
            if self.vis:
                self.vis.destroy_window()
                self.vis = None # 标记窗口已销毁
            
        except Exception as e:
            print(f"可视化线程出错: {e}")
            import traceback
            traceback.print_exc()
            
            # 确保窗口被关闭
            try:
                if self.vis:
                    self.vis.destroy_window()
                    self.vis = None
            except:
                pass
        finally:
             print("Open3D 可视化线程已退出。")

    def update_mujoco_viewer(self, smpl_joints, T1_positions, shape_new=None, scale=None, iteration=0, show_completion_text=False):
        """更新MuJoCo查看器内容"""
        if self.viewer is None or not self.viewer.is_running():
            return
        
        try:
            # 更新机器人姿态
            with torch.no_grad():
                joint_angles = self.optimizer.dof_pos[0].cpu().numpy()

            # 设置机器人位置和姿态
            qpos = np.zeros(self.optimizer.model.nq)
            qpos[3:7] = [1, 0, 0, 0]  # 根关节使用单位四元数

            # 设置关节角度
            num_dofs = len(joint_angles)
            if 7 + num_dofs <= len(qpos):
                qpos[7:7+num_dofs] = joint_angles
            else:
                print(f"警告: 关节角度数量 ({num_dofs}) 超出模型自由度 ({len(qpos)-7})")
                qpos[7:] = joint_angles[:len(qpos)-7]

            self.optimizer.data.qpos[:] = qpos
            mujoco.mj_forward(self.optimizer.model, self.optimizer.data)
            
            # 设置机器人为半透明
            try:
                for i in range(self.optimizer.model.ngeom):
                    if hasattr(self.optimizer.model, 'geom_rgba') and self.optimizer.model.geom_rgba.flags.writeable:
                        rgba = self.optimizer.model.geom_rgba[i].copy()
                        rgba[:3] = np.clip(rgba[:3] * 1.2, 0, 1)
                        rgba[3] = 0.4
                        self.optimizer.model.geom_rgba[i] = rgba
            except Exception as e:
                print(f"设置透明度时出错: {e}")
            
            # 清空所有几何体
            self.viewer.user_scn.ngeom = 0
            geom_idx = 0
            max_geoms = self.viewer.user_scn.maxgeom
            
            # 1. 添加坐标系
            # 定义不同坐标系的大小和样式
            coord_systems = {
                "world": {
                    "pos": np.zeros(3),  # 世界坐标系原点
                    "size": 0.15,        # 最大的坐标系
                    "radius": 0.008,     # 较粗的轴
                    "alpha": 0.8,        # 最不透明
                    "label": "World",    # 标签文本
                    "label_offset": np.array([0.02, 0.02, 0.02])  # 标签位置偏移
                }
            }
            
            # 坐标轴基础颜色 (RGB)
            axis_colors = {
                "x": np.array([1.0, 0.0, 0.0]),  # 红色
                "y": np.array([0.0, 1.0, 0.0]),  # 绿色
                "z": np.array([0.0, 0.0, 1.0])   # 蓝色
            }
            
            # 为每个坐标系绘制三个轴
            for system_name, system_info in coord_systems.items():
                for axis_name, direction in zip(['x', 'y', 'z'], np.eye(3)):
                    if geom_idx >= max_geoms: break
                    
                    # 创建圆柱体表示坐标轴
                    origin = system_info["pos"]
                    end_point = origin + direction * system_info["size"]
                    mid_point = (origin + end_point) / 2
                    
                    # 计算旋转矩阵
                    if not np.allclose(direction, np.array([0, 0, 1])):
                        v = np.cross(np.array([0, 0, 1]), direction)
                        s = np.linalg.norm(v)
                        if s > 1e-6:  # 避免除以零
                            c = np.dot(np.array([0, 0, 1]), direction)
                            v_x = np.array([
                                [0, -v[2], v[1]],
                                [v[2], 0, -v[0]],
                                [-v[1], v[0], 0]
                            ])
                            rot_mat = np.eye(3) + v_x + v_x.dot(v_x) * (1 - c) / (s * s)
                        else:
                            rot_mat = np.eye(3)
                    else:
                        rot_mat = np.eye(3)
                    
                    # 设置颜色和透明度
                    color = axis_colors[axis_name].copy()
                    rgba = np.append(color, system_info["alpha"])
                    
                    # 设置几何体
                    mujoco.mjv_initGeom(
                        self.viewer.user_scn.geoms[geom_idx],
                        mujoco.mjtGeom.mjGEOM_CYLINDER,
                        np.array([system_info["radius"], system_info["size"]/2, 0], dtype=np.float64),
                        mid_point.astype(np.float64),
                        rot_mat.flatten().astype(np.float64),
                        rgba.astype(np.float32)
                    )
                    geom_idx += 1
            
            # 2. 添加SMPL骨骼连接
            with torch.no_grad():
                smpl_np = smpl_joints[0].cpu().numpy()
            
            # 首先添加骨骼连接
            for child, parent in SMPL_PARENTS.items():
                if geom_idx >= max_geoms:
                    break
                
                if parent >= 0 and child < smpl_np.shape[0] and parent < smpl_np.shape[0]:
                    start = smpl_np[parent]
                    end = smpl_np[child]
                    
                    # 计算胶囊体的中心点和长度
                    mid_point = (start + end) / 2
                    direction = end - start
                    length = np.linalg.norm(direction)
                    
                    if length < 1e-6:  # 避免零长度骨骼
                        continue
                    
                    # 计算胶囊体的旋转矩阵
                    direction = direction / length
                    z_axis = np.array([0, 0, 1])
                    if np.allclose(direction, z_axis) or np.allclose(direction, -z_axis):
                        rot_mat = np.eye(3)
                    else:
                        v = np.cross(z_axis, direction)
                        s = np.linalg.norm(v)
                        c = np.dot(z_axis, direction)
                        v_x = np.array([
                            [0, -v[2], v[1]],
                            [v[2], 0, -v[0]],
                            [-v[1], v[0], 0]
                        ])
                        rot_mat = np.eye(3) + v_x + v_x.dot(v_x) * (1 - c) / (s * s)
                    
                    # 设置胶囊体参数 - 使用与calc_beta_T1.py相同的设置
                    radius = max(length * 0.08, 0.01)
                    
                    # 初始化几何体 - 使用蓝色，与calc_beta_T1.py一致
                    mujoco.mjv_initGeom(
                        self.viewer.user_scn.geoms[geom_idx],
                        mujoco.mjtGeom.mjGEOM_CAPSULE,
                        np.array([radius, length/2, 0], dtype=np.float64),
                        mid_point.astype(np.float64),
                        rot_mat.flatten().astype(np.float64),
                        np.array([0.0, 0.0, 1.0, 0.3], dtype=np.float32)  # 蓝色，与calc_beta_T1.py一致
                    )
                    geom_idx += 1
            
            # 3. 添加T1关节点和连接线
            with torch.no_grad():
                t1_np = T1_positions[0, 0].cpu().numpy()
                # 获取所有T1关节点位置
                fk_return = self.optimizer.Humanoid_fk.fk_batch(self.optimizer.pose_aa_gq[None, ], torch.zeros((1, 1, 3)), return_full=False)
                all_t1_positions = fk_return.global_translation[0, 0].cpu().numpy()
            
            # 获取优化使用的关节点索引
            smpl_joint_pick_idx = self.optimizer.smpl_joint_pick_idx
            
            # 使用集合来跟踪已显示的标签
            displayed_labels = set()
            
            # 首先显示所有T1关节点（使用灰色）
            for i, t1_pos in enumerate(all_t1_positions):
                if geom_idx >= max_geoms:
                    break
                
                # 检查是否是用于优化的关节点
                is_optimized = i in self.optimizer.T1_joint_pick_idx
                
                # 初始化几何体 - 优化的关节点使用红色，其他使用灰色
                mujoco.mjv_initGeom(
                    self.viewer.user_scn.geoms[geom_idx],
                    mujoco.mjtGeom.mjGEOM_SPHERE,
                    np.array([0.012, 0, 0], dtype=np.float64),  # 较小的球体
                    t1_pos.astype(np.float64),
                    np.eye(3).flatten().astype(np.float64),  # 单位矩阵
                    np.array([1.0, 0.0, 0.0, 0.5] if is_optimized else [0.5, 0.5, 0.5, 0.3], dtype=np.float32)  # 优化的红色，其他灰色
                )
                geom_idx += 1
                
                # 添加T1关节点标签（如果还没有显示过）
                joint_name = self.optimizer.joint_names[i]
                if joint_name not in displayed_labels and geom_idx < max_geoms:
                    label_pos = t1_pos + np.array([0.01, 0.01, 0.01])  # 减小标签位置偏移
                    mujoco.mjv_initGeom(
                        self.viewer.user_scn.geoms[geom_idx],
                        mujoco.mjtGeom.mjGEOM_LABEL,
                        np.array([0.015, 0, 0], dtype=np.float64),  # 减小标签大小
                        label_pos.astype(np.float64),
                        np.eye(3).flatten().astype(np.float64),  # 单位矩阵
                        np.array([1.0, 1.0, 1.0, 1.0], dtype=np.float32)  # 白色
                    )
                    self.viewer.user_scn.geoms[geom_idx].label = joint_name
                    displayed_labels.add(joint_name)
                    geom_idx += 1
            
            # 然后添加用于优化的T1关节点到SMPL的连接线
            for i, t1_pos in enumerate(t1_np):
                if i >= len(smpl_joint_pick_idx) or smpl_joint_pick_idx[i] >= smpl_np.shape[0]:
                    continue
                    
                if geom_idx >= max_geoms:
                    break
                
                smpl_idx = smpl_joint_pick_idx[i]
                smpl_pos = smpl_np[smpl_idx]
                
                # 添加SMPL选中关节点 (蓝色)
                mujoco.mjv_initGeom(
                    self.viewer.user_scn.geoms[geom_idx],
                    mujoco.mjtGeom.mjGEOM_SPHERE,
                    np.array([0.015, 0, 0], dtype=np.float64),  # 球体半径
                    smpl_pos.astype(np.float64),
                    np.eye(3).flatten().astype(np.float64),  # 单位矩阵
                    np.array([0.0, 0.0, 1.0, 0.4], dtype=np.float32)  # 蓝色
                )
                geom_idx += 1
                
                # 添加SMPL关节点标签
                if geom_idx >= max_geoms:
                    break
                label_pos = smpl_pos + np.array([0.01, 0.01, 0.01])  # 减小标签位置偏移
                mujoco.mjv_initGeom(
                    self.viewer.user_scn.geoms[geom_idx],
                    mujoco.mjtGeom.mjGEOM_LABEL,
                    np.array([0.015, 0, 0], dtype=np.float64),  # 减小标签大小
                    label_pos.astype(np.float64),
                    np.eye(3).flatten().astype(np.float64),  # 单位矩阵
                    np.array([1.0, 1.0, 1.0, 1.0], dtype=np.float32)  # 白色
                )
                self.viewer.user_scn.geoms[geom_idx].label = f"{self.optimizer.smpl_joint_pick[i]}"
                geom_idx += 1
                
                if geom_idx >= max_geoms:
                    break
                
                # 添加T1到SMPL的连接线 (紫色)
                mid_point = (t1_pos + smpl_pos) / 2
                direction = smpl_pos - t1_pos
                length = np.linalg.norm(direction)
                
                if length < 1e-6:  # 避免零长度连接线
                    continue
                
                # 计算胶囊体的旋转矩阵
                direction = direction / length
                z_axis = np.array([0, 0, 1])
                if np.allclose(direction, z_axis) or np.allclose(direction, -z_axis):
                    rot_mat = np.eye(3)
                else:
                    v = np.cross(z_axis, direction)
                    s = np.linalg.norm(v)
                    c = np.dot(z_axis, direction)
                    v_x = np.array([
                        [0, -v[2], v[1]],
                        [v[2], 0, -v[0]],
                        [-v[1], v[0], 0]
                    ])
                    rot_mat = np.eye(3) + v_x + v_x.dot(v_x) * (1 - c) / (s * s)
                
                # 设置胶囊体参数
                radius = 0.003  # 较细的连接线
                
                # 初始化几何体 - 使用紫色
                mujoco.mjv_initGeom(
                    self.viewer.user_scn.geoms[geom_idx],
                    mujoco.mjtGeom.mjGEOM_CAPSULE,
                    np.array([radius, length/2, 0], dtype=np.float64),
                    mid_point.astype(np.float64),
                    rot_mat.flatten().astype(np.float64),
                    np.array([0.7, 0.0, 0.7, 0.5], dtype=np.float32)  # 紫色半透明
                )
                geom_idx += 1
            
            # 4. 添加原始SMPL骨骼(beta=0的T-pose) - 与calc_beta_T1.py一致
            # 获取原始SMPL关节位置
            zero_beta_output = self.optimizer.smpl_model(
                betas=torch.zeros([1, 10], dtype=torch.float32).to(self.optimizer.device),
                global_orient=self.optimizer.pose_aa_stand[0, :3].clone().to(dtype=torch.float32)[None],
                body_pose=torch.zeros([1, 69], dtype=torch.float32).to(self.optimizer.device),
                transl=torch.tensor([[2.0, 0.0, 0.0]], dtype=torch.float32).to(self.optimizer.device),
            )
            zero_beta_joints = zero_beta_output.joints[0].detach().cpu().numpy()

            # 检查zero_beta_joints是否有效
            if zero_beta_joints is None or zero_beta_joints.shape[0] < 24:
                print("警告: 无法获取有效的原始SMPL关节点数据。")
            else:
                # 显示所有24个关节点(使用绿色)
                for i in range(24):
                    if geom_idx >= max_geoms:
                        print(f"警告: Geom limit reached ({geom_idx}/{max_geoms}) while drawing original SMPL joints.")
                        break

                    if i >= zero_beta_joints.shape[0]:
                        print(f"警告: 尝试访问索引 {i} 超出原始SMPL关节点数组范围 ({zero_beta_joints.shape[0]})。")
                        continue

                    joint_pos = zero_beta_joints[i]
                    if np.any(np.isnan(joint_pos)):
                        continue

                    mujoco.mjv_initGeom(
                        self.viewer.user_scn.geoms[geom_idx],
                        mujoco.mjtGeom.mjGEOM_SPHERE,
                        np.array([0.02, 0.0, 0.0], dtype=np.float64),
                        joint_pos,
                        np.eye(3, dtype=np.float64).flatten(),
                        np.array([0.0, 1.0, 0.0, 0.6], dtype=np.float32)  # 绿色，与calc_beta_T1.py一致
                    )
                    geom_idx += 1
                    
                    # 添加SMPL关节点标签
                    if geom_idx >= max_geoms:
                        break
                    label_pos = joint_pos + np.array([0.01, 0.01, 0.01])  # 减小标签位置偏移
                    mujoco.mjv_initGeom(
                        self.viewer.user_scn.geoms[geom_idx],
                        mujoco.mjtGeom.mjGEOM_LABEL,
                        np.array([0.015, 0, 0], dtype=np.float64),  # 减小标签大小
                        label_pos.astype(np.float64),
                        np.eye(3).flatten().astype(np.float64),  # 单位矩阵
                        np.array([1.0, 1.0, 1.0, 1.0], dtype=np.float32)  # 白色
                    )
                    self.viewer.user_scn.geoms[geom_idx].label = f"{SMPL_BONE_ORDER_NAMES[i]}"
                    geom_idx += 1

                # 添加所有骨骼连接 (使用绿色)
                for child, parent in SMPL_PARENTS.items():
                    if geom_idx >= max_geoms:
                        print(f"警告: Geom limit reached ({geom_idx}/{max_geoms}) while drawing original SMPL bones.")
                        break

                    if parent < 0 or parent >= zero_beta_joints.shape[0] or child >= zero_beta_joints.shape[0]:
                        continue

                    start = zero_beta_joints[parent]
                    end = zero_beta_joints[child]

                    if np.any(np.isnan(start)) or np.any(np.isnan(end)):
                        continue

                    mid_point = (start + end) / 2
                    direction = end - start
                    length = np.linalg.norm(direction)

                    if length < 1e-6: continue

                    direction = direction / length
                    z_axis = np.array([0, 0, 1])
                    if np.allclose(direction, z_axis) or np.allclose(direction, -z_axis):
                        rot_mat = np.eye(3)
                    else:
                        v = np.cross(z_axis, direction)
                        s = np.linalg.norm(v)
                        c = np.dot(z_axis, direction)
                        v_x = np.array([
                            [0, -v[2], v[1]],
                            [v[2], 0, -v[0]],
                            [-v[1], v[0], 0]
                        ])
                        rot_mat = np.eye(3) + v_x + v_x.dot(v_x) * (1 - c) / (s * s)

                    radius = max(length * 0.08, 0.01)
                    size = np.array([radius, length/2, 0], dtype=np.float64)

                    mujoco.mjv_initGeom(
                        self.viewer.user_scn.geoms[geom_idx],
                        mujoco.mjtGeom.mjGEOM_CAPSULE,
                        size,
                        mid_point,
                        rot_mat.flatten(),
                        np.array([0.0, 1.0, 0.0, 0.7], dtype=np.float32)  # 绿色，与calc_beta_T1.py一致
                    )
                    geom_idx += 1
            
            # 更新几何体数量
            self.viewer.user_scn.ngeom = geom_idx
            
            # 如果优化已完成，显示提示文本
            if show_completion_text and geom_idx < max_geoms:
                try:
                    # 在屏幕左上角添加完成提示
                    mujoco.mjv_initGeom(
                        self.viewer.user_scn.geoms[geom_idx],
                        mujoco.mjtGeom.mjGEOM_LABEL,
                        np.array([0.1, 0, 0], dtype=np.float64),  # 标签大小
                        np.array([-0.9, 0.9, 0], dtype=np.float64),  # 屏幕左上角位置
                        np.eye(3).flatten().astype(np.float64),  # 单位矩阵
                        np.array([1.0, 1.0, 1.0, 1.0], dtype=np.float32)  # 白色
                    )
                    self.viewer.user_scn.geoms[geom_idx].label = "优化已完成! 按ESC关闭窗口"
                    geom_idx += 1
                except Exception as e:
                    print(f"添加完成提示文本时出错: {e}")
            
            # 渲染场景
            self.viewer.sync()
        
        except Exception as e:
            print(f"更新MuJoCo查看器时出错: {e}")
            import traceback
            traceback.print_exc()

    def stop_visualization(self):
        """停止所有可视化"""
        print("正在请求停止可视化...")
        # 设置停止标志
        self.stop_vis_thread.set()

        # 等待Open3D可视化线程结束
        if self.vis_thread is not None and self.vis_thread.is_alive():
            print("等待 Open3D 线程结束...")
            self.vis_thread.join(timeout=5.0) # 增加超时时间

            if self.vis_thread.is_alive():
                print("警告：Open3D 可视化线程未能正常关闭")
            else:
                print("Open3D 可视化线程已正常关闭")
        else:
             print("Open3D 可视化线程未运行或已结束。")

        # 关闭MuJoCo查看器
        if self.viewer is not None:
            try:
                if self.viewer.is_running():
                     print("正在关闭 MuJoCo 查看器...")
                     self.viewer.close()
                     print("MuJoCo 查看器已关闭。")
                else:
                     print("MuJoCo 查看器未运行。")
            except Exception as e:
                print(f"关闭 MuJoCo 查看器时出错: {e}")
            finally:
                 self.viewer = None # 确保引用被清除
        else:
             print("MuJoCo 查看器未初始化。")
