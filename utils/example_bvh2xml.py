#!/usr/bin/env python3
"""
Example usage of BVH to MuJoCo XML converter

This script demonstrates how to use the bvh2xml converter.
"""

import os
import sys
from bvh2xml import BVH2MuJoCo

def example_conversion():
    """Example of converting a BVH file to MuJoCo XML"""
    
    # Example BVH file path (you need to provide your own BVH file)
    bvh_file = "example_motion.bvh"  # Replace with your BVH file path
    output_xml = "example_humanoid.xml"
    
    # Check if BVH file exists
    if not os.path.exists(bvh_file):
        print(f"Error: BVH file '{bvh_file}' not found.")
        print("Please provide a valid BVH file path.")
        return False
    
    # Create converter instance
    converter = BVH2MuJoCo()
    
    try:
        # Parse BVH file
        print(f"Parsing BVH file: {bvh_file}")
        root_joint = converter.parse_bvh(bvh_file)
        
        if root_joint is None:
            print("Failed to parse BVH file")
            return False
        
        # Print some information about the skeleton
        print(f"Root joint: {root_joint.Name}")
        print(f"Total keyframes: {len(root_joint.Keyframes) if hasattr(root_joint, 'Keyframes') else 'N/A'}")
        
        # Convert to MuJoCo XML
        print("Converting to MuJoCo XML...")
        output_path = converter.create_mujoco_xml(root_joint, output_xml)
        
        print(f"Conversion completed successfully!")
        print(f"Output file: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"Error during conversion: {e}")
        return False

def print_bvh_info(bvh_file):
    """Print information about a BVH file"""
    if not os.path.exists(bvh_file):
        print(f"Error: BVH file '{bvh_file}' not found.")
        return
    
    converter = BVH2MuJoCo()
    root_joint = converter.parse_bvh(bvh_file)
    
    if root_joint is None:
        print("Failed to parse BVH file")
        return
    
    print(f"\n=== BVH File Information ===")
    print(f"File: {bvh_file}")
    print(f"Root joint: {root_joint.Name}")
    
    def print_joint_hierarchy(joint, indent=0):
        """Recursively print joint hierarchy"""
        prefix = "  " * indent
        print(f"{prefix}- {joint.Name}")
        
        if hasattr(joint, 'Children') and joint.Children:
            for child in joint.Children:
                print_joint_hierarchy(child, indent + 1)
    
    print("\nJoint Hierarchy:")
    print_joint_hierarchy(root_joint)
    
    # Count total joints
    def count_joints(joint):
        count = 1
        if hasattr(joint, 'Children') and joint.Children:
            for child in joint.Children:
                count += count_joints(child)
        return count
    
    total_joints = count_joints(root_joint)
    print(f"\nTotal joints: {total_joints}")
    
    if hasattr(root_joint, 'Keyframes') and root_joint.Keyframes:
        print(f"Total keyframes: {len(root_joint.Keyframes)}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        bvh_file = sys.argv[1]
        print_bvh_info(bvh_file)
        
        # Ask if user wants to convert
        response = input("\nDo you want to convert this BVH to MuJoCo XML? (y/n): ")
        if response.lower() in ['y', 'yes']:
            output_name = input("Enter output XML filename (or press Enter for default): ").strip()
            if not output_name:
                base_name = os.path.splitext(bvh_file)[0]
                output_name = f"{base_name}_mujoco.xml"
            
            converter = BVH2MuJoCo()
            root_joint = converter.parse_bvh(bvh_file)
            if root_joint:
                converter.create_mujoco_xml(root_joint, output_name)
    else:
        print("Usage: python example_bvh2xml.py <bvh_file>")
        print("This will display information about the BVH file and optionally convert it.") 