# 移除SMPL相关的导入，改为BVH相关
import mujoco
import mujoco.viewer
import numpy as np
import time

class MujocoViwer:
    def __init__(self, CFG):

        self.robot_link_names = CFG.joint_names
        self.left_toe_name = CFG.left_toe_name
        self.right_toe_name = CFG.right_toe_name
        self.mjcf_file = CFG.mjcf_file

        self.left_toe_idx = self.robot_link_names.index(self.left_toe_name)
        self.right_toe_idx = self.robot_link_names.index(self.right_toe_name)

        # 🔧 添加安全检查：确保 bvh_joint_correspondence 存在且不为空
        if hasattr(CFG, 'bvh_joint_correspondence') and CFG.bvh_joint_correspondence:
            self.robot_link_pick = list(CFG.bvh_joint_correspondence.keys())
            self.bvh_joint_pick = list(CFG.bvh_joint_correspondence.values())
            print(f"✅ 从配置中加载BVH关节对应关系: {len(self.robot_link_pick)} 对")
        else:
            print("⚠️ 配置中没有bvh_joint_correspondence，使用默认关节选择")
            # 使用默认的关节选择策略
            if hasattr(CFG, 'joint_pick') and CFG.joint_pick:
                self.robot_link_pick = CFG.joint_pick
                self.bvh_joint_pick = []  # 将在运行时填充
            else:
                # 最后的回退方案：使用一些常见的关节名称
                common_joints = ['leg_l4_link', 'leg_l6_link', 'foot_l1_link', 
                               'leg_r4_link', 'leg_r6_link', 'foot_r1_link']
                self.robot_link_pick = [j for j in common_joints if j in self.robot_link_names]
                self.bvh_joint_pick = []
                
        # BVH关节重排序（用于标准人体骨架连接）
        self.bvh_joint_pick_reordered = ["Hips", "Spine", "Spine1", "Neck", "Head", 
                                        "LeftUpLeg", "LeftLeg", "LeftFoot", "LeftToeBase",
                                        "RightUpLeg", "RightLeg", "RightFoot", "RightToeBase",
                                        "LeftShoulder", "LeftForeArm", "LeftHand",
                                        "RightShoulder", "RightForeArm", "RightHand"]
        
        # 🔧 添加安全检查：确保所有关节名称都在robot_link_names中
        valid_robot_links = []
        for joint in self.robot_link_pick:
            if joint in self.robot_link_names:
                valid_robot_links.append(joint)
            else:
                print(f"⚠️ 关节 '{joint}' 不在robot_link_names中，已跳过")
        
        self.robot_link_pick = valid_robot_links
        
        # 🔧 安全地生成关节索引
        if self.robot_link_pick:
            self.robot_joint_pick_idx = [self.robot_link_names.index(j) for j in self.robot_link_pick]
        else:
            print("❌ 错误: 没有有效的机器人关节，使用空列表")
            self.robot_joint_pick_idx = []
        
        # 注意：BVH关节索引需要在运行时从BVH数据中获取，这里先初始化为空
        self.bvh_joint_pick_idx = []
        self.bvh_joint_pick_idx_reordered = []
        
        print(f"MujocoViwer初始化完成:")
        print(f"  - 机器人关节选择: {self.robot_link_pick}")
        print(f"  - 机器人关节索引: {self.robot_joint_pick_idx}")
        print(f"  - BVH关节选择: {self.bvh_joint_pick}")
        print(f"  - 左脚趾索引: {self.left_toe_idx} ('{self.left_toe_name}')")
        print(f"  - 右脚趾索引: {self.right_toe_idx} ('{self.right_toe_name}')")
    
    def euler_to_quat(self, euler):
        """将旋转向量转换为MuJoCo四元数格式 [w, x, y, z]。

        Args:
            euler: 大小为 (3,) 的旋转向量 (角轴表示)。

        Returns:
            大小为 (4,) 的 NumPy 数组，表示MuJoCo格式的四元数。
        """
        angle = np.linalg.norm(euler)
        if angle < 1e-10:  # 接近零旋转，返回单位四元数
            return np.array([1.0, 0.0, 0.0, 0.0])

        axis = euler / angle
        w = np.cos(angle / 2.0)
        xyz = axis * np.sin(angle / 2.0)
        return np.array([w, xyz[0], xyz[1], xyz[2]])

        
    def visRefPoint(self, bvh_joints_scaled, T1_joint_dump, T1_joint, bvh_key_joints, 
                    contact_sequence=None, root_trans_offset=None, dof_pos_new=None, 
                    root_rot_new=None, final_frame_losses=None, left_foot_samples=None, right_foot_samples=None,
                    bvh_skeleton_connections=None):
        """使用 MuJoCo 可视化BVH参考点、机器人运动和脚底采样点"""

        # --- Start: Define foot indices early --- #
        # --- 修改：直接使用类属性中的脚趾索引 ---
        left_foot_vis_idx = self.left_toe_idx
        right_foot_vis_idx = self.right_toe_idx
        # --- 结束修改 ---
        if left_foot_vis_idx == -1 or right_foot_vis_idx == -1:
             print(f"[visRefPoint Warning] 无效的脚趾/脚踝索引 ({left_foot_vis_idx}, {right_foot_vis_idx})。接触指示器将不可用。")
        else:
            print(f"[visRefPoint Info] 使用索引进行接触可视化: Left={left_foot_vis_idx}, Right={right_foot_vis_idx}")
            # --- Added Print --- #
            print(f"[visRefPoint Info] Link Name for Left Index {left_foot_vis_idx}: {self.robot_link_names[left_foot_vis_idx]}")
            print(f"[visRefPoint Info] Link Name for Right Index {right_foot_vis_idx}: {self.robot_link_names[right_foot_vis_idx]}")
            # --- End Added Print --- #
        # --- End: Define foot indices early --- #
        
        # 加载机器人模型
        model = mujoco.MjModel.from_xml_path(self.mjcf_file)
        data = mujoco.MjData(model)

        # 首先准备原始数据
        bvh_data = bvh_joints_scaled.squeeze(0).cpu().detach().numpy()  # [num_frames, num_joints, 3]
        print(f"[visRefPoint Debug] Shape of T1_joint_dump received: {T1_joint_dump.shape}")
        print(f"[visRefPoint Debug] Shape of T1_joint received: {T1_joint.shape}")
        print(f"[visRefPoint Debug] Shape of BVH data: {bvh_data.shape}")
        
        # 🔧 添加数据有效性检查
        if bvh_data.shape[0] == 0 or bvh_data.shape[1] == 0:
            print(f"[visRefPoint Error] BVH数据为空: {bvh_data.shape}")
            return
        
        T1_data_dump_vis = T1_joint_dump.squeeze(0).cpu().detach().numpy() # Use full joint data for vis
        T1_data_bias_vis = T1_joint.squeeze(0).cpu().detach().numpy()    # Use full joint data for vis
        
        # 🔧 添加T1数据有效性检查
        if T1_data_dump_vis.shape[0] == 0 or T1_data_dump_vis.shape[1] == 0:
            print(f"[visRefPoint Error] T1_data_dump_vis数据为空: {T1_data_dump_vis.shape}")
            return
        
        if T1_data_bias_vis.shape[0] == 0 or T1_data_bias_vis.shape[1] == 0:
            print(f"[visRefPoint Error] T1_data_bias_vis数据为空: {T1_data_bias_vis.shape}")
            return
        
        # 🔧 添加robot_joint_pick_idx有效性检查
        if not hasattr(self, 'robot_joint_pick_idx') or len(self.robot_joint_pick_idx) == 0:
            print(f"[visRefPoint Error] robot_joint_pick_idx为空或不存在")
            return
        
        print(f"[visRefPoint Debug] Shape of T1_data_dump_vis created: {T1_data_dump_vis.shape}")
        print(f"[visRefPoint Debug] Shape of T1_data_bias_vis created: {T1_data_bias_vis.shape}")
        print(f"[visRefPoint Debug] robot_joint_pick_idx: {self.robot_joint_pick_idx}")
        
        num_frames = bvh_data.shape[0]
        # Ensure num_frames matches
        if num_frames != T1_data_dump_vis.shape[0]:
             print(f"Warning: Frame count mismatch! bvh_data: {num_frames}, T1_data_dump_vis: {T1_data_dump_vis.shape[0]}")
             # Decide how to handle mismatch, maybe truncate or error out
             # For now, just print warning. Visualization might be weird.

        # 初始化帧计数器
        frame = [0]  # 使用列表以便在回调函数中修改
        
        # 初始化其他控制变量
        running = [True]  # 控制循环运行
        paused = [False]  # 播放/暂停状态
        playback_speed = [1.0]  # 播放速度
        last_time = time.time()
        camera_tracking = [True]  # 相机跟踪状态
        tracking_distance = [3.0]  # 相机距离

        # 🔧 修复：使用正确的BVH骨架连接关系
        # 优先使用外部传入的连接关系，这些是基于实际BVH文件计算的
        if bvh_skeleton_connections and len(bvh_skeleton_connections) > 0:
            bvh_skeleton = bvh_skeleton_connections
            print(f"[Debug] 使用外部BVH骨架连接关系: {len(bvh_skeleton)} 个连接")
        else:
            # 如果没有外部连接关系，使用基本的人体骨架连接
            # 但是要基于实际的BVH关键关节索引，而不是全部关节
            print(f"[Debug] 使用默认骨架连接关系，BVH关键关节数量: {bvh_data.shape[1]}")
            
            # 这里使用更保守的连接关系，只连接确定存在的关键关节
            bvh_skeleton = {}
            if bvh_data.shape[1] >= 8:  # 至少要有8个关键关节（双腿）
                # 基本的腿部连接：大腿->小腿->脚->脚趾
                bvh_skeleton.update({
                    1: 0,   # 左小腿 -> 左大腿
                    2: 1,   # 左脚 -> 左小腿  
                    3: 2,   # 左脚趾 -> 左脚
                    5: 4,   # 右小腿 -> 右大腿
                    6: 5,   # 右脚 -> 右小腿
                    7: 6,   # 右脚趾 -> 右脚
                })
            
            if bvh_data.shape[1] >= 14:  # 如果有手臂关节
                # 手臂连接：肩膀->上臂->前臂->手
                bvh_skeleton.update({
                    9: 8,   # 左前臂 -> 左上臂
                    10: 9,  # 左手 -> 左前臂
                    12: 11, # 右前臂 -> 右上臂
                    13: 12, # 右手 -> 右前臂
                })
            
            # 只保留在当前数据范围内的有效连接
            bvh_skeleton = {child: parent for child, parent in bvh_skeleton.items() 
                           if child < bvh_data.shape[1] and parent < bvh_data.shape[1]}
            
            print(f"[Debug] 最终使用的骨架连接: {bvh_skeleton}")
        
        # 定义对应关系的关键点 - 使用所有BVH关节
        key_joint_indices = list(range(bvh_data.shape[1]))

        # 播放控制变量
        frame = [0]  # 使用列表让回调函数可以修改
        last_time = time.time()
        playback_speed = [1.0]  # 播放速度，1.0是正常速度
        paused = [False]  # 暂停控制
        target_fps = 30.0  # 目标帧率
        running = [True]
        camera_tracking = [False]  # 相机跟踪状态
        tracking_distance = [3.0]  # 跟踪距离，可以在跟踪模式下调整
        show_environment = [True]  # 🌍 控制是否显示天空和地面
        
        # 键盘回调函数
        def key_callback(keycode):
            # 空格键 - 暂停/播放
            if keycode == 32:  # 空格键的ASCII码
                paused[0] = not paused[0]
            
            # 加速 - + 或 =
            elif keycode == 43 or keycode == 61:  # + 或 = 的ASCII码
                playback_speed[0] *= 1.5
            
            # 减速 - -
            elif keycode == 45:  # - 的ASCII码
                playback_speed[0] /= 1.5
                if playback_speed[0] < 0.1:
                    playback_speed[0] = 0.1
            
            # 前进 - .
            elif keycode == 46:  # . 的ASCII码
                frame[0] = (frame[0] + 1) % num_frames
            
            # 后退 - ,
            elif keycode == 44:  # , 的ASCII码
                frame[0] = (frame[0] - 1) % num_frames
            
            # 重置 - r 或 R
            elif keycode == 114 or keycode == 82:  # r 或 R 的ASCII码
                frame[0] = 0
            
            # 🌍 切换环境显示 - e 或 E
            elif keycode == 101 or keycode == 69:  # e 或 E 的ASCII码
                show_environment[0] = not show_environment[0]
                print(f"环境显示: {'开启' if show_environment[0] else '关闭'}")
            
            # 切换相机跟踪 - t 或 T
            elif keycode == 116 or keycode == 84:  # t 或 T 的ASCII码
                camera_tracking[0] = not camera_tracking[0]
                
                # 如果开启跟踪，记录当前的相机距离
                if camera_tracking[0]:
                    tracking_distance[0] = viewer.cam.distance
                
                # 强制设置透明度，无论开启还是关闭跟踪
                try:
                    viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_TRANSPARENT] = 1
                    
                    # 重新应用机器人模型的半透明效果
                    for i in range(model.ngeom):
                        if model.geom_contype[i] > 0:  # 只对机器人部件应用
                            rgba = model.geom_rgba[i].copy()
                            rgba[3] = 0.7  # 设置透明度为0.7
                            # 使机器人部件更亮
                            rgba[:3] = np.minimum(rgba[:3] * 1.2, 1.0)
                            model.geom_rgba[i] = rgba
                except Exception as e:
                    print(f"设置透明度时出错: {e}")
                
                # 如果关闭跟踪，恢复初始相机设置
                if not camera_tracking[0]:
                    viewer.cam.distance = initial_cam_distance
                    viewer.cam.azimuth = initial_cam_azimuth
                    viewer.cam.elevation = initial_cam_elevation
            
            # 退出 - q, Q 或 Esc
            elif keycode == 113 or keycode == 81 or keycode == 27:  # q, Q 或 Esc 的ASCII码
                running[0] = False
            
        # 创建可视化窗口，并传入键盘回调函数
        viewer = mujoco.viewer.launch_passive(model, data, key_callback=key_callback)
        
        # 设置天堂般明亮的场景
        viewer.cam.distance = 3.0
        viewer.cam.azimuth = 45
        viewer.cam.elevation = -20
        
        # 保存初始相机参数
        initial_cam_distance = viewer.cam.distance
        initial_cam_azimuth = viewer.cam.azimuth
        initial_cam_elevation = viewer.cam.elevation
        
        # 打印控制说明
        print("\n🎮 播放控制:")
        print("  空格键 - 暂停/播放")
        print("  + / = - 加快播放速度")
        print("  - - 减慢播放速度")
        print("  . - 前进一帧")
        print("  , - 后退一帧")
        print("  r / R - 重置播放")
        print("  🌍 e / E - 切换环境显示（天空和格子地面）")
        print("  t / T - 切换相机跟踪")
        print("  鼠标滚轮 - 缩放视图（跟踪模式下也可用）")
        print("  q / Q / Esc - 退出")
        print("")
        
        # 设置明亮的背景
        try:
            # 🌍 设置环境背景和光照
            # 设置天空蓝色背景
            if hasattr(viewer, 'opt') and hasattr(viewer.opt, 'flags'):
                viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_SKYBOX] = 1  # 启用天空盒
            
            # 设置地面为更自然的颜色
            for i in range(model.ngeom):
                if model.geom_type[i] == mujoco.mjtGeom.mjGEOM_PLANE:
                    model.geom_rgba[i] = np.array([0.6, 0.8, 0.4, 1.0], dtype=np.float32)  # 草绿色地面
            
            # 🌅 增强光照效果，模拟自然日光
            for i in range(model.nlight):
                # 主光源 - 模拟太阳光
                model.light_ambient[i] = np.array([0.4, 0.45, 0.5], dtype=np.float32)  # 稍带蓝色的环境光
                model.light_diffuse[i] = np.array([0.9, 0.85, 0.7], dtype=np.float32)  # 温暖的漫反射光
                model.light_specular[i] = np.array([1.0, 0.95, 0.8], dtype=np.float32)  # 明亮的高光
                
                # 设置光源方向（模拟太阳角度）
                if hasattr(model, 'light_dir') and i < len(model.light_dir):
                    model.light_dir[i] = np.array([0.3, 0.2, -0.9], dtype=np.float32)  # 从右上方照射
                    
            # 🎨 设置场景背景颜色
            if hasattr(model, 'vis') and hasattr(model.vis, 'rgba'):
                # 设置天空背景色
                model.vis.rgba.sky = np.array([0.53, 0.81, 0.98, 1.0], dtype=np.float32)  # 天蓝色
                model.vis.rgba.fog = np.array([0.7, 0.8, 0.9, 1.0], dtype=np.float32)   # 远景雾气色
                
        except Exception as e:
            print(f"设置场景环境时出错: {e}")
        
        # 设置渲染选项
        try:
            # 启用各种视觉效果
            viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_TRANSPARENT] = 1  # 透明效果
            viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_REFLECTION] = 1   # 反射效果
            viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_SHADOW] = 1       # 阴影效果
            viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_RANGEFINDER] = 0  # 关闭测距仪
            
            # 设置雾效果
            viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_FOG] = 1          # 启用雾效
            
        except Exception as e:
            print(f"设置渲染选项时出错: {e}")

        try:
            while viewer.is_running() and running[0]:
                current_time = time.time()
                delta_time = current_time - last_time
                
                # 控制帧率
                if delta_time < 1.0/target_fps:
                    time.sleep(1.0/target_fps - delta_time)
                    continue
                    
                # 如果暂停则不更新帧
                if not paused[0]:
                    # 更新帧计数
                    if playback_speed[0] < 1.0:
                        # 慢速播放
                        frame[0] = (frame[0] + 1) % num_frames
                        # 添加额外延迟来模拟慢速
                        additional_delay = (1.0/target_fps) * (1.0/playback_speed[0] - 1.0)
                        time.sleep(additional_delay)
                    else:
                        # 正常或快速播放
                        frame_step = max(1, int(playback_speed[0]))
                        frame[0] = (frame[0] + frame_step) % num_frames
                
                # 设置机器人模型的半透明效果
                for i in range(model.ngeom):
                    if model.geom_contype[i] > 0:  # 只对机器人部件应用
                        rgba = model.geom_rgba[i].copy()
                        rgba[3] = 0.7  # 设置透明度为0.7
                        # 使机器人部件更亮
                        rgba[:3] = np.minimum(rgba[:3] * 1.2, 1.0)
                        model.geom_rgba[i] = rgba
                
                # 重置用户场景中的几何体数量
                viewer.user_scn.ngeom = 0
                geom_idx = 0

                # 根据当前模式准备数据
                current_bvh_data = bvh_data[frame[0]:frame[0]+1]
                current_T1_data_raw = T1_data_dump_vis[frame[0]:frame[0]+1] # Raw FK data for this frame
                current_T1_data_bias = T1_data_bias_vis[frame[0]:frame[0]+1] # Bias corrected FK data

                # 添加自定义几何体
                try:
                    # 🌍 根据设置决定是否添加环境（简单的天空和格子地面）
                    if show_environment[0]:
                        # 🌌 简单的天空背景
                        sky_size = np.array([40.0, 40.0, 30.0], dtype=np.float64)
                        sky_pos = np.array([0.0, 0.0, 15.0], dtype=np.float64)
                        sky_mat = np.eye(3, dtype=np.float64).flatten()
                        sky_rgba = np.array([0.53, 0.81, 0.98, 0.2], dtype=np.float32)  # 浅蓝色天空
                        
                        mujoco.mjv_initGeom(
                            viewer.user_scn.geoms[geom_idx],
                            mujoco.mjtGeom.mjGEOM_BOX,
                            sky_size,
                            sky_pos,
                            sky_mat,
                            sky_rgba
                        )
                        geom_idx += 1
                        
                        # 📏 格子地面
                        grid_size = 20  # 20x20的格子
                        grid_spacing = 1.0  # 每个格子1米
                        line_width = 0.02  # 线条宽度
                        line_height = 0.001  # 线条高度
                        
                        # 绘制水平线条
                        for i in range(grid_size + 1):
                            y_pos = (i - grid_size/2) * grid_spacing
                            line_size = np.array([grid_size * grid_spacing / 2, line_width, line_height], dtype=np.float64)
                            line_pos = np.array([0.0, y_pos, 0.001], dtype=np.float64)
                            line_mat = np.eye(3, dtype=np.float64).flatten()
                            line_rgba = np.array([0.7, 0.7, 0.7, 0.8], dtype=np.float32)  # 灰色线条
                            
                            mujoco.mjv_initGeom(
                                viewer.user_scn.geoms[geom_idx],
                                mujoco.mjtGeom.mjGEOM_BOX,
                                line_size,
                                line_pos,
                                line_mat,
                                line_rgba
                            )
                            geom_idx += 1
                        
                        # 绘制垂直线条
                        for i in range(grid_size + 1):
                            x_pos = (i - grid_size/2) * grid_spacing
                            line_size = np.array([line_width, grid_size * grid_spacing / 2, line_height], dtype=np.float64)
                            line_pos = np.array([x_pos, 0.0, 0.001], dtype=np.float64)
                            line_mat = np.eye(3, dtype=np.float64).flatten()
                            line_rgba = np.array([0.7, 0.7, 0.7, 0.8], dtype=np.float32)  # 灰色线条
                            
                            mujoco.mjv_initGeom(
                                viewer.user_scn.geoms[geom_idx],
                                mujoco.mjtGeom.mjGEOM_BOX,
                                line_size,
                                line_pos,
                                line_mat,
                                line_rgba
                            )
                            geom_idx += 1

                    # 🔧 修复：启用BVH骨骼连接显示
                    skip_skeleton_connections = False
                    if not skip_skeleton_connections:
                        # 添加BVH骨骼连接
                        for child, parent in bvh_skeleton.items():
                            if child < len(current_bvh_data[0]) and parent < len(current_bvh_data[0]):
                                start = current_bvh_data[0, parent]
                                end = current_bvh_data[0, child]
                                
                                # 计算胶囊体的半径 (根据骨骼长度调整)
                                bone_length = np.linalg.norm(end - start)
                                radius = bone_length * 0.05  # 骨骼长度的5%作为半径
                                
                                # 创建胶囊体
                                size = np.array([radius, bone_length/2, 0], dtype=np.float64)  # 半径, 半长度
                                
                                # 计算胶囊体中心点和旋转
                                mid_point = (start + end) / 2
                                direction = end - start
                                # 归一化方向向量
                                direction_norm = direction / np.linalg.norm(direction)
                                
                                # 创建从z轴到方向向量的旋转矩阵
                                z_axis = np.array([0, 0, 1])
                                if np.allclose(direction_norm, z_axis) or np.allclose(direction_norm, -z_axis):
                                    # 如果方向与z轴平行，使用恒等旋转
                                    rot_mat = np.eye(3)
                                else:
                                    # 创建从z轴到方向向量的旋转矩阵
                                    v = np.cross(z_axis, direction_norm)
                                    s = np.linalg.norm(v)
                                    c = np.dot(z_axis, direction_norm)
                                    
                                    # 斜对称矩阵
                                    v_x = np.array([
                                        [0, -v[2], v[1]],
                                        [v[2], 0, -v[0]],
                                        [-v[1], v[0], 0]
                                    ])
                                    
                                    # Rodrigues公式
                                    rot_mat = np.eye(3) + v_x + v_x.dot(v_x) * (1 - c) / (s * s) if s > 1e-10 else np.eye(3)
                                
                                # 平坦化旋转矩阵
                                mat = rot_mat.flatten()
                                
                                # 设置颜色 - 半透明淡蓝色
                                rgba = np.array([0.7, 0.7, 0.9, 0.3], dtype=np.float32)
                                
                                # 初始化几何体
                                mujoco.mjv_initGeom(
                                    viewer.user_scn.geoms[geom_idx],
                                    mujoco.mjtGeom.mjGEOM_CAPSULE,
                                    size,
                                    mid_point,
                                    mat,
                                    rgba
                                )
                                geom_idx += 1

                    # 🔧 修复：添加BVH关键点 - 使用红色球体
                    for i in range(current_bvh_data.shape[1]):
                        # 🔧 添加数组边界检查
                        if current_bvh_data.shape[0] > 0 and i < current_bvh_data.shape[1]:
                            pos = current_bvh_data[0, i]
                            
                            # 设置统一的球体大小
                            size = np.array([0.018, 0, 0], dtype=np.float64)  # 稍微大一点
                            mat = np.eye(3, dtype=np.float64).flatten()
                            
                            # 🔧 修复：使用红色球体表示BVH关键点
                            rgba = np.array([1.0, 0.2, 0.2, 0.8], dtype=np.float32)  # 红色
                            
                            mujoco.mjv_initGeom(
                                viewer.user_scn.geoms[geom_idx],
                                mujoco.mjtGeom.mjGEOM_SPHERE,
                                size,
                                pos,
                                mat,
                                rgba
                            )
                            geom_idx += 1

                    # 🔧 修复：只显示选中的机器人关键点 - 使用绿色球体
                    # 确保与连线使用相同的关节索引
                    if hasattr(self, 'valid_robot_joint_indices'):
                        num_robot_keypoints = len(self.valid_robot_joint_indices)
                    else:
                        num_robot_keypoints = len(self.robot_joint_pick_idx)
                    
                    # 🔧 修复：使用局部索引访问关键点数据
                    # current_T1_data_bias已经是选中的关键点，使用局部索引
                    for local_idx in range(num_robot_keypoints):
                        # 🔧 添加数组边界检查
                        if (current_T1_data_bias.shape[0] > 0 and 
                            local_idx < current_T1_data_bias.shape[1]):
                            pos = current_T1_data_bias[0, local_idx] # 使用优化后的FK结果（局部索引）
                            size = np.array([0.016, 0, 0], dtype=np.float64)
                            mat = np.eye(3, dtype=np.float64).flatten()
                            
                            # 🔧 修复：使用绿色球体表示机器人关键点
                            rgba = np.array([0.2, 1.0, 0.2, 0.8], dtype=np.float32)  # 绿色
                            
                            mujoco.mjv_initGeom(
                                viewer.user_scn.geoms[geom_idx],
                                mujoco.mjtGeom.mjGEOM_SPHERE,
                                size,
                                pos,
                                mat,
                                rgba
                            )
                            geom_idx += 1
                    
                    # 🔧 修复：添加机器人关键点到BVH关键点的正确对应连接线
                    # 这是显示重定向效果的关键可视化功能
                    robot_to_bvh_connections = 0  # 计数器
                    
                    # 确保使用正确的关节对应关系
                    if hasattr(self, 'valid_robot_joint_indices') and hasattr(self, 'valid_bvh_joint_indices'):
                        # 使用配置文件中的正确对应关系
                        for i in range(min(len(self.valid_robot_joint_indices), current_bvh_data.shape[1])):
                            robot_global_idx = self.valid_robot_joint_indices[i]  # 配置中的机器人关节索引（全局）
                            robot_local_idx = i  # 机器人关键点在current_T1_data_bias中的局部索引
                            bvh_local_idx = i  # BVH关键点在current_bvh_data中的局部索引（已经是选中的关键点）
                            
                            # 🔧 修复：使用局部索引访问关键点数据
                            # current_T1_data_bias已经是选中的关键点，使用局部索引
                            if (robot_local_idx < current_T1_data_bias.shape[1] and 
                                bvh_local_idx < current_bvh_data.shape[1] and
                                current_T1_data_bias.shape[0] > 0 and current_bvh_data.shape[0] > 0):
                                
                                robot_pos = current_T1_data_bias[0, robot_local_idx]  # 机器人关键点位置（使用局部索引）
                                bvh_pos = current_bvh_data[0, bvh_local_idx]  # 对应的BVH关键点位置
                                
                                # 🔧 详细调试信息：显示每个连接的对应关系
                                if frame[0] % 30 == 0:  # 每30帧打印一次所有对应关系
                                    if robot_global_idx < len(self.robot_link_names):
                                        robot_name = self.robot_link_names[robot_global_idx]
                                    else:
                                        robot_name = f"Robot[{robot_global_idx}]"
                                    
                                    if hasattr(self, 'bvh_motion') and i < len(self.valid_bvh_joint_indices):
                                        bvh_global_idx = self.valid_bvh_joint_indices[i]
                                        if bvh_global_idx < len(self.bvh_motion.joint_name):
                                            bvh_name = self.bvh_motion.joint_name[bvh_global_idx]
                                        else:
                                            bvh_name = f"BVH[{bvh_global_idx}]"
                                    else:
                                        bvh_name = f"BVH[{i}]"
                                    
                                    # print(f"[连线{i}] Robot[{robot_global_idx}]{robot_name} ↔ BVH[{i}]{bvh_name}")
                                    
                                
                                # 计算连接线的中心点和长度
                                mid_point = (robot_pos + bvh_pos) / 2
                                direction = bvh_pos - robot_pos
                                length = np.linalg.norm(direction)
                                
                                # 计算胶囊体的旋转矩阵
                                if length > 1e-6:  # 避免零向量
                                    direction = direction / length
                                    # 计算从z轴到direction的旋转矩阵
                                    z_axis = np.array([0, 0, 1])
                                    if np.allclose(direction, z_axis) or np.allclose(direction, -z_axis):
                                        rot_mat = np.eye(3)
                                    else:
                                        v = np.cross(z_axis, direction)
                                        s = np.linalg.norm(v)
                                        c = np.dot(z_axis, direction)
                                        v_x = np.array([
                                            [0, -v[2], v[1]],
                                            [v[2], 0, -v[0]],
                                            [-v[1], v[0], 0]
                                        ])
                                        rot_mat = np.eye(3) + v_x + v_x.dot(v_x) * (1 - c) / (s * s)
                                else:
                                    rot_mat = np.eye(3)
                                
                                # 设置胶囊体参数 - 使用黄色表示机器人到BVH的对应关系
                                radius = 0.003  # 较细的连接线
                                size = np.array([radius, length/2, 0], dtype=np.float64)  # 半径和半长度
                                mat = rot_mat.flatten()
                                rgba = np.array([1.0, 1.0, 0.0, 0.7], dtype=np.float32)  # 黄色，显示对应关系
                                
                                # 创建胶囊体
                                if geom_idx < viewer.user_scn.maxgeom:
                                    mujoco.mjv_initGeom(
                                        viewer.user_scn.geoms[geom_idx],
                                        mujoco.mjtGeom.mjGEOM_CAPSULE,
                                        size,
                                        mid_point,
                                        mat,
                                        rgba
                                    )
                                    geom_idx += 1
                                    robot_to_bvh_connections += 1
                    else:
                        # 回退方案：使用robot_joint_pick_idx
                        print("⚠️ 使用回退的关节对应方案")
                        for i in range(min(len(self.robot_joint_pick_idx), current_bvh_data.shape[1])):
                            robot_global_idx = self.robot_joint_pick_idx[i]  # 机器人关节索引（全局）
                            robot_local_idx = i  # 机器人关键点在current_T1_data_bias中的局部索引
                            bvh_local_idx = i  # BVH关键点索引
                            
                            # 🔧 修复：使用局部索引访问关键点数据
                            if (robot_local_idx < current_T1_data_bias.shape[1] and 
                                bvh_local_idx < current_bvh_data.shape[1] and
                                current_T1_data_bias.shape[0] > 0 and current_bvh_data.shape[0] > 0):
                                
                                robot_pos = current_T1_data_bias[0, robot_local_idx]  # 机器人关键点位置（使用局部索引）
                                bvh_pos = current_bvh_data[0, bvh_local_idx]  # 对应的BVH关键点位置
                                
                                # 🔧 回退方案的调试信息
                                if frame[0] % 30 == 0:
                                    robot_name = self.robot_link_names[robot_global_idx] if robot_global_idx < len(self.robot_link_names) else f"Robot[{robot_global_idx}]"
                                    print(f"[回退连线{i}] Robot[{robot_global_idx}]{robot_name} ↔ BVH[{i}]")
                                    print(f"    Robot位置: {robot_pos}, BVH位置: {bvh_pos}")
                                
                                # 计算连接线的中心点和长度
                                mid_point = (robot_pos + bvh_pos) / 2
                                direction = bvh_pos - robot_pos
                                length = np.linalg.norm(direction)
                                
                                # 计算胶囊体的旋转矩阵
                                if length > 1e-6:  # 避免零向量
                                    direction = direction / length
                                    z_axis = np.array([0, 0, 1])
                                    if np.allclose(direction, z_axis) or np.allclose(direction, -z_axis):
                                        rot_mat = np.eye(3)
                                    else:
                                        v = np.cross(z_axis, direction)
                                        s = np.linalg.norm(v)
                                        c = np.dot(z_axis, direction)
                                        v_x = np.array([
                                            [0, -v[2], v[1]],
                                            [v[2], 0, -v[0]],
                                            [-v[1], v[0], 0]
                                        ])
                                        rot_mat = np.eye(3) + v_x + v_x.dot(v_x) * (1 - c) / (s * s)
                                else:
                                    rot_mat = np.eye(3)
                                
                                # 设置胶囊体参数
                                radius = 0.003
                                size = np.array([radius, length/2, 0], dtype=np.float64)
                                mat = rot_mat.flatten()
                                rgba = np.array([1.0, 1.0, 0.0, 0.7], dtype=np.float32)  # 黄色
                                
                                # 创建胶囊体
                                if geom_idx < viewer.user_scn.maxgeom:
                                    mujoco.mjv_initGeom(
                                        viewer.user_scn.geoms[geom_idx],
                                        mujoco.mjtGeom.mjGEOM_CAPSULE,
                                        size,
                                        mid_point,
                                        mat,
                                        rgba
                                    )
                                    geom_idx += 1
                                    robot_to_bvh_connections += 1
                    
                    # 调试信息（每10帧打印一次，避免控制台刷屏）
                    if robot_to_bvh_connections > 0 and frame[0] % 10 == 0:
                        print(f"[Debug] 绘制了 {robot_to_bvh_connections} 条机器人-BVH对应连接线")

                
                    # --- 添加接触指示器 ---
                    if contact_sequence is not None and geom_idx < viewer.user_scn.maxgeom - 2 and left_foot_vis_idx != -1 and right_foot_vis_idx != -1:
                        current_frame_int = frame[0] % num_frames # Use modulo for safety with sequence length

                        if current_frame_int < len(contact_sequence):
                            frame_contacts = contact_sequence[current_frame_int] # [left_contact, right_contact]

                            indicator_radius = 0.08 # Increased radius from 0.04
                            indicator_height = 0.005 # 稍微加厚
                            indicator_color_contact = np.array([0.1, 1.0, 0.1, 0.8], dtype=np.float32) # 绿色，半透明
                            indicator_color_no_contact = np.array([0.0, 0.0, 0.0, 0.0], dtype=np.float32) # 完全透明

                            # --- 左脚指示器 --- #
                            # 🔧 添加脚部索引边界检查
                            if (left_foot_vis_idx < current_T1_data_raw.shape[1] and 
                                current_T1_data_raw.shape[0] > 0):
                                left_foot_pos = current_T1_data_raw[0, left_foot_vis_idx]
                                indicator_pos_left = np.array([left_foot_pos[0], left_foot_pos[1], indicator_height / 2.0])
                                current_indicator_color_left = indicator_color_contact if frame_contacts[0] else indicator_color_no_contact
                                if np.any(current_indicator_color_left > 0): # Only draw if not fully transparent
                                    mujoco.mjv_initGeom(
                                        viewer.user_scn.geoms[geom_idx],
                                        type=mujoco.mjtGeom.mjGEOM_CYLINDER, # Corrected to CYLINDER
                                        size=np.array([indicator_radius, indicator_height / 2.0, 0.0], dtype=np.float64), # Corrected size for cylinder
                                        pos=indicator_pos_left.astype(np.float64),
                                        mat=np.eye(3).flatten(),
                                        rgba=current_indicator_color_left
                                    )
                                    geom_idx += 1

                            # --- 右脚指示器 --- #
                            # Need to check geom_idx again in case left foot was drawn
                            if (geom_idx < viewer.user_scn.maxgeom and 
                                right_foot_vis_idx < current_T1_data_raw.shape[1] and
                                current_T1_data_raw.shape[0] > 0):
                                right_foot_pos = current_T1_data_raw[0, right_foot_vis_idx]
                                indicator_pos_right = np.array([right_foot_pos[0], right_foot_pos[1], indicator_height / 2.0])
                                current_indicator_color_right = indicator_color_contact if frame_contacts[1] else indicator_color_no_contact
                                if np.any(current_indicator_color_right > 0): # Only draw if not fully transparent
                                    mujoco.mjv_initGeom(
                                        viewer.user_scn.geoms[geom_idx],
                                        type=mujoco.mjtGeom.mjGEOM_CYLINDER, # Corrected to CYLINDER
                                        size=np.array([indicator_radius, indicator_height / 2.0, 0.0], dtype=np.float64), # Corrected size for cylinder
                                        pos=indicator_pos_right.astype(np.float64),
                                        mat=np.eye(3).flatten(),
                                        rgba=current_indicator_color_right
                                    )
                                    geom_idx += 1
                        else:
                            # 可选：如果帧索引超出接触序列范围，打印警告
                            # print(f"\r警告: 帧索引 {current_frame_int} 超出接触序列范围 {len(contact_sequence)}", end="")
                            pass
                    # --- 结束接触指示器添加 ---

                    # --- 添加脚底采样点可视化 ---
                    sample_point_radius = 0.001
                    sample_point_color = np.array([1.0, 0.0, 0.0, 0.9], dtype=np.float32) # 不透明红色

                    # 准备当前帧的采样点数据 (需要 .cpu().numpy())
                    current_left_samples_np = None
                    current_right_samples_np = None
                    if left_foot_samples is not None and right_foot_samples is not None:
                        # 确保 left_foot_samples 和 right_foot_samples 有数据
                        if left_foot_samples.numel() > 0 and right_foot_samples.numel() > 0:
                            current_left_samples_np = left_foot_samples.squeeze(0)[frame[0]].cpu().detach().numpy() # [num_sample_points, 3]
                            current_right_samples_np = right_foot_samples.squeeze(0)[frame[0]].cpu().detach().numpy() # [num_sample_points, 3]
                        else:
                             # print("Warning: foot_samples tensor is empty.") # Optional warning
                             pass

                    # 绘制左脚采样点
                    if current_left_samples_np is not None:
                        for sample_pos in current_left_samples_np:
                            if geom_idx < viewer.user_scn.maxgeom:
                                mujoco.mjv_initGeom(
                                    viewer.user_scn.geoms[geom_idx],
                                    mujoco.mjtGeom.mjGEOM_SPHERE,
                                    np.array([sample_point_radius, 0, 0], dtype=np.float64),
                                    sample_pos.astype(np.float64),
                                    np.eye(3).flatten(),
                                    sample_point_color
                                )
                                geom_idx += 1

                    # 绘制右脚采样点
                    if current_right_samples_np is not None:
                        for sample_pos in current_right_samples_np:
                            if geom_idx < viewer.user_scn.maxgeom:
                                mujoco.mjv_initGeom(
                                    viewer.user_scn.geoms[geom_idx],
                                    mujoco.mjtGeom.mjGEOM_SPHERE,
                                    np.array([sample_point_radius, 0, 0], dtype=np.float64),
                                    sample_pos.astype(np.float64),
                                    np.eye(3).flatten(),
                                    sample_point_color
                                )
                                geom_idx += 1
                    # --- 结束脚底采样点可视化 ---

                except Exception as e:
                    print(f"添加几何体时出错: {e}")
                    continue
                
                viewer.user_scn.ngeom = geom_idx

                # 更新机器人姿态
                dof_pos = dof_pos_new.squeeze(0)[frame[0]].detach().cpu().numpy() if dof_pos_new is not None else np.zeros(model.nq-7)
                root_rot = root_rot_new[frame[0]].detach().cpu().numpy() if root_rot_new is not None else np.zeros(3)
                
                # 构建完整的qpos
                qpos = np.zeros(model.nq)
                qpos[0:3] = root_trans_offset[frame[0]].detach().cpu().numpy() if root_trans_offset is not None else np.zeros(3)
                qpos[3:7] = self.euler_to_quat(root_rot)
                qpos[7:] = dof_pos.reshape(-1)[:model.nq-7]
                
                data.qpos[:] = qpos
                
                mujoco.mj_forward(model, data)
                
                # 如果相机跟踪开启，更新相机位置
                if camera_tracking[0]:
                    try:
                        # 获取机器人基座位置
                        base_pos = data.qpos[:3]  # 根节点位置
                        
                        # 设置相机目标位置为基座位置
                        viewer.cam.lookat[:] = base_pos
                        
                        # 保存当前缩放距离，允许用户调整
                        tracking_distance[0] = viewer.cam.distance
                    except Exception as e:
                        print(f"\r相机跟踪出错: {e}", end="")
                
                # 无论相机是否跟踪，都强制应用透明度设置
                try:
                    # 确保透明度渲染选项始终开启
                    viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_TRANSPARENT] = 1
                except Exception:
                    pass
                
                # 在控制台显示帧信息
                status = "暂停" if paused[0] else "播放"
                track_status = "跟踪中" if camera_tracking[0] else "自由视角"
                
                # 显示当前帧的最终优化后的损失值
                if final_frame_losses is not None:
                    current_frame_loss = final_frame_losses[frame[0]]
                else:
                    current_frame_loss = 0.0
                
                # --- 修改：在基础信息后添加换行符 ---
                print(f"\r帧: {frame[0]:>4d}/{num_frames} | 速度: {playback_speed[0]:.1f}x | {status:<4s} | {track_status:<5s} | 当前帧损失: {current_frame_loss:.6f}", end="\\n")

                viewer.sync()
                last_time = current_time
                
                # 在每一帧开始时强制设置透明度
                try:
                    viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_TRANSPARENT] = 1
                except Exception:
                    pass
                
        except KeyboardInterrupt:
            print("\n可视化被用户中断")
        except Exception as e:
            print(f"可视化过程中出错: {e}")
        finally:
            viewer.close()