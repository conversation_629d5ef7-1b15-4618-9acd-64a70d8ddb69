#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BVH文件详细解析脚本
用于分析BVH文件的结构、骨骼层次、运动数据等信息
"""

import os
import sys
import argparse
import numpy as np
from collections import defaultdict
import json

class BVHParser:
    def __init__(self, bvh_file_path, if_scale=None):
        self.bvh_file_path = bvh_file_path
        self.hierarchy = {}
        self.joints = []
        self.channels = []
        self.motion_data = None
        self.frame_count = 0
        self.frame_time = 0.0
        self.total_channels = 0
        self.euler_order = None  # 存储欧拉角顺序
        self.if_scale = if_scale
        
    def parse(self):
        """解析BVH文件"""
        # print(f"正在解析BVH文件: {self.bvh_file_path}")
        # print("=" * 60)
        
        with open(self.bvh_file_path, 'r') as f:
            lines = f.readlines()
        
        # 解析层次结构 self.joints
        self._parse_hierarchy(lines, self.if_scale)
         
        # 解析运动数据 self.motion_data
        self._parse_motion(lines)
        
        # 生成详细报告
        # self._generate_report()
        return self.joints, self.motion_data.tolist()
        
        # 返回期望的值
        if not hasattr(self, 'joints') or self.joints is None:
            self.joints = []
        if not hasattr(self, 'motion_data') or self.motion_data is None:
            self.motion_data = []
        return self.joints, self.motion_data
        
    def _parse_hierarchy(self, lines, if_scale=None):
        """解析骨骼层次结构 - 使用改进的load_meta_data方法"""
        # print("1. 解析骨骼层次结构...")
        
        # 使用类似load_meta_data的方法进行解析
        channels = []
        joint_names = []
        joint_parents = []
        joint_offsets = []
        end_sites = []
        parent_stack = [None]
        rotation_order = None  # 用于存储检测到的旋转顺序
        
        # 重置
        self.joints = []
        self.total_channels = 0
        
        for line in lines:
            if 'ROOT' in line or 'JOINT' in line:
                joint_name = line.split()[-1]
                joint_names.append(joint_name)
                joint_parents.append(parent_stack[-1])
                channels.append('')
                joint_offsets.append([0, 0, 0])

            elif 'End Site' in line:
                # continue
                end_sites.append(len(joint_names))
                joint_names.append(parent_stack[-1] + '_end')
                joint_parents.append(parent_stack[-1])
                channels.append('')
                joint_offsets.append([0, 0, 0])

            elif '{' in line:
                parent_stack.append(joint_names[-1])

            elif '}' in line:
                parent_stack.pop()

            elif 'OFFSET' in line:
                offset_values = [float(x) for x in line.split()[-3:]]
                # BVH文件中的OFFSET通常是厘米单位，转换为米单位
                if if_scale:
                    offset_values = [x * 0.01 for x in offset_values]
                joint_offsets[-1] = offset_values

            elif 'CHANNELS' in line:
                trans_order = []
                rot_order = []
                channel_count = 0
                tokens = line.split()
                for token in tokens:
                    if 'position' in token:
                        trans_order.append(token[0])
                        channel_count += 1
                    elif 'rotation' in token:
                        rot_order.append(token[0])
                        channel_count += 1

                channels[-1] = ''.join(trans_order) + ''.join(rot_order)
                self.total_channels += channel_count
                
                # 动态检测欧拉角顺序（只在第一次遇到旋转通道时设置）
                if rotation_order is None and len(rot_order) == 3:
                    # 将旋转顺序转换为小写（符合scipy的约定）
                    rotation_order = ''.join(rot_order).lower()
                    print(f"检测到BVH欧拉角顺序: {rotation_order.upper()}")

            elif 'Frame Time:' in line:
                self.frame_time = float(line.split()[-1])
                break
        
        # 设置欧拉角顺序
        self.euler_order = rotation_order if rotation_order else "zyx"
        if not rotation_order:
            print(f"警告：未检测到旋转通道，使用默认欧拉角顺序: {self.euler_order.upper()}")
        
        # 转换为原有的数据结构以保持兼容性
        self.joint_names = joint_names
        self.joint_parents = joint_parents
        self.joint_offsets = joint_offsets
        self.end_sites = end_sites
        self.joint_channels = channels
        
        # 重新构建joints列表以保持兼容性
        for i, joint_name in enumerate(joint_names):
            parent_name = joint_parents[i]
            joint_type = 'ROOT' if parent_name is None else ('End Site' if '_end' in joint_name else 'JOINT')
            
            joint = {
                'name': joint_name,
                'type': joint_type,
                'offset': joint_offsets[i],
                'channels': channels[i],
                'parent_name': parent_name,
                'index': i
            }
            self.joints.append(joint)
        
        # print(f"   发现 {len(self.joints)} 个关节")
        # print(f"   总通道数: {self.total_channels}")
        
    def _parse_motion(self, lines):
        """解析运动数据"""
        # print("\n2. 解析运动数据...")
        
        motion_start = -1
        for i, line in enumerate(lines):
            if line.strip() == "MOTION":
                motion_start = i
                break
                
        if motion_start == -1:
            # print("    错误: 未找到MOTION部分")
            return
            
        # 解析帧数和帧时间
        for i in range(motion_start + 1, len(lines)):
            line = lines[i].strip()
            if line.startswith("Frames:"):
                self.frame_count = int(line.split(":")[1])
            elif line.startswith("Frame Time:"):
                self.frame_time = float(line.split(":")[1])
            elif line and not line.startswith("Frames:") and not line.startswith("Frame Time:"):
                # 找到第一行运动数据
                motion_data_start = i
                break
                
        # 读取所有运动数据
        motion_lines = []
        for i in range(motion_data_start, len(lines)):
            line = lines[i].strip()
            if line:
                motion_lines.append(line)
                
        # 解析运动数据
        self.motion_data = []
        for line in motion_lines:
            values = [float(x) for x in line.split()]
            self.motion_data.append(values)
            
        self.motion_data = np.array(self.motion_data)
        
        # print(f"   帧数: {self.frame_count}")
        # print(f"   帧时间: {self.frame_time:.6f} 秒")
        # print(f"   总时长: {self.frame_count * self.frame_time:.3f} 秒")
        # print(f"   数据形状: {self.motion_data.shape}")
        
    def _generate_report(self):
        """生成详细报告"""
        # print("\n3. 生成详细报告...")
        # print("=" * 60)
        
        # 基本信息
        # print(f"文件路径: {self.bvh_file_path}")
        # print(f"文件大小: {os.path.getsize(self.bvh_file_path) / (1024*1024):.2f} MB")
        # print(f"总帧数: {self.frame_count}")
        # print(f"帧率: {1/self.frame_time:.2f} FPS")
        # print(f"总时长: {self.frame_count * self.frame_time:.3f} 秒")
        # print(f"总通道数: {self.total_channels}")
        
        # 骨骼结构分析
        # print("\n骨骼结构分析:")
        # print("-" * 40)
        for i, joint in enumerate(self.joints):
            # print(f"{i+1:2d}. {joint['type']:8s} {joint['name']:20s}")
            # print(f"     偏移量: {joint['offset']}")
            # print(f"     通道: {joint['channels']}")
            # print(f"     父关节: {joint['parent_name'] if joint['parent_name'] else 'None'}")
            # print()
            pass
            
        # 通道统计
        # print("通道类型统计:")
        # print("-" * 40)
        channel_counts = defaultdict(int)
        for joint in self.joints:
            for channel in joint['channels']:
                channel_counts[channel] += 1
                
        # for channel, count in sorted(channel_counts.items()):
        #     print(f"{channel:15s}: {count:3d} 个")
            
        # 运动数据分析
        # print("\n运动数据分析:")
        # print("-" * 40)
        if self.motion_data is not None:
            # print(f"数据形状: {self.motion_data.shape}")
            # print(f"数据类型: {self.motion_data.dtype}")
            
            # 位置数据统计 (前3个通道通常是根关节的位置)
            if self.motion_data.shape[1] >= 3:
                pos_data = self.motion_data[:, :3]
                # print(f"根关节位置范围:")
                # print(f"  X: [{pos_data[:, 0].min():.3f}, {pos_data[:, 0].max():.3f}]")
                # print(f"  Y: [{pos_data[:, 1].min():.3f}, {pos_data[:, 1].max():.3f}]")
                # print(f"  Z: [{pos_data[:, 2].min():.3f}, {pos_data[:, 2].max():.3f}]")
                pass
                
            # 旋转数据统计
            if self.motion_data.shape[1] > 3:
                rot_data = self.motion_data[:, 3:]
                # print(f"旋转数据范围:")
                # print(f"  最小值: {rot_data.min():.3f}")
                # print(f"  最大值: {rot_data.max():.3f}")
                # print(f"  平均值: {rot_data.mean():.3f}")
                # print(f"  标准差: {rot_data.std():.3f}")
                pass
                
        # 保存详细报告到JSON文件
        # self._save_report()
        
    def _save_report(self):
        """保存详细报告到JSON文件"""
        # 创建可序列化的关节数据（移除循环引用）
        serializable_joints = []
        for joint in self.joints:
            serializable_joint = {
                'name': joint['name'],
                'type': joint['type'],
                'offset': joint['offset'],
                'channels': joint['channels'],
                'parent_name': joint['parent_name']
            }
            serializable_joints.append(serializable_joint)
        
        report = {
            'file_info': {
                'path': self.bvh_file_path,
                'size_mb': os.path.getsize(self.bvh_file_path) / (1024*1024),
                'frame_count': self.frame_count,
                'frame_time': self.frame_time,
                'fps': 1/self.frame_time,
                'duration': self.frame_count * self.frame_time,
                'total_channels': self.total_channels
            },
            'joints': serializable_joints,
            'motion_data_info': {
                'shape': self.motion_data.shape if self.motion_data is not None else None,
                'dtype': str(self.motion_data.dtype) if self.motion_data is not None else None
            }
        }
        
        output_file = self.bvh_file_path.replace('.bvh', '_analysis.json')
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        print(f"\n详细报告已保存到: {output_file}")
        
    def get_joint_names(self):
        """获取所有关节名称"""
        return [joint['name'] for joint in self.joints]
        
    def get_channel_names(self):
        """获取所有通道名称"""
        all_channels = []
        for joint in self.joints:
            all_channels.extend(joint['channels'])
        return all_channels
        
    def get_motion_data(self):
        """获取运动数据"""
        return self.motion_data
    
        # ---------------------------------------------------------------------
    def save_optimized_bvh(
        self,
        output_path: str,
        optimized_offsets: dict,
        root_trans_offset=None,   # 不再使用，保持向后兼容
        root_rot_offset=None,     # 不再使用，保持向后兼容
        root_height_adjust: float = 0.0,  # 不再使用，保持向后兼容
        unit_scale: float = 0.01,         # 不再使用，保持向后兼容
        scale: float = 1.0,               # 不再使用，保持向后兼容
    ):
        """
        保存优化后的 BVH：
        1. HIERARCHY 部分更新各关节 OFFSET 为 optimized_offsets
        2. MOTION 部分保持原始数据完全不变
        
        注意：为了解决180度翻转问题，现在只优化骨骼结构，不修改动作数据
        """
        with open(self.bvh_file_path, "r", encoding="utf-8") as fr:
            lines = fr.readlines()

        # 找到MOTION部分的开始位置
        motion_start = next(i for i, l in enumerate(lines) if l.strip() == "MOTION")

        with open(output_path, "w", encoding="utf-8") as fw:
            # === 1. HIERARCHY部分（只更新OFFSET） ===
            current_joint = None
            for i, line in enumerate(lines[:motion_start]):
                stripped = line.strip()

                if stripped.startswith(("ROOT", "JOINT")):
                    current_joint = stripped.split()[1]
                    fw.write(line)  # 直接复制

                elif stripped.startswith("OFFSET") and current_joint:
                    indent = line[:len(line) - len(line.lstrip())]
                    if current_joint in optimized_offsets:
                        # 使用优化后的偏移量，转换回BVH文件单位（厘米）
                        off_m = optimized_offsets[current_joint]  # 米
                        off_cm = [x * 100.0 for x in off_m]  # 转换为厘米
                        fw.write(f"{indent}OFFSET "
                                f"{off_cm[0]:.6f} {off_cm[1]:.6f} {off_cm[2]:.6f}\n")
                        print(f"更新关节 {current_joint} 偏移量: {off_cm}")
                    else:
                        fw.write(line)  # 未优化的关节保持原值
                else:
                    fw.write(line)  # 其他行直接复制

            # === 2. MOTION部分（完全保持原始数据） ===
            for line in lines[motion_start:]:
                fw.write(line)  # 直接复制原始MOTION数据

        print(f"✔ 优化后的BVH已保存到: {output_path}")
        print(f"✔ 已更新 {len([k for k in optimized_offsets.keys()])} 个关节的偏移量")
        print(f"✔ MOTION数据保持原始不变，避免180度翻转问题")
        return output_path

def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description='BVH文件详细解析工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python parse_bvh.py data/ACCAD/eric1.bvh
  python parse_bvh.py --help
        """
    )
    
    parser.add_argument(
        'bvh_file',
        type=str,
        help='要解析的BVH文件路径'
    )
    
    parser.add_argument(
        '--output', '-o',
        type=str,
        help='输出JSON报告文件的路径（可选，默认与输入文件同目录）'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细输出信息'
    )
    
    return parser

def main():
    parser = create_parser()
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.bvh_file):
        print(f"错误: 文件 {args.bvh_file} 不存在")
        sys.exit(1)
    
    # 检查文件扩展名
    if not args.bvh_file.lower().endswith('.bvh'):
        print(f"警告: 文件 {args.bvh_file} 可能不是BVH格式文件")
    
    # 创建解析器并解析文件
    bvh_parser = BVHParser(args.bvh_file)
    bvh_parser.parse()
    
    if args.verbose:
        print("\n详细模式信息:")
        print(f"关节名称列表: {bvh_parser.get_joint_names()}")
        print(f"通道名称列表: {bvh_parser.get_channel_names()}")

if __name__ == "__main__":
    main() 