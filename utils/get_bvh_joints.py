#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BVH关节名称提取脚本
输入BVH文件路径，返回该文件中所有关节名称的列表
"""

import os
import sys
import argparse
from typing import List


def parse_bvh_joints(bvh_file_path: str) -> dict:
    """
    解析BVH文件，提取需要与smpl_joint_correspondence配对的关节名称
    
    Args:
        bvh_file_path: BVH文件路径
        
    Returns:
        机器人关节到BVH关节的映射字典
    """
    if not os.path.exists(bvh_file_path):
        raise FileNotFoundError(f"BVH文件不存在: {bvh_file_path}")
    
    all_joint_names = []
    
    with open(bvh_file_path, 'r') as f:
        lines = f.readlines()
    
    in_hierarchy = False
    
    for line in lines:
        line = line.strip()
        
        if line == "HIERARCHY":
            in_hierarchy = True
            continue
            
        if line == "MOTION":
            break
            
        if not in_hierarchy:
            continue
            
        # 解析ROOT和JOINT行
        if line.startswith("ROOT") or line.startswith("JOINT"):
            joint_name = line.split()[1]
            all_joint_names.append(joint_name)
    
    # 定义机器人关节到BVH关节的映射关系
    robot_to_bvh_mapping = {
        'leg_l4_link_knee': ['leftknee', 'lknee', 'left_knee', 'l_knee', 'leftthigh', 'lthigh', 'lfemur', 'leftleg', 'lleg'],
        'leg_l6_link': ['leftankle', 'lankle', 'left_ankle', 'l_ankle', 'leftshin', 'lshin', 'ltibia', 'leftfoot', 'lfoot'],
        'foot_l1_link': ['leftfoot', 'lfoot', 'left_foot', 'l_foot', 'lefttoe', 'ltoe', 'left_toe', 'l_toe'],
        'leg_r4_link_knee': ['rightknee', 'rknee', 'right_knee', 'r_knee', 'rightthigh', 'rthigh', 'rfemur', 'rightleg', 'rleg'],
        'leg_r6_link': ['rightankle', 'rankle', 'right_ankle', 'r_ankle', 'rightshin', 'rshin', 'rtibia', 'rightfoot', 'rfoot'],
        'foot_r1_link': ['rightfoot', 'rfoot', 'right_foot', 'r_foot', 'righttoe', 'rtoe', 'right_toe', 'r_toe'],
        'arm_l2_link': ['leftshoulder', 'lshoulder', 'left_shoulder', 'l_shoulder', 'leftarm', 'larm', 'lshoulderjoint'],
        'arm_l4_link': ['leftelbow', 'lelbow', 'left_elbow', 'l_elbow', 'leftforearm', 'lforearm', 'lhumerus'],
        'arm_l7_link': ['leftwrist', 'lwrist', 'left_wrist', 'l_wrist', 'lefthand', 'lhand'],
        'arm_r2_link': ['rightshoulder', 'rshoulder', 'right_shoulder', 'r_shoulder', 'rightarm', 'rarm', 'rshoulderjoint'],
        'arm_r4_link': ['rightelbow', 'relbow', 'right_elbow', 'r_elbow', 'rightforearm', 'rforearm', 'rhumerus'],
        'arm_r7_link': ['rightwrist', 'rwrist', 'right_wrist', 'r_wrist', 'righthand', 'rhand']
    }
    
    # 匹配关节
    result_mapping = {}
    used_bvh_joints = set()
    
    # 第一轮：精确匹配（优先匹配更具体的名称）
    for robot_joint, bvh_patterns in robot_to_bvh_mapping.items():
        for bvh_joint in all_joint_names:
            bvh_lower = bvh_joint.lower()
            for pattern in bvh_patterns:
                # 使用更精确的匹配：必须是完整单词匹配或包含关系
                if (pattern == bvh_lower or 
                    pattern in bvh_lower or 
                    bvh_lower in pattern) and bvh_joint not in used_bvh_joints:
                    result_mapping[robot_joint] = bvh_joint
                    used_bvh_joints.add(bvh_joint)
                    break
            if robot_joint in result_mapping:
                break
    
    # 第二轮：对于未匹配的机器人关节，使用更智能的匹配
    for robot_joint in robot_to_bvh_mapping.keys():
        if robot_joint not in result_mapping:
            # 找到最相似的未使用的BVH关节
            best_match = None
            best_score = 0
            
            for bvh_joint in all_joint_names:
                if bvh_joint in used_bvh_joints:
                    continue
                
                # 计算相似度
                robot_lower = robot_joint.lower()
                bvh_lower = bvh_joint.lower()
                
                # 更智能的相似度计算
                score = 0
                
                # 检查位置关键词匹配
                if 'left' in robot_lower and 'left' in bvh_lower:
                    score += 3
                elif 'right' in robot_lower and 'right' in bvh_lower:
                    score += 3
                elif 'l_' in robot_lower and ('left' in bvh_lower or 'l' in bvh_lower):
                    score += 2
                elif 'r_' in robot_lower and ('right' in bvh_lower or 'r' in bvh_lower):
                    score += 2
                
                # 检查关节类型匹配
                if 'knee' in robot_lower and 'knee' in bvh_lower:
                    score += 2
                elif 'ankle' in robot_lower and 'ankle' in bvh_lower:
                    score += 2
                elif 'foot' in robot_lower and 'foot' in bvh_lower:
                    score += 2
                elif 'shoulder' in robot_lower and 'shoulder' in bvh_lower:
                    score += 2
                elif 'elbow' in robot_lower and 'elbow' in bvh_lower:
                    score += 2
                elif 'wrist' in robot_lower and 'wrist' in bvh_lower:
                    score += 2
                elif 'hand' in robot_lower and 'hand' in bvh_lower:
                    score += 2
                
                # 检查数字匹配（如leg_l4_link_knee中的4）
                import re
                robot_numbers = re.findall(r'\d+', robot_lower)
                bvh_numbers = re.findall(r'\d+', bvh_lower)
                if robot_numbers and bvh_numbers and robot_numbers[0] == bvh_numbers[0]:
                    score += 1
                
                if score > best_score:
                    best_score = score
                    best_match = bvh_joint
            
            if best_match and best_score >= 2:  # 只有相似度足够高才匹配
                result_mapping[robot_joint] = best_match
                used_bvh_joints.add(best_match)
    
    # 检查是否所有关节都已匹配
    unmatched_robot_joints = [joint for joint in robot_to_bvh_mapping.keys() if joint not in result_mapping]
    if unmatched_robot_joints:
        error_msg = f"""
自动匹配失败！以下机器人关节无法找到对应的BVH关节：
{unmatched_robot_joints}

BVH文件中的所有关节：
{all_joint_names}

请手动设置关节对应关系。
"""
        raise ValueError(error_msg)
    
    return result_mapping


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="提取BVH文件中的关节名称")
    parser.add_argument('bvh_file', type=str, help='BVH文件路径')
    parser.add_argument('--output', '-o', type=str, help='输出文件路径（可选）')
    
    args = parser.parse_args()
    
    try:
        # 解析BVH文件
        joint_mapping = parse_bvh_joints(args.bvh_file)
        print(joint_mapping)
        print(f"BVH文件: {args.bvh_file}")
        print(f"总关节数: {len(joint_mapping)}")
        print("\n关节名称列表:")
        print("-" * 40)
        
        for robot_joint, bvh_joint in joint_mapping.items():
            print(f"{robot_joint}: {bvh_joint}")
        
        # 如果指定了输出文件，保存到文件
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(f"# BVH文件: {args.bvh_file}\n")
                f.write(f"# 总关节数: {len(joint_mapping)}\n")
                f.write("# 关节名称列表:\n")
                for robot_joint, bvh_joint in joint_mapping.items():
                    f.write(f"{robot_joint}: {bvh_joint}\n")
            print(f"\n关节名称已保存到: {args.output}")
        
        # 返回关节名称列表（用于其他脚本调用）
        return joint_mapping
        
    except Exception as e:
        print(f"错误: {e}")
        return {}


if __name__ == "__main__":
    main() 