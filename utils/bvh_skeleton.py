#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BVH骨架解析器
类似于SMPL_Parser，专门用于解析和管理BVH骨架结构
"""

import torch
import numpy as np
from typing import List, Dict, Optional, Tuple
from collections import defaultdict

class BVHSkeleton:
    """BVH骨架解析器，专门用于解析和管理BVH骨架结构"""
    
    def __init__(self, bvh_parser):
        """初始化BVH骨架解析器
        
        Args:
            bvh_parser: BVHParser实例，已经解析过BVH文件
        """
        self.joints = {}
        self.root = None
        self.joint_names = []
        self.joint_types = {}
        self.joint_offsets = {}
        self.parent_children = defaultdict(list)
        self.child_parent = {}
        
        # 构建骨架结构
        self._build_skeleton(bvh_parser.joints)

        # 调试输出：打印所有关节及其父关节
        print("[BVHSkeleton] 关节父子关系:")
        for joint in self.joint_names:
            parent = self.get_parent(joint)
            print(f"  {joint} -> {parent}")
        
    def _build_skeleton(self, joint_list):
        """构建骨架结构
        
        Args:
            joint_list: BVH解析器提供的关节列表
        """
        # 第一遍：创建关节信息
        for joint in joint_list:
            name = joint['name']
            joint_type = joint['type']
            offset = joint['offset']
            parent_name = joint['parent_name']
            
            # 存储关节信息
            self.joints[name] = {
                'name': name,
                'type': joint_type,
                'offset': offset,
                'parent': parent_name,
                'children': [],
                'channels': joint.get('channels', [])
            }
            
            # 记录关节类型和偏移量
            self.joint_types[name] = joint_type
            if offset is not None:
                self.joint_offsets[name] = torch.tensor(offset, dtype=torch.float32)
            else:
                self.joint_offsets[name] = torch.zeros(3, dtype=torch.float32)
            
            # 记录根节点
            if joint_type == 'ROOT':
                self.root = name
                
        # 第二遍：建立父子关系
        for name, joint_info in self.joints.items():
            parent_name = joint_info['parent']
            if parent_name:
                # 建立父子关系
                self.parent_children[parent_name].append(name)
                self.child_parent[name] = parent_name
                
        # 更新关节信息中的children字段
        for name, joint_info in self.joints.items():
            joint_info['children'] = self.parent_children[name]
            
        # 生成关节名称列表（按解析顺序）
        self.joint_names = [joint['name'] for joint in joint_list]
        
    def get_root_joint(self) -> Optional[str]:
        """获取根关节名称
        
        Returns:
            根关节名称，如果没有找到则返回None
        """
        return self.root
        
    def get_joint_names(self) -> List[str]:
        """获取所有关节名称列表
        
        Returns:
            关节名称列表
        """
        return self.joint_names.copy()
        
    def get_joint_offset(self, joint_name: str) -> torch.Tensor:
        """获取指定关节的偏移量
        
        Args:
            joint_name: 关节名称
            
        Returns:
            关节偏移量张量，形状为(3,)
        """
        if joint_name not in self.joint_offsets:
            raise ValueError(f"关节 '{joint_name}' 不存在")
        return self.joint_offsets[joint_name]
        
    def get_joint_type(self, joint_name: str) -> str:
        """获取指定关节的类型
        
        Args:
            joint_name: 关节名称
            
        Returns:
            关节类型（'ROOT', 'JOINT', 'End Site'）
        """
        if joint_name not in self.joint_types:
            raise ValueError(f"关节 '{joint_name}' 不存在")
        return self.joint_types[joint_name]
        
    def get_parent(self, joint_name: str) -> Optional[str]:
        """获取指定关节的父关节
        
        Args:
            joint_name: 关节名称
            
        Returns:
            父关节名称，如果是根关节则返回None
        """
        if joint_name not in self.joints:
            raise ValueError(f"关节 '{joint_name}' 不存在")
        
        # 如果是根关节，返回None
        if joint_name == self.root:
            return None
            
        # 检查是否有父关节记录
        if joint_name in self.child_parent:
            return self.child_parent[joint_name]
        else:
            # 如果没有父关节记录，可能是解析器的问题，返回None
            # print(f"警告: 关节 '{joint_name}' 没有父关节记录")
            return None
        
    def get_children(self, joint_name: str) -> List[str]:
        """获取指定关节的子关节列表
        
        Args:
            joint_name: 关节名称
            
        Returns:
            子关节名称列表
        """
        if joint_name not in self.parent_children:
            raise ValueError(f"关节 '{joint_name}' 不存在")
        return self.parent_children[joint_name].copy()
        
    def get_joint_info(self, joint_name: str) -> Dict:
        """获取指定关节的完整信息
        
        Args:
            joint_name: 关节名称
            
        Returns:
            包含关节完整信息的字典
        """
        if joint_name not in self.joints:
            raise ValueError(f"关节 '{joint_name}' 不存在")
        return self.joints[joint_name].copy()
        
    def get_all_offsets(self) -> Dict[str, torch.Tensor]:
        """获取所有关节的偏移量
        
        Returns:
            关节名称到偏移量的映射字典
        """
        return self.joint_offsets.copy()
        
    def get_root_offset(self) -> torch.Tensor:
        """获取根关节的偏移量
        
        Returns:
            根关节偏移量张量
        """
        if self.root is None:
            raise ValueError("未找到根关节")
        return self.joint_offsets[self.root]
        
    def get_joint_hierarchy(self) -> Dict[str, List[str]]:
        """获取关节层级结构
        
        Returns:
            关节名称到子关节列表的映射字典
        """
        return dict(self.parent_children)
        
    def get_joint_path(self, joint_name: str) -> List[str]:
        """获取从根关节到指定关节的路径
        
        Args:
            joint_name: 目标关节名称
            
        Returns:
            从根关节到目标关节的路径列表
        """
        if joint_name not in self.joints:
            raise ValueError(f"关节 '{joint_name}' 不存在")
            
        path = []
        current = joint_name
        while current is not None:
            path.append(current)
            current = self.get_parent(current)
            
        return path[::-1]  # 反转列表，从根关节开始
        
    def get_joint_depth(self, joint_name: str) -> int:
        """获取指定关节在骨架中的深度
        
        Args:
            joint_name: 关节名称
            
        Returns:
            关节深度（根关节深度为0）
        """
        return len(self.get_joint_path(joint_name)) - 1
        
    def get_joints_by_type(self, joint_type: str) -> List[str]:
        """根据类型获取关节列表
        
        Args:
            joint_type: 关节类型（'ROOT', 'JOINT', 'End Site'）
            
        Returns:
            指定类型的关节名称列表
        """
        return [name for name, jtype in self.joint_types.items() if jtype == joint_type]
        
    def get_end_joints(self) -> List[str]:
        """获取所有末端关节（没有子关节的关节）
        
        Returns:
            末端关节名称列表
        """
        return [name for name in self.joint_names if not self.get_children(name)]
        
    def get_bone_length(self, joint_name: str) -> float:
        """获取指定关节到其父关节的骨骼长度
        
        Args:
            joint_name: 关节名称
            
        Returns:
            骨骼长度
        """
        if joint_name == self.root:
            return 0.0
            
        parent_name = self.get_parent(joint_name)
        if parent_name is None:
            return 0.0
            
        joint_offset = self.get_joint_offset(joint_name)
        return torch.norm(joint_offset).item()
        
    def get_all_bone_lengths(self) -> Dict[str, float]:
        """获取所有骨骼的长度
        
        Returns:
            关节名称到骨骼长度的映射字典
        """
        bone_lengths = {}
        for joint_name in self.joint_names:
            bone_lengths[joint_name] = self.get_bone_length(joint_name)
        return bone_lengths
        
    def get_symmetric_joints(self) -> List[Tuple[str, str]]:
        """获取对称关节对
        
        Returns:
            对称关节对列表，每个元素为(左关节, 右关节)
        """
        symmetric_pairs = []
        joint_names = set(self.joint_names)
        
        # 常见的对称关节对
        common_pairs = [
            ('LeftHip', 'RightHip'),
            ('LeftKnee', 'RightKnee'),
            ('LeftAnkle', 'RightAnkle'),
            ('LeftFoot', 'RightFoot'),
            ('LeftShoulder', 'RightShoulder'),
            ('LeftElbow', 'RightElbow'),
            ('LeftWrist', 'RightWrist'),
            ('LeftHand', 'RightHand'),
            ('LeftToe', 'RightToe'),
            ('LeftCollar', 'RightCollar'),
        ]
        
        for left, right in common_pairs:
            if left in joint_names and right in joint_names:
                symmetric_pairs.append((left, right))
                
        return symmetric_pairs
        
    def to_device(self, device: torch.device) -> 'BVHSkeleton':
        """将骨架数据移动到指定设备
        
        Args:
            device: 目标设备
            
        Returns:
            移动后的骨架对象
        """
        for name in self.joint_offsets:
            self.joint_offsets[name] = self.joint_offsets[name].to(device)
        return self
        
    def __len__(self) -> int:
        """返回关节数量"""
        return len(self.joint_names)
        
    def __contains__(self, joint_name: str) -> bool:
        """检查关节是否存在"""
        return joint_name in self.joints
        
    def __getitem__(self, joint_name: str) -> Dict:
        """通过关节名称获取关节信息"""
        return self.get_joint_info(joint_name)
        
    def __str__(self) -> str:
        """字符串表示"""
        return f"BVHSkeleton(root='{self.root}', joints={len(self.joint_names)})"
        
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"BVHSkeleton(root='{self.root}', joints={self.joint_names})" 