#!/usr/bin/env python3
"""
BVH专用的MuJoCo播放器

这个模块提供专门用于播放BVH重定向结果的MuJoCo播放器类。
基于utils/mujoco_viewer.py的功能，但专门优化用于BVH数据的可视化。
"""

import mujoco
import mujoco.viewer
import numpy as np
import time
import torch

class BVHMujocoPlayer:
    """专门用于播放BVH重定向结果的MuJoCo播放器"""
    
    def __init__(self, motion_lib_cfg, skeleton_params):
        """初始化BVH播放器
        
        Args:
            motion_lib_cfg: 机器人配置
            skeleton_params: 骨骼参数配置，包含BVH和机器人的映射关系
        """
        self.motion_lib_cfg = motion_lib_cfg
        self.skeleton_params = skeleton_params
        self.mjcf_file = motion_lib_cfg.mjcf_file
        self.robot_joint_names = motion_lib_cfg.joint_names
        
        # 配置文件中获取左右脚指关节名称
        self.left_toe_name = motion_lib_cfg.left_toe_name
        self.right_toe_name = motion_lib_cfg.right_toe_name

        self.left_toe_idx = self.robot_joint_names.index(self.left_toe_name)
        self.right_toe_idx = self.robot_joint_names.index(self.right_toe_name)

        # 获取关节对应关系 - 修复索引映射问题
        self.robot_joint_pick = list(motion_lib_cfg.bvh_joint_correspondence.keys())
        print(f"机器人关节名称: {self.robot_joint_pick}")
        
        # 使用skeleton_params中的正确索引（如果可用）
        if 'robot_joint_indices' in skeleton_params:
            self.robot_joint_pick_idx = skeleton_params['robot_joint_indices']
            print(f"✅ 使用skeleton_params中的机器人关节索引: {self.robot_joint_pick_idx}")
        else:
            # 回退到原始方法
            self.robot_joint_pick_idx = [self.robot_joint_names.index(name) for name in self.robot_joint_pick]
            print(f"⚠️ 使用原始计算的机器人关节索引: {self.robot_joint_pick_idx}")

        # 从skeleton_params获取BVH关节信息
        self.bvh_joint_pick = list(skeleton_params['bvh_info']['selected_joints'])
        print(self.bvh_joint_pick)
        self.bvh_joint_pick_idx = [skeleton_params['bvh_info']['joint_names'].index(name) for name in self.bvh_joint_pick]
        print(self.bvh_joint_pick_idx)

        # 创建MuJoCo模型
        self.model = mujoco.MjModel.from_xml_path(self.mjcf_file)
        self.data = mujoco.MjData(self.model)
        
        # 保存模块引用
        self.mujoco = mujoco
        self.np = np
        
        # 直接从配置文件获取脚部关节索引
        self.left_foot_indices = motion_lib_cfg.left_foot_joint_indices
        self.right_foot_indices = motion_lib_cfg.right_foot_joint_indices
        self.contact_height_threshold = motion_lib_cfg.contact_height_threshold
        self.contact_velocity_threshold = motion_lib_cfg.contact_velocity_threshold

        print(f"BVH MuJoCo播放器初始化完成:")
        print(f"  - 机器人模型: {self.mjcf_file}")
        print(f"  - 关节数量: {len(self.robot_joint_names)}")
        print(f"  - 左脚关节: {self.left_toe_name} (索引: {self.left_toe_idx})")
        print(f"  - 右脚关节: {self.right_toe_name} (索引: {self.right_toe_idx})")
        print(f"  - 左脚接触检测关节索引: {self.left_foot_indices}")
        print(f"  - 右脚接触检测关节索引: {self.right_foot_indices}")

    def get_foot_positions(self):
        """获取脚部位置 - 直接使用脚趾关节位置"""
        left_foot_pos = self.data.xpos[self.left_toe_idx]
        right_foot_pos = self.data.xpos[self.right_toe_idx]
        return left_foot_pos, right_foot_pos

    def euler_to_quat(self, euler):
        """将旋转向量转换为MuJoCo四元数格式 [w, x, y, z]"""
        angle = self.np.linalg.norm(euler)
        if angle < 1e-10:
            return self.np.array([1.0, 0.0, 0.0, 0.0])
        
        axis = euler / angle
        w = self.np.cos(angle / 2.0)
        xyz = axis * self.np.sin(angle / 2.0)
        return self.np.array([w, xyz[0], xyz[1], xyz[2]])

    def play_bvh_motion(self, dof_pos, root_pos, root_rot, bvh_target_positions, 
                       contact_sequence=None, final_frame_losses=None, fps=30.0):
        """播放BVH重定向的机器人动作
        
        Args:
            dof_pos: 优化后的关节角度 [1, num_frames, num_joints, 1]
            root_pos: 根节点位置 [num_frames, 3] 
            root_rot: 根节点旋转 [num_frames, 3]
            bvh_target_positions: BVH目标位置 [num_frames, num_bvh_joints, 3]
            contact_sequence: 接触序列 [num_frames, 2] (左脚, 右脚)
            final_frame_losses: 每帧损失 [num_frames]
            fps: 播放帧率
        """
        # 数据预处理
        dof_data = dof_pos.squeeze(0).detach().cpu().numpy()  # [num_frames, num_joints, 1]
        root_pos_data = root_pos.detach().cpu().numpy()      # [num_frames, 3]
        root_rot_data = root_rot.detach().cpu().numpy()      # [num_frames, 3]
        bvh_target_data = bvh_target_positions.detach().cpu().numpy()  # [num_frames, num_bvh_joints, 3]
        
        num_frames = dof_data.shape[0]
        target_fps = fps
        
        print(f"开始播放BVH重定向动作:")
        print(f"  - 总帧数: {num_frames}")
        print(f"  - 播放帧率: {target_fps} fps")
        print(f"  - 总时长: {num_frames / target_fps:.2f} 秒")
        print(f"  - 关节数: {dof_data.shape[1]}")
        print(f"  - BVH目标点数: {bvh_target_data.shape[1]}")
        
        # 数据验证：检查关节角度是否有变化
        print(f"\n📊 数据验证:")
        if num_frames > 1:
            # 检查DOF数据变化
            dof_diff = self.np.abs(dof_data[1:] - dof_data[:-1]).max()
            print(f"  - 关节角度最大变化: {dof_diff:.6f}")
            
            # 检查根节点位置变化
            root_pos_diff = self.np.abs(root_pos_data[1:] - root_pos_data[:-1]).max()
            print(f"  - 根节点位置最大变化: {root_pos_diff:.6f}")
            
            # 检查根节点旋转变化
            root_rot_diff = self.np.abs(root_rot_data[1:] - root_rot_data[:-1]).max()
            print(f"  - 根节点旋转最大变化: {root_rot_diff:.6f}")
            
            # 检查BVH目标位置变化
            bvh_diff = self.np.abs(bvh_target_data[1:] - bvh_target_data[:-1]).max()
            print(f"  - BVH目标位置最大变化: {bvh_diff:.6f}")
            
            # 警告检查
            if dof_diff < 1e-6:
                print(f"  ⚠️ 警告: 关节角度数据似乎没有变化！")
            if root_pos_diff < 1e-6:
                print(f"  ⚠️ 警告: 根节点位置数据似乎没有变化！")
            if bvh_diff < 1e-6:
                print(f"  ⚠️ 警告: BVH目标位置数据似乎没有变化！")
                
        # 显示数据范围
        print(f"\n📏 数据范围:")
        print(f"  - 关节角度: [{dof_data.min():.3f}, {dof_data.max():.3f}]")
        print(f"  - 根节点位置: X[{root_pos_data[:, 0].min():.3f}, {root_pos_data[:, 0].max():.3f}], "
              f"Y[{root_pos_data[:, 1].min():.3f}, {root_pos_data[:, 1].max():.3f}], "
              f"Z[{root_pos_data[:, 2].min():.3f}, {root_pos_data[:, 2].max():.3f}]")
        print(f"  - BVH目标: X[{bvh_target_data[:, :, 0].min():.3f}, {bvh_target_data[:, :, 0].max():.3f}], "
              f"Y[{bvh_target_data[:, :, 1].min():.3f}, {bvh_target_data[:, :, 1].max():.3f}], "
              f"Z[{bvh_target_data[:, :, 2].min():.3f}, {bvh_target_data[:, :, 2].max():.3f}]")

        # 定义BVH骨架连接关系（根据实际BVH数据调整）
        # 这里使用一个基本的人体骨架连接关系
        bvh_skeleton = self._create_bvh_skeleton_connections()
        
        # 播放控制变量
        frame = [0]
        running = [True]
        paused = [False]
        playback_speed = [1.0]
        camera_tracking = [True]
        tracking_distance = [3.0]
        show_bvh_targets = [True]
        show_contact = [True]
        show_robot_skeleton = [True]
        
        def key_callback(keycode):
            """键盘回调函数"""
            # 空格键 - 暂停/播放
            if keycode == 32:  # 空格键
                paused[0] = not paused[0]
                print(f"{'暂停' if paused[0] else '播放'}")
            
            # 加速/减速
            elif keycode == 43 or keycode == 61:  # + 或 =
                playback_speed[0] *= 1.5
                print(f"播放速度: {playback_speed[0]:.2f}x")
            elif keycode == 45:  # -
                playback_speed[0] /= 1.5
                if playback_speed[0] < 0.1:
                    playback_speed[0] = 0.1
                print(f"播放速度: {playback_speed[0]:.2f}x")
            
            # 前进/后退一帧
            elif keycode == 46:  # .
                frame[0] = (frame[0] + 1) % num_frames
                print(f"前进到帧: {frame[0]}")
            elif keycode == 44:  # ,
                frame[0] = (frame[0] - 1) % num_frames
                print(f"后退到帧: {frame[0]}")
            
            # 重置
            elif keycode == 114 or keycode == 82:  # r 或 R
                frame[0] = 0
                print("重置到第一帧")
            
            # 切换相机跟踪
            elif keycode == 116 or keycode == 84:  # t 或 T
                camera_tracking[0] = not camera_tracking[0]
                print(f"相机跟踪: {'开启' if camera_tracking[0] else '关闭'}")
            
            # 切换BVH目标点显示
            elif keycode == 98 or keycode == 66:  # b 或 B
                show_bvh_targets[0] = not show_bvh_targets[0]
                print(f"BVH目标点: {'显示' if show_bvh_targets[0] else '隐藏'}")
            
            # 切换接触显示
            elif keycode == 99 or keycode == 67:  # c 或 C
                if contact_sequence is not None:
                    show_contact[0] = not show_contact[0]
                    print(f"接触显示: {'开启' if show_contact[0] else '关闭'}")
            
            # 切换机器人骨架显示
            elif keycode == 115 or keycode == 83:  # s 或 S
                show_robot_skeleton[0] = not show_robot_skeleton[0]
                print(f"机器人骨架: {'显示' if show_robot_skeleton[0] else '隐藏'}")
            
            # 退出
            elif keycode == 113 or keycode == 81 or keycode == 27:  # q, Q 或 Esc
                running[0] = False
                print("退出播放")
        
        # 创建MuJoCo查看器
        viewer = mujoco.viewer.launch_passive(self.model, self.data, key_callback=key_callback)
        
        # 设置初始相机位置
        viewer.cam.distance = 3.0
        viewer.cam.azimuth = 45
        viewer.cam.elevation = -20
        
        # 保存初始相机参数
        initial_cam_distance = viewer.cam.distance
        initial_cam_azimuth = viewer.cam.azimuth  
        initial_cam_elevation = viewer.cam.elevation
        
        # 设置场景属性
        self._setup_scene_properties(viewer)
        
        # 打印控制说明
        print("\n🎮 播放控制:")
        print("  空格键 - 暂停/播放")
        print("  + / = - 加快播放")
        print("  - - 减慢播放") 
        print("  . - 前进一帧")
        print("  , - 后退一帧")
        print("  r / R - 重置播放")
        print("  t / T - 切换相机跟踪")
        print("  b / B - 切换BVH目标点显示")
        print("  c / C - 切换接触显示")
        print("  s / S - 切换机器人骨架显示")
        print("  q / Q / Esc - 退出")
        print("  鼠标滚轮 - 缩放\n")
        
        last_time = time.time()
        
        try:
            while viewer.is_running() and running[0]:
                current_time = time.time()
                delta_time = current_time - last_time
                
                # 控制帧率
                if delta_time < 1.0/target_fps:
                    time.sleep(1.0/target_fps - delta_time)
                    continue
                
                # 更新帧计数
                if not paused[0]:
                    if playback_speed[0] < 1.0:
                        frame[0] = (frame[0] + 1) % num_frames
                        additional_delay = (1.0/target_fps) * (1.0/playback_speed[0] - 1.0)
                        time.sleep(additional_delay)
                    else:
                        frame_step = max(1, int(playback_speed[0]))
                        frame[0] = (frame[0] + frame_step) % num_frames
                
                current_frame = frame[0]
                
                # 设置机器人姿态
                self._update_robot_pose(current_frame, dof_data, root_pos_data, root_rot_data)
                
                # 相机跟踪
                if camera_tracking[0]:
                    # 使用当前根节点位置
                    viewer.cam.lookat = root_pos_data[current_frame]
                    viewer.cam.distance = tracking_distance[0]
                
                # 重置用户场景几何体
                viewer.user_scn.ngeom = 0
                geom_idx = 0
                
                # 显示BVH目标点
                if show_bvh_targets[0]:
                    geom_idx = self._draw_bvh_targets(viewer, bvh_target_data, current_frame, geom_idx)
                    geom_idx = self._draw_bvh_skeleton(viewer, bvh_target_data, bvh_skeleton, current_frame, geom_idx)
                
                # 显示机器人关键点
                if show_robot_skeleton[0]:
                    geom_idx = self._draw_robot_keypoints(viewer, current_frame, geom_idx)
                    geom_idx = self._draw_target_connections(viewer, bvh_target_data, current_frame, geom_idx)
                
                # 显示接触指示器
                if show_contact[0] and contact_sequence is not None:
                    geom_idx = self._draw_contact_indicators(viewer, contact_sequence, current_frame, geom_idx)
                
                # 更新几何体数量
                viewer.user_scn.ngeom = geom_idx
                
                # 同步显示更新 - 这是关键！
                viewer.sync()
                
                # 显示损失信息
                if final_frame_losses is not None:
                    loss_value = final_frame_losses[current_frame]
                    status = "暂停" if paused[0] else "播放"
                    track_status = "跟踪中" if camera_tracking[0] else "自由视角"
                    print(f"\r帧: {current_frame:>4d}/{num_frames} | 速度: {playback_speed[0]:.1f}x | {status:<4s} | {track_status:<5s} | 损失: {loss_value:.6f}", end="")
                
                last_time = current_time
                
        except KeyboardInterrupt:
            print("用户中断播放")
        finally:
            print("播放结束")

    def _create_bvh_skeleton_connections(self):
        """创建BVH骨架连接关系 - 使用skeleton_params中预计算的连接关系"""
        # 如果skeleton_params中有预计算的连接关系，直接使用
        if 'bvh_skeleton_connections' in self.skeleton_params:
            return self.skeleton_params['bvh_skeleton_connections']
        
        # 否则返回空的连接关系（不显示骨架连接线）
        return {}

    def _setup_scene_properties(self, viewer):
        """设置场景属性"""
        try:
            # 设置地面为白色
            for i in range(self.model.ngeom):
                if self.model.geom_type[i] == mujoco.mjtGeom.mjGEOM_PLANE:
                    self.model.geom_rgba[i] = self.np.array([0.95, 0.95, 0.95, 1.0], dtype=self.np.float32)
            
            # 增强光照效果
            for i in range(self.model.nlight):
                self.model.light_ambient[i] = self.np.array([0.5, 0.5, 0.5], dtype=self.np.float32)
                self.model.light_diffuse[i] = self.np.array([0.8, 0.8, 0.8], dtype=self.np.float32)
                self.model.light_specular[i] = self.np.array([0.8, 0.8, 0.8], dtype=self.np.float32)
            
            # 设置透明效果
            viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_TRANSPARENT] = 1
            
        except Exception as e:
            print(f"设置场景属性时出错: {e}")

    def _update_robot_pose(self, frame_idx, dof_data, root_pos_data, root_rot_data):
        """更新机器人姿态"""
        # 第一帧时打印调试信息
        if frame_idx == 0:
            print(f"\n🔧 机器人姿态更新调试:")
            print(f"  - 数据形状: dof_data={dof_data.shape}, root_pos={root_pos_data.shape}, root_rot={root_rot_data.shape}")
            print(f"  - 模型qpos总数: {self.model.nq}")
            
        # 设置根节点位置
        root_pos_current = root_pos_data[frame_idx].copy()
        root_pos_current[2] = max(root_pos_current[2], 0.8)  # 确保在地面以上
        self.data.qpos[0:3] = root_pos_current
        
        if frame_idx == 0:
            print(f"  - 根节点位置: {root_pos_current}")
        
        # 设置根节点旋转 (转换为四元数)
        root_quat = self.euler_to_quat(root_rot_data[frame_idx])
        self.data.qpos[3:7] = root_quat  # [w, x, y, z]
        
        if frame_idx == 0:
            print(f"  - 根节点旋转 (euler): {root_rot_data[frame_idx]}")
            print(f"  - 根节点旋转 (quat): {root_quat}")
        
        # 设置关节角度
        joint_start_idx = 7  # 跳过根节点的位置和旋转
        available_joints = self.model.nq - joint_start_idx
        
        # 检查是否有变化的关节角度
        angles_changed = False
        for i in range(min(dof_data.shape[1], available_joints)):
            qpos_idx = joint_start_idx + i
            if qpos_idx < self.model.nq:
                angle = dof_data[frame_idx, i, 0]
                
                # 检查角度是否有变化
                if frame_idx > 0:
                    prev_angle = dof_data[frame_idx-1, i, 0]
                    if abs(angle - prev_angle) > 1e-6:
                        angles_changed = True
                
                # 限制角度范围到合理值
                angle = self.np.clip(angle, -3.14, 3.14)
                self.data.qpos[qpos_idx] = angle
                
                # 第一帧时打印前几个关节的值
                if frame_idx == 0 and i < 5:
                    print(f"    关节{i} (qpos[{qpos_idx}]): {angle:.4f}")
        
        # 检查角度变化
        if frame_idx == 1:
            print(f"  - 第二帧角度是否有变化: {angles_changed}")
            if not angles_changed:
                print("  ⚠️ 警告: 关节角度似乎没有变化！")
        
        # 更新MuJoCo仿真
        self.mujoco.mj_forward(self.model, self.data)

    def _draw_bvh_targets(self, viewer, bvh_target_data, frame_idx, geom_idx):
        """绘制BVH目标点"""
        for i in range(bvh_target_data.shape[1]):
            if geom_idx >= viewer.user_scn.maxgeom:
                break
            
            pos = bvh_target_data[frame_idx, i]
            size = self.np.array([0.02, 0, 0], dtype=self.np.float64)
            mat = self.np.eye(3, dtype=self.np.float64).flatten()
            rgba = self.np.array([0.2, 0.8, 0.2, 0.8], dtype=self.np.float32)  # 绿色
            
            self.mujoco.mjv_initGeom(
                viewer.user_scn.geoms[geom_idx],
                self.mujoco.mjtGeom.mjGEOM_SPHERE,
                size, pos, mat, rgba
            )
            geom_idx += 1
        
        return geom_idx

    def _draw_bvh_skeleton(self, viewer, bvh_target_data, connections, frame_idx, geom_idx):
        """绘制BVH骨架连接线"""
        for child, parent in connections.items():
            if geom_idx >= viewer.user_scn.maxgeom:
                break
            
            if child < bvh_target_data.shape[1] and parent < bvh_target_data.shape[1]:
                start = bvh_target_data[frame_idx, parent]
                end = bvh_target_data[frame_idx, child]
                
                # 计算胶囊体参数
                mid_point = (start + end) / 2
                direction = end - start
                length = self.np.linalg.norm(direction)
                
                if length > 1e-6:
                    direction = direction / length
                    radius = length * 0.05
                    
                    # 计算旋转矩阵
                    z_axis = self.np.array([0, 0, 1])
                    if self.np.allclose(direction, z_axis) or self.np.allclose(direction, -z_axis):
                        rot_mat = self.np.eye(3)
                    else:
                        v = self.np.cross(z_axis, direction)
                        s = self.np.linalg.norm(v)
                        c = self.np.dot(z_axis, direction)
                        v_x = self.np.array([
                            [0, -v[2], v[1]],
                            [v[2], 0, -v[0]],
                            [-v[1], v[0], 0]
                        ])
                        rot_mat = self.np.eye(3) + v_x + v_x.dot(v_x) * (1 - c) / (s * s)
                    
                    size = self.np.array([radius, length/2, 0], dtype=self.np.float64)
                    mat = rot_mat.flatten()
                    rgba = self.np.array([0.7, 0.7, 0.9, 0.3], dtype=self.np.float32)  # 半透明蓝色
                    
                    self.mujoco.mjv_initGeom(
                        viewer.user_scn.geoms[geom_idx],
                        self.mujoco.mjtGeom.mjGEOM_CAPSULE,
                        size, mid_point, mat, rgba
                    )
                    geom_idx += 1
        
        return geom_idx

    def _draw_robot_keypoints(self, viewer, frame_idx, geom_idx):
        """绘制机器人关键点"""
        for i in self.robot_joint_pick_idx:
            if geom_idx >= viewer.user_scn.maxgeom:
                break
            
            if i < len(self.data.xpos):
                pos = self.data.xpos[i]
                size = self.np.array([0.015, 0, 0], dtype=self.np.float64)
                mat = self.np.eye(3, dtype=self.np.float64).flatten()
                rgba = self.np.array([0.9, 0.5, 0.1, 0.8], dtype=self.np.float32)  # 橙色
                
                self.mujoco.mjv_initGeom(
                    viewer.user_scn.geoms[geom_idx],
                    self.mujoco.mjtGeom.mjGEOM_SPHERE,
                    size, pos, mat, rgba
                )
                geom_idx += 1
        
        return geom_idx

    def _draw_target_connections(self, viewer, bvh_target_data, frame_idx, geom_idx):
        """绘制机器人关键点到BVH目标点的连接线"""
        min_joints = min(len(self.robot_joint_pick_idx), bvh_target_data.shape[1])
        
        for i in range(min_joints):
            if geom_idx >= viewer.user_scn.maxgeom:
                break
            
            robot_joint_idx = self.robot_joint_pick_idx[i]
            if robot_joint_idx < len(self.data.xpos):
                start = self.data.xpos[robot_joint_idx]
                end = bvh_target_data[frame_idx, i]
                
                # 计算连接线
                mid_point = (start + end) / 2
                direction = end - start
                length = self.np.linalg.norm(direction)
                
                if length > 1e-6:
                    direction = direction / length
                    
                    # 计算旋转矩阵
                    z_axis = self.np.array([0, 0, 1])
                    if self.np.allclose(direction, z_axis) or self.np.allclose(direction, -z_axis):
                        rot_mat = self.np.eye(3)
                    else:
                        v = self.np.cross(z_axis, direction)
                        s = self.np.linalg.norm(v)
                        c = self.np.dot(z_axis, direction)
                        v_x = self.np.array([
                            [0, -v[2], v[1]],
                            [v[2], 0, -v[0]],
                            [-v[1], v[0], 0]
                        ])
                        rot_mat = self.np.eye(3) + v_x + v_x.dot(v_x) * (1 - c) / (s * s)
                    
                    radius = 0.005
                    size = self.np.array([radius, length/2, 0], dtype=self.np.float64)
                    mat = rot_mat.flatten()
                    rgba = self.np.array([0.3, 0.8, 0.3, 0.4], dtype=self.np.float32)  # 半透明绿色
                    
                    self.mujoco.mjv_initGeom(
                        viewer.user_scn.geoms[geom_idx],
                        self.mujoco.mjtGeom.mjGEOM_CAPSULE,
                        size, mid_point, mat, rgba
                    )
                    geom_idx += 1
        
        return geom_idx

    def _draw_contact_indicators(self, viewer, contact_sequence, frame_idx, geom_idx):
        """绘制接触指示器"""
        if frame_idx < len(contact_sequence) and geom_idx < viewer.user_scn.maxgeom - 1:
            contacts = contact_sequence[frame_idx]  # [left_contact, right_contact]
            
            # 获取脚部位置
            left_foot_pos, right_foot_pos = self.get_foot_positions()
            
            indicator_radius = 0.08
            indicator_height = 0.01
            
            # 左脚接触指示器
            if contacts[0]:  # 左脚接触
                pos = self.np.array([left_foot_pos[0], left_foot_pos[1], indicator_height/2])
                size = self.np.array([indicator_radius, indicator_height/2, 0], dtype=self.np.float64)
                mat = self.np.eye(3, dtype=self.np.float64).flatten()
                rgba = self.np.array([1.0, 0.2, 0.2, 0.8], dtype=self.np.float32)  # 红色
                
                self.mujoco.mjv_initGeom(
                    viewer.user_scn.geoms[geom_idx],
                    self.mujoco.mjtGeom.mjGEOM_CYLINDER,
                    size, pos, mat, rgba
                )
                geom_idx += 1
            
            # 右脚接触指示器
            if contacts[1] and geom_idx < viewer.user_scn.maxgeom:  # 右脚接触
                pos = self.np.array([right_foot_pos[0], right_foot_pos[1], indicator_height/2])
                size = self.np.array([indicator_radius, indicator_height/2, 0], dtype=self.np.float64)
                mat = self.np.eye(3, dtype=self.np.float64).flatten()
                rgba = self.np.array([1.0, 0.2, 0.2, 0.8], dtype=self.np.float32)  # 红色
                
                self.mujoco.mjv_initGeom(
                    viewer.user_scn.geoms[geom_idx],
                    self.mujoco.mjtGeom.mjGEOM_CYLINDER,
                    size, pos, mat, rgba
                )
                geom_idx += 1
        
        return geom_idx