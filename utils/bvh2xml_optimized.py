#!/usr/bin/env python3
"""
优化的BVH到MuJoCo XML转换器

这个脚本将BVH动作捕捉文件转换为MuJoCo兼容的XML格式，用于物理仿真。
解决了原版本在MuJoCo中显示混乱的问题。

依赖:
    pip install bvhio numpy

用法:
    python bvh2xml_optimized.py input.bvh output.xml
    python bvh2xml_optimized.py input.bvh --info  # 显示BVH信息
"""

import sys
import os
import argparse
import xml.etree.ElementTree as ET
from xml.dom import minidom
import math
import numpy as np

try:
    import bvhio
except ImportError:
    print("错误: 未找到bvhio库。请使用以下命令安装:")
    print("pip install bvhio")
    sys.exit(1)


class OptimizedBVH2MuJoCo:
    """优化的BVH到MuJoCo转换器"""
    
    def __init__(self, scale_factor=1.0, unit_scale=0.01):
        """
        初始化转换器
        
        Args:
            scale_factor: 整体缩放因子
            unit_scale: 单位转换 (BVH通常以cm为单位，MuJoCo以m为单位)
        """
        self.scale_factor = scale_factor
        self.unit_scale = unit_scale
        self.joint_count = 0
        self.geom_count = 0
        
        # 人体骨骼标准尺寸 (用于更好的几何体生成)
        self.bone_radius_map = {
            'Hips': 0.08,
            'Spine': 0.06,
            'Spine1': 0.05,
            'Neck': 0.03,
            'Head': 0.04,
            'Shoulder': 0.04,
            'Arm': 0.035,
            'ForeArm': 0.03,
            'Hand': 0.025,
            'UpLeg': 0.055,
            'Leg': 0.04,
            'Foot': 0.035,
            'Toe': 0.02
        }
    
    def parse_bvh(self, file_path):
        """解析BVH文件"""
        try:
            root_joint = bvhio.readAsHierarchy(file_path)
            return root_joint
        except Exception as e:
            print(f"解析BVH文件错误: {e}")
            return None
    
    def get_bone_radius(self, joint_name):
        """根据关节名称获取合适的骨骼半径"""
        for key, radius in self.bone_radius_map.items():
            if key.lower() in joint_name.lower():
                return radius * self.scale_factor
        return 0.02 * self.scale_factor  # 默认半径
    
    def calculate_bone_vector(self, parent_pos, child_pos):
        """计算骨骼向量和长度"""
        bone_vector = np.array(child_pos) - np.array(parent_pos)
        bone_length = np.linalg.norm(bone_vector)
        return bone_vector, bone_length
    
    def get_joint_type(self, joint_name):
        """根据关节名称确定关节类型"""
        joint_name_lower = joint_name.lower()
        
        # 脊椎和头部关节 - 限制更多的自由度
        if any(keyword in joint_name_lower for keyword in ['spine', 'neck', 'head']):
            return 'ball'
        
        # 肩膀和髋部 - 球关节
        elif any(keyword in joint_name_lower for keyword in ['shoulder', 'hip', 'upleg']):
            return 'ball'
        
        # 肘部和膝盖 - 铰链关节
        elif any(keyword in joint_name_lower for keyword in ['forearm', 'leg', 'knee', 'elbow']):
            return 'hinge'
        
        # 手腕和脚踝 - 万向节
        elif any(keyword in joint_name_lower for keyword in ['hand', 'foot', 'wrist', 'ankle']):
            return 'ball'
        
        # 默认球关节
        else:
            return 'ball'
    
    def get_joint_range(self, joint_name, joint_type):
        """根据关节名称和类型获取运动范围"""
        joint_name_lower = joint_name.lower()
        
        if joint_type == 'hinge':
            # 铰链关节范围
            if 'forearm' in joint_name_lower or 'elbow' in joint_name_lower:
                return "0 150"  # 肘部弯曲
            elif 'leg' in joint_name_lower or 'knee' in joint_name_lower:
                return "0 150"  # 膝盖弯曲
            else:
                return "0 90"
        elif joint_type == 'ball':
            # 球关节范围 - 注意球关节的range[0]必须是0
            return "0 180"
        else:
            return "0 180"
    
    def create_mujoco_xml(self, root_joint, output_path="optimized_humanoid.xml"):
        """创建优化的MuJoCo XML"""
        
        # 创建根mujoco元素
        mujoco = ET.Element("mujoco", model="optimized_humanoid_from_bvh")
        
        # 编译器设置
        compiler = ET.SubElement(mujoco, "compiler")
        compiler.set("angle", "degree")
        compiler.set("inertiafromgeom", "true")
        compiler.set("strippath", "false")
        
        # 选项设置
        option = ET.SubElement(mujoco, "option")
        option.set("timestep", "0.005")
        option.set("iterations", "50")
        option.set("solver", "PGS")
        option.set("gravity", "0 0 -9.81")
        
        # 默认设置 - 修复球关节范围问题
        default = ET.SubElement(mujoco, "default")
        
        # 球关节默认设置
        ball_default = ET.SubElement(default, "default", {"class": "ball_joint"})
        ball_joint = ET.SubElement(ball_default, "joint")
        ball_joint.set("type", "ball")
        ball_joint.set("limited", "true")
        ball_joint.set("range", "0 180")  # 球关节范围必须从0开始
        ball_joint.set("damping", "0.1")
        ball_joint.set("stiffness", "1")
        
        # 铰链关节默认设置
        hinge_default = ET.SubElement(default, "default", {"class": "hinge_joint"})
        hinge_joint = ET.SubElement(hinge_default, "joint")
        hinge_joint.set("type", "hinge")
        hinge_joint.set("limited", "true")
        hinge_joint.set("range", "0 150")
        hinge_joint.set("damping", "0.05")
        
        # 几何体默认设置
        geom_default = ET.SubElement(default, "geom")
        geom_default.set("rgba", "0.7 0.7 0.7 1")
        geom_default.set("density", "1000")
        geom_default.set("friction", "1 0.005 0.0001")
        
        # 材质资产
        asset = ET.SubElement(mujoco, "asset")
        
        bone_material = ET.SubElement(asset, "material")
        bone_material.set("name", "bone")
        bone_material.set("rgba", "0.8 0.6 0.4 1")
        bone_material.set("specular", "0.5")
        
        ground_material = ET.SubElement(asset, "material")
        ground_material.set("name", "ground")
        ground_material.set("rgba", "0.3 0.5 0.3 1")
        ground_material.set("specular", "0.1")
        
        # 世界体
        worldbody = ET.SubElement(mujoco, "worldbody")
        
        # 地面
        ground = ET.SubElement(worldbody, "geom")
        ground.set("name", "ground")
        ground.set("type", "plane")
        ground.set("size", "20 20 0.1")
        ground.set("material", "ground")
        
        # 光源
        light1 = ET.SubElement(worldbody, "light")
        light1.set("diffuse", "0.6 0.6 0.6")
        light1.set("pos", "0 0 4")
        light1.set("dir", "0 0 -1")
        
        light2 = ET.SubElement(worldbody, "light")
        light2.set("diffuse", "0.3 0.3 0.3")
        light2.set("pos", "2 2 3")
        light2.set("dir", "-1 -1 -1")
        
        # 转换BVH层次结构为MuJoCo体
        self.create_optimized_body_hierarchy(worldbody, root_joint)
        
        # 添加执行器
        actuator = ET.SubElement(mujoco, "actuator")
        self.add_optimized_actuators(actuator, root_joint)
        
        # 保存XML文件
        self.save_xml(mujoco, output_path)
        
        return output_path
    
    def create_optimized_body_hierarchy(self, parent_element, joint, parent_pos=None):
        """递归创建优化的体层次结构"""
        if parent_pos is None:
            parent_pos = [0, 0, 0]
        
        # 获取关节位置并应用缩放
        joint_pos = joint.RestPose.Position
        pos = [
            joint_pos.x * self.unit_scale * self.scale_factor,
            joint_pos.y * self.unit_scale * self.scale_factor,
            joint_pos.z * self.unit_scale * self.scale_factor
        ]
        
        # 创建体元素
        body = ET.SubElement(parent_element, "body")
        body.set("name", joint.Name)
        body.set("pos", f"{pos[0]:.6f} {pos[1]:.6f} {pos[2]:.6f}")
        
        # 添加关节（非根节点）
        if hasattr(joint, 'Parent') and joint.Parent is not None:
            joint_elem = ET.SubElement(body, "joint")
            joint_elem.set("name", f"{joint.Name}_joint")
            
            joint_type = self.get_joint_type(joint.Name)
            joint_elem.set("type", joint_type)
            joint_elem.set("pos", "0 0 0")
            
            # 设置关节范围
            joint_range = self.get_joint_range(joint.Name, joint_type)
            joint_elem.set("range", joint_range)
            
            # 设置关节类别
            if joint_type == "ball":
                joint_elem.set("class", "ball_joint")
            else:
                joint_elem.set("class", "hinge_joint")
        
        # 添加几何体
        self.add_optimized_geometry(body, joint, pos)
        
        # 递归处理子关节
        if hasattr(joint, 'Children') and joint.Children:
            for child in joint.Children:
                self.create_optimized_body_hierarchy(body, child, pos)
        
        return body
    
    def add_optimized_geometry(self, body, joint, joint_pos):
        """添加优化的几何体"""
        if hasattr(joint, 'Children') and joint.Children:
            # 为每个子关节创建骨骼
            for i, child in enumerate(joint.Children):
                child_pos = child.RestPose.Position
                child_pos_scaled = [
                    child_pos.x * self.unit_scale * self.scale_factor,
                    child_pos.y * self.unit_scale * self.scale_factor,
                    child_pos.z * self.unit_scale * self.scale_factor
                ]
                
                bone_vector, bone_length = self.calculate_bone_vector(joint_pos, child_pos_scaled)
                
                if bone_length > 0.001:  # 只有足够长的骨骼才添加几何体
                    geom = ET.SubElement(body, "geom")
                    geom.set("name", f"{joint.Name}_to_{child.Name}_geom")
                    geom.set("type", "capsule")
                    
                    # 计算合适的半径
                    radius = self.get_bone_radius(joint.Name)
                    radius = min(radius, bone_length * 0.1)  # 限制半径不超过长度的10%
                    geom.set("size", f"{radius:.6f}")
                    
                    # 设置骨骼向量
                    relative_pos = [
                        child_pos_scaled[0] - joint_pos[0],
                        child_pos_scaled[1] - joint_pos[1],
                        child_pos_scaled[2] - joint_pos[2]
                    ]
                    geom.set("fromto", f"0 0 0 {relative_pos[0]:.6f} {relative_pos[1]:.6f} {relative_pos[2]:.6f}")
                    geom.set("material", "bone")
        else:
            # 端效应器 - 添加小球
            geom = ET.SubElement(body, "geom")
            geom.set("name", f"{joint.Name}_end")
            geom.set("type", "sphere")
            geom.set("size", f"{0.015 * self.scale_factor:.6f}")
            geom.set("material", "bone")
    
    def add_optimized_actuators(self, actuator_element, joint):
        """添加优化的执行器"""
        # 为非根关节添加执行器
        if hasattr(joint, 'Parent') and joint.Parent is not None:
            motor = ET.SubElement(actuator_element, "motor")
            motor.set("name", f"{joint.Name}_motor")
            motor.set("joint", f"{joint.Name}_joint")
            
            # 根据关节类型设置增益
            joint_type = self.get_joint_type(joint.Name)
            if joint_type == "ball":
                motor.set("gear", "150")
            else:
                motor.set("gear", "100")
            
            motor.set("ctrllimited", "true")
            motor.set("ctrlrange", "-1 1")
        
        # 递归处理子关节
        if hasattr(joint, 'Children') and joint.Children:
            for child in joint.Children:
                self.add_optimized_actuators(actuator_element, child)
    
    def save_xml(self, root_element, output_path):
        """保存格式化的XML"""
        # 转换为字符串
        rough_string = ET.tostring(root_element, 'unicode')
        
        # 格式化
        reparsed = minidom.parseString(rough_string)
        pretty_xml = reparsed.toprettyxml(indent="  ")
        
        # 移除空行并修复编码
        lines = []
        for line in pretty_xml.split('\n'):
            if line.strip():
                lines.append(line)
        
        # 替换XML声明
        if lines[0].startswith('<?xml'):
            lines[0] = '<?xml version="1.0" ?>'
        
        pretty_xml = '\n'.join(lines)
        
        # 保存文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(pretty_xml)
        
        print(f"优化的MuJoCo XML模型已保存到: {output_path}")
    
    def print_bvh_info(self, bvh_file):
        """打印BVH文件信息"""
        if not os.path.exists(bvh_file):
            print(f"错误: BVH文件 '{bvh_file}' 未找到。")
            return
        
        root_joint = self.parse_bvh(bvh_file)
        
        if root_joint is None:
            print("解析BVH文件失败")
            return
        
        print(f"\n=== BVH文件信息 ===")
        print(f"文件: {bvh_file}")
        print(f"根关节: {root_joint.Name}")
        
        def print_joint_hierarchy(joint, indent=0):
            """递归打印关节层次结构"""
            prefix = "  " * indent
            joint_type = self.get_joint_type(joint.Name)
            pos = joint.RestPose.Position
            print(f"{prefix}- {joint.Name} [{joint_type}] pos:({pos.x:.2f}, {pos.y:.2f}, {pos.z:.2f})")
            
            if hasattr(joint, 'Children') and joint.Children:
                for child in joint.Children:
                    print_joint_hierarchy(child, indent + 1)
        
        print("\n关节层次结构:")
        print_joint_hierarchy(root_joint)
        
        # 统计关节数量
        def count_joints(joint):
            count = 1
            if hasattr(joint, 'Children') and joint.Children:
                for child in joint.Children:
                    count += count_joints(child)
            return count
        
        total_joints = count_joints(root_joint)
        print(f"\n总关节数: {total_joints}")
        
        if hasattr(root_joint, 'Keyframes') and root_joint.Keyframes:
            print(f"总关键帧数: {len(root_joint.Keyframes)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="优化的BVH动作捕捉文件到MuJoCo XML格式转换器"
    )
    parser.add_argument(
        "input_bvh", 
        help="输入BVH文件路径"
    )
    parser.add_argument(
        "output_xml", 
        nargs='?',
        default=None,
        help="输出XML文件路径 (可选，默认为输入文件名.xml)"
    )
    parser.add_argument(
        "--scale", 
        type=float, 
        default=1.0,
        help="模型缩放因子 (默认: 1.0)"
    )
    parser.add_argument(
        "--unit-scale", 
        type=float, 
        default=0.01,
        help="单位转换因子，BVH->MuJoCo (默认: 0.01, cm->m)"
    )
    parser.add_argument(
        "--info", 
        action="store_true",
        help="只显示BVH信息，不转换"
    )
    
    args = parser.parse_args()
    
    # 验证输入文件
    if not os.path.exists(args.input_bvh):
        print(f"错误: 输入文件 '{args.input_bvh}' 未找到")
        sys.exit(1)
    
    # 创建转换器
    converter = OptimizedBVH2MuJoCo(
        scale_factor=args.scale,
        unit_scale=args.unit_scale
    )
    
    # 如果只是显示信息
    if args.info:
        converter.print_bvh_info(args.input_bvh)
        return
    
    # 设置输出路径
    if args.output_xml is None:
        base_name = os.path.splitext(args.input_bvh)[0]
        args.output_xml = f"{base_name}_optimized.xml"
    
    # 解析BVH文件
    print(f"解析BVH文件: {args.input_bvh}")
    root_joint = converter.parse_bvh(args.input_bvh)
    
    if root_joint is None:
        print("解析BVH文件失败")
        sys.exit(1)
    
    # 显示BVH信息
    converter.print_bvh_info(args.input_bvh)
    
    # 转换为MuJoCo XML
    print(f"\n转换为优化的MuJoCo XML...")
    try:
        output_path = converter.create_mujoco_xml(root_joint, args.output_xml)
        print(f"转换成功完成!")
        print(f"输出文件: {output_path}")
        
        # 提供测试建议
        print(f"\n测试建议:")
        print(f"python utils/vis_mjcf.py  # 修改文件中的路径为: {output_path}")
        
    except Exception as e:
        print(f"转换过程中出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 