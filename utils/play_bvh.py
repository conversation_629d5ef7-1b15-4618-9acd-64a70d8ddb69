#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BVH动作播放器 - 在MuJoCo中直接播放BVH格式的骨架动作

功能：
- 直接加载和播放BVH文件
- 在MuJoCo中可视化骨架树
- 提供交互式播放控制
- 支持多种播放模式（播放/暂停/单步/重置）
"""

import os
import sys
import argparse
import numpy as np
import mujoco
import mujoco.viewer
import time
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.append(str(project_root))

from bvh_utils import BVHMotion
from bvh_parser import BVHParser


class BVHMuJoCoPlayer:
    """BVH动作的MuJoCo播放器"""
    
    def __init__(self, bvh_file_path):
        self.bvh_file_path = bvh_file_path
        self.bvh_motion = None
        self.bvh_parser = None
        
        # 播放控制
        self.current_frame = 0
        self.is_playing = False
        self.playback_speed = 1.0
        self.fps = 30.0  # 默认播放帧率
        
        # 可视化设置
        self.joint_radius = 0.02
        self.bone_radius = 0.01
        self.joint_color = [1.0, 0.0, 0.0, 1.0]  # 红色关节
        self.bone_color = [0.0, 0.0, 1.0, 1.0]   # 蓝色骨骼
        
        # MuJoCo相关
        self.model = None
        self.data = None
        self.viewer = None
        
        # 时间控制
        self.last_update_time = 0
        
        # 加载BVH数据
        self.load_bvh_data()
        
        # 创建MuJoCo模型
        self.create_mujoco_model()
    
    def load_bvh_data(self):
        """加载BVH文件数据"""
        print(f"加载BVH文件: {self.bvh_file_path}")
        
        # 使用BVHMotion加载动作数据
        self.bvh_motion = BVHMotion(self.bvh_file_path)
        
        # 使用BVHParser解析文件结构
        self.bvh_parser = BVHParser(self.bvh_file_path)
        self.bvh_parser.parse()
        
        # 获取基本信息
        self.fps = self.bvh_motion.mocap_framerate
        self.total_frames = self.bvh_motion.motion_length
        
        print(f"BVH数据加载完成:")
        print(f"  - 关节数: {len(self.bvh_motion.joint_name)}")
        print(f"  - 帧数: {self.total_frames}")
        print(f"  - 帧率: {self.fps} fps")
        print(f"  - 时长: {self.total_frames / self.fps:.2f} 秒")
        
        # 计算全局关节位置
        self.bvh_motion.batch_forward_kinematics()
    
    def create_mujoco_model(self):
        """创建MuJoCo模型以可视化BVH骨架"""
        # 创建一个空的MuJoCo模型XML
        xml_string = self.generate_skeleton_xml()
        
        # 创建模型
        self.model = mujoco.MjModel.from_xml_string(xml_string)
        self.data = mujoco.MjData(self.model)
        
        print("MuJoCo骨架模型创建完成")
    
    def generate_skeleton_xml(self):
        """生成骨架的MuJoCo XML"""
        joint_names = self.bvh_motion.joint_name
        joint_parents = self.bvh_motion.joint_parent
        
        # 获取T-pose位置作为初始位置
        t_pose_positions = self.bvh_motion.get_T_pose()
        
        xml_parts = [
            '<?xml version="1.0" ?>',
            '<mujoco model="bvh_skeleton">',
            '  <compiler angle="degree" coordinate="local"/>',
            '  <option timestep="0.01" integrator="Euler"/>',
            '',
            '  <default>',
            '    <geom rgba="1 1 1 1" density="1000"/>',
            '    <joint limited="false" damping="0" armature="0"/>',
            '  </default>',
            '',
            '  <asset>',
            '    <material name="joint_mat" rgba="1 0 0 1"/>',
            '    <material name="bone_mat" rgba="0 0 1 1"/>',
            '  </asset>',
            '',
            '  <worldbody>',
            '    <light diffuse=".5 .5 .5" pos="0 0 3" dir="0 0 -1"/>',
            '    <geom type="plane" size="10 10 0.1" rgba=".9 .9 .9 1"/>',
            ''
        ]
        
        # 递归生成骨架结构
        def add_joint_recursive(joint_idx, depth=1):
            joint_name = joint_names[joint_idx]
            parent_idx = joint_parents[joint_idx]
            pos = t_pose_positions[joint_idx] * 0.01  # 转换为米
            
            indent = '  ' * (depth + 1)
            
            if parent_idx == -1:  # 根节点
                xml_parts.extend([
                    f'{indent}<body name="{joint_name}" pos="{pos[0]:.6f} {pos[1]:.6f} {pos[2]:.6f}">',
                    f'{indent}  <geom name="{joint_name}_geom" type="sphere" size="{self.joint_radius}" material="joint_mat"/>',
                    f'{indent}  <freejoint name="{joint_name}_free"/>',
                ])
            else:
                parent_pos = t_pose_positions[parent_idx] * 0.01
                relative_pos = pos - parent_pos
                
                xml_parts.extend([
                    f'{indent}<body name="{joint_name}" pos="{relative_pos[0]:.6f} {relative_pos[1]:.6f} {relative_pos[2]:.6f}">',
                    f'{indent}  <geom name="{joint_name}_geom" type="sphere" size="{self.joint_radius}" material="joint_mat"/>',
                ])
                
                # 添加连接父子关节的骨骼
                bone_length = np.linalg.norm(relative_pos)
                if bone_length > 0.001:  # 避免长度太小的骨骼
                    xml_parts.append(
                        f'{indent}  <geom name="{joint_name}_bone" type="capsule" size="{self.bone_radius}" '
                        f'fromto="0 0 0 {relative_pos[0]:.6f} {relative_pos[1]:.6f} {relative_pos[2]:.6f}" material="bone_mat"/>'
                    )
            
            # 递归添加子关节
            children = [i for i, parent in enumerate(joint_parents) if parent == joint_idx]
            for child_idx in children:
                add_joint_recursive(child_idx, depth + 1)
            
            # 关闭当前body标签
            xml_parts.append(f'{indent}</body>')
        
        # 找到根节点（parent为-1的节点）
        root_indices = [i for i, parent in enumerate(joint_parents) if parent == -1]
        
        # 添加所有根节点及其子树
        for root_idx in root_indices:
            add_joint_recursive(root_idx)
        
        xml_parts.extend([
            '  </worldbody>',
            '</mujoco>'
        ])
        
        return '\n'.join(xml_parts)
    
    def update_skeleton_pose(self, frame_idx):
        """更新骨架姿态到指定帧"""
        if frame_idx >= self.total_frames:
            frame_idx = self.total_frames - 1
        elif frame_idx < 0:
            frame_idx = 0
        
        # 获取当前帧的关节位置
        joint_positions = self.bvh_motion.joint_translation[frame_idx] * 0.01  # 转换为米
        joint_rotations = self.bvh_motion.joint_orientation[frame_idx]
        
        joint_names = self.bvh_motion.joint_name
        
        # 更新根节点位置（如果存在freejoint）
        if self.model.njnt > 0:
            try:
                root_pos = joint_positions[0]
                root_quat = joint_rotations[0]  # [x, y, z, w] - scipy格式
                
                # 找到freejoint的qpos索引
                freejoint_qpos_start = 0
                if freejoint_qpos_start + 7 <= len(self.data.qpos):
                    # 设置位置 (3个坐标)
                    self.data.qpos[freejoint_qpos_start:freejoint_qpos_start+3] = root_pos
                    # 设置四元数 (MuJoCo格式: [w, x, y, z])
                    self.data.qpos[freejoint_qpos_start+3:freejoint_qpos_start+7] = [
                        root_quat[3], root_quat[0], root_quat[1], root_quat[2]
                    ]
            except Exception as e:
                print(f"更新根节点失败: {e}")
        
        # 如果有其他关节，尝试更新它们（这是一个简化的方法）
        # 在实际应用中，可能需要为每个关节设置适当的joint类型
        
        # 前向运动学更新
        mujoco.mj_forward(self.model, self.data)
        
        # 存储当前帧信息用于调试
        self.current_frame = frame_idx
    
    def play(self):
        """开始播放BVH动作"""
        print("开始播放BVH动作...")
        print("\n🎮 播放控制:")
        print("  空格键 - 暂停/播放")
        print("  R - 重置到第一帧")
        print("  + / = - 加快播放速度")
        print("  - - 减慢播放速度")
        print("  → - 下一帧")
        print("  ← - 上一帧")
        print("  Q / Esc - 退出")
        print("  鼠标 - 旋转视角")
        print("  鼠标滚轮 - 缩放")
        
        # 启动MuJoCo查看器
        with mujoco.viewer.launch_passive(self.model, self.data) as viewer:
            self.viewer = viewer
            
            # 设置初始摄像机位置
            viewer.cam.distance = 3.0
            viewer.cam.elevation = -20
            viewer.cam.azimuth = 45
            
            # 设置键盘回调
            self.setup_keyboard_callbacks()
            
            self.is_playing = True
            self.last_update_time = time.time()
            
            # 初始化第一帧
            self.update_skeleton_pose(0)
            
            while viewer.is_running():
                current_time = time.time()
                
                # 更新动画
                if self.is_playing:
                    dt = current_time - self.last_update_time
                    frame_increment = dt * self.fps * self.playback_speed
                    
                    if frame_increment >= 1.0:
                        self.current_frame += int(frame_increment)
                        
                        if self.current_frame >= self.total_frames:
                            if hasattr(self, 'loop_playback') and self.loop_playback:
                                self.current_frame = 0
                            else:
                                self.current_frame = self.total_frames - 1
                                self.is_playing = False
                        
                        self.update_skeleton_pose(self.current_frame)
                        self.last_update_time = current_time
                
                # 更新显示信息
                self.update_display_info()
                
                # 同步显示
                viewer.sync()
                
                # 控制帧率
                time.sleep(0.01)
    
    def setup_keyboard_callbacks(self):
        """设置键盘回调"""
        # 由于MuJoCo viewer的键盘处理比较复杂，这里使用简化的方式
        # 在实际应用中，可以通过检查特定的按键状态来实现
        self.key_states = {}
        self.last_key_press_time = {}
        
    def handle_key_press(self, key):
        """处理按键事件"""
        current_time = time.time()
        
        # 防止按键重复触发
        if key in self.last_key_press_time:
            if current_time - self.last_key_press_time[key] < 0.2:  # 200ms防抖
                return
        
        self.last_key_press_time[key] = current_time
        
        if key == ' ':  # 空格键 - 播放/暂停
            self.toggle_playback()
        elif key.lower() == 'r':  # R键 - 重置
            self.reset_playback()
        elif key in ['+', '=']:  # 加速
            self.adjust_speed(1.2)
        elif key == '-':  # 减速
            self.adjust_speed(0.8)
        elif key == 'right':  # 右箭头 - 下一帧
            self.next_frame()
        elif key == 'left':  # 左箭头 - 上一帧
            self.previous_frame()
        elif key.lower() in ['q', 'escape']:  # 退出
            if self.viewer:
                print("\n退出播放器")
                # 这里可以添加退出逻辑
                pass
    
    def update_display_info(self):
        """更新显示信息"""
        if not self.viewer:
            return
        
        # 在MuJoCo viewer中显示播放信息
        status = "播放" if self.is_playing else "暂停"
        frame_info = f"帧: {self.current_frame}/{self.total_frames}"
        speed_info = f"速度: {self.playback_speed:.1f}x"
        time_info = f"时间: {self.current_frame/self.fps:.2f}s"
        
        # 在控制台显示信息（每30帧显示一次）
        if self.current_frame % 30 == 0:
            print(f"\r{status} | {frame_info} | {speed_info} | {time_info}", end="", flush=True)
    
    def reset_playback(self):
        """重置播放到第一帧"""
        self.current_frame = 0
        self.update_skeleton_pose(0)
        print("\n重置到第一帧")
    
    def toggle_playback(self):
        """切换播放/暂停状态"""
        self.is_playing = not self.is_playing
        status = "播放" if self.is_playing else "暂停"
        print(f"\n{status}")
        
        if self.is_playing:
            self.last_update_time = time.time()
    
    def adjust_speed(self, factor):
        """调整播放速度"""
        self.playback_speed *= factor
        self.playback_speed = max(0.1, min(5.0, self.playback_speed))  # 限制在0.1x到5.0x之间
        print(f"\n播放速度: {self.playback_speed:.1f}x")
    
    def next_frame(self):
        """下一帧"""
        self.current_frame = min(self.current_frame + 1, self.total_frames - 1)
        self.update_skeleton_pose(self.current_frame)
        self.is_playing = False
    
    def previous_frame(self):
        """上一帧"""
        self.current_frame = max(self.current_frame - 1, 0)
        self.update_skeleton_pose(self.current_frame)
        self.is_playing = False


def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description='BVH动作播放器 - 在MuJoCo中播放BVH骨架动作',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python play_bvh.py data/bvh/walk_forward.bvh
  python play_bvh.py data/bvh/walk_forward.bvh --fps 60
  python play_bvh.py data/bvh/walk_forward.bvh --joint-radius 0.03 --bone-radius 0.015
        """
    )
    
    parser.add_argument(
        'bvh_file',
        type=str,
        help='要播放的BVH文件路径'
    )
    
    parser.add_argument(
        '--fps',
        type=float,
        default=None,
        help='播放帧率 (默认使用BVH文件中的帧率)'
    )
    
    parser.add_argument(
        '--joint-radius',
        type=float,
        default=0.02,
        help='关节球体半径 (默认: 0.02)'
    )
    
    parser.add_argument(
        '--bone-radius',
        type=float,
        default=0.01,
        help='骨骼胶囊半径 (默认: 0.01)'
    )
    
    parser.add_argument(
        '--loop',
        action='store_true',
        help='循环播放'
    )
    
    parser.add_argument(
        '--speed',
        type=float,
        default=1.0,
        help='初始播放速度 (默认: 1.0)'
    )
    
    return parser


def main():
    parser = create_parser()
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.bvh_file):
        print(f"错误: BVH文件 '{args.bvh_file}' 不存在")
        sys.exit(1)
    
    try:
        # 创建播放器
        player = BVHMuJoCoPlayer(args.bvh_file)
        
        # 设置参数
        if args.fps:
            player.fps = args.fps
        
        player.joint_radius = args.joint_radius
        player.bone_radius = args.bone_radius
        player.playback_speed = args.speed
        player.loop_playback = args.loop
        
        # 开始播放
        player.play()
        
    except Exception as e:
        print(f"播放失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    print("\n播放结束")


if __name__ == "__main__":
    main()
