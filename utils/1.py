class BVHMujocoPlayer:
    """专门用于播放BVH重定向结果的MuJoCo播放器"""
    
    def __init__(self, motion_lib_cfg, skeleton_params):
        """初始化BVH播放器
        
        Args:
            motion_lib_cfg: 机器人配置
        """
        self.motion_lib_cfg = motion_lib_cfg
        self.mjcf_file = motion_lib_cfg.mjcf_file
        self.robot_joint_names = motion_lib_cfg.joint_names
        # 配置文件中获取左右脚指关节名称
        self.left_toe_name = motion_lib_cfg.left_toe_name
        self.right_toe_name = motion_lib_cfg.right_toe_name

        self.left_toe_idx = self.robot_joint_names.index(self.left_toe_name)
        self.right_toe_idx = self.robot_joint_names.index(self.right_toe_name)

        self.robot_joint_pick = list(motion_lib_cfg.bvh_joint_correspondence.keys())
        self.robot_joint_pick_idx = [self.robot_joint_names.index(name) for name in self.robot_joint_pick]


        self.bvh_joint_pick = list(skeleton_params['bvh_info']['selected_joints'])
        self.bvh_joint_pick_idx = [skeleton_params['bvh_info']['joint_names'].index(name) for name in self.bvh_joint_pick]

        
        # 创建MuJoCo模型
        import mujoco
        import numpy as np
        self.model = mujoco.MjModel.from_xml_path(self.mjcf_file)
        self.data = mujoco.MjData(self.model)
        
        # 保存模块引用
        self.mujoco = mujoco
        self.np = np
        
        print(f"BVH MuJoCo播放器初始化完成:")
        print(f"  - 机器人模型: {self.mjcf_file}")
        print(f"  - 关节数量: {len(self.robot_joint_names)}")
        
        # 查找脚部关节或site
        self.left_foot_indices = []
        self.right_foot_indices = []
        self.left_foot_site_id = -1
        self.right_foot_site_id = -1
        
        # 查找脚部site
        for i in range(self.model.nsite):
            site_name = self.mujoco.mj_id2name(self.model, self.mujoco.mjtObj.mjOBJ_SITE, i)
            if site_name and ('left' in site_name.lower() or 'l_' in site_name.lower()) and 'foot' in site_name.lower():
                self.left_foot_site_id = i
            elif site_name and ('right' in site_name.lower() or 'r_' in site_name.lower()) and 'foot' in site_name.lower():
                self.right_foot_site_id = i
        
        # 查找脚部关节 - 同时查找body和joint
        for i in range(self.model.nbody):
            body_name = self.mujoco.mj_id2name(self.model, self.mujoco.mjtObj.mjOBJ_BODY, i)
            if body_name:
                if any(keyword in body_name.lower() for keyword in ['left_foot', 'foot_l', 'l_foot', 'left_ankle', 'ankle_l']):
                    self.left_foot_indices.append(i)
                elif any(keyword in body_name.lower() for keyword in ['right_foot', 'foot_r', 'r_foot', 'right_ankle', 'ankle_r']):
                    self.right_foot_indices.append(i)
        
        # 如果没找到body，尝试查找joint
        if not self.left_foot_indices and not self.right_foot_indices:
            for i in range(self.model.nq):
                joint_name = self.mujoco.mj_id2name(self.model, self.mujoco.mjtObj.mjOBJ_JOINT, i)
                if joint_name:
                    if any(keyword in joint_name.lower() for keyword in ['left_foot', 'foot_l', 'l_foot', 'left_ankle', 'ankle_l']):
                        self.left_foot_indices.append(i)
                    elif any(keyword in joint_name.lower() for keyword in ['right_foot', 'foot_r', 'r_foot', 'right_ankle', 'ankle_r']):
                        self.right_foot_indices.append(i)
        
        print(f"  - 左脚site ID: {self.left_foot_site_id}")
        print(f"  - 右脚site ID: {self.right_foot_site_id}")
        print(f"  - 左脚关节: {self.left_foot_indices}")
        print(f"  - 右脚关节: {self.right_foot_indices}")
        
        # 打印所有body和joint名称以便调试
        print(f"\n📝 机器人模型结构调试:")
        print(f"  - 总body数: {self.model.nbody}")
        print(f"  - 总joint数: {self.model.nq}")
        print(f"  - 总site数: {self.model.nsite}")
        
        print(f"\n🔍 所有body名称:")
        for i in range(min(self.model.nbody, 20)):  # 只显示前20个
            body_name = self.mujoco.mj_id2name(self.model, self.mujoco.mjtObj.mjOBJ_BODY, i)
            if body_name:
                print(f"    {i}: {body_name}")
        
        print(f"\n🔍 所有joint名称:")
        for i in range(min(self.model.nq, 20)):  # 只显示前20个
            joint_name = self.mujoco.mj_id2name(self.model, self.mujoco.mjtObj.mjOBJ_JOINT, i)
            if joint_name:
                print(f"    {i}: {joint_name}")
        
        print(f"\n🔍 所有site名称:")
        for i in range(self.model.nsite):
            site_name = self.mujoco.mj_id2name(self.model, self.mujoco.mjtObj.mjOBJ_SITE, i)
            if site_name:
                print(f"    {i}: {site_name}")

    def get_foot_positions(self):
        """获取脚部位置"""
        # 优先使用site位置
        if self.left_foot_site_id >= 0:
            left_foot_pos = self.data.site_xpos[self.left_foot_site_id]
        else:
            # 使用body位置
            if self.left_foot_indices:
                left_foot_pos = self.data.xpos[self.left_foot_indices[0]]
            else:
                # 默认位置 - 相对于根节点
                left_foot_pos = self.np.array([self.data.qpos[0] - 0.1, self.data.qpos[1], 0.0])
        
        if self.right_foot_site_id >= 0:
            right_foot_pos = self.data.site_xpos[self.right_foot_site_id]
        else:
            # 使用body位置
            if self.right_foot_indices:
                right_foot_pos = self.data.xpos[self.right_foot_indices[0]]
            else:
                # 默认位置 - 相对于根节点
                right_foot_pos = self.np.array([self.data.qpos[0] + 0.1, self.data.qpos[1], 0.0])
        
        return left_foot_pos, right_foot_pos

    def euler_to_quat(self, euler):
        """将旋转向量转换为MuJoCo四元数格式 [w, x, y, z]"""
        angle = self.np.linalg.norm(euler)
        if angle < 1e-10:
            return self.np.array([1.0, 0.0, 0.0, 0.0])
        
        axis = euler / angle
        w = self.np.cos(angle / 2.0)
        xyz = axis * self.np.sin(angle / 2.0)
        return self.np.array([w, xyz[0], xyz[1], xyz[2]])

    def play_bvh_motion(self, dof_pos, root_pos, root_rot, bvh_target_positions, 
                       contact_sequence=None, final_frame_losses=None, fps=30.0):
        """播放BVH重定向的机器人动作
        
        Args:
            dof_pos: 优化后的关节角度 [1, num_frames, num_joints, 1]
            root_pos: 根节点位置 [num_frames, 3] 
            root_rot: 根节点旋转 [num_frames, 3]
            bvh_target_positions: BVH目标位置 [num_frames, num_bvh_joints, 3]
            contact_sequence: 接触序列 [num_frames, 2] (左脚, 右脚)
            final_frame_losses: 每帧损失 [num_frames]
            fps: 播放帧率
        """
        import mujoco.viewer
        import time
        
        # 数据预处理
        dof_data = dof_pos.squeeze(0).detach().cpu().numpy()  # [num_frames, num_joints, 1]
        root_pos_data = root_pos.detach().cpu().numpy()      # [num_frames, 3]
        root_rot_data = root_rot.detach().cpu().numpy()      # [num_frames, 3]
        bvh_target_data = bvh_target_positions.detach().cpu().numpy()  # [num_frames, num_bvh_joints, 3]
        
        num_frames = dof_data.shape[0]
        target_fps = fps
        
        print(f"开始播放BVH重定向动作:")
        print(f"  - 总帧数: {num_frames}")
        print(f"  - 播放帧率: {target_fps} fps")
        print(f"  - 总时长: {num_frames / target_fps:.2f} 秒")
        print(f"  - 关节数: {dof_data.shape[1]}")
        print(f"  - BVH目标点数: {bvh_target_data.shape[1]}")
        
        # 播放控制变量
        frame = [0]
        running = [True]
        paused = [False]
        playback_speed = [1.0]
        camera_tracking = [True]
        tracking_distance = [3.0]
        show_bvh_targets = [True]
        show_contact = [True]
        
        def key_callback(keycode):
            """键盘回调函数"""
            # 空格键 - 暂停/播放
            if keycode == 32:  # 空格键
                paused[0] = not paused[0]
                print(f"{'暂停' if paused[0] else '播放'}")
            
            # 加速/减速
            elif keycode == 43 or keycode == 61:  # + 或 =
                playback_speed[0] *= 1.5
                print(f"播放速度: {playback_speed[0]:.2f}x")
            elif keycode == 45:  # -
                playback_speed[0] /= 1.5
                if playback_speed[0] < 0.1:
                    playback_speed[0] = 0.1
                print(f"播放速度: {playback_speed[0]:.2f}x")
            
            # 前进/后退一帧
            elif keycode == 46:  # .
                frame[0] = (frame[0] + 1) % num_frames
                print(f"前进到帧: {frame[0]}")
            elif keycode == 44:  # ,
                frame[0] = (frame[0] - 1) % num_frames
                print(f"后退到帧: {frame[0]}")
            
            # 重置
            elif keycode == 114 or keycode == 82:  # r 或 R
                frame[0] = 0
                print("重置到第一帧")
            
            # 切换相机跟踪
            elif keycode == 116 or keycode == 84:  # t 或 T
                camera_tracking[0] = not camera_tracking[0]
                print(f"相机跟踪: {'开启' if camera_tracking[0] else '关闭'}")
            
            # 切换BVH目标点显示
            elif keycode == 98 or keycode == 66:  # b 或 B
                show_bvh_targets[0] = not show_bvh_targets[0]
                print(f"BVH目标点: {'显示' if show_bvh_targets[0] else '隐藏'}")
            
            # 切换接触显示
            elif keycode == 99 or keycode == 67:  # c 或 C
                if contact_sequence is not None:
                    show_contact[0] = not show_contact[0]
                    print(f"接触显示: {'开启' if show_contact[0] else '关闭'}")
            
            # 退出
            elif keycode == 113 or keycode == 81 or keycode == 27:  # q, Q 或 Esc
                running[0] = False
                print("退出播放")
        
        # 创建MuJoCo查看器
        viewer = mujoco.viewer.launch_passive(self.model, self.data, key_callback=key_callback)
        
        # 设置初始相机位置
        viewer.cam.distance = 3.0
        viewer.cam.azimuth = 45
        viewer.cam.elevation = -20
        
        # 保存初始相机参数
        initial_cam_distance = viewer.cam.distance
        initial_cam_azimuth = viewer.cam.azimuth  
        initial_cam_elevation = viewer.cam.elevation
        
        # 打印控制说明
        print("\n🎮 播放控制:")
        print("  空格键 - 暂停/播放")
        print("  + / = - 加快播放")
        print("  - - 减慢播放") 
        print("  . - 前进一帧")
        print("  , - 后退一帧")
        print("  r / R - 重置播放")
        print("  t / T - 切换相机跟踪")
        print("  b / B - 切换BVH目标点显示")
        print("  c / C - 切换接触显示")
        print("  q / Q / Esc - 退出")
        print("  鼠标滚轮 - 缩放\n")
        
        last_time = time.time()
        
        try:
            while viewer.is_running() and running[0]:
                current_time = time.time()
                delta_time = current_time - last_time
                
                # 控制帧率
                if delta_time < 1.0/target_fps:
                    time.sleep(1.0/target_fps - delta_time)
                    continue
                
                # 更新帧计数
                if not paused[0]:
                    if playback_speed[0] < 1.0:
                        frame[0] = (frame[0] + 1) % num_frames
                        additional_delay = (1.0/target_fps) * (1.0/playback_speed[0] - 1.0)
                        time.sleep(additional_delay)
                    else:
                        frame_step = max(1, int(playback_speed[0]))
                        frame[0] = (frame[0] + frame_step) % num_frames
                
                current_frame = frame[0]
                
                # 设置机器人姿态
                # 设置根节点位置 (数据已经是正确的米单位)
                root_pos_current = root_pos_data[current_frame].copy()
                root_pos_current[2] = max(root_pos_current[2], 0.8)  # 确保在地面以上
                self.data.qpos[0:3] = root_pos_current
                
                # 设置根节点旋转 (转换为四元数)
                root_quat = self.euler_to_quat(root_rot_data[current_frame])
                self.data.qpos[3:7] = root_quat  # [w, x, y, z]
                
                # 设置关节角度 - 确保角度在合理范围内
                joint_start_idx = 7  # 跳过根节点的位置和旋转
                available_joints = self.model.nq - joint_start_idx
                
                if current_frame == 0:  # 第一帧时打印调试信息
                    print(f"🔧 关节角度设置调试:")
                    print(f"  - 总qpos数: {self.model.nq}")
                    print(f"  - 根节点占用: {joint_start_idx}")
                    print(f"  - 可用关节数: {available_joints}")
                    print(f"  - DOF数据关节数: {dof_data.shape[1]}")
                    print(f"  - 实际设置关节数: {min(dof_data.shape[1], available_joints)}")
                
                for i in range(min(dof_data.shape[1], available_joints)):
                    qpos_idx = joint_start_idx + i
                    if qpos_idx < self.model.nq:
                        angle = dof_data[current_frame, i, 0]
                        # 限制角度范围到合理值
                        angle = self.np.clip(angle, -3.14, 3.14)
                        self.data.qpos[qpos_idx] = angle
                        
                        # 第一帧时打印前几个关节的值
                        if current_frame == 0 and i < 5:
                            print(f"    关节{i} (qpos[{qpos_idx}]): {angle:.3f}")
                
                # 更新MuJoCo仿真
                self.mujoco.mj_forward(self.model, self.data)
                
                # 相机跟踪
                if camera_tracking[0]:
                    # 使用当前根节点位置
                    viewer.cam.lookat = root_pos_current
                    viewer.cam.distance = tracking_distance[0]
                
                # 重置用户场景几何体
                viewer.user_scn.ngeom = 0
                geom_idx = 0
                
                # 显示BVH目标点
                if show_bvh_targets[0] and geom_idx < viewer.user_scn.maxgeom:
                    for i in range(bvh_target_data.shape[1]):
                        if geom_idx >= viewer.user_scn.maxgeom:
                            break
                        
                        # BVH目标点位置已经是正确的米单位
                        pos = bvh_target_data[current_frame, i]
                        size = self.np.array([0.02, 0, 0], dtype=self.np.float64)
                        mat = self.np.eye(3, dtype=self.np.float64).flatten()
                        rgba = self.np.array([0.2, 0.8, 0.2, 0.8], dtype=self.np.float32)  # 绿色
                        
                        self.mujoco.mjv_initGeom(
                            viewer.user_scn.geoms[geom_idx],
                            self.mujoco.mjtGeom.mjGEOM_SPHERE,
                            size, pos, mat, rgba
                        )
                        geom_idx += 1
                
                # 显示接触指示器
                if show_contact[0] and contact_sequence is not None and geom_idx < viewer.user_scn.maxgeom - 1:
                    if current_frame < len(contact_sequence):
                        contacts = contact_sequence[current_frame]  # [left_contact, right_contact]
                        
                        # 获取脚部位置 - 智能查找脚部关节
                        left_foot_pos, right_foot_pos = self.get_foot_positions()
                        
                        indicator_radius = 0.08
                        indicator_height = 0.01
                        
                        # 左脚接触指示器
                        if contacts[0]:  # 左脚接触
                            pos = self.np.array([left_foot_pos[0], left_foot_pos[1], indicator_height/2])
                            size = self.np.array([indicator_radius, indicator_height/2, 0], dtype=self.np.float64)
                            mat = self.np.eye(3, dtype=self.np.float64).flatten()
                            rgba = self.np.array([1.0, 0.2, 0.2, 0.8], dtype=self.np.float32)  # 红色
                            
                            self.mujoco.mjv_initGeom(
                                viewer.user_scn.geoms[geom_idx],
                                self.mujoco.mjtGeom.mjGEOM_CYLINDER,
                                size, pos, mat, rgba
                            )
                            geom_idx += 1
                        
                        # 右脚接触指示器
                        if contacts[1] and geom_idx < viewer.user_scn.maxgeom:  # 右脚接触
                            pos = self.np.array([right_foot_pos[0], right_foot_pos[1], indicator_height/2])
                            size = self.np.array([indicator_radius, indicator_height/2, 0], dtype=self.np.float64)
                            mat = self.np.eye(3, dtype=self.np.float64).flatten()
                            rgba = self.np.array([1.0, 0.2, 0.2, 0.8], dtype=self.np.float32)  # 红色
                            
                            self.mujoco.mjv_initGeom(
                                viewer.user_scn.geoms[geom_idx],
                                self.mujoco.mjtGeom.mjGEOM_CYLINDER,
                                size, pos, mat, rgba
                            )
                            geom_idx += 1
                
                # 显示损失信息 (在窗口标题或状态栏)
                if final_frame_losses is not None:
                    loss_info = f"帧: {current_frame}/{num_frames} | 损失: {final_frame_losses[current_frame]:.6f}"
                    # 可以在这里添加文本显示逻辑
                
                last_time = current_time
                
        except KeyboardInterrupt:
            print("用户中断播放")
        finally:
            print("播放结束")