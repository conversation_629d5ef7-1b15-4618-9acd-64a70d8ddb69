#!/usr/bin/env python3
"""
BVH to MuJoCo XML Converter

This script converts BVH (Biovision Hierarchical Data) motion capture files
to MuJoCo-compatible XML format for physics simulation.

Dependencies:
    pip install bvhio

Usage:
    python bvh2xml.py input.bvh output.xml
"""

import sys
import os
import argparse
import xml.etree.ElementTree as ET
from xml.dom import minidom
import math

try:
    import bvhio
except ImportError:
    print("Error: bvhio library not found. Please install it using:")
    print("pip install bvhio")
    sys.exit(1)


class BVH2MuJoCo:
    """Convert BVH files to MuJoCo XML format"""
    
    def __init__(self):
        self.joint_count = 0
        self.geom_count = 0
        
    def parse_bvh(self, file_path):
        """Parse BVH file using bvhio library"""
        try:
            # Read BVH file as hierarchy
            root_joint = bvhio.readAsHierarchy(file_path)
            return root_joint
        except Exception as e:
            print(f"Error parsing BVH file: {e}")
            return None
    
    def create_mujoco_xml(self, root_joint, output_path="humanoid_model.xml"):
        """Create MuJoCo XML from BVH hierarchy"""
        
        # Create root mujoco element
        mujoco = ET.Element("mujoco", model="humanoid_from_bvh")
        
        # Add compiler settings
        compiler = ET.SubElement(mujoco, "compiler")
        compiler.set("angle", "degree")
        compiler.set("inertiafromgeom", "true")
        
        # Add default settings
        default = ET.SubElement(mujoco, "default")
        joint_default = ET.SubElement(default, "joint")
        joint_default.set("limited", "true")
        joint_default.set("range", "-180 180")
        joint_default.set("damping", "0.1")
        
        geom_default = ET.SubElement(default, "geom")
        geom_default.set("rgba", "0.7 0.7 0.7 1")
        geom_default.set("density", "1000")
        
        # Add assets (materials)
        asset = ET.SubElement(mujoco, "asset")
        material = ET.SubElement(asset, "material")
        material.set("name", "bone_material")
        material.set("rgba", "0.8 0.6 0.4 1")
        
        # Create worldbody
        worldbody = ET.SubElement(mujoco, "worldbody")
        
        # Add ground plane
        ground = ET.SubElement(worldbody, "geom")
        ground.set("name", "ground")
        ground.set("type", "plane")
        ground.set("size", "10 10 0.1")
        ground.set("rgba", "0.5 0.5 0.5 1")
        
        # Add light
        light = ET.SubElement(worldbody, "light")
        light.set("diffuse", ".5 .5 .5")
        light.set("pos", "0 0 3")
        light.set("dir", "0 0 -1")
        
        # Convert BVH hierarchy to MuJoCo bodies
        root_body = self.create_body_hierarchy(worldbody, root_joint)
        
        # Add actuators (optional)
        actuator = ET.SubElement(mujoco, "actuator")
        self.add_actuators(actuator, root_joint)
        
        # Save XML file
        self.save_xml(mujoco, output_path)
        
        return output_path
    
    def create_body_hierarchy(self, parent_element, joint, parent_pos=None):
        """Recursively create body hierarchy"""
        if parent_pos is None:
            parent_pos = [0, 0, 0]
        
        # Get joint position (rest pose)
        joint_pos = joint.RestPose.Position
        pos = [joint_pos.x, joint_pos.y, joint_pos.z]
        
        # Create body element
        body = ET.SubElement(parent_element, "body")
        body.set("name", joint.Name)
        body.set("pos", f"{pos[0]:.6f} {pos[1]:.6f} {pos[2]:.6f}")
        
        # Add joint if not root
        if hasattr(joint, 'Parent') and joint.Parent is not None:
            joint_elem = ET.SubElement(body, "joint")
            joint_elem.set("name", f"{joint.Name}_joint")
            joint_elem.set("type", "ball")  # 3DOF rotational joint
            joint_elem.set("pos", "0 0 0")
        
        # Add geometry (capsule representing bone)
        if hasattr(joint, 'Children') and joint.Children:
            # Calculate bone length to first child
            child = joint.Children[0]
            child_pos = child.RestPose.Position
            bone_length = math.sqrt(
                (child_pos.x - joint_pos.x)**2 + 
                (child_pos.y - joint_pos.y)**2 + 
                (child_pos.z - joint_pos.z)**2
            )
            
            if bone_length > 0.01:  # Only add geom if bone is long enough
                geom = ET.SubElement(body, "geom")
                geom.set("name", f"{joint.Name}_geom")
                geom.set("type", "capsule")
                geom.set("size", f"{bone_length * 0.05:.6f}")  # Radius
                geom.set("fromto", f"0 0 0 {child_pos.x - joint_pos.x:.6f} {child_pos.y - joint_pos.y:.6f} {child_pos.z - joint_pos.z:.6f}")
                geom.set("material", "bone_material")
        else:
            # End effector - add small sphere
            geom = ET.SubElement(body, "geom")
            geom.set("name", f"{joint.Name}_end")
            geom.set("type", "sphere")
            geom.set("size", "0.02")
            geom.set("material", "bone_material")
        
        # Recursively add children
        if hasattr(joint, 'Children') and joint.Children:
            for child in joint.Children:
                self.create_body_hierarchy(body, child, pos)
        
        return body
    
    def add_actuators(self, actuator_element, joint):
        """Add actuators for controllable joints"""
        # Add actuator if joint has parent (not root)
        if hasattr(joint, 'Parent') and joint.Parent is not None:
            motor = ET.SubElement(actuator_element, "motor")
            motor.set("name", f"{joint.Name}_motor")
            motor.set("joint", f"{joint.Name}_joint")
            motor.set("gear", "1")
        
        # Recursively add actuators for children
        if hasattr(joint, 'Children') and joint.Children:
            for child in joint.Children:
                self.add_actuators(actuator_element, child)
    
    def save_xml(self, root_element, output_path):
        """Save XML with pretty formatting"""
        # Convert to string
        rough_string = ET.tostring(root_element, 'unicode')
        
        # Pretty print
        reparsed = minidom.parseString(rough_string)
        pretty_xml = reparsed.toprettyxml(indent="  ")
        
        # Remove empty lines
        lines = [line for line in pretty_xml.split('\n') if line.strip()]
        pretty_xml = '\n'.join(lines)
        
        # Save to file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(pretty_xml)
        
        print(f"MuJoCo XML model saved to: {output_path}")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Convert BVH motion capture files to MuJoCo XML format"
    )
    parser.add_argument(
        "input_bvh", 
        help="Path to input BVH file"
    )
    parser.add_argument(
        "output_xml", 
        nargs='?',
        default=None,
        help="Path to output XML file (optional, defaults to input filename with .xml extension)"
    )
    parser.add_argument(
        "--scale", 
        type=float, 
        default=1.0,
        help="Scale factor for the model (default: 1.0)"
    )
    
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.input_bvh):
        print(f"Error: Input file '{args.input_bvh}' not found")
        sys.exit(1)
    
    # Set output path if not provided
    if args.output_xml is None:
        base_name = os.path.splitext(args.input_bvh)[0]
        args.output_xml = f"{base_name}_mujoco.xml"
    
    # Create converter
    converter = BVH2MuJoCo()
    
    # Parse BVH file
    print(f"Parsing BVH file: {args.input_bvh}")
    root_joint = converter.parse_bvh(args.input_bvh)
    
    if root_joint is None:
        print("Failed to parse BVH file")
        sys.exit(1)
    
    # Convert to MuJoCo XML
    print(f"Converting to MuJoCo XML...")
    try:
        output_path = converter.create_mujoco_xml(root_joint, args.output_xml)
        print(f"Conversion completed successfully!")
        print(f"Output file: {output_path}")
    except Exception as e:
        print(f"Error during conversion: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
