#!/usr/bin/env python3
"""
简单的BVH播放器测试工具
用于验证BVH文件的播放效果，检查机器人是否悬空或翻转
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import pickle
import argparse
from utils.bvh_mujoco_viewer import BVHMujocoPlayer

def load_pkl_result(pkl_path):
    """加载pkl结果文件"""
    with open(pkl_path, 'rb') as f:
        data = pickle.load(f)
    
    # 获取第一个（通常是唯一的）键值对
    key = list(data.keys())[0]
    result = data[key]
    
    return result

def main():
    parser = argparse.ArgumentParser(description="BVH播放器测试工具")
    parser.add_argument("--pkl", type=str, required=True, help="重定向结果pkl文件路径")
    parser.add_argument("--fps", type=int, default=120, help="播放帧率")
    parser.add_argument("--robot", type=str, default="unitreeG1", help="机器人类型")
    
    args = parser.parse_args()
    
    # 验证文件存在
    if not os.path.exists(args.pkl):
        print(f"错误：文件不存在 {args.pkl}")
        return
    
    print(f"加载重定向结果: {args.pkl}")
    
    # 加载数据
    result = load_pkl_result(args.pkl)
    
    # 获取数据
    dof_pos = result['dof_pos']  # [num_frames, num_dof]
    root_trans = result['root_trans_offset']  # [num_frames, 3]
    pose_aa = result['pose_aa']  # [num_frames, num_joints, 3]
    
    print(f"数据形状:")
    print(f"  - dof_pos: {dof_pos.shape}")
    print(f"  - root_trans: {root_trans.shape}")
    print(f"  - pose_aa: {pose_aa.shape}")
    
    # 检查数据范围
    print(f"\n数据范围:")
    print(f"  - 根节点位置: X[{root_trans[:, 0].min():.3f}, {root_trans[:, 0].max():.3f}]")
    print(f"  - 根节点位置: Y[{root_trans[:, 1].min():.3f}, {root_trans[:, 1].max():.3f}]")
    print(f"  - 根节点位置: Z[{root_trans[:, 2].min():.3f}, {root_trans[:, 2].max():.3f}]")
    
    # 检查根节点高度
    min_height = root_trans[:, 2].min()
    max_height = root_trans[:, 2].max()
    
    if min_height > 1.5:
        print(f"⚠️  警告：机器人可能悬空过高（最低高度: {min_height:.3f}m）")
    elif min_height < 0.5:
        print(f"⚠️  警告：机器人可能过低（最低高度: {min_height:.3f}m）")
    else:
        print(f"✅ 根节点高度正常（{min_height:.3f}m - {max_height:.3f}m）")
    
    # 检查相邻帧的变化
    root_diff = np.diff(root_trans, axis=0)
    max_position_change = np.max(np.abs(root_diff))
    
    if max_position_change > 0.1:
        print(f"⚠️  警告：相邻帧位置变化过大（最大变化: {max_position_change:.3f}m）")
    else:
        print(f"✅ 相邻帧位置变化正常（最大变化: {max_position_change:.3f}m）")
    
    # 检查根节点旋转
    if pose_aa.shape[1] > 0:
        root_rot_aa = pose_aa[:, 0, :]  # 根节点旋转（轴角）
        root_rot_angles = np.linalg.norm(root_rot_aa, axis=1)  # 旋转角度
        
        rot_diff = np.diff(root_rot_angles)
        max_rotation_change = np.max(np.abs(rot_diff))
        
        if max_rotation_change > np.pi:
            print(f"⚠️  警告：相邻帧旋转变化过大（最大变化: {max_rotation_change:.3f}rad = {np.degrees(max_rotation_change):.1f}°）")
        else:
            print(f"✅ 相邻帧旋转变化正常（最大变化: {max_rotation_change:.3f}rad = {np.degrees(max_rotation_change):.1f}°）")
    
    # 创建播放器
    mjcf_file = f'resources/robots/{args.robot}/mjcf/{args.robot}_retarget.xml'
    if args.robot == "unitreeG1":
        mjcf_file = 'resources/robots/unitreeG1/mjcf/unitree_G1_retarget.xml'
    
    print(f"\n初始化播放器...")
    print(f"机器人模型: {mjcf_file}")
    
    try:
        player = BVHMujocoPlayer(mjcf_file, fps=args.fps)
        
        # 播放动作
        print(f"\n开始播放动作...")
        print(f"帧数: {len(dof_pos)}")
        print(f"播放时长: {len(dof_pos) / args.fps:.1f}秒")
        
        # 播放机器人动作
        player.play_robot_motion(
            dof_pos=dof_pos,
            root_pos=root_trans,
            root_rot_aa=pose_aa[:, 0, :],  # 根节点旋转
            fps=args.fps
        )
        
    except Exception as e:
        print(f"播放错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 