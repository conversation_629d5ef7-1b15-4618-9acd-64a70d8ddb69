#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BVH逆运动学重定向工具

从BVH骨骼优化结果中读取参数，将BVH动作重定向到机器人身上
"""

import os
import sys
import json
import numpy as np
import torch
from typing import Dict, List, Tuple, Optional
import argparse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.bvh_parser import BVHParser
# 移除对isaacgym的依赖
# from humanoid.utils.motion_lib_humanoid import Humanoid_Batch


class BVHRetargetIK:
    """BVH逆运动学重定向类"""
    
    def __init__(self, optimization_result_path: str, device: str = "cpu"):
        """
        初始化重定向器
        
        Args:
            optimization_result_path: 优化结果文件路径
            device: 计算设备
        """
        self.device = device
        self.optimization_result = self.load_optimization_result(optimization_result_path)
        self.bvh_parser = BVHParser(self.optimization_result['original_bvh']['file_path'])
        self.bvh_parser.parse()
        
        # 初始化机器人FK
        self.init_robot_fk()
        
        # 加载优化后的参数
        self.load_optimized_params()
        
    def load_optimization_result(self, result_path: str) -> Dict:
        """加载优化结果"""
        with open(result_path, 'r') as f:
            return json.load(f)
    
    def init_robot_fk(self):
        """初始化机器人前向运动学"""
        mjcf_file = self.optimization_result['robot_config']['mjcf_file']
        print(f"机器人配置文件: {mjcf_file}")
        print("注意: 当前版本使用简化的FK计算，完整功能需要isaacgym环境")
        # 暂时使用占位符，后续可以集成完整的FK计算
        self.robot_fk = None
    
    def load_optimized_params(self):
        """加载优化后的参数"""
        # 加载优化后的偏移量
        self.optimized_offsets = {}
        for joint_name, offset in self.optimization_result['optimized_offsets'].items():
            self.optimized_offsets[joint_name] = torch.tensor(offset, device=self.device)
        
        # 加载缩放因子
        self.scale = self.optimization_result['scale']
        
        # 加载根节点调整
        self.root_height_adjustment = None
        if self.optimization_result['root_height_adjustment']:
            self.root_height_adjustment = torch.tensor(
                self.optimization_result['root_height_adjustment'], 
                device=self.device
            )
        
        # 加载根节点变换
        self.root_trans = None
        if self.optimization_result['root_trans']:
            self.root_trans = torch.tensor(
                self.optimization_result['root_trans'], 
                device=self.device
            )
        
        # 加载关节对应关系
        self.joint_correspondence = self.optimization_result['joint_correspondence']
        
        print("优化参数加载完成")
    
    def apply_optimized_offsets_to_bvh(self, frame_data: np.ndarray) -> np.ndarray:
        """将优化后的偏移量应用到BVH帧数据"""
        # 这里需要实现具体的偏移量应用逻辑
        # 暂时返回原始数据
        return frame_data
    
    def transform_bvh_to_robot_coordinates(self, bvh_positions: np.ndarray) -> np.ndarray:
        """将BVH坐标系转换为机器人坐标系"""
        # BVH: z向前，y向上，x向左 -> 机器人: z向上，y向前，x向右
        robot_positions = np.zeros_like(bvh_positions)
        robot_positions[:, 0] = bvh_positions[:, 2]   # x_robot = z_bvh
        robot_positions[:, 1] = bvh_positions[:, 0]   # y_robot = x_bvh
        robot_positions[:, 2] = bvh_positions[:, 1]   # z_robot = y_bvh
        return robot_positions
    
    def apply_scale_and_root_adjustment(self, positions: np.ndarray) -> np.ndarray:
        """应用缩放和根节点调整"""
        # 应用缩放
        positions = positions * self.scale
        
        # 应用根节点高度调整
        if self.root_height_adjustment is not None:
            positions[:, 1] += self.root_height_adjustment.item()  # y方向（高度）
        
        # 应用根节点变换
        if self.root_trans is not None:
            positions[:, 0] += self.root_trans[0].item()  # x方向
            positions[:, 2] += self.root_trans[1].item()  # z方向
        
        return positions
    
    def solve_ik_for_frame(self, target_positions: np.ndarray, joint_names: List[str]) -> np.ndarray:
        """为单帧求解逆运动学
        
        Args:
            target_positions: 目标关节位置 [num_joints, 3]
            joint_names: 关节名称列表
            
        Returns:
            关节角度 [num_dofs]
        """
        # 这里需要实现具体的IK求解算法
        # 可以使用数值优化方法（如梯度下降）或解析方法
        
        # 暂时返回零角度（T-pose）
        num_dofs = len(self.optimization_result['robot_config']['joint_names'])
        return np.zeros(num_dofs)
    
    def retarget_motion(self, output_path: str = None) -> np.ndarray:
        """重定向整个动作序列
        
        Args:
            output_path: 输出文件路径（可选）
            
        Returns:
            重定向后的关节角度序列 [num_frames, num_dofs]
        """
        motion_data = self.bvh_parser.get_motion_data()
        num_frames = motion_data.shape[0]
        num_dofs = len(self.optimization_result['robot_config']['joint_names'])
        
        print(f"开始重定向 {num_frames} 帧动作...")
        
        # 存储重定向结果
        retargeted_angles = np.zeros((num_frames, num_dofs))
        
        # 获取选中的关节名称
        selected_joint_names = self.optimization_result['robot_joint_names']
        
        for frame_idx in range(num_frames):
            if frame_idx % 100 == 0:
                print(f"处理帧 {frame_idx}/{num_frames}")
            
            # 获取当前帧的BVH数据
            frame_data = motion_data[frame_idx]
            
            # 应用优化后的偏移量
            modified_frame_data = self.apply_optimized_offsets_to_bvh(frame_data)
            
            # 计算BVH关节位置
            bvh_positions = self.calculate_bvh_joint_positions(modified_frame_data)
            
            # 转换为机器人坐标系
            robot_positions = self.transform_bvh_to_robot_coordinates(bvh_positions)
            
            # 应用缩放和根节点调整
            robot_positions = self.apply_scale_and_root_adjustment(robot_positions)
            
            # 选择对应的关节位置
            selected_positions = robot_positions[self.optimization_result['bvh_joint_pick_idx']]
            
            # 求解逆运动学
            joint_angles = self.solve_ik_for_frame(selected_positions, selected_joint_names)
            
            retargeted_angles[frame_idx] = joint_angles
        
        print("重定向完成")
        
        # 保存结果
        if output_path:
            self.save_retargeted_motion(retargeted_angles, output_path)
        
        return retargeted_angles
    
    def calculate_bvh_joint_positions(self, frame_data: np.ndarray) -> np.ndarray:
        """计算BVH关节位置（简化版本）"""
        # 这里需要实现完整的BVH前向运动学
        # 暂时返回T-pose位置
        num_joints = len(self.optimization_result['bvh_skeleton_info'])
        return np.zeros((num_joints, 3))
    
    def save_retargeted_motion(self, joint_angles: np.ndarray, output_path: str):
        """保存重定向后的动作"""
        result = {
            'joint_angles': joint_angles.tolist(),
            'joint_names': self.optimization_result['robot_config']['joint_names'],
            'frame_time': self.optimization_result['original_bvh']['frame_time'],
            'num_frames': joint_angles.shape[0],
            'description': 'BVH重定向到机器人的关节角度序列'
        }
        
        with open(output_path, 'w') as f:
            json.dump(result, f, indent=2)
        
        print(f"重定向结果已保存到: {output_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BVH逆运动学重定向工具")
    parser.add_argument('--result', type=str, required=True, 
                       help='优化结果文件路径')
    parser.add_argument('--output', type=str, default='retargeted_motion.json',
                       help='输出文件路径')
    parser.add_argument('--device', type=str, default='cpu',
                       choices=['cpu', 'cuda'], help='计算设备')
    
    args = parser.parse_args()
    
    # 创建重定向器
    retargeter = BVHRetargetIK(args.result, args.device)
    
    # 执行重定向
    retargeted_angles = retargeter.retarget_motion(args.output)
    
    print(f"重定向完成，结果形状: {retargeted_angles.shape}")


if __name__ == "__main__":
    main() 