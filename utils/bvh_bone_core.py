import os
import sys
import os.path as osp
import numpy as np
import torch
import open3d as o3d
import joblib
import torch.nn.functional as F
from torch.optim.lr_scheduler import StepLR
from torch.autograd import Variable
import mujoco
import mujoco.viewer
import time
import threading
import queue
from dataclasses import dataclass
from typing import List, Tuple, Dict, Optional
import json
from datetime import datetime
import pickle
from humanoid.utils.config_register import ConfigFactory
from humanoid.utils.humanoid_batch_registry import humanoid_batch_register
from humanoid.utils.torch_humanoid_batch import Humanoid_Batch
from humanoid.retarget_motion.base.motion_lib_cfg import MotionLibCfg
from scipy.spatial.transform import Rotation as sRot

from utils.bvh_parser import BVHParser
from utils.bvh_skeleton import BVHSkeleton


@dataclass
class BVHOptimizationParams:
    """BVH骨骼优化参数类"""
    def __init__(self):
        self.joint_loss_weight: float = 1.0        # 关节损失权重，默认值1.0
        self.offset_reg_weight: float = 0.001      # 偏移量正则化权重，默认值0.001
        self.symmetry_loss_weight: float = 0.1     # 对称性损失权重，默认值0.1
        self.bone_length_weight: float = 0.02      # 骨骼长度保持损失权重，默认值0.02
        self.endpoint_loss_weight: float = 0.5     # 末端位置损失权重，默认值0.5
        self.learning_rate: float = 0.005          # 学习率，默认值0.005
        self.iterations: int = 1500                # 迭代次数，默认值1500
        self.scale: float = 1.0                    # 整体缩放因子，默认值1.0 (OFFSET已经是米单位)
        self.optimize_scale: bool = False          # 是否优化scale参数，默认值False
        self.optimize_root_translation: bool = False  # 是否优化根节点平移（x和z方向），默认值False
        self.adaptive_root_height: bool = True     # 是否自适应调整根节点高度，默认值True
        self.root_height_weight: float = 0.1       # 根节点高度调整权重，默认值0.1


class BVHBoneOptimizer:
    """BVH骨骼优化器"""
    
    def __init__(self, motion_lib_cfg, bvh_parser: BVHParser, device=None):
        """初始化BVH骨骼优化器
        
        Args:
            motion_lib_cfg: 运动库配置
            bvh_parser: BVH解析器实例
            device: 计算设备
        """
        self.motion_lib_cfg = motion_lib_cfg
        self.bvh_parser = bvh_parser
        
        # 确保BVH解析器已经解析了文件
        if not hasattr(self.bvh_parser, 'joints') or not self.bvh_parser.joints:
            self.bvh_parser.parse()
        
        self.device = device if device else torch.device('cpu')
        
        # 确保motion_lib_cfg中的所有tensor都在正确的device上
        if hasattr(motion_lib_cfg, 'dof_pos') and motion_lib_cfg.dof_pos is not None:
            motion_lib_cfg.dof_pos = motion_lib_cfg.dof_pos.to(self.device)
        if hasattr(motion_lib_cfg, 'root_bias') and motion_lib_cfg.root_bias is not None:
            motion_lib_cfg.root_bias = motion_lib_cfg.root_bias.to(self.device)
        if hasattr(motion_lib_cfg, 'ankle_bias') and motion_lib_cfg.ankle_bias is not None:
            motion_lib_cfg.ankle_bias = motion_lib_cfg.ankle_bias.to(self.device)
        
        # 初始化参数
        self.params = BVHOptimizationParams()
        
        # 加载 MuJoCo 模型
        try:
            import mujoco
            self.model = mujoco.MjModel.from_xml_path(motion_lib_cfg.mjcf_file)
            self.data = mujoco.MjData(self.model)
            print(f"成功加载MuJoCo模型: {motion_lib_cfg.mjcf_file}")
        except Exception as e:
            print(f"警告：无法加载MuJoCo模型: {e}")
            self.model = None
            self.data = None
        
        # 初始化变量
        self.initialize_variables()
        
        # 初始化BVH根节点位置的优化变量，x 和 z 方向
        self.bvh_root_trans = torch.zeros(2, device=self.device, requires_grad=True)  # 只优化 x 和 z 方向
        
        # 初始化BVH根节点旋转的优化变量，3个轴的旋转
        self.bvh_root_rot = torch.zeros(3, device=self.device, requires_grad=True)  # 优化根节点旋转（以度为单位）
        
        # 初始化根节点高度调整变量
        self.root_height_adjustment = torch.zeros(1, device=self.device, requires_grad=True)  # 优化 y 方向（高度）
        
        # 初始化缩放因子
        if self.params.optimize_scale:
            self.scale = torch.tensor(self.params.scale, device=self.device, requires_grad=True)
        else:
            self.scale = torch.tensor(self.params.scale, device=self.device, requires_grad=False)

    def initialize_variables(self):
        """初始化所需变量"""
        # 初始化关节选择和索引
        self.joint_names = self.motion_lib_cfg.joint_names
        
        # 获取BVH关节名称列表
        self.bvh_joint_names = self.bvh_parser.get_joint_names()  #用于后续验证key-points是否都有效
        
        # 检查是否有bvh_joint_correspondence，如果没有则等待后续设置
        if hasattr(self.motion_lib_cfg, 'bvh_joint_correspondence') and self.motion_lib_cfg.bvh_joint_correspondence:
            self.setup_joint_correspondence()
        else:
            # 暂时设置为空，等待后续设置
            self.robot_joint_pick = []
            self.bvh_joint_pick = []
            self.robot_joint_pick_idx = []
            self.bvh_joint_pick_idx = []
        
        # 初始化 Humanoid Forward Kinematics
        self.Humanoid_fk = Humanoid_Batch(self.motion_lib_cfg.mjcf_file, self.motion_lib_cfg.extend_node_dict, device=self.device)
        
        # 初始化 dof_pos 关节角度 - 修复T-pose设置
        self.dof_pos = torch.zeros((1, self.Humanoid_fk.joints_axis.shape[1]), device=self.device)
        
        # 检查配置文件中是否有dof_pos设置
        if hasattr(self.motion_lib_cfg, 'dof_pos') and self.motion_lib_cfg.dof_pos is not None:
            # 使用配置文件中的T-pose设置
            self.dof_pos = self.motion_lib_cfg.dof_pos.to(self.device)
            # print(f"使用配置文件中的T-pose设置")
            # print(f"dof_pos shape: {self.dof_pos.shape}")
            # print(f"dof_pos values: {self.dof_pos}")
        else:
            # 如果没有配置文件设置，使用零向量（默认T-pose）
            self.dof_pos = torch.zeros((1, self.Humanoid_fk.joints_axis.shape[1]), device=self.device)
            print(f"使用默认T-pose（所有关节角度为0）")
        
        # 计算姿态角度
        self.calculate_pose_angles()
        
        # 初始化BVH偏移量优化变量
        self.initialize_bvh_variables()

    def setup_joint_correspondence(self):
        """设置关节对应关系（在bvh_joint_correspondence设置后调用）"""
        if not hasattr(self.motion_lib_cfg, 'bvh_joint_correspondence') or not self.motion_lib_cfg.bvh_joint_correspondence:
            error_msg = """
                错误: 配置文件中未找到 bvh_joint_correspondence 配置项。

                请按照以下步骤操作:
                1. 在您的配置文件中添加 bvh_joint_correspondence 字典
                2. 字典的键应该是机器人的关节名称
                3. 字典的值应该是对应的BVH关节名称

                示例配置:
                bvh_joint_correspondence = {
                    'base_link': 'Hips',
                    'leg_l4_link': 'LeftKnee',
                    'leg_l6_link': 'LeftAnkle',
                    'foot_l1_link': 'LeftFoot',
                    'leg_r4_link': 'RightKnee',
                    'leg_r6_link': 'RightAnkle',
                    'foot_r1_link': 'RightFoot',
                    'left_arm_link02': 'LeftShoulder',
                    'left_arm_link04': 'LeftElbow',
                    'right_arm_link02': 'RightShoulder',
                    'right_arm_link04': 'RightElbow'
                }

                请确保:
                1. 所有机器人关节名称都在 self.joint_names 中存在
                2. 所有BVH关节名称都在 bvh_parser.get_joint_names() 中存在
                3. 关节对应关系合理且完整
                """
            raise ValueError(error_msg)
        
        # 使用配置文件中的对应关系
        self.robot_joint_pick = list(self.motion_lib_cfg.bvh_joint_correspondence.keys())
        self.bvh_joint_pick = list(self.motion_lib_cfg.bvh_joint_correspondence.values())
        
        # 验证所有选中的BVH关键点是否有效
        invalid_joints = [joint for joint in self.bvh_joint_pick if joint not in self.bvh_joint_names]
        if invalid_joints:
            error_msg = f"""
                错误: 以下BVH关键点无效: {invalid_joints}

                有效的BVH关节名称包括:
                {self.bvh_joint_names}

                请检查您的 bvh_joint_correspondence 配置，确保所有BVH关节名称都正确。
                """
            raise ValueError(error_msg)
        
        # 验证所有机器人关节名称是否有效
        invalid_robot_joints = [joint for joint in self.robot_joint_pick if joint not in self.joint_names]
        if invalid_robot_joints:
            error_msg = f"""
                错误: 以下机器人关节名称无效: {invalid_robot_joints}

                有效的机器人关节名称包括:
                {self.joint_names}

                请检查您的 bvh_joint_correspondence 配置，确保所有机器人关节名称都正确。
                """
            raise ValueError(error_msg)
        
        self.robot_joint_names_augment = self.joint_names
        self.robot_joint_pick_idx = [self.robot_joint_names_augment.index(j) for j in self.robot_joint_pick]
        self.bvh_joint_pick_idx = [self.bvh_joint_names.index(j) for j in self.bvh_joint_pick]

    def calculate_pose_angles(self):
        """计算各个部位的姿态角度（参考SMPL优化器）"""
        # 获取关节轴 (形状为 [1, num_dofs, 3])
        axes = self.Humanoid_fk.joints_axis
        # 获取关节角度 (形状 [1, num_dofs])
        angles = self.dof_pos
        # 计算所有非根关节的轴角 (利用广播机制)
        # angles [1, num_dofs] * axes [1, num_dofs, 3] -> joint_aa [1, num_dofs, 3]
        joint_aa = angles.unsqueeze(-1) * axes
        
        # 调整根旋转的形状以进行拼接 (形状 [1, 3] -> [1, 1, 3])
        root_rot = torch.zeros(1, 1, 3, device=self.device)
        
        # 拼接根旋转和关节轴角
        self.pose_aa_gq = torch.cat([root_rot, joint_aa], dim=1).to(self.device)
        
        self.root_trans_gq = torch.zeros((1, 1, 3), device=self.device)

    def initialize_bvh_variables(self):
        """初始化BVH相关变量"""
        # 创建BVH骨架解析器
        self.bvh_skeleton = BVHSkeleton(self.bvh_parser)
        
        # 将骨架数据移动到指定设备
        self.bvh_skeleton.to_device(self.device)
        
        # 获取原始偏移量
        self.original_offsets = {}
        self.optimizable_offsets = {}
        
        # 使用BVH骨架解析器获取关节信息
        root_joint_name = self.bvh_skeleton.get_root_joint()
        for joint_name in self.bvh_skeleton.get_joint_names():
            original_offset = self.bvh_skeleton.get_joint_offset(joint_name)
            self.original_offsets[joint_name] = original_offset
            
            # 根据是否优化根节点平移来决定根节点偏移量是否可优化
            if joint_name == root_joint_name and not self.params.optimize_root_translation:
                # 根节点不优化时，使用固定的原始偏移量
                optimizable_offset = torch.tensor(original_offset.clone(), device=self.device, requires_grad=False)
                print(f"根节点 '{root_joint_name}' 偏移量固定不变: {original_offset}")
            else:
                # 创建可优化的偏移量
                optimizable_offset = torch.nn.Parameter(original_offset.clone(), requires_grad=True)
            
            self.optimizable_offsets[joint_name] = optimizable_offset
        
        # 获取根节点偏移量（类似SMPL的root_trans_offset）
        if root_joint_name:
            self.root_trans_offset = self.bvh_skeleton.get_root_offset()
        else:
            # 如果没有找到根节点，使用零向量
            self.root_trans_offset = torch.zeros(3, device=self.device, dtype=torch.float32)

    def update_root_offset_optimization_state(self):
        """更新根节点偏移量的优化状态"""
        root_joint_name = self.bvh_skeleton.get_root_joint()
        if root_joint_name in self.optimizable_offsets:
            current_offset = self.optimizable_offsets[root_joint_name]
            original_offset = self.original_offsets[root_joint_name]
            
            if self.params.optimize_root_translation:
                # 启用根节点偏移量优化
                if not current_offset.requires_grad:
                    # 重新创建为可优化的参数
                    self.optimizable_offsets[root_joint_name] = torch.nn.Parameter(
                        original_offset.clone(), requires_grad=True
                    )
                    print(f"根节点 '{root_joint_name}' 偏移量设置为可优化")
            else:
                # 禁用根节点偏移量优化
                if current_offset.requires_grad:
                    # 重新创建为固定的张量
                    self.optimizable_offsets[root_joint_name] = torch.tensor(
                        original_offset.clone(), device=self.device, requires_grad=False
                    )
                    print(f"根节点 '{root_joint_name}' 偏移量设置为固定不变")

    def get_robot_positions(self, selected_only=False):
        """获取机器人关节位置（参考SMPL优化器）
        
        Args:
            selected_only: 如果为True，只返回选中的关节位置；如果为False，返回所有关节位置
        """
        fk_return = self.Humanoid_fk.fk_batch(self.pose_aa_gq[None, ], torch.zeros((1, 1, 3), device=self.device), return_full=False)
        
        if selected_only:
            # 只返回选中的关节位置
            robot_positions = fk_return.global_translation[:,:,self.robot_joint_pick_idx]
            selected_positions = robot_positions.squeeze(0)  # [num_picked_joints, 3]
            return selected_positions
        else:
            # 返回所有关节位置
            all_positions = fk_return.global_translation.squeeze(0)  # [num_all_joints, 3]
            return all_positions

    def get_bvh_joints(self, optimized_offsets=None):
        """获取BVH关节位置（支持梯度传播）"""
        if optimized_offsets is None:
            optimized_offsets = self.optimizable_offsets
        
        # 获取关节名称和父子关系
        joint_names = self.bvh_skeleton.get_joint_names()
        parent_dict = {}
        for joint_name in joint_names:
            parent_dict[joint_name] = self.bvh_skeleton.get_parent(joint_name)
        
        # 准备偏移量字典
        offsets_dict = {}
        for joint_name in joint_names:
            if joint_name in optimized_offsets:
                offsets_dict[joint_name] = optimized_offsets[joint_name]
            else:
                # 对于没有优化偏移量的关节，使用原始偏移量
                original_offset = self.bvh_skeleton.get_joint_offset(joint_name)
                offsets_dict[joint_name] = torch.tensor(original_offset, device=self.device, dtype=torch.float32)
        
        # 使用正确的层次化前向运动学计算关节位置（支持梯度传播）
        bvh_positions = []
        
        # 为每个关节计算世界坐标位置
        for joint_name in joint_names:
            # 获取当前关节的偏移量（保持梯度连接）
            if joint_name in offsets_dict:
                offset = offsets_dict[joint_name]  # 移除detach()，保持梯度连接
            else:
                offset = torch.zeros(3, device=self.device, dtype=torch.float32)
            
            # 计算当前关节的世界坐标位置
            world_pos = torch.zeros(3, device=self.device, dtype=torch.float32)
            
            # 从根节点开始，沿着到当前关节的路径累加偏移量
            # 首先找到根节点
            root_joints = [name for name in joint_names if parent_dict[name] is None]
            if not root_joints:
                # 如果没有找到根节点，使用第一个关节作为根节点
                root_joints = [joint_names[0]]
            
            # 从根节点开始，沿着父子关系路径计算位置
            for root_joint in root_joints:
                # 检查当前关节是否在从根节点开始的子树中
                if self._is_descendant(joint_name, root_joint, parent_dict):
                    world_pos = self._calculate_joint_position_from_root_torch(
                        joint_name, root_joint, parent_dict, offsets_dict
                    )
                    break
            
            bvh_positions.append(world_pos)
        
        bvh_positions = torch.stack(bvh_positions)
        
        # 坐标系转换：BVH坐标系 -> 机器人坐标系
        # BVH: z向前，y向上，x向左 -> 机器人: z向上，y向前，x向右
        # 转换矩阵：[x_bvh, y_bvh, z_bvh] -> [x_robot, y_robot, z_robot]
        # x_robot = z_bvh (BVH的z轴正方向对应机器人的x轴正方向)
        # y_robot = x_bvh (BVH的x轴正方向对应机器人的y轴正方向)  
        # z_robot = y_bvh (BVH的y轴正方向对应机器人的z轴正方向)
        bvh_positions_converted = torch.zeros_like(bvh_positions)
        bvh_positions_converted[:, 0] = bvh_positions[:, 2]   # x_robot = z_bvh
        bvh_positions_converted[:, 1] = bvh_positions[:, 0]   # y_robot = x_bvh
        bvh_positions_converted[:, 2] = bvh_positions[:, 1]   # z_robot = y_bvh
        
        # 应用缩放因子
        bvh_positions_converted *= self.scale
        
        # 应用根节点变换（如果需要）
        if hasattr(self, 'bvh_root_trans') and self.bvh_root_trans is not None:
            root_trans = torch.zeros(3, device=self.device)
            root_trans[0] = self.bvh_root_trans[0]  # x方向
            root_trans[2] = self.bvh_root_trans[1]  # z方向
            
            # 应用自适应根节点高度调整
            if self.params.adaptive_root_height and hasattr(self, 'root_height_adjustment'):
                root_trans[1] = self.root_height_adjustment[0]  # y方向（高度）
            
            bvh_positions_converted += root_trans
        
        return bvh_positions_converted
    
    def _is_descendant(self, joint_name, ancestor_name, parent_dict):
        """检查joint_name是否是ancestor_name的后代"""
        current = joint_name
        while current is not None:
            if current == ancestor_name:
                return True
            current = parent_dict.get(current)
        return False
    
    def _calculate_joint_position_from_root_torch(self, joint_name, root_joint, parent_dict, offsets_dict):
        """从根节点开始计算指定关节的位置"""
        # 从根节点到目标关节的路径
        path = []
        current = joint_name
        while current is not None and current != root_joint:
            path.append(current)
            current = parent_dict.get(current)
        path.append(root_joint)
        path.reverse()  # 从根节点到目标关节
        
        # 沿着路径累加偏移量
        world_pos = torch.zeros(3, device=self.device, dtype=torch.float32)
        for joint in path:
            if joint in offsets_dict:
                offset = offsets_dict[joint]
                world_pos += offset
        
        return world_pos

    def get_selected_bvh_joints(self, optimized_offsets=None):
        """获取选择后的BVH关节位置（用于与机器人关节比较）"""
        full_bvh_positions = self.get_bvh_joints(optimized_offsets)
        
        # 选择指定的关节位置
        # 确保索引在有效范围内
        valid_indices = [idx for idx in self.bvh_joint_pick_idx if idx < len(full_bvh_positions)]
        if len(valid_indices) != len(self.bvh_joint_pick_idx):
            print(f"警告: 部分关节索引超出范围，跳过无效索引")
            print(f"原始索引: {self.bvh_joint_pick_idx}")
            print(f"有效索引: {valid_indices}")
            print(f"无效索引: {[idx for idx in self.bvh_joint_pick_idx if idx >= len(full_bvh_positions)]}")
        
        selected_positions = full_bvh_positions[valid_indices, :]
        
        return selected_positions

    def compute_symmetry_loss(self, bvh_positions, params):
        """计算对称性损失 - 只针对选中的关节"""
        symmetry_loss = torch.tensor(0.0, device=self.device, dtype=torch.float32)
        
        # 获取选中的关节名称
        selected_joint_names = self.get_selected_joint_names()
        
        # 只处理选中的关节中的对称对
        symmetric_pairs = self.bvh_skeleton.get_symmetric_joints()
        
        # 筛选出在选中关节中的对称对
        selected_symmetric_pairs = []
        for left_joint, right_joint in symmetric_pairs:
            if left_joint in selected_joint_names and right_joint in selected_joint_names:
                selected_symmetric_pairs.append((left_joint, right_joint))
        
        # 计算对称性损失
        total_error = torch.tensor(0.0, device=self.device, dtype=torch.float32)
        pair_count = 0
        for left_joint, right_joint in selected_symmetric_pairs:
            try:
                left_idx = selected_joint_names.index(left_joint)
                right_idx = selected_joint_names.index(right_joint)
                if left_idx < bvh_positions.shape[0] and right_idx < bvh_positions.shape[0]:
                    left_pos = bvh_positions[left_idx]
                    right_pos = bvh_positions[right_idx]
                    x_error = torch.abs(left_pos[0] - right_pos[0])
                    y_error = torch.abs(left_pos[1] + right_pos[1])
                    z_error = torch.abs(left_pos[2] - right_pos[2])
                    total_error += x_error + y_error + z_error
                    pair_count += 1
            except (ValueError, IndexError):
                continue
        if pair_count > 0:
            symmetry_loss = total_error / pair_count
        return symmetry_loss

    def compute_root_height_loss(self, bvh_positions, robot_positions):
        """计算根节点高度调整损失 - 保持脚部接触地面"""
        # 获取脚部关节的索引（假设脚部关节在关节列表的后几个位置）
        # 这里需要根据具体的机器人模型来调整脚部关节的识别
        foot_joint_indices = []
        
        # 尝试识别脚部关节（通常包含"foot", "ankle", "toe"等关键词）
        for i, joint_name in enumerate(self.get_selected_joint_names()):
            if any(keyword in joint_name.lower() for keyword in ['foot', 'ankle', 'toe']):
                if i < len(bvh_positions):
                    foot_joint_indices.append(i)
        
        if not foot_joint_indices:
            # 如果没有找到脚部关节，使用最后几个关节作为脚部
            foot_joint_indices = [len(bvh_positions) - 1]  # 使用最后一个关节
        
        # 计算脚部关节的平均高度
        bvh_foot_height = torch.mean(bvh_positions[foot_joint_indices, 1])  # y坐标
        robot_foot_height = torch.mean(robot_positions[foot_joint_indices, 1])  # y坐标
        
        # 计算高度差异损失
        height_error = torch.abs(bvh_foot_height - robot_foot_height)
        
        return height_error

    def compute_losses(self, robot_positions, bvh_positions, optimized_offsets, params):
        """计算所有损失"""
        loss_dict = {}
        
        # 获取选择后的BVH关节位置（用于与机器人关节比较）
        selected_bvh_positions = self.get_selected_bvh_joints(optimized_offsets)
        
        # 确保维度匹配
        # robot_positions: [1, 12, 3] -> [12, 3]
        # selected_bvh_positions: [12, 3]
        if robot_positions.ndim == 3:
            robot_positions = robot_positions.squeeze(0)  # 移除batch维度
        
        # 1. 关节位置损失 - 主要优化目标（让BVH关节位置接近机器人关节位置）
        joint_loss = torch.mean(torch.norm(robot_positions - selected_bvh_positions, dim=-1))
        loss_dict['joint_loss'] = joint_loss
        
        # 2. 对称性损失 - 维持人体对称性
        symmetry_loss = self.compute_symmetry_loss(selected_bvh_positions, params)
        loss_dict['symmetry_loss'] = symmetry_loss
        
        # 3. 根节点高度调整损失 - 保持脚部接触地面
        if self.params.adaptive_root_height:
            root_height_loss = self.compute_root_height_loss(selected_bvh_positions, robot_positions)
            loss_dict['root_height_loss'] = root_height_loss
        else:
            loss_dict['root_height_loss'] = torch.tensor(0.0, device=self.device, dtype=torch.float32)
        
        # 4. 末端位置损失（可选）
        endpoint_loss = torch.tensor(0.0, device=self.device, dtype=torch.float32)
        # 这里可以添加末端位置的特定约束
        loss_dict['endpoint_loss'] = endpoint_loss
        
        return loss_dict

    def compute_total_loss(self, loss_dict):
        """计算总损失"""
        joint_loss = loss_dict['joint_loss']
        symmetry_loss = loss_dict['symmetry_loss']
        root_height_loss = loss_dict['root_height_loss']
        endpoint_loss = loss_dict['endpoint_loss']
        
        weighted_joint_loss = self.params.joint_loss_weight * joint_loss
        weighted_symmetry_loss = self.params.symmetry_loss_weight * symmetry_loss
        weighted_root_height_loss = self.params.root_height_weight * root_height_loss
        weighted_endpoint_loss = self.params.endpoint_loss_weight * endpoint_loss
        
        total_loss = weighted_joint_loss + weighted_symmetry_loss + weighted_root_height_loss + weighted_endpoint_loss
        
        return total_loss

    def save_optimization_result(self, optimized_offsets, robot_positions, bvh_positions, task_name, loss=None, save_path=None):
        """保存优化结果到JSON文件
        
        Args:
            optimized_offsets: 优化后的关节偏移量字典
            robot_positions: 机器人关节位置
            bvh_positions: BVH关节位置
            task_name: 任务名称
            loss: 最终损失值
            save_path: 保存路径
        """
        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = f"data/calc_bvh/{task_name}/bvh_retarget_params_{timestamp}.json"
        
        # 确保目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # 转换优化后的偏移量为可序列化格式
        optimized_offsets_dict = {}
        for joint_name, offset_tensor in optimized_offsets.items():
            if isinstance(offset_tensor, torch.Tensor):
                optimized_offsets_dict[joint_name] = offset_tensor.detach().cpu().numpy().tolist()
            else:
                optimized_offsets_dict[joint_name] = offset_tensor
        
        # 转换位置数据
        def tensor_to_list(tensor_data):
            if isinstance(tensor_data, torch.Tensor):
                return tensor_data.detach().cpu().numpy().tolist()
            elif isinstance(tensor_data, np.ndarray):
                return tensor_data.tolist()
            else:
                return tensor_data
        
        # 构建完整的保存数据结构
        save_data = {
            # 基本信息
            "metadata": {
                "task_name": task_name,
                "timestamp": datetime.now().isoformat(),
                "bvh_file": getattr(self.bvh_parser, 'filename', 'unknown'),
                "robot_model": self.motion_lib_cfg.mjcf_file,
                "optimization_version": "BVH_Direct_v1.0"
            },
            
            # 骨骼映射关系 - 动作重定向的核心
            "joint_correspondence": {
                "robot_to_bvh": dict(zip(self.robot_joint_pick, self.bvh_joint_pick)),
                "bvh_to_robot": dict(zip(self.bvh_joint_pick, self.robot_joint_pick)),
                "robot_joint_indices": self.robot_joint_pick_idx,
                "bvh_joint_indices": self.bvh_joint_pick_idx
            },
            
            # 优化后的骨骼参数
            "skeleton_params": {
                "optimized_offsets": optimized_offsets_dict,
                "original_offsets": {name: tensor_to_list(offset) for name, offset in self.original_offsets.items()},
                "scale_factor": float(self.scale.detach().cpu()) if isinstance(self.scale, torch.Tensor) else float(self.scale),
                "root_height_adjustment": float(self.root_height_adjustment.detach().cpu()) if hasattr(self, 'root_height_adjustment') else 0.0,
                "bvh_root_trans": tensor_to_list(self.bvh_root_trans) if hasattr(self, 'bvh_root_trans') else [0.0, 0.0],
                "bvh_root_rot": tensor_to_list(self.bvh_root_rot) if hasattr(self, 'bvh_root_rot') else [0.0, 0.0, 0.0]
            },
            
            # T-pose配置
            "t_pose_config": {
                "robot_dof_pos": tensor_to_list(self.dof_pos),
                "robot_t_pose_positions": tensor_to_list(robot_positions),
                "bvh_t_pose_positions": tensor_to_list(bvh_positions)
            },
            
            # 坐标系转换信息
            "coordinate_transform": {
                "bvh_to_robot_description": "BVH: z前,y上,x左 -> Robot: z上,y前,x右",
                "euler_rotation_order": getattr(self.bvh_parser, 'euler_order', 'zyx').upper(),  # 动态获取BVH欧拉角顺序
                "transform_matrix": self._build_4x4_transform_matrix()
            },
            
            # 优化结果统计
            "optimization_stats": {
                "final_loss": float(loss) if loss is not None else None,
                "optimization_params": {
                    "joint_loss_weight": self.params.joint_loss_weight,
                    "symmetry_loss_weight": self.params.symmetry_loss_weight,
                    "endpoint_loss_weight": self.params.endpoint_loss_weight,
                    "optimize_scale": self.params.optimize_scale,
                    "adaptive_root_height": self.params.adaptive_root_height
                }
            },
            
            # BVH文件信息
            "bvh_info": {
                "joint_names": self.bvh_joint_names,
                "selected_joints": self.bvh_joint_pick,
                "frame_count": getattr(self.bvh_parser, 'frame_count', 0),
                "frame_rate": getattr(self.bvh_parser, 'frame_time', 0.0)
            },
            
            # 机器人信息  
            "robot_info": {
                "joint_names": self.joint_names,
                "selected_joints": self.robot_joint_pick,
                "dof_count": len(self.joint_names) if self.joint_names else 0
            }
        }
        
        # 保存到JSON文件
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False)
        
        print(f"骨骼拟合结果已保存到: {save_path}")
        print(f"包含内容:")
        print(f"  - 关节映射关系: {len(self.robot_joint_pick)} 个关节对")
        print(f"  - 优化偏移量: {len(optimized_offsets_dict)} 个关节")
        print(f"  - 缩放因子: {save_data['skeleton_params']['scale_factor']:.4f}")
        print(f"  - T-pose配置和坐标转换信息")
        
        return save_path

    def _build_4x4_transform_matrix(self):
        """构建完整的4x4齐次变换矩阵，包含旋转、平移和缩放"""
        # 获取当前的变换参数
        scale = float(self.scale.detach().cpu()) if isinstance(self.scale, torch.Tensor) else float(self.scale)
        
        # 获取平移参数
        t_x = float(self.bvh_root_trans[0].detach().cpu()) if hasattr(self, 'bvh_root_trans') and self.bvh_root_trans is not None else 0.0
        t_z = float(self.bvh_root_trans[1].detach().cpu()) if hasattr(self, 'bvh_root_trans') and self.bvh_root_trans is not None else 0.0
        t_y = float(self.root_height_adjustment[0].detach().cpu()) if hasattr(self, 'root_height_adjustment') and self.root_height_adjustment is not None else 0.0
        
        # 构建4x4齐次变换矩阵
        # 旋转矩阵部分 * 缩放 + 平移部分
        # 基础旋转矩阵：BVH坐标系 -> 机器人坐标系
        # x_robot = z_bvh, y_robot = x_bvh, z_robot = y_bvh
        transform_matrix_4x4 = [
            [0.0*scale, 0.0*scale, 1.0*scale, t_x],  # x_robot = z_bvh*scale + t_x
            [1.0*scale, 0.0*scale, 0.0*scale, t_y],  # y_robot = x_bvh*scale + t_y  
            [0.0*scale, 1.0*scale, 0.0*scale, t_z],  # z_robot = y_bvh*scale + t_z
            [0.0,       0.0,       0.0,       1.0]   # 齐次坐标
        ]
        
        print(f"构建4x4变换矩阵:")
        print(f"  - 缩放因子: {scale:.6f}")
        print(f"  - 平移向量: X={t_x:.6f}, Y={t_y:.6f}, Z={t_z:.6f}")
        print(f"  - 变换矩阵: {transform_matrix_4x4}")
        
        return transform_matrix_4x4

    def save_config(self, config_path=None):
        """保存配置"""
        if config_path is None:
            task_prefix = self.motion_lib_cfg.__class__.__name__.replace('RetargetMotionLibCfg', '')
            config_dir = os.path.join("configs", "bvh")
            os.makedirs(config_dir, exist_ok=True)
            config_path = os.path.join(config_dir, f"{task_prefix}_bvh_config.json")
        
        config_data = {
            'bvh_joint_correspondence': self.motion_lib_cfg.bvh_joint_correspondence,
            'optimization_params': {
                'joint_loss_weight': self.params.joint_loss_weight,
                'symmetry_loss_weight': self.params.symmetry_loss_weight,
                'endpoint_loss_weight': self.params.endpoint_loss_weight,
                'learning_rate': self.params.learning_rate,
                'optimize_scale': self.params.optimize_scale,
            }
        }
        
        with open(config_path, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        print(f"配置已保存到: {config_path}")
        return config_path

    def get_selected_joint_names(self):
        """获取选中的关节名称列表"""
        # 动态返回从配置文件中获取的BVH关节名称
        if hasattr(self, 'bvh_joint_pick') and self.bvh_joint_pick:
            return self.bvh_joint_pick
        else:
            # 如果没有配置，抛出错误
            error_msg = """
                错误: 未找到BVH关节对应关系配置。

                请按照以下步骤操作:
                1. 在您的配置文件中添加 bvh_joint_correspondence 字典
                2. 字典的键应该是机器人的关节名称
                3. 字典的值应该是对应的BVH关节名称

                示例配置:
                bvh_joint_correspondence = {
                    'base_link': 'Hips',
                    'leg_l4_link': 'LeftKnee',
                    'leg_l6_link': 'LeftAnkle',
                    'foot_l1_link': 'LeftFoot',
                    'leg_r4_link': 'RightKnee',
                    'leg_r6_link': 'RightAnkle',
                    'foot_r1_link': 'RightFoot',
                    'left_arm_link02': 'LeftShoulder',
                    'left_arm_link04': 'LeftElbow',
                    'right_arm_link02': 'RightShoulder',
                    'right_arm_link04': 'RightElbow'
                }

                请确保:
                1. 所有机器人关节名称都在 self.joint_names 中存在
                2. 所有BVH关节名称都在 bvh_parser.get_joint_names() 中存在
                3. 关节对应关系合理且完整
                """
            raise ValueError(error_msg)


class BVHVisualizer:
    """BVH可视化工具类"""
    
    def __init__(self, optimizer):
        """初始化可视化工具
        
        Args:
            optimizer: BVHBoneOptimizer实例
        """
        self.optimizer = optimizer
        self.device = optimizer.device
        
        # MuJoCo查看器
        self.mujoco_viewer = None
        
        # Open3D可视化相关变量
        self.mesh_queue = queue.Queue(maxsize=1)  # 用于线程间通信的队列
        self.vis_thread = None
        self.stop_vis_thread = threading.Event()
        self.o3d_vis_ready = threading.Event()  # 标记可视化窗口是否准备好
        self.vis = None # 初始化 vis 属性
        
        # 初始化MuJoCo查看器
        self.init_mujoco_viewer()  # 显示正常但不是T-pose
    
    def init_mujoco_viewer(self):
        """初始化MuJoCo查看器"""
        if self.mujoco_viewer is not None:
            return
            
        try:
            import mujoco
            # 检查是否有可用的MuJoCo模型
            if hasattr(self.optimizer, 'model') and hasattr(self.optimizer, 'data'):
                # 创建 viewer
                self.mujoco_viewer = mujoco.viewer.launch_passive(
                    self.optimizer.model, 
                    self.optimizer.data
                )
                # 设置相机参数
                self.mujoco_viewer.cam.distance = 3.0
                self.mujoco_viewer.cam.azimuth = 45
                self.mujoco_viewer.cam.elevation = -20
                
                # 设置渲染选项 - 启用透明度
                self.mujoco_viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_TRANSPARENT] = 1
                
                print("已创建持久性 MuJoCo 查看器窗口")
            else:
                print("警告：没有可用的MuJoCo模型，跳过MuJoCo查看器初始化")
        except ImportError:
            print("警告：mujoco模块未安装，跳过MuJoCo查看器初始化")
        except Exception as e:
            print(f"创建 MuJoCo 查看器时出错: {e}")
            self.mujoco_viewer = None
    
    def start_visualization_thread(self):
        """启动Open3D可视化线程"""
        try:
            # 创建并启动线程
            self.vis_thread = threading.Thread(target=self.run_visualization, daemon=True)
            self.vis_thread.start()
            
            # 等待可视化窗口准备好
            if not self.o3d_vis_ready.wait(timeout=5.0):
                print("警告：Open3D可视化窗口启动超时")
            else:
                print("Open3D可视化线程已成功启动")
        except Exception as e:
            print(f"启动可视化线程时出错: {e}")
    
    def run_visualization(self):
        """Open3D可视化线程的主函数"""
        try:
            # 创建Open3D可视化窗口
            self.vis = o3d.visualization.Visualizer()
            self.vis.create_window(window_name="BVH骨骼优化可视化", width=1024, height=768)
            
            # 设置渲染选项
            render_option = self.vis.get_render_option()
            if render_option is not None:
                render_option.background_color = np.array([0.2, 0.2, 0.2])
                render_option.point_size = 5.0
                render_option.line_width = 2.0
                render_option.mesh_show_back_face = True
            else:
                print("警告：无法获取渲染选项，使用默认设置")
            
            # 添加坐标系
            coordinate_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.2)
            self.vis.add_geometry(coordinate_frame)
            
            # 初始化机器人骨架可视化
            self.robot_skeleton = o3d.geometry.LineSet()
            self.robot_points = []
            self.robot_lines = []
            
            # 初始化BVH骨架可视化
            self.bvh_skeleton = o3d.geometry.LineSet()
            self.bvh_points = []
            self.bvh_lines = []
            
            # 添加到可视化器
            self.vis.add_geometry(self.robot_skeleton)
            self.vis.add_geometry(self.bvh_skeleton)
            
            # 设置初始视角
            view_control = self.vis.get_view_control()
            
            # 标记窗口已准备好
            self.o3d_vis_ready.set()
            
            # 运行可视化循环
            last_update_time = time.time()
            first_update = True
            
            while not self.stop_vis_thread.is_set():
                update_occurred = False # 标记本次循环是否有更新
                try:
                    if not self.mesh_queue.empty():
                        # 获取最新的可视化数据
                        update_data = self.mesh_queue.get(block=False)
                        
                        # 验证数据
                        if isinstance(update_data, dict) and 'robot_positions' in update_data and 'bvh_positions' in update_data:
                            robot_positions = update_data['robot_positions']
                            bvh_positions = update_data['bvh_positions']
                            iteration = update_data.get('iteration', 0)
                            
                            # 更新机器人骨架
                            self.update_robot_skeleton(robot_positions)
                            
                            # 更新BVH骨架
                            self.update_bvh_skeleton(bvh_positions)
                            
                            # 更新窗口标题
                            self.vis.get_window_name = lambda: f"BVH骨骼优化可视化 - 迭代 {iteration}"
                            
                            update_occurred = True
                            
                            # 如果是第一次更新，调整视角以适应模型
                            if first_update and update_occurred:
                                try:
                                    # 计算模型边界
                                    all_points = []
                                    if len(self.robot_points) > 0:
                                        all_points.extend(self.robot_points)
                                    if len(self.bvh_points) > 0:
                                        all_points.extend(self.bvh_points)
                                    
                                    if len(all_points) > 0:
                                        all_points = np.array(all_points)
                                        min_bound = np.min(all_points, axis=0)
                                        max_bound = np.max(all_points, axis=0)
                                        center = (min_bound + max_bound) / 2
                                        size = np.linalg.norm(max_bound - min_bound)
                                        
                                        # 设置新的视角
                                        if view_control is not None:
                                            view_control.set_front([0, -1, 0]) # 调整观察方向
                                            view_control.set_lookat(center)    # 看向模型中心
                                            view_control.set_up([0, 0, 1])     # 保持z轴向上
                                            view_control.set_zoom(2.0)         # 调整缩放

                                    first_update = False
                                except Exception as e:
                                    print(f"调整初始视图时出错: {e}")
                            
                            # 重置上次更新时间
                            last_update_time = time.time()
                        
                except queue.Empty:
                    pass
                except Exception as e:
                    print(f"处理可视化更新时出错: {e}")
                    import traceback
                    traceback.print_exc()

                # 更新可视化窗口事件
                if not self.vis.poll_events():
                    print("Open3D 窗口已关闭。")
                    break # 如果窗口关闭，退出循环
                
                # 更新渲染器
                self.vis.update_renderer()
    
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.01)
            
            # 循环结束后关闭窗口
            print("正在关闭 Open3D 窗口...")
            if self.vis:
                self.vis.destroy_window()
                self.vis = None # 标记窗口已销毁
            
        except Exception as e:
            print(f"可视化线程出错: {e}")
            import traceback
            traceback.print_exc()
            
            # 确保窗口被关闭
            try:
                if self.vis:
                    self.vis.destroy_window()
                    self.vis = None
            except:
                pass
        finally:
            print("Open3D 可视化线程已退出。")
    
    def update_robot_skeleton(self, robot_positions):
        """更新机器人骨架可视化"""
        # 这里需要根据机器人的骨架结构来构建线条
        # 简化实现：只显示关节点
        points = []
        lines = []
        
        # 添加关节点
        if hasattr(robot_positions, 'cpu'):
            robot_positions = robot_positions.cpu()
        if hasattr(robot_positions, 'numpy'):
            robot_positions = robot_positions.numpy()
        
        for i, pos in enumerate(robot_positions):
            points.append(pos)
        
        # 构建线条 - 使用机器人骨架的实际连接关系
        if hasattr(self.optimizer, 'Humanoid_fk') and hasattr(self.optimizer.Humanoid_fk, 'joints_parent'):
            # 获取机器人骨架的父子关系
            joints_parent = self.optimizer.Humanoid_fk.joints_parent
            for i, parent_idx in enumerate(joints_parent):
                if parent_idx >= 0 and parent_idx < len(points) and i < len(points):
                    lines.append([parent_idx, i])
        else:
            # 如果没有骨架信息，使用简单的相邻连接
            for i in range(len(points) - 1):
                lines.append([i, i + 1])
        
        # 更新LineSet
        if len(points) > 0:
            self.robot_skeleton.points = o3d.utility.Vector3dVector(points)
            self.robot_skeleton.lines = o3d.utility.Vector2iVector(lines)
            self.robot_skeleton.paint_uniform_color([1, 0, 0])  # 红色表示机器人
            self.robot_points = points
            self.robot_lines = lines
    
    def update_bvh_skeleton(self, bvh_positions):
        """更新BVH骨架可视化"""
        # 类似机器人骨架的更新
        points = []
        lines = []
        
        # 添加关节点
        if hasattr(bvh_positions, 'cpu'):
            bvh_positions = bvh_positions.cpu()
        if hasattr(bvh_positions, 'numpy'):
            bvh_positions = bvh_positions.numpy()
        
        for i, pos in enumerate(bvh_positions):
            points.append(pos)
        
        # 构建线条 - 使用BVH骨架的实际连接关系
        if hasattr(self.optimizer, 'bvh_skeleton'):
            # 获取BVH骨架的父子关系
            bvh_joint_names = self.optimizer.bvh_skeleton.get_joint_names()
            bvh_parents = {}
            for i, joint_name in enumerate(bvh_joint_names):
                parent_name = self.optimizer.bvh_skeleton.get_parent(joint_name)
                if parent_name:
                    parent_idx = bvh_joint_names.index(parent_name)
                    bvh_parents[i] = parent_idx
        else:
            # 如果没有骨架信息，使用简单的相邻连接
            for i in range(len(points) - 1):
                lines.append([i, i + 1])
        
        # 更新LineSet
        if len(points) > 0:
            self.bvh_skeleton.points = o3d.utility.Vector3dVector(points)
            self.bvh_skeleton.lines = o3d.utility.Vector2iVector(lines)
            self.bvh_skeleton.paint_uniform_color([0, 1, 0])  # 绿色表示BVH
            self.bvh_points = points
            self.bvh_lines = lines
    
    def update_visualization_data(self, all_robot_positions, selected_robot_positions, bvh_positions, iteration=0):
        """更新可视化数据 - 参考SMPL版本实现"""
        try:
            # 准备更新数据
            update_data = {
                'all_robot_positions': all_robot_positions,
                'selected_robot_positions': selected_robot_positions,
                'bvh_positions': bvh_positions,
                'iteration': iteration
            }
            
            # 清空队列中的旧数据
            while not self.mesh_queue.empty():
                try:
                    self.mesh_queue.get_nowait()
                except queue.Empty:
                    break
            
            # 放入新的可视化数据
            self.mesh_queue.put(update_data)
            
            # 定期打印更新信息
            if iteration % 100 == 0:
                # 正确计算关节数量，考虑batch维度
                all_robot_count = all_robot_positions.shape[-2] if all_robot_positions.ndim >= 2 else len(all_robot_positions)
                bvh_count = bvh_positions.shape[-2] if bvh_positions.ndim >= 2 else len(bvh_positions)
                print(f"可视化更新 - 迭代 {iteration}: 机器人关节数 {all_robot_count}, BVH关节数 {bvh_count}")
                
        except Exception as e:
            print(f"更新可视化数据时出错: {e}")
            import traceback
            traceback.print_exc()
        
        # 更新MuJoCo查看器
        self.update_mujoco_viewer(all_robot_positions, selected_robot_positions, bvh_positions, iteration)

    def update_mujoco_viewer(self, all_robot_positions, selected_robot_positions, bvh_positions, iteration=0):
        """更新MuJoCo查看器内容（与SMPL版本保持一致的参数形状）"""
        if self.mujoco_viewer is None or not self.mujoco_viewer.is_running():
            return
        
        try:
            # 首先更新MuJoCo模型中的机器人关节角度
            if hasattr(self.optimizer, 'dof_pos') and self.optimizer.dof_pos is not None:
                # 将T-pose角度设置到MuJoCo模型中
                dof_pos_np = self.optimizer.dof_pos.detach().cpu().numpy()
                if dof_pos_np.size > 0:
                    # 设置MuJoCo模型中的关节角度
                    self.optimizer.data.qpos[7:7+dof_pos_np.shape[1]] = dof_pos_np[0]
                    # 更新MuJoCo模型
                    mujoco.mj_forward(self.optimizer.model, self.optimizer.data)
            
            # 处理batch维度，提取实际数据
            # all_robot_positions: [1, num_joints, 3] -> [num_joints, 3]
            if all_robot_positions.ndim == 3:
                all_robot_positions = all_robot_positions[0]  # 移除batch维度
            
            # selected_robot_positions: [1, 1, num_selected_joints, 3] -> [num_selected_joints, 3]
            if selected_robot_positions.ndim == 4:
                selected_robot_positions = selected_robot_positions[0, 0]  # 移除batch维度
            elif selected_robot_positions.ndim == 3:
                selected_robot_positions = selected_robot_positions[0]  # 移除batch维度
            
            # bvh_positions: [1, num_bvh_joints, 3] -> [num_bvh_joints, 3]
            if bvh_positions.ndim == 3:
                bvh_positions = bvh_positions[0]  # 移除batch维度
            
            # 确保数据类型正确
            if isinstance(all_robot_positions, torch.Tensor):
                all_robot_positions = all_robot_positions.detach().cpu().numpy()
            if isinstance(selected_robot_positions, torch.Tensor):
                selected_robot_positions = selected_robot_positions.detach().cpu().numpy()
            if isinstance(bvh_positions, torch.Tensor):
                bvh_positions = bvh_positions.detach().cpu().numpy()
            
            # 清空所有几何体
            self.mujoco_viewer.user_scn.ngeom = 0
            geom_idx = 0
            max_geoms = self.mujoco_viewer.user_scn.maxgeom
            
            # 获取BVH骨架信息
            bvh_np = bvh_positions
            # 使用正确的方法获取父子关系
            bvh_joint_names = self.optimizer.bvh_skeleton.get_joint_names()
            bvh_parents = {}
            for i, joint_name in enumerate(bvh_joint_names):
                parent_name = self.optimizer.bvh_skeleton.get_parent(joint_name)
                if parent_name:
                    parent_idx = bvh_joint_names.index(parent_name)
                    bvh_parents[i] = parent_idx
            
            # 1. 添加坐标系
            # 定义不同坐标系的大小和样式
            coord_systems = {
                "world": {
                    "pos": np.zeros(3),  # 世界坐标系原点
                    "size": 0.15,        # 最大的坐标系
                    "radius": 0.008,     # 较粗的轴
                    "alpha": 0.8,        # 最不透明
                    "label": "World",    # 标签文本
                    "label_offset": np.array([0.02, 0.02, 0.02])  # 标签位置偏移
                }
            }
            
            # 坐标轴基础颜色 (RGB)
            axis_colors = {
                "x": np.array([1.0, 0.0, 0.0]),  # 红色
                "y": np.array([0.0, 1.0, 0.0]),  # 绿色
                "z": np.array([0.0, 0.0, 1.0])   # 蓝色
            }
            
            # 为每个坐标系绘制三个轴
            for system_name, system_info in coord_systems.items():
                for axis_name, direction in zip(['x', 'y', 'z'], np.eye(3)):
                    if geom_idx >= max_geoms: 
                        break
                    
                    # 创建圆柱体表示坐标轴
                    origin = system_info["pos"]
                    end_point = origin + direction * system_info["size"]
                    mid_point = (origin + end_point) / 2
                    
                    # 确保mid_point是1D数组
                    if mid_point.ndim > 1:
                        mid_point = mid_point.flatten()
                    
                    # 计算旋转矩阵
                    if not np.allclose(direction, np.array([0, 0, 1])):
                        v = np.cross(np.array([0, 0, 1]), direction)
                        s = np.linalg.norm(v)
                        if s > 1e-6:  # 避免除以零
                            c = np.dot(np.array([0, 0, 1]), direction)
                            v_x = np.array([
                                [0, -v[2], v[1]],
                                [v[2], 0, -v[0]],
                                [-v[1], v[0], 0]
                            ])
                            rot_mat = np.eye(3) + v_x + v_x.dot(v_x) * (1 - c) / (s * s)
                        else:
                            rot_mat = np.eye(3)
                    else:
                        rot_mat = np.eye(3)
                    
                    # 设置颜色和透明度
                    color = axis_colors[axis_name].copy()
                    rgba = np.append(color, system_info["alpha"])
                    
                    # 设置几何体
                    mujoco.mjv_initGeom(
                        self.mujoco_viewer.user_scn.geoms[geom_idx],
                        mujoco.mjtGeom.mjGEOM_CYLINDER,
                        np.array([system_info["radius"], system_info["size"]/2, 0], dtype=np.float64),
                        mid_point.astype(np.float64),
                        rot_mat.flatten().astype(np.float64),
                        rgba.astype(np.float32)
                    )
                    geom_idx += 1
            
            # 2. 绘制BVH骨骼连接（绿色）
            for child, parent in bvh_parents.items():
                if geom_idx >= max_geoms:
                    break
                
                if parent >= 0 and child < bvh_np.shape[0] and parent < bvh_np.shape[0]:
                    start = bvh_np[parent]
                    end = bvh_np[child]
                    
                    # 确保start和end是1D数组
                    if start.ndim > 1:
                        start = start.flatten()
                    if end.ndim > 1:
                        end = end.flatten()
                    
                    # 计算胶囊体的中心点和长度
                    mid_point = (start + end) / 2
                    direction = end - start
                    length = np.linalg.norm(direction)
                    
                    if length < 1e-6:  # 避免零长度骨骼
                        continue
                    
                    # 计算胶囊体的旋转矩阵
                    direction = direction / length
                    z_axis = np.array([0, 0, 1])
                    if np.allclose(direction, z_axis) or np.allclose(direction, -z_axis):
                        rot_mat = np.eye(3)
                    else:
                        v = np.cross(z_axis, direction)
                        s = np.linalg.norm(v)
                        c = np.dot(z_axis, direction)
                        v_x = np.array([
                            [0, -v[2], v[1]],
                            [v[2], 0, -v[0]],
                            [-v[1], v[0], 0]
                        ])
                        rot_mat = np.eye(3) + v_x + v_x.dot(v_x) * (1 - c) / (s * s)
                    
                    # 设置胶囊体参数
                    radius = max(length * 0.05, 0.005)  # BVH骨骼保持圆柱体显示
                    
                    # 初始化几何体 - 使用绿色表示BVH
                    mujoco.mjv_initGeom(
                        self.mujoco_viewer.user_scn.geoms[geom_idx],
                        mujoco.mjtGeom.mjGEOM_CAPSULE,
                        np.array([radius, length/2, 0], dtype=np.float64),
                        mid_point.astype(np.float64),
                        rot_mat.flatten().astype(np.float64),
                        np.array([0.0, 1.0, 0.0, 0.3], dtype=np.float32)  # 绿色
                    )
                    geom_idx += 1
            
            # 3. 添加机器人关节点和连接线
            robot_joint_pick_idx = self.optimizer.robot_joint_pick_idx
            bvh_joint_pick_idx = self.optimizer.bvh_joint_pick_idx
            displayed_labels = set()
            
            # 首先显示所有机器人关节点（使用灰色）
            # all_robot_positions的形状是 [num_joints, 3]，需要正确遍历
            for i in range(all_robot_positions.shape[0]):
                if geom_idx >= max_geoms:
                    break
                
                # 获取单个关节的3D位置
                robot_pos = all_robot_positions[i]
                
                # 检查是否是用于优化的关节点
                is_optimized = i in robot_joint_pick_idx
                
                # 确保robot_pos是1D数组
                if hasattr(robot_pos, 'ndim') and robot_pos.ndim > 1:
                    robot_pos = robot_pos.flatten()
                
                # 确保robot_pos是3D位置
                if hasattr(robot_pos, 'size') and robot_pos.size != 3:
                    continue
                
                # 初始化几何体 - 优化的关节点使用红色，其他使用灰色
                mujoco.mjv_initGeom(
                    self.mujoco_viewer.user_scn.geoms[geom_idx],
                    mujoco.mjtGeom.mjGEOM_SPHERE,
                    np.array([0.012, 0, 0], dtype=np.float64),  # 较小的球体
                    robot_pos.astype(np.float64),
                    np.eye(3).flatten().astype(np.float64),  # 单位矩阵
                    np.array([1.0, 0.0, 0.0, 0.5] if is_optimized else [0.5, 0.5, 0.5, 0.3], dtype=np.float32)  # 优化的红色，其他灰色
                )
                geom_idx += 1
                
                # 添加机器人关节点标签（如果还没有显示过）
                if i < len(self.optimizer.joint_names):
                    joint_name = self.optimizer.joint_names[i]
                    if joint_name not in displayed_labels and geom_idx < max_geoms:
                        label_pos = robot_pos + np.array([0.01, 0.01, 0.01])  # 减小标签位置偏移
                        mujoco.mjv_initGeom(
                            self.mujoco_viewer.user_scn.geoms[geom_idx],
                            mujoco.mjtGeom.mjGEOM_LABEL,
                            np.array([0.015, 0, 0], dtype=np.float64),  # 减小标签大小
                            label_pos.astype(np.float64),
                            np.eye(3).flatten().astype(np.float64),  # 单位矩阵
                            np.array([1.0, 1.0, 1.0, 1.0], dtype=np.float32)  # 白色
                        )
                        self.mujoco_viewer.user_scn.geoms[geom_idx].label = joint_name
                        displayed_labels.add(joint_name)
                        geom_idx += 1
            
            # 4. 添加选中关节与BVH关节的连线
            # selected_robot_positions的形状是 [num_selected_joints, 3]，需要正确遍历
            for i in range(selected_robot_positions.shape[0]):
                # 获取单个关节的3D位置
                robot_pos = selected_robot_positions[i]
                
                # 确保robot_pos是1D数组
                if robot_pos.ndim > 1:
                    robot_pos = robot_pos.flatten()
                
                if robot_pos.size != 3:
                    continue
                
                if i >= len(bvh_joint_pick_idx) or bvh_joint_pick_idx[i] >= bvh_np.shape[0]:
                    continue
                
                bvh_pos = bvh_np[bvh_joint_pick_idx[i]]
                
                # 确保bvh_pos是1D数组
                if bvh_pos.ndim > 1:
                    bvh_pos = bvh_pos.flatten()
                
                # 绘制连线（黄色）
                if geom_idx < max_geoms:
                    mid_point = (robot_pos + bvh_pos) / 2
                    direction = bvh_pos - robot_pos
                    length = np.linalg.norm(direction)
                    
                    if length > 1e-6:
                        direction = direction / length
                        z_axis = np.array([0, 0, 1])
                        
                        if np.allclose(direction, z_axis) or np.allclose(direction, -z_axis):
                            rot_mat = np.eye(3)
                        else:
                            v = np.cross(z_axis, direction)
                            s = np.linalg.norm(v)
                            c = np.dot(z_axis, direction)
                            v_x = np.array([
                                [0, -v[2], v[1]],
                                [v[2], 0, -v[0]],
                                [-v[1], v[0], 0]
                            ])
                            rot_mat = np.eye(3) + v_x + v_x.dot(v_x) * (1 - c) / (s * s)
                        
                        radius = 0.003  # 固定细线
                        
                        mujoco.mjv_initGeom(
                            self.mujoco_viewer.user_scn.geoms[geom_idx],
                            mujoco.mjtGeom.mjGEOM_CAPSULE,
                            np.array([radius, length/2, 0], dtype=np.float64),
                            mid_point.astype(np.float64),
                            rot_mat.flatten().astype(np.float64),
                            np.array([1.0, 1.0, 1.0, 0.6], dtype=np.float32)  # 白色连线
                        )
                        geom_idx += 1
                
                # 在BVH关节位置添加绿色球体
                if geom_idx < max_geoms:
                    mujoco.mjv_initGeom(
                        self.mujoco_viewer.user_scn.geoms[geom_idx],
                        mujoco.mjtGeom.mjGEOM_SPHERE,
                        np.array([0.015, 0, 0], dtype=np.float64),  # 稍大的球体
                        bvh_pos.astype(np.float64),
                        np.eye(3).flatten().astype(np.float64),
                        np.array([0.0, 1.0, 0.0, 0.6], dtype=np.float32)  # 绿色
                    )
                    geom_idx += 1
            
            # 5. 设置几何体数量并同步
            self.mujoco_viewer.user_scn.ngeom = geom_idx
            self.mujoco_viewer.sync()
                    
        except Exception as e:
            print(f"更新MuJoCo查看器时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def stop_visualization(self):
        """停止所有可视化"""
        print("正在请求停止可视化...")
        # 设置停止标志
        self.stop_vis_thread.set()

        # 等待Open3D可视化线程结束
        if self.vis_thread is not None and self.vis_thread.is_alive():
            print("等待 Open3D 线程结束...")
            self.vis_thread.join(timeout=5.0) # 增加超时时间

            if self.vis_thread.is_alive():
                print("警告：Open3D 可视化线程未能正常关闭")
            else:
                print("Open3D 可视化线程已正常关闭")
        else:
             print("Open3D 可视化线程未运行或已结束。")

        # 关闭MuJoCo查看器
        if self.mujoco_viewer is not None:
            try:
                if self.mujoco_viewer.is_running():
                     print("正在关闭 MuJoCo 查看器...")
                     self.mujoco_viewer.close()
                     print("MuJoCo 查看器已关闭。")
                else:
                     print("MuJoCo 查看器未运行。")
            except Exception as e:
                print(f"关闭 MuJoCo 查看器时出错: {e}")
            finally:
                 self.mujoco_viewer = None # 确保引用被清除
        else:
             print("MuJoCo 查看器未初始化。") 