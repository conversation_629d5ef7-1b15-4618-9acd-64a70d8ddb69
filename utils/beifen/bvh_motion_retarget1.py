#!/usr/bin/env python3
"""
基于BVH的动作重定向脚本

这个脚本直接使用BVH动作数据进行重定向，不依赖SMPL模型。
使用bone_optimizer_gui.py拟合的骨骼参数进行动作重定向。
参考grad_rotation_fit_ik.py的实现思路。

"""

import argparse
import os
import sys
import json
import numpy as np
import torch
import time
from datetime import datetime
from tqdm import tqdm
import matplotlib.pyplot as plt
from scipy.spatial.transform import Rotation as sRot
import joblib

# 导入自定义模块
from humanoid.utils import humanoid_batch_register
from humanoid.utils.torch_humanoid_batch import Humanoid_Batch
from humanoid.retarget_motion.base.motion_lib_cfg import MotionLibCfg
from humanoid.utils.math_tool import butter_lowpass_filter_torch, gaussian_filter_1d_batch
from utils.bvh_parser import BVHParser
from utils.bvh_utils import BVHMotion
from utils.bvh_mujoco_viewer import BVHMujocoPlayer



class BVHMotionRetargeter:
    """BVH动作重定向器"""
    
    def __init__(self, args):
        """初始化重定向器
        
        Args:
            args: 命令行参数
        """
        self.args = args
        self.device = torch.device(args.device)
        
        # 设置参数
        self.setPrama()
        
        # 解析输入路径并自动查找配置文件
        self.parse_input_files(args.optimized_bvh)
        
        # 加载骨骼拟合结果配置
        print(f"加载骨骼拟合配置: {self.config_file}")
        self.skeleton_params = self.load_skeleton_params(self.config_file)  # 解析json，返回值包含优化后的offset，机器人t_pose dof，关节映射，机器人 keypoint关节索引，bvh keypoint关节索引等
        
        # 注册机器人和初始化
        self.registryRobot()
        self.initRobotBody()
        
        # 加载优化后的BVH动作数据
        print(f"加载优化后的BVH文件: {self.bvh_file}")
        self.bvh_motion = BVHMotion(self.bvh_file)  # BVHMotion会直接读取offset，通常是厘米单位,得到的self.bvh_motion包含相对于父的旋转（欧拉角（度））和位置等其他信息
        
        # 检查并转换BVH数据单位（从厘米转换为米）
        self.convert_bvh_units_to_meters()
        
        # 初始化BVH播放器 - 在此之前先设置正确的机器人关节索引
        if not args.no_vis:
            # 从配置文件中获取机器人关节索引，确保在播放器初始化时就有正确的索引
            robot_joint_indices = self.skeleton_params['joint_correspondence'].get('robot_joint_indices', [])
            if robot_joint_indices:
                self.skeleton_params['robot_joint_indices'] = robot_joint_indices
                print(f"✅ 预设机器人关节索引到skeleton_params: {robot_joint_indices}")
            else:
                print("⚠️ 配置文件中没有robot_joint_indices，将使用原始计算")
                
            self.bvh_player = BVHMujocoPlayer(self.motion_lib_cfg, self.skeleton_params)
        
        # 初始化脚趾索引用于接触检测
        try:
            self.left_toe_idx = self.motion_lib_cfg.joint_names.index(self.motion_lib_cfg.left_toe_name)
            self.right_toe_idx = self.motion_lib_cfg.joint_names.index(self.motion_lib_cfg.right_toe_name)
            print(f"✅ 脚趾索引初始化成功: Left={self.left_toe_idx} ('{self.motion_lib_cfg.left_toe_name}'), Right={self.right_toe_idx} ('{self.motion_lib_cfg.right_toe_name}')")
        except ValueError as e:
            print(f"⚠️ 脚趾索引初始化失败: {e}")
            self.left_toe_idx = -1
            self.right_toe_idx = -1
        
        # 损失函数
        self.mse_loss = torch.nn.MSELoss()
        
        # 初始化关节权重
        self.init_joint_weights()
        
        print("BVH动作重定向器初始化完成")

    def convert_bvh_units_to_meters(self):
        """
        BVH单位转换 - 检查并转换运动数据单位
        
        注意：虽然bone_optimizer_gui.py处理了骨架结构，
        但运动数据可能仍需要单位转换（厘米->米）。
        """
        import numpy as np
        
        # 检查位置数据的数值范围来判断单位
        if self.bvh_motion.joint_position is not None:
            pos_range = np.abs(self.bvh_motion.joint_position).max()
            
            print(f"BVH运动数据单位检查:")
            print(f"  - 最大position值: {pos_range:.3f}")
            
            # 如果数值很大（>10），很可能是厘米单位，需要转换为米
            if pos_range > 10.0:
                print(f"  - 检测到厘米单位，转换运动数据为米单位")
                
                # 转换所有position数据（厘米 -> 米）
                self.bvh_motion.joint_position *= 0.01
                
                # 如果已经计算了全局位置，也需要转换
                if self.bvh_motion.joint_translation is not None:
                    self.bvh_motion.joint_translation *= 0.01
                
                # 重新检查转换后的范围
                new_range = np.abs(self.bvh_motion.joint_position).max()
                print(f"  - 转换后最大position值: {new_range:.3f}")
                
                return 0.01  # 返回转换因子
            else:
                print(f"  - 数据已经是米单位，无需转换")
                return 1.0
        else:
            print("  - 警告: BVH joint_position为空")
            return 1.0

    def setPrama(self):
        """设置可调参数"""
        self.device = self.args.device
        self.kernel_size = self.args.kernel_size
        self.sigma = self.args.sigma
        self.iterations = self.args.iterations
        self.learning_rate = self.args.learning_rate
        
        # 损失权重
        self.keypoint_loss_weight = self.args.keypoint_loss_weight
        self.smoothness_loss_weight = self.args.smoothness_loss_weight
        self.contact_loss_weight = self.args.contact_loss_weight
        self.foot_height_loss_weight = self.args.foot_height_loss_weight
        

    def load_skeleton_params(self, config_path):
        """加载骨骼拟合参数 - 从bone_optimizer_gui的输出中读取"""
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 验证配置文件格式
        if not self.validate_config_file(config_path):
            raise ValueError(f"无效的配置文件格式: {config_path}")
        
        # 提取task_name
        self.task_name = config['metadata']['task_name']
        
        # 提取关节对应关系
        joint_correspondence = config['joint_correspondence']
        
        # 提取BVH关节信息
        bvh_info = config.get('bvh_info', {})
        bvh_joint_names = bvh_info.get('joint_names', [])
        bvh_selected_joints = bvh_info.get('selected_joints', [])
        
        # 提取机器人关节信息  
        robot_info = config.get('robot_info', {})
        robot_joint_names = robot_info.get('joint_names', [])
        robot_selected_joints = robot_info.get('selected_joints', [])
        
        robot_joint_indices = joint_correspondence['robot_joint_indices']
        bvh_joint_indices = joint_correspondence['bvh_joint_indices']
             
        # 构建完整的参数结构
        params = {
            'metadata': {
                'task_name': self.task_name,
                'source_file': config_path,
                'optimization_version': config['metadata'].get('optimization_version', 'BVH_Direct_v1.0')
            },
            'joint_correspondence': {
                'robot_to_bvh': joint_correspondence.get('robot_to_bvh', {}),
                'bvh_to_robot': joint_correspondence.get('bvh_to_robot', {}),
                'robot_joint_indices': robot_joint_indices,  # selected_joints
                'bvh_joint_indices': bvh_joint_indices  
            },
            'skeleton_params': config.get('skeleton_params', {}),  # 优化后的offset
            't_pose_config': config.get('t_pose_config', {}),  # 机器人t_pose dof
            'coordinate_transform': config.get('coordinate_transform', {}),  # 坐标系变换信息
            'bvh_info': {
                'joint_names': bvh_joint_names, 
                'selected_joints': bvh_selected_joints
            },
            'robot_info': {
                'joint_names': robot_joint_names,
                'selected_joints': robot_selected_joints
            }
        }
        
        print("骨骼参数加载成功:")
        print(f"  - 任务: {self.task_name}")
        print(f"  - 关节映射: {len(joint_correspondence.get('robot_to_bvh', {}))} 对")
        
        
        skeleton_params = params['skeleton_params']
        if 'scale_factor' in skeleton_params:
            print(f"  - 缩放因子: {skeleton_params['scale_factor']:.4f}")
        
        # 显示坐标变换信息
        coord_transform = params.get('coordinate_transform', {})
        if 'transform_matrix' in coord_transform:
            print(f"  - 坐标变换矩阵: {coord_transform['transform_matrix']}")
            if 'bvh_to_robot_description' in coord_transform:
                print(f"  - 变换描述: {coord_transform['bvh_to_robot_description']}")
        
        return params

    def parse_input_files(self, optimized_bvh_path):
        """解析输入文件路径并自动查找相应的配置文件
        
        Args:
            optimized_bvh_path: 优化后的BVH文件路径
        """
        if not os.path.exists(optimized_bvh_path):
            raise FileNotFoundError(f"优化后的BVH文件不存在: {optimized_bvh_path}")
        
        self.bvh_file = optimized_bvh_path
        bvh_dir = os.path.dirname(optimized_bvh_path)
        bvh_basename = os.path.splitext(os.path.basename(optimized_bvh_path))[0]
        
        # 查找对应的配置JSON文件
        possible_config_files = [
            # 优先查找与BVH文件同名的JSON文件
            os.path.join(bvh_dir, bvh_basename.replace('_optimized', '') + '.json'),
            os.path.join(bvh_dir, bvh_basename + '.json'),
            # 查找通用名称的配置文件
            os.path.join(bvh_dir, 'bvh_optimization_result.json'),
            # 查找最新的JSON文件
            *sorted([f for f in os.listdir(bvh_dir) if f.endswith('.json')], 
                   key=lambda x: os.path.getmtime(os.path.join(bvh_dir, x)), reverse=True)[:3]
        ]
        
        self.config_file = None
        for config_path in possible_config_files:
            if isinstance(config_path, str) and not os.path.isabs(config_path):
                config_path = os.path.join(bvh_dir, config_path)
            
            if os.path.exists(config_path):
                # 验证是否是有效的bone_optimizer_gui输出配置
                if self.validate_config_file(config_path):
                    self.config_file = config_path
                    break
        
        if self.config_file is None:
            raise FileNotFoundError(
                f"未找到与BVH文件 {optimized_bvh_path} 对应的配置文件。\n"
                f"请确保在相同目录下存在bone_optimizer_gui.py的输出配置文件。\n"
                f"查找过的路径: {possible_config_files[:5]}"
            )
        
        print(f"找到配置文件: {self.config_file}")

    def validate_config_file(self, config_path):
        """验证配置文件是否是有效的bone_optimizer_gui输出
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            bool: 是否是有效的配置文件
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查必需的字段
            required_fields = [
                'metadata', 
                'joint_correspondence', 
                'skeleton_params', 
                't_pose_config'
            ]
            
            for field in required_fields:
                if field not in config:
                    return False
            
            # 检查metadata中是否有task_name
            if 'task_name' not in config['metadata']:
                return False
            
            # 检查是否是bone_optimizer_gui的输出
            if 'optimization_version' in config['metadata']:
                version = config['metadata']['optimization_version']
                if 'BVH' in version or 'Direct' in version:
                    return True
            
            return True
            
        except (json.JSONDecodeError, KeyError, Exception):
            return False

    def registryRobot(self):
        """注册机器人信息 - 使用从配置中提取的task_name"""
        # 注册机器人任务
        humanoid_batch_register.register_task(self.task_name)
        self.motion_lib_cfg: MotionLibCfg = humanoid_batch_register.get_config(self.task_name)
        self.motion_lib_cfg.device = self.device
        
        # 直接从motion_lib_cfg中读取optimize_root配置
        self.optimize_root = getattr(self.motion_lib_cfg, 'optimize_root', False)
        print(f"optimize_root: {self.optimize_root}")
        
        # 初始化前向运动学
        self.Humanoid_fk = Humanoid_Batch(
            self.motion_lib_cfg.mjcf_file, 
            self.motion_lib_cfg.extend_node_dict, 
            device=self.device
        )
        
    def initRobotBody(self):
        """初始化机器人关节信息"""
        # 获取关节信息
        self.robot_joint_names = self.motion_lib_cfg.joint_names
        self.robot_pos_names = self.motion_lib_cfg.pos_names
        self.limb_names = self.motion_lib_cfg.limb_names
        self.robot_joint_pick = self.motion_lib_cfg.joint_pick
        self.robot_joint_pick_bias = self.motion_lib_cfg.joint_pick_bias
        
        # BVH关节信息 - 从配置文件中获取
        self.bvh_joint_pick = self.skeleton_params['bvh_info']['selected_joints']
        
        # 如果配置中没有BVH关节索引，尝试根据关节名称生成
        if 'bvh_joint_indices' in self.skeleton_params['joint_correspondence']:
            self.bvh_joint_pick_idx = self.skeleton_params['joint_correspondence']['bvh_joint_indices']
        else:
            print("警告: 配置中没有BVH关节索引，将在运行时动态生成")
            self.bvh_joint_pick_idx = []
        
        # 创建关节链条
        self.limb_index = {}
        for key in self.limb_names:
            limb_name = self.limb_names[key]
            self.limb_index[key] = [self.robot_pos_names.index(i) for i in limb_name if i in self.robot_pos_names]
        
        # 获取关节名称对应的索引 - 统一使用robot_joint_pick_idx
        self.robot_joint_pick_idx = [self.robot_joint_names.index(j) for j in self.robot_joint_pick]
        
        print(f"机器人模型初始化完成: {len(self.robot_joint_names)} 个关节")
        print(f"机器人关键关节数: {len(self.robot_joint_pick)}")
        print(f"BVH关键关节数: {len(self.bvh_joint_pick)}")

    def init_joint_weights(self):
        """初始化关节权重"""
        # 创建关节权重张量 - 基于实际的机器人关节数量
        self.joint_weights = torch.ones(len(self.robot_joint_pick), device=self.device)
        
        # 如果配置中有关节权重设置，应用它们
        if hasattr(self.motion_lib_cfg, 'joint_weights'):
            for i, joint_name in enumerate(self.robot_joint_pick):
                if joint_name in self.motion_lib_cfg.joint_weights:
                    self.joint_weights[i] = self.motion_lib_cfg.joint_weights[joint_name]
        
        # 扩展权重到3D - 这里先用原始数量，后面会根据有效关节调整
        self.joint_weights = self.joint_weights.unsqueeze(0).unsqueeze(-1).expand(1, -1, 3)

    def update_joint_weights_for_valid_joints(self, num_valid_joints):
        """根据有效关节数量更新关节权重"""
        if num_valid_joints != len(self.robot_joint_pick):
            print(f"调整关节权重: {len(self.robot_joint_pick)} -> {num_valid_joints}")
            # 只保留有效关节的权重
            if num_valid_joints <= len(self.robot_joint_pick):
                self.joint_weights = self.joint_weights[:, :num_valid_joints, :]
            else:
                # 如果有效关节更多，扩展权重
                additional_weights = torch.ones(1, num_valid_joints - len(self.robot_joint_pick), 3, device=self.device)
                self.joint_weights = torch.cat([self.joint_weights, additional_weights], dim=1)

    def apply_skeleton_transform(self, bvh_positions):
        """
        应用坐标系变换 - 直接从JSON配置文件中获取4x4变换矩阵
        """
        print("应用坐标系转换...")
        
        # 从配置文件中获取变换信息
        coord_transform = self.skeleton_params.get('coordinate_transform', {})
        
        if 'transform_matrix' in coord_transform:
            # 直接使用配置文件中的4x4变换矩阵
            transform_matrix = coord_transform['transform_matrix']
            transform_4x4 = torch.tensor(transform_matrix, device=self.device, dtype=torch.float32)
            
            print(f"使用配置文件中的4x4变换矩阵")
            # 应用齐次变换
            transformed_positions = self._apply_homogeneous_transform(bvh_positions, transform_4x4)
        else:
            print("配置文件中没有变换矩阵，使用默认变换")
            transformed_positions = self._apply_default_transform(bvh_positions)
        
        # print(f"变换后位置范围: X[{transformed_positions[:,:,0].min():.3f}, {transformed_positions[:,:,0].max():.3f}], "
        #       f"Y[{transformed_positions[:,:,1].min():.3f}, {transformed_positions[:,:,1].max():.3f}], "
        #       f"Z[{transformed_positions[:,:,2].min():.3f}, {transformed_positions[:,:,2].max():.3f}]")
        
        return transformed_positions



    def _apply_homogeneous_transform(self, positions, transform_matrix):
        """应用齐次坐标变换"""
        original_shape = positions.shape
        
        # 将位置转换为齐次坐标 [x, y, z, 1]
        positions_flat = positions.reshape(-1, 3)
        ones = torch.ones(positions_flat.shape[0], 1, device=self.device, dtype=torch.float32)
        positions_homogeneous = torch.cat([positions_flat, ones], dim=1)  # [N, 4]
        
        # 应用变换: new_pos = transform_matrix @ old_pos.T
        transformed_homogeneous = (transform_matrix @ positions_homogeneous.T).T[..., :3]# [4, N]
        transformed_positions = transformed_homogeneous.reshape(original_shape)
        
        return transformed_positions

    def _apply_default_transform(self, bvh_positions):
        """应用默认的坐标系转换"""
        print("使用默认坐标系转换: BVH(x=左右,y=上下,z=前后) -> Robot(x=前后,y=左右,z=上下)")
        transformed_positions = torch.zeros_like(bvh_positions)
        transformed_positions[..., 0] = bvh_positions[..., 2]  # x_robot = z_bvh (前后)
        transformed_positions[..., 1] = bvh_positions[..., 0]  # y_robot = x_bvh (左右)
        transformed_positions[..., 2] = bvh_positions[..., 1]  # z_robot = y_bvh (上下)
        return transformed_positions



    def apply_rotation_coordinate_transform(self, bvh_rotations):
        """
        简单的旋转坐标系转换 - 直接从JSON配置文件中获取变换矩阵
        
        Args:
            bvh_rotations: [num_frames, 3] 轴角表示的旋转
            
        Returns:
            transformed_rotations: [num_frames, 3] 转换后的旋转
        """
        from scipy.spatial.transform import Rotation as R
        import numpy as np
        
        coord_cfg = self.skeleton_params.get('coordinate_transform', {})
        if 'transform_matrix' not in coord_cfg:
            # 未配置时不变
            return bvh_rotations

        T = np.asarray(coord_cfg['transform_matrix'], dtype=np.float32)[:3, :3]  # (3,3)
        T_inv = T.T    # 因为 T 是纯正交旋转，T⁻¹ = Tᵀ

        # ── 2. 轴角 → 旋转矩阵
        rot_mat = sRot.from_rotvec(
            bvh_rotations.detach().cpu().numpy()     # (T,3)
        ).as_matrix()                                 # (T,3,3)

        # ── 3. 坐标系变换  R' = T · R · Tᵀ 
        #    等价于把 R 表示的姿态从 "参考系" 改写到 "机器人系"
        rot_mat_tr = np.einsum('ij,njk,kl->nil', T, rot_mat, T_inv)  # (T,3,3)

        # ── 4. 再转回轴角 
        rotvec_tr = sRot.from_matrix(rot_mat_tr).as_rotvec()         # (T,3) rad

        return torch.tensor(
            rotvec_tr, device=bvh_rotations.device, dtype=bvh_rotations.dtype
        )



    def caluBVH2Robot(self):
        """
        计算BVH到机器人的映射 - 简化版本
        
        注意：输入的BVH文件已经由bone_optimizer_gui.py完全处理，
        包括坐标转换、缩放、偏移等，可以直接使用作为目标参考。
        
        Returns:
            dof_pos: 初始化的关节角度（T-pose）
            bvh_target_positions: BVH目标关节位置（已处理）
            root_pos: 根节点位置（已处理）
            root_rot: 根节点旋转（已处理）
        """
        # 获取BVH动作数据
        self.num_frames = self.bvh_motion.joint_position.shape[0]
        self.fps = self.bvh_motion.mocap_framerate
        
        print(f"BVH动作数据:")
        print(f"  - 帧数: {self.num_frames}")
        print(f"  - 帧率: {self.fps:.1f} fps")
        print(f"  - 时长: {self.num_frames / self.fps:.2f} 秒")
        print(f"  - 关节数: {len(self.bvh_motion.joint_name)}")
        print(f"  - 输入类型: 已预处理的BVH文件")
        
        # 直接使用load_skeleton_params中已经正确解析的关节对应关系
        joint_correspondence = self.skeleton_params.get('joint_correspondence', {})
        
        print(f"\n✅ 使用load_skeleton_params中已解析的关节对应关系:")
        
        # 直接使用已经解析好的索引，不再重新计算
        if 'bvh_joint_indices' in joint_correspondence and 'robot_joint_indices' in joint_correspondence:
            self.valid_bvh_joint_indices = joint_correspondence['bvh_joint_indices']
            self.valid_robot_joint_indices = joint_correspondence['robot_joint_indices']
            
            print(f"  - BVH key-point索引: {self.valid_bvh_joint_indices}")
            print(f"  - 机器人 key-pointey-point索引: {self.valid_robot_joint_indices}")
            
            # 验证并显示对应关系
            bvh_joint_names = self.bvh_motion.joint_name
            robot_joint_names = self.robot_joint_names
            
            print(f"  - BVH文件关节总数: {len(bvh_joint_names)}")
            print(f"  - 机器人关节总数: {len(robot_joint_names)}")
            print(f"  - 有效关节对应: {len(self.valid_bvh_joint_indices)} 对")
            
            # 显示对应关系
            for i, (bvh_idx, robot_idx) in enumerate(zip(self.valid_bvh_joint_indices, self.valid_robot_joint_indices)):
                if bvh_idx < len(bvh_joint_names) and robot_idx < len(robot_joint_names):
                    bvh_name = bvh_joint_names[bvh_idx]
                    robot_name = robot_joint_names[robot_idx]
                    print(f"    ✓ BVH[{bvh_idx}]:{bvh_name} <-> Robot[{robot_idx}]:{robot_name}")
                else:
                    print(f"    ✗ 无效索引: BVH[{bvh_idx}] Robot[{robot_idx}]")
        else:
            raise ValueError("配置文件中缺少关节索引信息！请检查load_skeleton_params的输出。")
        
        # 计算BVH前向运动学并应用坐标系转换
        print("\n计算BVH前向运动学并转换到机器人坐标系...")

        # 先使用json文件中的旋转矩阵将bvh中的数据转到机器人坐标系
        coord_transform = self.skeleton_params.get('coordinate_transform', {})
        if 'transform_matrix' in coord_transform:
            # 直接使用配置文件中的4x4变换矩阵
            transform_matrix = coord_transform['transform_matrix']
            transform_4x4 = torch.tensor(transform_matrix, device=self.device, dtype=torch.float32)
        
        # 根的平移变换
        p_root = self.bvh_motion.joint_position[:, 0, :]
        p_root = (transform_4x4[:3, :3] @ p_root.T).T
        self.bvh_motion.joint_position[:, 0, :] = p_root

        # 根的旋转变换
        r_root = self.bvh_motion.joint_rotation[:, 0, :]
        R_ref = sRot.from_euler(self.bvh_motion.euler, r_root, degrees=True).as_matrix()
        r_root = transform_4x4[:3, :3] @ R_ref
        r_root = sRot.from_matrix(r_root).as_euler(self.bvh_motion.euler, degrees=True)
        self.bvh_motion.joint_rotation[:, 0, :] = r_root

        # 将r_root转换为旋转矩阵再转换为轴角
        r_root = sRot.from_euler(self.bvh_motion.euler, r_root, degrees=True).as_matrix()
        r_root = sRot.from_matrix(r_root).as_rotvec()

        # 计算BVH前向运动学
        self.bvh_motion.batch_forward_kinematics()
        bvh_joint_positions = self.bvh_motion.joint_translation  # [num_frames, num_joints, 3] 全局位移，相对于世界坐标系
        p_root = bvh_joint_positions[:, 0, :]
        # 提取目标关节位置（使用有效的BVH关节索引）
        if len(self.valid_bvh_joint_indices) == 0:
            raise ValueError("没有找到任何匹配的BVH关节")
        
        bvh_target_positions = bvh_joint_positions[:, self.valid_bvh_joint_indices, :]
        bvh_target_positions = torch.tensor(bvh_target_positions, device=self.device, dtype=torch.float32)  # 全局位移，相对于世界坐标系
        
        
        # 初始化关节角度 - 使用机器人的T-pose作为起点
        if hasattr(self.motion_lib_cfg, 'dof_pos') and self.motion_lib_cfg.dof_pos is not None:
            t_pose_dof = self.motion_lib_cfg.dof_pos.to(self.device)
            print(f"\n使用motion_lib_cfg中的机器人T-pose DOF位置: {t_pose_dof.shape}")
        else:
            # 如果没有T-pose配置，使用零位置
            print("\n警告: motion_lib_cfg中没有dof_pos，使用零位置")
            t_pose_dof = torch.zeros(self.Humanoid_fk.joints_axis.shape[1], device=self.device)
        
        # 为所有帧设置初始DOF值
        dof_pos = torch.zeros((1, self.num_frames, self.Humanoid_fk.joints_axis.shape[1], 1), device=self.device)
        
        # 正确处理张量维度
        if t_pose_dof.dim() == 1:
            t_pose_expanded = t_pose_dof.unsqueeze(0).repeat(self.num_frames, 1)
        else:
            t_pose_expanded = t_pose_dof.repeat(self.num_frames, 1)
        
        dof_pos[0, :, :, 0] = t_pose_expanded
        
        print(f"\n初始DOF形状: {dof_pos.shape}")
        print("✅ BVH到机器人映射完成，数据已就绪")
        
        return dof_pos, bvh_target_positions, p_root, r_root
    


    def parse_bvh_motion_data(self):
        """
        解析BVH动作数据，获取所有帧的关节位置
        
        Returns:
            joint_positions: [num_frames, num_joints, 3] 关节位置
        """
        # 使用BVHMotion类计算前向运动学
        self.bvh_motion.batch_forward_kinematics()
        
        # 获取全局关节位置
        joint_positions = self.bvh_motion.joint_translation  # [num_frames, num_joints, 3]
        
        # 转换为torch张量
        joint_positions = torch.tensor(joint_positions, device=self.device, dtype=torch.float32)
        
        return joint_positions

    def calcAA(self, dof_pos, root_rot):
        """计算轴角表示，类似于grad_rotation_fit_ik.py中的calcAA"""
        # 获取关节轴
        axes = self.Humanoid_fk.joints_axis
        # 获取关节角度
        angles = dof_pos
        
        # 计算所有非根关节的轴角
        joint_aa = angles * axes.unsqueeze(1)
        
        # 调整根旋转的形状以进行拼接
        root_rot_reshaped = root_rot.view(1, -1, 1, 3)
        
        # 拼接根旋转和关节轴角
        pose_aa_new = torch.cat([root_rot_reshaped, joint_aa], dim=2).to(self.device)
        
        return pose_aa_new

    def calcJointPosition(self, fk_return):
        """计算关节位置并应用偏置修正"""
        # 获取关节位置
        joint_positions = fk_return['global_translation']
        
        # 如果有偏置配置，应用偏置修正
        if hasattr(self.motion_lib_cfg, 'joint_pick_bias') and self.motion_lib_cfg.joint_pick_bias:
            # 这里可以添加偏置修正逻辑
            # 暂时直接返回原始位置
            pass
        
        return joint_positions

    def calcKeyPointDiffLoss(self, robot_positions, target_positions):
        """计算关键点差异损失"""
        # robot_positions: [1, num_frames, num_joints, 3]
        # target_positions: [num_frames, num_joints, 3]
        
        # 提取关键点位置
        robot_key_pos = robot_positions[0]  # [num_frames, num_joints, 3]
        
        # 确保维度匹配
        if robot_key_pos.shape != target_positions.shape:
            min_frames = min(robot_key_pos.shape[0], target_positions.shape[0])
            min_joints = min(robot_key_pos.shape[1], target_positions.shape[1])
            robot_key_pos = robot_key_pos[:min_frames, :min_joints, :]
            target_positions = target_positions[:min_frames, :min_joints, :]
            
            print(f"维度调整: 机器人位置 {robot_key_pos.shape}, 目标位置 {target_positions.shape}")
        
        # 确保关节权重维度匹配
        current_joint_weights = self.joint_weights
        if current_joint_weights.shape[1] != robot_key_pos.shape[1]:
            # 调整权重维度
            weights_needed = robot_key_pos.shape[1]
            if weights_needed <= current_joint_weights.shape[1]:
                current_joint_weights = current_joint_weights[:, :weights_needed, :]
            else:
                additional_weights = torch.ones(1, weights_needed - current_joint_weights.shape[1], 3, device=self.device)
                current_joint_weights = torch.cat([current_joint_weights, additional_weights], dim=1)
        
        # 计算加权损失
        diff = robot_key_pos - target_positions
        weighted_diff = diff * current_joint_weights
        loss = torch.mean(torch.norm(weighted_diff, dim=-1))
        
        return loss

    def calcSmoothnessLoss(self, robot_positions):
        """计算平滑损失"""
        # robot_positions: [1, num_frames, num_joints, 3]
        positions = robot_positions[0]  # [num_frames, num_joints, 3]
        
        if positions.shape[0] < 3:
            return torch.tensor(0.0, device=self.device), torch.zeros(positions.shape[0])
        
        # 计算二阶差分（加速度）
        vel1 = positions[1:] - positions[:-1]
        vel2 = vel1[1:] - vel1[:-1]
        
        # 计算每帧的平滑损失
        per_frame_loss = torch.norm(vel2, dim=-1).mean(dim=-1)
        
        # 计算总平滑损失
        total_loss = torch.mean(per_frame_loss)
        
        # 扩展到原始帧数（前两帧设为0）
        expanded_loss = torch.zeros(positions.shape[0], device=self.device)
        expanded_loss[2:] = per_frame_loss
        
        return total_loss, expanded_loss

    def calcFootHeightLoss(self, robot_positions):
        """计算脚部高度损失"""
        # robot_positions: [1, num_frames, num_joints, 3]
        positions = robot_positions[0]  # [num_frames, num_joints, 3]
        
        # 查找脚部相关关节
        foot_indices = []
        for i, joint_name in enumerate(self.robot_pos_names):
            if any(keyword in joint_name.lower() for keyword in ['foot', 'ankle', 'toe']):
                foot_indices.append(i)
        
        if not foot_indices:
            return torch.tensor(0.0, device=self.device)
        
        # 提取脚部位置
        foot_positions = positions[:, foot_indices, :]
        
        # 计算脚部高度损失（防止穿地）
        foot_heights = foot_positions[:, :, 2]  # z坐标
        target_foot_height = 0.0
        height_violations = torch.clamp(target_foot_height - foot_heights, min=0.0)
        
        foot_height_loss = torch.mean(height_violations ** 2)
        
        return foot_height_loss

    def retargetBVHData(self):
        """重定向BVH数据 - 简化版本，专注于dof_pos优化"""
        print("="*50)
        print("开始BVH动作重定向")
        print("="*50)
        
        # 1. 获取预处理的BVH数据作为目标参考
        dof_pos, bvh_target_positions, root_pos, root_rot = self.caluBVH2Robot()
        
        print(f"目标位置数据形状: {bvh_target_positions.shape}")
        print(f"关节角度数据形状: {dof_pos.shape}")
        print(f"根节点位置形状: {root_pos.shape}")
        print(f"根节点旋转形状: {root_rot.shape}")
        
        # 2. 更新关节权重以匹配有效关节数量
        num_valid_joints = bvh_target_positions.shape[1]
        self.update_joint_weights_for_valid_joints(num_valid_joints)
        
        # 使用配置中定义的有效关节索引
        if hasattr(self, 'valid_robot_joint_indices'):
            self.current_robot_joint_pick_idx = self.valid_robot_joint_indices
            print(f"使用配置文件中的机器人关节索引: {len(self.current_robot_joint_pick_idx)} 个")
            print(f"BVH关节索引数量: {len(self.valid_bvh_joint_indices)} 个")
            
            # 验证数量一致性
            if len(self.current_robot_joint_pick_idx) != len(self.valid_bvh_joint_indices):
                print(f"警告: 机器人关节数({len(self.current_robot_joint_pick_idx)}) != BVH关节数({len(self.valid_bvh_joint_indices)})")
        else:
            # 回退方案
            self.current_robot_joint_pick_idx = self.robot_joint_pick_idx
            print(f"使用默认机器人关节索引: {len(self.current_robot_joint_pick_idx)} 个")
        
        # 3. 创建优化变量
        from torch.autograd import Variable
        
        dof_pos_new = Variable(dof_pos.clone(), requires_grad=True)
        
        if self.optimize_root:
            print("开启根节点优化")
            root_pos_tensor = torch.tensor(root_pos, device=self.device, dtype=torch.float32)
            root_rot_tensor = torch.tensor(root_rot, device=self.device, dtype=torch.float32)
            root_pos_new = Variable(root_pos_tensor.clone(), requires_grad=True)
            root_rot_new = Variable(root_rot_tensor.clone(), requires_grad=True)
        else:
            print("关闭根节点优化")
            root_pos_new = torch.tensor(root_pos, device=self.device, dtype=torch.float32)
            root_rot_new = torch.tensor(root_rot, device=self.device, dtype=torch.float32)
        
        # 4. 创建优化器
        if self.optimize_root:
            optimizer = torch.optim.Adam([dof_pos_new, root_pos_new, root_rot_new], lr=self.learning_rate)
        else:
            optimizer = torch.optim.Adam([dof_pos_new], lr=self.learning_rate)
        scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=500, gamma=0.5)
        
        # 5. 优化循环 - 核心任务：调整dof_pos使机器人FK结果匹配BVH目标
        loss_history = []
        
        print(f"\n开始优化循环: {self.iterations} 次迭代")
        print(f"学习率: {self.learning_rate}")
        print(f"优化目标: 使机器人FK结果匹配预处理的BVH目标")
        
        for iteration in range(self.iterations):
            optimizer.zero_grad()
            
            # 限制关节角度范围
            dof_pos_new.data.clamp_(
                self.Humanoid_fk.joints_range[:, 0, None], 
                self.Humanoid_fk.joints_range[:, 1, None]
            )
            
            # 计算前向运动学
            pose_aa_new = self.calcAA(dof_pos_new, root_rot_new)
            fk_return = self.Humanoid_fk.fk_batch(
                pose_aa_new, 
                root_pos_new[None,], 
                return_full=True
            )
            
            # 计算关节位置
            robot_joint_positions = self.calcJointPosition(fk_return)
            robot_key_positions = robot_joint_positions[:, :, self.current_robot_joint_pick_idx]
            
            # 第一次迭代时打印详细的对应关系调试信息
            if iteration == 0:
                print(f"\n🔍 关键点对应关系验证 (第{iteration}次迭代):")
                print(f"  - 机器人关节索引: {self.current_robot_joint_pick_idx}")
                print(f"  - BVH关节索引: {self.valid_bvh_joint_indices}")
                print(f"  - robot_key_positions形状: {robot_key_positions.shape}")
                print(f"  - bvh_target_positions形状: {bvh_target_positions.shape}")
                
                print(f"\n  详细对应关系:")
                min_joints = min(len(self.current_robot_joint_pick_idx), len(self.valid_bvh_joint_indices))
                for i in range(min_joints):
                    robot_joint_idx = self.current_robot_joint_pick_idx[i]
                    robot_joint_name = self.robot_joint_names[robot_joint_idx] if robot_joint_idx < len(self.robot_joint_names) else f"未知关节{robot_joint_idx}"
                    
                    bvh_joint_idx = self.valid_bvh_joint_indices[i]
                    bvh_joint_name = self.bvh_motion.joint_name[bvh_joint_idx]
                    
                    # 获取第一帧的位置进行距离计算
                    robot_pos = robot_key_positions[0, 0, i].detach().cpu().numpy() if robot_key_positions.shape[2] > i else [0,0,0]
                    bvh_pos = bvh_target_positions[0, i].detach().cpu().numpy() if bvh_target_positions.shape[1] > i else [0,0,0]
                    distance = np.linalg.norm(robot_pos - bvh_pos)
                    
                    print(f"    [{i}] Robot[{robot_joint_idx}]:{robot_joint_name} ↔ BVH[{bvh_joint_idx}]:{bvh_joint_name}")
                    print(f"        位置: Robot{robot_pos} vs BVH{bvh_pos}, 距离: {distance:.3f}")
                
                # 检查是否有明显的对应错误
                problematic_pairs = []
                for i in range(min_joints):
                    robot_joint_idx = self.current_robot_joint_pick_idx[i]
                    robot_joint_name = self.robot_joint_names[robot_joint_idx] if robot_joint_idx < len(self.robot_joint_names) else f"未知关节{robot_joint_idx}"
                    
                    bvh_joint_idx = self.valid_bvh_joint_indices[i]
                    bvh_joint_name = self.bvh_motion.joint_name[bvh_joint_idx]
                    
                    # 简单的名称匹配检查
                    if ("left" in robot_joint_name.lower() and "right" in bvh_joint_name.lower()) or \
                       ("right" in robot_joint_name.lower() and "left" in bvh_joint_name.lower()) or \
                       ("leg" in robot_joint_name.lower() and "arm" in bvh_joint_name.lower()) or \
                       ("arm" in robot_joint_name.lower() and "leg" in bvh_joint_name.lower()):
                        problematic_pairs.append((i, robot_joint_name, bvh_joint_name))
                
                if problematic_pairs:
                    print(f"\n  ⚠️ 发现可疑的对应关系:")
                    for i, robot_name, bvh_name in problematic_pairs:
                        print(f"    [{i}] {robot_name} ↔ {bvh_name} (可能不匹配)")
                
                print("") # 空行分隔
            
            # 计算各项损失：机器人FK结果 vs BVH目标
            keypoint_loss = self.calcKeyPointDiffLoss(robot_key_positions, bvh_target_positions)
            smoothness_loss_total, per_frame_smoothness_loss = self.calcSmoothnessLoss(robot_key_positions)
            foot_height_loss = self.calcFootHeightLoss(robot_joint_positions)
            
            # 总损失
            total_loss = (
                self.keypoint_loss_weight * keypoint_loss +
                self.smoothness_loss_weight * smoothness_loss_total +
                self.foot_height_loss_weight * foot_height_loss
            )
            
            # 反向传播和优化
            total_loss.backward()
            optimizer.step()
            scheduler.step()
            
            # 记录损失
            loss_dict = {
                'total': total_loss.item(),
                'keypoint': keypoint_loss.item(),
                'smoothness': smoothness_loss_total.item(),
                'foot_height': foot_height_loss.item()
            }
            loss_history.append(loss_dict)
            
            # 输出进度
            if iteration % 100 == 0 or iteration == self.iterations - 1:
                print(f"迭代 {iteration:4d}: 总损失={total_loss.item():.6f}, "
                      f"关键点={keypoint_loss.item():.6f}, "
                      f"平滑={smoothness_loss_total.item():.6f}, "
                      f"脚高={foot_height_loss.item():.6f}, "
                      f"学习率={scheduler.get_last_lr()[0]:.6f}")
        
        print("\n优化完成!")
        
        # 6. 应用平滑滤波（可选）
        if self.args.apply_filter:
            print("应用高斯平滑滤波...")
            try:
                original_shape = dof_pos_new.shape
                dof_reshaped = dof_pos_new.squeeze(-1)
                dof_transposed = dof_reshaped.transpose(1, 2)
                
                dof_filtered = gaussian_filter_1d_batch(
                    dof_transposed, 
                kernel_size=self.kernel_size, 
                sigma=self.sigma
            )
                
                dof_pos_new = dof_filtered.transpose(1, 2).unsqueeze(-1)
                print(f"滤波完成: {original_shape} -> {dof_pos_new.shape}")
                
            except Exception as e:
                print(f"滤波失败: {e}")
                print("跳过平滑滤波步骤，使用原始优化结果")
        
        # 7. 计算最终FK结果（避免重复计算）
        print("计算最终FK结果...")
        final_pose_aa = self.calcAA(dof_pos_new, root_rot_new)
        final_fk_return = self.Humanoid_fk.fk_batch(
            final_pose_aa, 
            root_pos_new[None,], 
            return_full=True
        )
        final_joint_positions = final_fk_return['global_translation']
        
        # 8. 保存结果
        output_path = self.save_results(dof_pos_new, root_pos_new, root_rot_new, loss_history, 
                                      final_pose_aa, final_joint_positions)
        
        # 9. 播放优化后的动作
        if not self.args.no_vis:
            self.play_optimized_motion(dof_pos_new, root_pos_new, root_rot_new, bvh_target_positions,
                                     final_joint_positions)
        
        print("="*50)
        print("BVH动作重定向完成!")
        print(f"结果保存在: {output_path}")
        print("="*50)
        
        return output_path

    def save_results(self, dof_pos, root_pos, root_rot, loss_history, final_pose_aa, final_joint_positions):
        """保存重定向结果为pkl文件 - 参考grad_rotation_fit_ik.py的格式"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"data/calc_bvh/{self.skeleton_params['metadata']['task_name']}"
        os.makedirs(output_dir, exist_ok=True)
        
        # 按照grad_rotation_fit_ik.py的格式准备数据
        bvh_key = os.path.splitext(os.path.basename(self.args.optimized_bvh))[0]
        
        data_dump = {
            bvh_key: {
                "root_trans_offset": root_pos.detach().cpu().numpy(),
                "pose_aa": final_pose_aa.squeeze(0).detach().cpu().numpy(),
                "dof_pos": dof_pos.squeeze(0).detach().cpu().numpy(),
                "root_rot": sRot.from_rotvec(root_rot.detach().cpu().numpy()).as_quat(),
                "global_translation": final_joint_positions.squeeze(0).detach().cpu().numpy(),
                "fps": float(self.fps),
                # BVH特有的元数据
                "metadata": {
                'timestamp': timestamp,
                    'source_bvh': self.args.optimized_bvh,
                    'skeleton_params': self.skeleton_params,
                'task_name': self.skeleton_params['metadata']['task_name'],
                    'optimization_version': 'BVH_Retarget_v2.0',
                'iterations': self.iterations,
                'final_loss': loss_history[-1]['total'] if loss_history else None,
                'parameters': {
                    'learning_rate': self.learning_rate,
                    'keypoint_loss_weight': self.keypoint_loss_weight,
                    'smoothness_loss_weight': self.smoothness_loss_weight,
                    'foot_height_loss_weight': self.foot_height_loss_weight
                    }
                }
            }
        }
        
        # 保存为pkl文件
        output_path = os.path.join(output_dir, f"bvh_retarget_result_{timestamp}.pkl")
        joblib.dump(data_dump, output_path)
        
        print(f"重定向结果已保存到: {output_path}")
        
        return output_path

    def play_optimized_motion(self, dof_pos, root_pos, root_rot, bvh_target_positions, final_joint_positions):
        """使用新的BVH播放器播放优化后的动作"""
        print("开始播放优化后的动作...")
        
        # 使用预计算的FK结果
        with torch.no_grad():
            # 获取关节位置（使用传入的预计算结果）
            robot_joint_positions = final_joint_positions
            
            # 计算关键点位置 - 使用有效的机器人关节索引
            if hasattr(self, 'current_robot_joint_pick_idx'):
                robot_joint_indices = self.current_robot_joint_pick_idx
            else:
                robot_joint_indices = self.robot_joint_pick_idx
            robot_key_pos = robot_joint_positions[:, :, robot_joint_indices]
            
            print(f"播放器关节索引验证:")
            print(f"  - 使用的机器人关节索引: {robot_joint_indices}")
            print(f"  - 机器人关节位置形状: {robot_joint_positions.shape}")
            print(f"  - 提取的关键点形状: {robot_key_pos.shape}")
            print(f"  - BVH目标位置形状: {bvh_target_positions.shape}")
            
            # 确保维度匹配
            # min_joints = min(robot_key_pos.shape[2], bvh_target_positions.shape[1])
            # robot_key_pos_matched = robot_key_pos[:, :, :min_joints]
            # bvh_target_matched = bvh_target_positions[:, :min_joints, :]
            
            final_frame_losses = torch.norm(robot_key_pos - bvh_target_positions, p=2, dim=-1).mean(dim=-1).squeeze().cpu().numpy()
        
        # 生成接触序列 - 简单的基于脚部高度的接触检测
        contact_sequence = self.generate_contact_sequence(robot_joint_positions)
        
        # 计算BVH骨架连接关系
        self._calculate_bvh_skeleton_connections()
        
        # 向skeleton_params添加正确的机器人关节索引信息
        self.skeleton_params['robot_joint_indices'] = robot_joint_indices
        
        # 使用新的BVH播放器播放动作
        try:
            self.bvh_player.play_bvh_motion(
                dof_pos=dof_pos,
                root_pos=root_pos,
                root_rot=root_rot,
                bvh_target_positions=bvh_target_positions,
                contact_sequence=contact_sequence,
                final_frame_losses=final_frame_losses,
                fps=self.fps
            )
            
        except Exception as e:
            print(f"BVH播放错误: {e}")
            import traceback
            traceback.print_exc()

    def generate_contact_sequence(self, robot_joint_positions):
        """生成接触序列 - 直接使用脚趾关节进行接触检测
        
        Args:
            robot_joint_positions: [1, num_frames, num_joints, 3] 机器人关节位置
            
        Returns:
            contact_sequence: [num_frames, 2] 接触序列 (左脚, 右脚)
        """
        try:
            # 检查脚趾索引是否有效
            if self.left_toe_idx == -1 or self.right_toe_idx == -1:
                print("⚠️ 脚趾索引无效，使用默认接触序列")
                num_frames = robot_joint_positions.shape[1]
                contact_sequence = np.zeros((num_frames, 2), dtype=bool)
                # 简单的交替接触模式
                for i in range(num_frames):
                    if i % 20 < 10:  # 每20帧交替一次
                        contact_sequence[i, 0] = True  # 左脚接触
                    else:
                        contact_sequence[i, 1] = True  # 右脚接触
                return contact_sequence
            
            positions = robot_joint_positions[0]  # [num_frames, num_joints, 3]
            num_frames = positions.shape[0]
            contact_sequence = torch.zeros((num_frames, 2), dtype=torch.bool)
            
            # 接触检测参数
            contact_height_threshold = self.motion_lib_cfg.contact_height_threshold
            velocity_threshold = self.motion_lib_cfg.contact_velocity_threshold
            
            # 直接使用脚趾关节位置
            left_toe_positions = positions[:, self.left_toe_idx, :]  # [num_frames, 3]
            right_toe_positions = positions[:, self.right_toe_idx, :]  # [num_frames, 3]
            
            # 提取脚趾高度（z坐标）
            left_toe_heights = left_toe_positions[:, 2]  # [num_frames]
            right_toe_heights = right_toe_positions[:, 2]  # [num_frames]
            
            # 计算脚趾垂直速度
            left_toe_velocity = torch.zeros_like(left_toe_heights)
            right_toe_velocity = torch.zeros_like(right_toe_heights)
            
            if num_frames > 1:
                left_toe_velocity[1:] = torch.diff(left_toe_heights) * self.fps  # 垂直速度
                right_toe_velocity[1:] = torch.diff(right_toe_heights) * self.fps  # 垂直速度
            
            # 综合高度和速度判断接触
            # 左脚接触检测
            left_height_contact = left_toe_heights < contact_height_threshold
            left_low_velocity = torch.abs(left_toe_velocity) < velocity_threshold
            contact_sequence[:, 0] = left_height_contact & left_low_velocity
            
            # 右脚接触检测
            right_height_contact = right_toe_heights < contact_height_threshold
            right_low_velocity = torch.abs(right_toe_velocity) < velocity_threshold
            contact_sequence[:, 1] = right_height_contact & right_low_velocity
            
            # 后处理：消除短暂的接触间隙
            contact_sequence = self.smooth_contact_sequence(contact_sequence)
            
            # 转换为numpy数组
            contact_sequence_np = contact_sequence.detach().cpu().numpy()
            
            print(f"接触序列生成完成:")
            print(f"  - 使用脚趾关节: Left[{self.left_toe_idx}], Right[{self.right_toe_idx}]")
            print(f"  - 左脚接触帧数: {contact_sequence_np[:, 0].sum()}")
            print(f"  - 右脚接触帧数: {contact_sequence_np[:, 1].sum()}")
            print(f"  - 左脚高度范围: [{left_toe_heights.min():.3f}, {left_toe_heights.max():.3f}]")
            print(f"  - 右脚高度范围: [{right_toe_heights.min():.3f}, {right_toe_heights.max():.3f}]")
            
            return contact_sequence_np
            
        except Exception as e:
            print(f"接触序列生成失败: {e}")
            import traceback
            traceback.print_exc()
            # 返回默认的接触序列（交替接触）
            import numpy as np
            num_frames = robot_joint_positions.shape[1]
            contact_sequence = np.zeros((num_frames, 2), dtype=bool)
            # 简单的交替接触模式
            for i in range(num_frames):
                if i % 20 < 10:  # 每20帧交替一次
                    contact_sequence[i, 0] = True  # 左脚接触
                else:
                    contact_sequence[i, 1] = True  # 右脚接触
            return contact_sequence
    
    def smooth_contact_sequence(self, contact_sequence, min_contact_duration=3):
        """平滑接触序列，消除短暂的接触间隙
        
        Args:
            contact_sequence: [num_frames, 2] 原始接触序列
            min_contact_duration: 最小接触持续时间（帧数）
            
        Returns:
            smoothed_sequence: [num_frames, 2] 平滑后的接触序列
        """
        smoothed_sequence = contact_sequence.clone()
        num_frames = contact_sequence.shape[0]
        
        for foot_idx in range(2):  # 左脚和右脚
            # 找到所有接触段
            contact_changes = torch.diff(contact_sequence[:, foot_idx].float())
            start_frames = torch.where(contact_changes > 0)[0] + 1
            end_frames = torch.where(contact_changes < 0)[0] + 1
            
            # 处理边界情况
            if contact_sequence[0, foot_idx]:
                start_frames = torch.cat([torch.tensor([0]), start_frames])
            if contact_sequence[-1, foot_idx]:
                end_frames = torch.cat([end_frames, torch.tensor([num_frames])])
            
            # 删除过短的接触段
            for start, end in zip(start_frames, end_frames):
                if end - start < min_contact_duration:
                    smoothed_sequence[start:end, foot_idx] = False
            
            # 填补短暂的间隙
            for i in range(len(start_frames) - 1):
                gap_start = end_frames[i]
                gap_end = start_frames[i + 1]
                if gap_end - gap_start < min_contact_duration:
                    smoothed_sequence[gap_start:gap_end, foot_idx] = True
        
        return smoothed_sequence

    def _calculate_bvh_skeleton_connections(self):
        """计算BVH骨架连接关系 - 基于BVH文件中的真实父子关系"""
        connections = {}
        
        # 如果没有有效的BVH关节索引，返回空连接
        if not hasattr(self, 'valid_bvh_joint_indices'):
            self.skeleton_params['bvh_skeleton_connections'] = connections
            return
        
        print(f"\n🔍 调试BVH关节层次结构:")
        print(f"选中的BVH关节索引: {self.valid_bvh_joint_indices}")
        
        # 遍历选中的BVH关节
        for i, child_idx in enumerate(self.valid_bvh_joint_indices):
            child_name = self.bvh_motion.joint_name[child_idx]
            # 获取这个关节在原始BVH中的父关节索引
            parent_idx_in_original = self.bvh_motion.joint_parent[child_idx]
            
            print(f"  [{i}] {child_name} (BVH[{child_idx}]) -> 父关节: BVH[{parent_idx_in_original}]", end="")
            
            if parent_idx_in_original >= 0:
                parent_name = self.bvh_motion.joint_name[parent_idx_in_original]
                print(f" ({parent_name})")
                
                # 如果父关节也在我们选中的关节中，建立连接
                if parent_idx_in_original in self.valid_bvh_joint_indices:
                    parent_idx_in_selected = self.valid_bvh_joint_indices.index(parent_idx_in_original)
                    connections[i] = parent_idx_in_selected
                    print(f"    ✓ 建立连接: [{i}] -> [{parent_idx_in_selected}]")
                else:
                    print(f"    ✗ 父关节不在选中列表中，跳过连接")
            else:
                print(f" (根关节)")
        
        # 手动添加缺失的逻辑连接
        print(f"\n🔧 检查并添加缺失的手臂连接:")
        missing_connections = self._add_missing_arm_connections(connections)
        if missing_connections:
            print(f"  ✓ 添加了 {len(missing_connections)} 个缺失的连接")
            connections.update(missing_connections)
        else:
            print(f"  ✓ 没有发现缺失的连接")
        
        # 保存到skeleton_params中
        self.skeleton_params['bvh_skeleton_connections'] = connections
        
        print(f"\nBVH骨架连接关系计算完成: {len(connections)} 个连接")
        print(f"完整连接列表:")
        for child, parent in sorted(connections.items()):
            child_name = self.bvh_motion.joint_name[self.valid_bvh_joint_indices[child]]
            parent_name = self.bvh_motion.joint_name[self.valid_bvh_joint_indices[parent]]
            print(f"  [{child}]{child_name} -> [{parent}]{parent_name}")
        
        print(f"关节索引映射验证:")
        for i, joint_idx in enumerate(self.valid_bvh_joint_indices):
            joint_name = self.bvh_motion.joint_name[joint_idx]
            print(f"  索引{i}: BVH[{joint_idx}] = {joint_name}")
    
    def _add_missing_arm_connections(self, connections):
        """添加缺失的手臂连接 - 基于关节名称的逻辑推断"""
        missing_connections = {}
        
        # 建立关节名称到索引的映射
        joint_name_to_idx = {}
        for i, joint_idx in enumerate(self.valid_bvh_joint_indices):
            joint_name = self.bvh_motion.joint_name[joint_idx]
            joint_name_to_idx[joint_name] = i
        
        # 定义期望的连接关系
        expected_connections = [
            ('LeftForeArm', 'LeftShoulder'),
            ('RightForeArm', 'RightShoulder'),
            ('LeftHand', 'LeftForeArm'),
            ('RightHand', 'RightForeArm'),
            ('LeftFoot', 'LeftLeg'),
            ('RightFoot', 'RightLeg'),
            ('LeftToeBase', 'LeftFoot'),
            ('RightToeBase', 'RightFoot'),
        ]
        
        # 检查每个期望的连接
        for child_name, parent_name in expected_connections:
            if child_name in joint_name_to_idx and parent_name in joint_name_to_idx:
                child_idx = joint_name_to_idx[child_name]
                parent_idx = joint_name_to_idx[parent_name]
                
                # 如果这个连接还不存在，添加它
                if child_idx not in connections:
                    missing_connections[child_idx] = parent_idx
                    print(f"    + 添加缺失连接: [{child_idx}]{child_name} -> [{parent_idx}]{parent_name}")
        
        return missing_connections
    


    def run_retargeting(self):
        """运行完整的重定向流程"""
        return self.retargetBVHData()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="BVH动作重定向 - 使用bone_optimizer_gui.py的输出进行动作重定向",
        epilog="""
使用示例:
  python bvh_motion_retarget.py --optimized-bvh data/calc_bvh/unitreeG1/walk_forward_optimized.bvh --iterations 1000

输入要求:
  - 优化后的BVH文件：由bone_optimizer_gui.py生成的BVH文件，包含已经缩放到机器人尺寸的骨骼长度
  - 配置文件：脚本会自动在BVH文件同目录下查找对应的JSON配置文件
  
工作流程:
  1. 运行bone_optimizer_gui.py优化骨骼结构
  2. 使用本脚本对优化后的BVH文件进行动作重定向
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # 必需参数
    parser.add_argument("--optimized-bvh", type=str, default="data/calc_bvh/unitreeG1/bvh_optimization_result_optimized.bvh",
                       help="优化后的BVH文件路径 (由bone_optimizer_gui.py生成)")
    
    # 优化参数
    parser.add_argument("--iterations", type=int, default=200,
                       help="优化迭代次数 (默认: 500)")
    parser.add_argument("--learning-rate", type=float, default=0.01,
                       help="学习率 (默认: 0.01)")
    
    # 损失权重
    parser.add_argument("--keypoint-loss-weight", type=float, default=1.0,
                       help="关键点损失权重 (默认: 1.0)")
    parser.add_argument("--smoothness-loss-weight", type=float, default=0.1,
                       help="平滑损失权重 (默认: 0.1)")
    parser.add_argument("--contact-loss-weight", type=float, default=0.05,
                       help="接触损失权重 (默认: 0.05)")
    parser.add_argument("--foot-height-loss-weight", type=float, default=0.2,
                       help="脚部高度损失权重 (默认: 0.2)")
    
    # 平滑滤波
    parser.add_argument("--apply-filter", action="store_true", default=True,
                       help="应用高斯平滑滤波 (默认: True)")
    parser.add_argument("--kernel-size", type=int, default=7,
                       help="高斯核大小 (默认: 7)")
    parser.add_argument("--sigma", type=float, default=2.0,
                       help="高斯核标准差 (默认: 2.0)")
    
    # 其他选项
    parser.add_argument("--optimize-root", action="store_true", default=True,
                       help="优化根节点位置和旋转 (默认: True)")
    parser.add_argument("--device", type=str, default="cpu", choices=["cpu", "cuda"],
                       help="计算设备 (默认: cpu)")
    parser.add_argument("--no-vis", action="store_true", default=False,
                       help="不显示可视化")
    
    args = parser.parse_args()
    
    # 验证输入文件
    if not os.path.exists(args.optimized_bvh):
        print(f"❌ 错误: 优化后的BVH文件不存在: {args.optimized_bvh}")
        print("\n💡 使用提示:")
        print("1. 请先运行 bone_optimizer_gui.py 进行骨骼拟合和优化")
        print("2. 确保输入的是优化后的BVH文件路径（通常以_optimized.bvh结尾）")
        print("3. 确保在BVH文件同目录下存在相应的配置JSON文件")
        sys.exit(1)
    
    print("🚀 BVH动作重定向")
    print("=" * 50)
    print(f"📁 优化后的BVH文件: {args.optimized_bvh}")
    print(f"💻 计算设备: {args.device}")
    print(f"🔄 优化迭代次数: {args.iterations}")
    print("=" * 50)
    
    try:
        # 创建重定向器并运行
        retargeter = BVHMotionRetargeter(args)
        output_path = retargeter.run_retargeting()
        
        print("\n" + "=" * 50)
        print("✅ 重定向成功完成!")
        print(f"📁 输出文件: {output_path}")
        print("=" * 50)
        
    except FileNotFoundError as e:
        print(f"\n❌ 文件错误: {e}")
        print("\n💡 解决方案:")
        print("1. 检查BVH文件路径是否正确")
        print("2. 确保已运行bone_optimizer_gui.py生成配置文件")
        print("3. 检查配置文件是否在BVH文件同目录下")
        sys.exit(1)
    except ValueError as e:
        print(f"\n❌ 配置错误: {e}")
        print("\n💡 解决方案:")
        print("1. 确保使用的是bone_optimizer_gui.py的输出文件")
        print("2. 检查配置文件格式是否正确")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 重定向失败: {e}")
        import traceback
        print("\n🔍 详细错误信息:")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 