# -*- coding: utf-8 -*-

# Max-<PERSON>ck-Gesellschaft zur Förderung der Wissenschaften e.V. (MPG) is
# holder of all proprietary rights on this computer program.
# You can only use this computer program if you have closed
# a license agreement with MPG or you get the right to use the computer
# program from someone who is authorized to grant you that right.
# Any use of the computer program without a valid license is prohibited and
# liable to prosecution.
#
# Copyright©2019 Max-Planck-Gesellschaft zur Förderung
# der Wissenschaften e.V. (MPG). acting on behalf of its Max Planck Institute
# for Intelligent Systems. All rights reserved.
#
# Contact: <EMAIL>
import os
import sys
import pdb
import os.path as osp

sys.path.append(os.getcwd())


import joblib
import argparse
import numpy as np
from tqdm import tqdm
from pathlib import Path
import glob

dict_keys = ["betas", "dmpls", "gender", "mocap_framerate", "poses", "trans"]

# extract SMPL joints from SMPL-H model
# joints_to_use = np.array(
#     [
#         0,
#         1,
#         2,
#         3,
#         4,
#         5,
#         6,
#         7,
#         8,
#         9,
#         10,
#         11,
#         12,
#         13,
#         14,
#         15,
#         16,
#         17,
#         18,
#         19,
#         20,
#         21,
#         22,
#         37,
#     ]
# )
# joints_to_use = np.arange(0, 156).reshape((-1, 3))[joints_to_use].reshape(-1)

if __name__ == "__main__":
    #### 从 raw_dir 文件夹读取数据集中的每个npz文件，处理后保存到 out_dir 中对应的子目录，键为文件名，值为数据，存储为pt格式文件。
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--raw_dir", type=str, help="dataset directory", default="data/smpl_data/Male1Walking_c3d",
    )
    parser.add_argument(
        "--out_dir", type=str, help="output directory", default="data/processed_data/amass/after_raw"
    )
    parser.add_argument(
        "--keys", type=str, help="data keys", default="walk"
    )

    args = parser.parse_args()
    raw_dir = args.raw_dir
    out_dir = args.out_dir
    keys = args.keys
    # Create the base output directory if it doesn't exist
    os.makedirs(out_dir, exist_ok=True)

    # Find all .npz files recursively in the raw_dir
    # Using Path.rglob for simplicity and compatibility across OS
    raw_path = Path(raw_dir)
    npz_files = list(raw_path.rglob("*.npz"))

    if not npz_files:
        print(f"Warning: No .npz files found in {raw_dir}")
        sys.exit(0)

    print(f"Found {len(npz_files)} .npz files in {raw_dir}. Processing...")

    for npz_file_path in tqdm(npz_files):
        # Skip shape files if necessary (based on original logic)
        if npz_file_path.name == "shape.npz":
            continue

        try:
            # Load data from the .npz file
            data = dict(np.load(npz_file_path, allow_pickle=True))
            data_raw = {keys: data}
            # Perform any necessary processing on 'data' here
            # Example: Select specific joints (if joints_to_use is defined and needed)
            # if 'poses' in data and 'joints_to_use' in globals():
            #    data['poses'] = data['poses'][:, joints_to_use]


            # Determine the relative path to maintain directory structure
            relative_path = npz_file_path.relative_to(raw_path)

            # Construct the output path
            out_file_path = Path(out_dir) / relative_path.with_suffix(".pt")

            # Create the necessary subdirectory in the output directory
            out_file_path.parent.mkdir(parents=True, exist_ok=True)

            # Save the processed data to a .pt file
            joblib.dump(data_raw, out_file_path)
            # print(f"Processed and saved {npz_file_path} to {out_file_path}")

        except Exception as e:
            print(f"Error processing file {npz_file_path}: {e}")


    print(f"Processing complete. Processed files are saved in {out_dir}")
