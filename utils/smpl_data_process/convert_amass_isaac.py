from ast import Try
import torch
import joblib
import matplotlib
matplotlib.use('Agg') # Force non-GUI backend for matplotlib
import matplotlib.pyplot as plt
import numpy as np
from scipy import ndimage
from scipy.spatial.transform import Rotation as sRot
import glob
import os
import sys
import pdb
import os.path as osp
from pathlib import Path

sys.path.append(os.getcwd())

from smpl_sim.smpllib.smpl_mujoco_new import SMPL_BONE_ORDER_NAMES as joint_names
from smpl_sim.smpllib.smpl_local_robot import SMPL_Robot as LocalRobot
import scipy.ndimage.filters as filters
from typing import List, Optional
from tqdm import tqdm
from poselib.skeleton.skeleton3d import SkeletonTree, SkeletonMotion, SkeletonState
import argparse

def run(in_file: str, out_file: str, fps: float):

    robot_cfg = {
        "mesh": False,
        "model": "smpl",
        "upright_start": True,
        "body_params": {},
        "joint_params": {},
        "geom_params": {},
        "actuator_params": {},
    }
    print(robot_cfg)

    smpl_local_robot = LocalRobot(  # useless
        robot_cfg,
        data_dir="data/smpl",
    )

    amass_data = joblib.load(in_file)

    double = False

    mujoco_joint_names = ['Pelvis', 'L_Hip', 'L_Knee', 'L_Ankle', 'L_Toe', 'R_Hip', 'R_Knee', 'R_Ankle', 'R_Toe', 'Torso', 'Spine', 'Chest', 'Neck', 'Head', 
                          'L_Thorax', 'L_Shoulder', 'L_Elbow', 'L_Wrist', 'L_Hand', 'R_Thorax', 'R_Shoulder', 'R_Elbow', 'R_Wrist', 'R_Hand']
    
#     mujoco_joint_names = [
#     'base_link', 'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link', 'leg_l1_link', 'leg_l2_link', 
#     'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link', 'r_shoulder_pitch_link', 'r_shoulder_roll_link', 
#     'r_shoulder_yaw_link', 'r_elbow_link', 'l_shoulder_pitch_link', 'l_shoulder_roll_link', 'l_shoulder_yaw_link', 'l_elbow_link', 'torso', 'neck', 'head'
# ]  # 21

    amass_full_motion_dict = {}
    for key_name in tqdm(amass_data.keys()):
        smpl_data_entry = amass_data[key_name]
        B = smpl_data_entry['pose_aa'].shape[0]

        start, end = 0, 0

        pose_aa = smpl_data_entry['pose_aa'].copy()[start:]     #轴角表示的旋转
        root_trans = smpl_data_entry['trans'].copy()[start:]    #根节点的位移
        B = pose_aa.shape[0]

        beta = smpl_data_entry['beta'].copy() if "beta" in smpl_data_entry else smpl_data_entry['betas'].copy()
        if len(beta.shape) == 2:
            beta = beta[0]

        gender = smpl_data_entry.get("gender", "neutral")

        if isinstance(gender, np.ndarray):
            gender = gender.item()

        if isinstance(gender, bytes):
            gender = gender.decode("utf-8")
        if gender == "neutral":
            gender_number = [0]
        elif gender == "male":
            gender_number = [1]
        elif gender == "female":
            gender_number = [2]
        else:
            import ipdb
            ipdb.set_trace()
            raise Exception("Gender Not Supported!!")

        smpl_2_mujoco = [joint_names.index(q) for q in mujoco_joint_names if q in joint_names]   #获取mujoco需要的关节index，并且安装mujoco排序
        # smpl_2_mujoco = [q for q in range(len(mujoco_joint_names))]    #这里是全要了
        batch_size = pose_aa.shape[0]
        # pose_aa = np.concatenate([pose_aa[:, :66], np.zeros((batch_size, 6))], axis=1)
        pose_aa_mj = pose_aa.reshape(-1, 24, 3)[..., smpl_2_mujoco, :].copy()  # 排序

        num = 1
        if double:
            num = 2
        for idx in range(num):
            pose_quat = sRot.from_rotvec(pose_aa_mj.reshape(-1, 3)).as_quat().reshape(batch_size, 24, 4)  #轴角转四元数

            gender_number, beta[:], gender = [0], 0, "neutral"
            print("using neutral model")

            smpl_local_robot.load_from_skeleton(betas=torch.from_numpy(beta[None,]), gender=gender_number, objs_info=None)
            smpl_local_robot.write_xml("data/mjcf/smpl_humanoid_1.xml")
            skeleton_tree = SkeletonTree.from_mjcf("data/mjcf/smpl_humanoid_1.xml")
            
            # skeleton_tree = SkeletonTree.from_mjcf("phc/data/assets/mjcf/gaoqing_24link.xml")    

            root_trans_offset = torch.from_numpy(root_trans) + skeleton_tree.local_translation[0]  # local_translation： 读取 body 的pos

            ##### 这里原本相当于做retarget，把male参考数据映射到neutral上，现在想映射到gaoqing，感觉很诡异。
            ##### 给关节旋转，和root位置，得到新的旋转和位置信息，相当于保持关节角度一致的retarget
            new_sk_state = SkeletonState.from_rotation_and_root_translation(
                skeleton_tree,  # This is the wrong skeleton tree (location wise) here, but it's fine since we only use the parent relationship here. 
                torch.from_numpy(pose_quat),
                root_trans_offset,
                is_local=True)

            if robot_cfg['upright_start']:
                # 转换坐标系
                pose_quat_global = (sRot.from_quat(new_sk_state.global_rotation.reshape(-1, 4).numpy()) * sRot.from_quat([0.5, 0.5, 0.5, 0.5]).inv()).as_quat().reshape(B, -1, 4)  # should fix pose_quat as well here...

                new_sk_state = SkeletonState.from_rotation_and_root_translation(skeleton_tree, torch.from_numpy(pose_quat_global), root_trans_offset, is_local=False)
                pose_quat = new_sk_state.local_rotation.numpy()

                ############################################################
                # key_name_dump = key_name + f"_{idx}"
                key_name_dump = key_name
                if idx == 1:
                    left_to_right_index = [0, 5, 6, 7, 8, 1, 2, 3, 4, 9, 10, 11, 12, 13, 19, 20, 21, 22, 23, 14, 15, 16, 17, 18]
                    pose_quat_global = pose_quat_global[:, left_to_right_index]
                    pose_quat_global[..., 0] *= -1
                    pose_quat_global[..., 2] *= -1

                    root_trans_offset[..., 1] *= -1
                ############################################################

            new_motion_out = {}
            new_motion_out['pose_quat_global'] = pose_quat_global   # global
            new_motion_out['pose_quat'] = pose_quat   # local
            new_motion_out['trans_orig'] = root_trans
            new_motion_out['root_trans_offset'] = root_trans_offset
            new_motion_out['beta'] = beta
            new_motion_out['gender'] = gender
            new_motion_out['pose_aa'] = pose_aa
            new_motion_out['fps'] = fps
            amass_full_motion_dict[key_name_dump] = new_motion_out
    joblib.dump(amass_full_motion_dict, out_file)
    return

# import ipdb

# ipdb.set_trace()
##### 这里原本相当于做retarget，把male参考数据映射到neutral上
##### 相当于保持关节角度一致的retarget，给参考关节旋转和root位置，计算得到新的旋转和位置信息
##### 最终得到了retarget之后的 全局旋转四元数；局部旋转四元数；原始根关节位移；新的根关节位移；原始的local 关节轴角表示
if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--pkl_path", type=str, default="data/process_data/after_db/walk_LiHang", help="Directory containing input .pkl files")
    parser.add_argument("--out_dir", type=str, default="data/process_data/after_isaac/walk_LiHang", help="Directory to save output .pkl files")
    parser.add_argument("--fps", type=float, default=90.0, help="Frames per second for the motion data")
    args = parser.parse_args()

    # Check if pkl_path is a directory
    if not osp.isdir(args.pkl_path):
        print(f"Error: --pkl_path '{args.pkl_path}' is not a valid directory.")
        sys.exit(1)

    # Create output directory
    out_parent_dir = Path(args.out_dir)
    out_parent_dir.mkdir(parents=True, exist_ok=True)

    # Find all .pkl files in the input directory
    pkl_files = glob.glob(osp.join(args.pkl_path, '*.pkl'))

    if not pkl_files:
        print(f"No .pkl files found in {args.pkl_path}")
        sys.exit(0)

    print(f"Found {len(pkl_files)} .pkl files in {args.pkl_path}")

    for in_file_path in tqdm(pkl_files, desc="Processing files"):
        # Construct output file path
        file_name = osp.basename(in_file_path)
        out_file_path = osp.join(out_parent_dir, file_name)

        print(f"Processing {in_file_path} -> {out_file_path}")
        try:
            run(
                in_file=in_file_path,
                out_file=out_file_path,
                fps=args.fps
            )
        except Exception as e:
            print(f"Error processing {in_file_path}: {e}")

    print(f"Processing complete. Output saved to {out_parent_dir}")
