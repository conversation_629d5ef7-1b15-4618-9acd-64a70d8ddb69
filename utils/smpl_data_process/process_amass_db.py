import glob
import os
import sys
import pdb
import os.path as osp
sys.path.append(os.getcwd())

import numpy as np
import glob
import pickle as pk
import joblib
import torch
import argparse
from pathlib import Path

from tqdm import tqdm
from smpl_sim.utils.transform_utils import convert_aa_to_orth6d
from scipy.spatial.transform import Rotation as sRot
from smpl_sim.smpllib.smpl_parser import SMPL_Parser
from smpl_sim.utils.flags import flags

np.random.seed(1)
left_right_idx = [
    0,
    2,
    1,
    3,
    5,
    4,
    6,
    8,
    7,
    9,
    11,
    10,
    12,
    14,
    13,
    15,
    17,
    16,
    19,
    18,
    21,
    20,
    23,
    22,
]


def left_to_rigth_euler(pose_euler):
    pose_euler[:, :, 0] = pose_euler[:, :, 0] * -1
    pose_euler[:, :, 2] = pose_euler[:, :, 2] * -1
    pose_euler = pose_euler[:, left_right_idx, :]
    return pose_euler


def flip_smpl(pose, trans=None):
    """
    Pose input batch * 72
    """
    curr_spose = sRot.from_rotvec(pose.reshape(-1, 3))
    curr_spose_euler = curr_spose.as_euler("ZXY", degrees=False).reshape(pose.shape[0], 24, 3)
    curr_spose_euler = left_to_rigth_euler(curr_spose_euler)
    curr_spose_rot = sRot.from_euler("ZXY", curr_spose_euler.reshape(-1, 3), degrees=False)
    curr_spose_aa = curr_spose_rot.as_rotvec().reshape(pose.shape[0], 24, 3)
    if trans != None:
        pass
        # target_root_mat = curr_spose.as_matrix().reshape(pose.shape[0], 24, 3, 3)[:, 0]
        # root_mat = curr_spose_rot.as_matrix().reshape(pose.shape[0], 24, 3, 3)[:, 0]
        # apply_mat = np.matmul(target_root_mat[0], np.linalg.inv(root_mat[0]))

    return curr_spose_aa.reshape(-1, 72)


def sample_random_hemisphere_root():
    rot = np.random.random() * np.pi * 2
    pitch = np.random.random() * np.pi / 3 + np.pi
    r = sRot.from_rotvec([pitch, 0, 0])
    r2 = sRot.from_rotvec([0, rot, 0])
    root_vec = (r * r2).as_rotvec()
    return root_vec


def sample_seq_length(seq, tran, seq_length=150):
    if seq_length != -1:
        num_possible_seqs = seq.shape[0] // seq_length
        max_seq = seq.shape[0]

        start_idx = np.random.randint(0, 10)
        start_points = [max(0, max_seq - (seq_length + start_idx))]

        for i in range(1, num_possible_seqs - 1):
            start_points.append(i * seq_length + np.random.randint(-10, 10))

        if num_possible_seqs >= 2:
            start_points.append(max_seq - seq_length - np.random.randint(0, 10))

        seqs = [seq[i:(i + seq_length)] for i in start_points]
        trans = [tran[i:(i + seq_length)] for i in start_points]
    else:
        seqs = [seq]
        trans = [tran]
        start_points = []
    return seqs, trans, start_points


def get_random_shape(batch_size):
    shape_params = torch.rand(1, 10).repeat(batch_size, 1)
    s_id = torch.tensor(np.random.normal(scale=1.5, size=(3)))
    shape_params[:, :3] = s_id
    return shape_params



def count_consec(lst):
    consec = [1]
    for x, y in zip(lst, lst[1:]):
        if x == y - 1:
            consec[-1] += 1
        else:
            consec.append(1)
    return consec



def fix_height_smpl_vanilla(pose_aa, th_trans, th_betas, gender, seq_name):
    # no filtering, just fix height
    gender = gender.item() if isinstance(gender, np.ndarray) else gender
    if isinstance(gender, bytes):
        gender = gender.decode("utf-8")

    if gender == "neutral":
        smpl_parser = smpl_parser_n
    elif gender == "male":
        smpl_parser = smpl_parser_m
    elif gender == "female":
        smpl_parser = smpl_parser_f
    else:
        print(gender)
        raise Exception("Gender Not Supported!!")

    batch_size = pose_aa.shape[0]
    verts, jts = smpl_parser.get_joints_verts(pose_aa[0:1], th_betas.repeat((1, 1)), th_trans=th_trans[0:1])

    # vertices = verts[0].numpy()
    gp = torch.min(verts[:, :, 2])

    # if gp < 0:
    th_trans[:, 2] -= gp

    return th_trans

def axis_angle_to_euler_with_zero_roll(axis_angles):
    # Step 1: 计算每个轴角表示的旋转角度 theta
    thetas = np.linalg.norm(axis_angles, axis=1, keepdims=True)
    mask = thetas >= 1e-6  # 用于处理接近零的旋转情况

    # Step 2: 处理非零旋转的轴角表示
    normalized_axes = np.zeros_like(axis_angles)  # 初始化为零
    normalized_axes = np.where(mask, axis_angles / thetas, axis_angles)  # 直接使用np.where处理条件

    # Step 3: 将轴角转换为四元数
    quaternions = sRot.from_rotvec(normalized_axes * thetas).as_quat()

    # Step 4: 将四元数转换为欧拉角（ZYX 旋转顺序）
    eulers = sRot.from_quat(quaternions).as_euler('ZYX')

    # Step 5: 设置 roll 角度为 0
    eulers[:, 2] = 0

    # Step 6: 将修改后的欧拉角转换回四元数，再转换为轴角表示
    axis_angle_final = sRot.from_euler('ZYX', eulers).as_rotvec()

    return axis_angle_final


def process_single_pt_data(k, v):
    # Process data from a single .pt file (loaded as v)
    # k is the identifier (e.g., relative path string)
    seq_name = "0-" + k # Maintain the "0-" prefix if needed for compatibility, or adjust
    try:
        betas = v["betas"]  # 控制人体形状如身高、体重、肌肉分布等。
        gender = v["gender"]
        amass_fr = v.get("mocap_frame_rate", v.get("mocap_framerate"))
        skip = int(amass_fr / target_fr) if target_fr > 0 else 1
        if skip == 0:
            skip = 1

        amass_pose = v["poses"][::skip]  # v["poses"].shape:(4052, 156) ->amass_pose.shape(1013, 156)  HDM_dg_03-09_03_120: Frams:4052, SMPL+H joints: 52 轴角表示法（axis-angle representation）
        amass_trans = v["trans"][::skip] # v["trans"].shape:(4052, 3) -> amass_trans.shape(1013, 3)  translation

        bound = amass_pose.shape[0]

        # --- Occlusion Handling (Commented out as it might not apply well to single files) ---
        # original_key = k # Need the original key before adding "0-"
        # if original_key in amass_occlusion:
        #     issue = amass_occlusion[original_key]["issue"]
        #     if (issue == "sitting" or issue == "airborne") and "idxes" in amass_occlusion[original_key]:
        #         # Note: Occlusion bound might be based on original framerate, careful with skip!
        #         bound = amass_occlusion[original_key]["idxes'][0] # This bound is calculated assuming 30 FPS.....
        #         if bound < 10:
        #             print(f"Skipping {k} due to occlusion bound too small: {bound}")
        #             return None
        #         bound = bound // skip # Adjust bound for skipped frames? Needs verification.
        #         bound = min(bound, amass_pose.shape[0]) # Ensure bound is not out of range after skip
        #     else:
        #         print(f"Skipping {k} due to irrecoverable issue: {issue}")
        #         return None
        # --- End Occlusion Handling ---

        seq_length = amass_pose.shape[0]
        if seq_length < 10:
            print(f"Skipping {k} due to short sequence length: {seq_length}")
            return None # Skip short sequences

        with torch.no_grad():
            amass_pose = amass_pose[:bound]
            batch_size = amass_pose.shape[0]
            # Ensure pose has at least 66 elements before slicing
            if amass_pose.shape[1] >= 66:
                 amass_pose = np.concatenate([amass_pose[:, :66], np.zeros((batch_size, 6))], axis=1) # We use SMPL and not SMPLH
            elif amass_pose.shape[1] < 72: # Pad if less than 72 (e.g. only 66 were present)
                 padding_needed = 72 - amass_pose.shape[1]
                 amass_pose = np.concatenate([amass_pose, np.zeros((batch_size, padding_needed))], axis=1)
            # else: it's already 72 or more, use as is or truncate if needed, assuming first 72 are SMPL
            amass_pose = amass_pose[:, :72]

            pose_aa = torch.tensor(amass_pose)
            amass_trans = torch.tensor(amass_trans[:bound])
            # Ensure betas is treated correctly (might be single frame or repeated)
            if betas.ndim == 2 and betas.shape[0] > 1:
                betas = betas[0] # Take the first frame's betas if multiple are present
            if betas.ndim == 1:
                 betas = betas[np.newaxis, :] # Add batch dim if needed by fix_height

            betas = torch.from_numpy(betas).float() # Ensure float tensor

            # print(f"Processing {k}: pose_aa {pose_aa.shape}, amass_trans {amass_trans.shape}, betas {betas.shape}, gender {gender}")
            amass_trans = fix_height_smpl_vanilla(
                pose_aa=pose_aa,
                th_betas=betas,
                th_trans=amass_trans,
                gender=gender,
                seq_name=seq_name, # Use the modified seq_name
            )

            pose_seq_6d = convert_aa_to_orth6d(pose_aa).reshape(batch_size, -1, 6)

            processed_data = {
                "pose_aa": pose_aa.numpy(),
                "pose_6d": pose_seq_6d.numpy(),
                "trans": amass_trans.numpy(),
                "beta": betas.numpy().flatten(), # Flatten beta back to 1D
                "seq_name": seq_name, # Use the modified seq_name
                "gender": gender,
            }
            return processed_data

    except Exception as e:
        print(f"Error processing {k}: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    #### 读取 pt_path 文件夹下的所有 pt 文件，对每个文件进行处理：轴角后9维度置零，计算6D rotation, 修复离地问题，按采样率等间隔采样，最后保存处理后的数据到 out_dir 中对应的子目录和文件名，后缀改为 .pkl。
    #### 最终数据包含 local坐标每个关节旋转的轴角表示 pose_aa; 旋转的6D表示 pose_6d；根节点位移 trans；序列名字 seq_name； 形状参数 beta;性别参数 gender
    parser = argparse.ArgumentParser()
    parser.add_argument("--pt_path", type=str, default="data/process_data/after_raw/walk_LiHang", help="Input directory containing .pt files")
    parser.add_argument("--out_dir", type=str, default="data/process_data/after_db/walk_LiHang", help="Output directory for processed .pkl files")
    parser.add_argument("--target_fr", type=int, default=90, help="Target framerate for resampling")
    parser.add_argument("--keys", type=str, default="walk", help="data keys")
    args = parser.parse_args()

    np.random.seed(0)
    target_fr = args.target_fr # Use target_fr from args

    # Initialize SMPL parsers once
    try:
        smpl_parser_n = SMPL_Parser(model_path="data/smpl", gender="neutral", use_pca=False, create_transl=False)
        smpl_parser_m = SMPL_Parser(model_path="data/smpl", gender="male", use_pca=False, create_transl=False)
        smpl_parser_f = SMPL_Parser(model_path="data/smpl", gender="female", use_pca=False, create_transl=False)
    except Exception as e:
        print(f"Error initializing SMPL parsers: {e}. Make sure the 'data/smpl' directory exists and contains the models.")
        sys.exit(1)

    in_dir_path = Path(args.pt_path)
    out_dir_path = Path(args.out_dir)
    keys = args.keys
    out_dir_path.mkdir(parents=True, exist_ok=True) # Create base output directory

    pt_files = list(in_dir_path.rglob("*.pt"))

    if not pt_files:
        print(f"Warning: No .pt files found in {in_dir_path}")
        sys.exit(0)

    print(f"Found {len(pt_files)} .pt files in {in_dir_path}. Processing...")

    processed_count = 0
    skipped_count = 0
    for pt_file in tqdm(pt_files):
        try:
            # Load data from the .pt file
            # Assuming the .pt file contains the dictionary directly
            data_dict = joblib.load(pt_file)
            data_dict = data_dict[next(iter(data_dict.keys()))]

            # Derive a key/identifier from the file path relative to the input dir
            relative_path = pt_file.relative_to(in_dir_path)
            file_key = str(relative_path.with_suffix('')) # Use relative path w/o suffix as key

            # Process the loaded data
            processed_data = process_single_pt_data(file_key, data_dict)

            if processed_data is not None:
                # Construct the output path
                out_file_path = out_dir_path / relative_path.with_suffix(".pkl")

                # Create the necessary subdirectory in the output directory
                out_file_path.parent.mkdir(parents=True, exist_ok=True)

                # Save the processed data to a .pkl file
                processed_data_raw = {keys: processed_data}
                joblib.dump(processed_data_raw, out_file_path)
                processed_count += 1
            else:
                 skipped_count += 1
                 # print(f"Skipped processing for {pt_file}")

            if flags.debug and processed_count >= 10:
                print("Debug mode: Stopping after processing 10 files.")
                break

        except Exception as e:
            print(f"Error loading or processing file {pt_file}: {e}")
            skipped_count += 1

    print(f"Processing complete. {processed_count} files processed and saved to {out_dir_path}. {skipped_count} files skipped.")

    # Removed splitting logic and saving train/test/valid files
