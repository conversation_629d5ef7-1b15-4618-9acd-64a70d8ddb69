import torch
from easydict import EasyDict
from ..base.motion_lib_cfg import MotionLibCfg

# unitreeG1配置文件
class unitreeG1RetargetMotionLibCfg(MotionLibCfg):
    motion_file = None
    device = "cuda:0"
    fix_height = True
    masterfoot_config = None
    min_length = -1
    im_eval = False
    multi_thread = False
    mjcf_file = 'resources/robots/unitreeG1/mjcf/unitree_G1_retarget.xml'
    urdf_file = 'resources/robots/unitreeG1/urdf/unitree_G1.urdf'

    # 是否优化根节点位置和旋转
    optimize_root = True

    # --- 脚部关节配置 ---
    left_toe_name = 'foot_l1_link'
    right_toe_name = 'foot_r1_link'
    
    # 脚部关节索引（用于接触检测）
    left_foot_joint_indices = [5, 6, 7]   # leg_l5_link, leg_l6_link, foot_l1_link 在joint_names中的索引
    right_foot_joint_indices = [12, 13, 14]  # leg_r5_link, leg_r6_link, foot_r1_link 在joint_names中的索引
    
    # 接触检测参数
    contact_height_threshold = 0.15
    contact_velocity_threshold = 0.05

    # 选择偏置的旋转参考系，True: 相对于父关节旋转, False: 相对于子关节旋转
    bias_relative_to_parent = True

    root_bias = torch.tensor([0, 0, 0])
    ankle_bias = torch.tensor([0, 0, 0])

    # retarget用的xml跟imitate用的xml可能不一样，需要指定imitation_link_name
    imitation_link_name = ['base_link',
                          'leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link_knee', 'leg_l5_link', 'leg_l6_link',
                          'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link_knee', 'leg_r5_link', 'leg_r6_link',
                          'waist_yaw_link', 'waist_roll_link', 'torso_link',
                          'arm_l1_link', 'arm_l2_link', 'arm_l3_link', 'arm_l4_link', 'arm_l5_link', 'arm_l6_link', 'arm_l7_link',
                          'arm_r1_link', 'arm_r2_link', 'arm_r3_link', 'arm_r4_link', 'arm_r5_link', 'arm_r6_link', 'arm_r7_link']

    isaac_vis_link_name = ['leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link_knee', 'leg_l5_link', 'leg_l6_link',
                          'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link_knee', 'leg_r5_link', 'leg_r6_link',
                          'waist_yaw_link', 'waist_roll_link', 'torso_link',
                          'arm_l1_link', 'arm_l2_link', 'arm_l3_link', 'arm_l4_link', 'arm_l5_link', 'arm_l6_link', 'arm_l7_link',
                          'arm_r1_link', 'arm_r2_link', 'arm_r3_link', 'arm_r4_link', 'arm_r5_link', 'arm_r6_link', 'arm_r7_link']

    task = "unitree_G1_retarget"
    
    # 所有的关节节点（包括虚拟节点，总共30个）
    joint_names = ['base_link',  # floating base
                   'leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link_knee', 'leg_l5_link', 'leg_l6_link',
                   'foot_l1_link',  # 虚拟节点
                   'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link_knee', 'leg_r5_link', 'leg_r6_link',
                   'foot_r1_link',  # 虚拟节点
                   'waist_yaw_link', 'waist_roll_link', 'torso_link',
                   'arm_l1_link', 'arm_l2_link', 'arm_l3_link', 'arm_l4_link', 'arm_l5_link', 'arm_l6_link', 'arm_l7_link',
                   'arm_r1_link', 'arm_r2_link', 'arm_r3_link', 'arm_r4_link', 'arm_r5_link', 'arm_r6_link', 'arm_r7_link']

    # 所有实际的关节节点（不包括虚拟节点）
    pos_names = ['leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link_knee', 'leg_l5_link', 'leg_l6_link',  # 左腿6个关节
                 'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link_knee', 'leg_r5_link', 'leg_r6_link',  # 右腿6个关节
                 'waist_yaw_link', 'waist_roll_link', 'torso_link',  # 腰部3个关节
                 'arm_l1_link', 'arm_l2_link', 'arm_l3_link', 'arm_l4_link', 'arm_l5_link', 'arm_l6_link', 'arm_l7_link',  # 左臂7个关节
                 'arm_r1_link', 'arm_r2_link', 'arm_r3_link', 'arm_r4_link', 'arm_r5_link', 'arm_r6_link', 'arm_r7_link']  # 右臂7个关节

    limb_names = {
        "leg_l": ['leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link_knee', 'leg_l5_link', 'leg_l6_link'],
        "leg_r": ['leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link_knee', 'leg_r5_link', 'leg_r6_link'],
        "waist": ['waist_yaw_link', 'waist_roll_link', 'torso_link'],
        "arm_l": ['arm_l1_link', 'arm_l2_link', 'arm_l3_link', 'arm_l4_link', 'arm_l5_link', 'arm_l6_link', 'arm_l7_link'],
        "arm_r": ['arm_r1_link', 'arm_r2_link', 'arm_r3_link', 'arm_r4_link', 'arm_r5_link', 'arm_r6_link', 'arm_r7_link']
    }



    # BVH关键点与机器人关键点的对应关系配置
    bvh_joint_correspondence = {
        # 左腿
        'leg_l1_link': 'LeftUpLeg',    # 左胯关节
        'leg_l4_link_knee': 'LeftLeg',    # 左膝关节
        'leg_l6_link': 'LeftFoot',   # 左踝关节
        'foot_l1_link': 'LeftToeBase',    # 左脚趾
        
        # 右腿
        'leg_r1_link': 'RightUpLeg',    # 右胯关节
        'leg_r4_link_knee': 'RightLeg',    # 右膝关节
        'leg_r6_link': 'RightFoot',   # 右踝关节
        'foot_r1_link': 'RightToeBase',    # 右脚趾
        
        # 左臂
        'arm_l2_link': 'LeftArm',     # 左肩关节
        'arm_l4_link': 'LeftForeArm',     # 左肘关节
        'arm_l7_link': 'LeftHand',     # 左腕关节
        
        # 右臂
        'arm_r2_link': 'RightArm',     # 右肩关节
        'arm_r4_link': 'RightForeArm',     # 右肘关节
        'arm_r7_link': 'RightHand'      # 右腕关节
    }

    # quick walk
    # bvh_joint_correspondence = {
    #     # 左腿
    #     'leg_l1_link': 'lfemur',    # 左胯关节
    #     'leg_l4_link_knee': 'ltibia',    # 左膝关节
    #     'leg_l6_link': 'lfoot',   # 左踝关节
    #     'foot_l1_link': 'ltoes',    # 左脚趾
        
    #     # 右腿
    #     'leg_r1_link': 'rfemur',    # 右胯关节
    #     'leg_r4_link_knee': 'rtibia',    # 右膝关节
    #     'leg_r6_link': 'rfoot',   # 右踝关节
    #     'foot_r1_link': 'rtoes',    # 右脚趾
        
    #     # 左臂
    #     'arm_l2_link': 'lhumerus',     # 左肩关节
    #     'arm_l4_link': 'lradius',     # 左肘关节
    #     'arm_l7_link': 'lhand',     # 左腕关节
        
    #     # 右臂
    #     'arm_r2_link': 'rhumerus',     # 右肩关节
    #     'arm_r4_link': 'rradius',     # 右肘关节
    #     'arm_r7_link': 'rhand'      # 右腕关节
    # }

    # 用于FK计算的关节选择
    joint_pick = list(bvh_joint_correspondence.keys())
    joint_pick_bias = list(bvh_joint_correspondence.keys())

    # T-Pose bias:
    __dof_pos__ = {
        # 左腿关节
        'leg_l1_link': 0,
        'leg_l2_link': 0,
        'leg_l3_link': 0,
        'leg_l4_link_knee': 0,
        'leg_l5_link': 0,
        'leg_l6_link': 0,
        
        # 右腿关节
        'leg_r1_link': 0,
        'leg_r2_link': 0,
        'leg_r3_link': 0,
        'leg_r4_link_knee': 0,
        'leg_r5_link': 0,
        'leg_r6_link': 0,
        
        # 腰部关节
        'waist_yaw_link': 0,
        'waist_roll_link': 0,
        'torso_link': 0,
        
        # 左臂关节 - T-pose应该是手臂展平在身体两侧
        'arm_l1_link': 0,
        'arm_l2_link': torch.pi * 0.5,  # 左肩关节，手臂向外展开（水平）
        'arm_l3_link': 0,
        'arm_l4_link': torch.pi * 0.5,  # 左肘关节，手臂伸直（补偿角度）
        'arm_l5_link': -torch.pi * 0.5,
        'arm_l6_link': 0,
        'arm_l7_link': 0,
        
        # 右臂关节 - T-pose应该是手臂展平在身体两侧
        'arm_r1_link': 0,
        'arm_r2_link': -torch.pi * 0.5,  # 右肩关节，手臂向外展开（水平）
        'arm_r3_link': 0,
        'arm_r4_link': torch.pi * 0.5,  # 右肘关节，手臂伸直（补偿角度）
        'arm_r5_link': torch.pi * 0.5,
        'arm_r6_link': 0,
        'arm_r7_link': 0,
    }
    # Note: dof_pos will be moved to correct device in motion_lib_cfg.py
    dof_pos = torch.zeros((1, len(pos_names)))
    for key in list(__dof_pos__.keys()):
        dof_pos[:, pos_names.index(key)] = __dof_pos__[key]


    # 关节权重配置，用于损失函数计算
    joint_weights = {
        # 左腿
        'leg_l1_link': 1.0,     # 左胯关节
        'leg_l4_link_knee': 1.0,    # 左膝关节
        'leg_l6_link': 1.0,    # 左踝关节
        'foot_l1_link': 1.0,   # 左脚趾
        
        # 右腿
        'leg_r1_link': 1.0,     # 右胯关节
        'leg_r4_link_knee': 1.0,    # 右膝关节
        'leg_r6_link': 1.0,    # 右踝关节
        'foot_r1_link': 1.0,   # 右脚趾
        
        # 左臂
        'arm_l2_link': 1.0,     # 左肩关节
        'arm_l4_link': 1.0,     # 左肘关节
        'arm_l7_link': 1.0,      # 左腕关节
        
        # 右臂
        'arm_r2_link': 1.0,    # 右肩关节
        'arm_r4_link': 1.0,    # 右肘关节
        'arm_r7_link': 1.0      # 右腕关节
    }

    foot_sample_points_local_offsets = [
        # 角落点
        [ 0.02,   0.035, -0.02], # Front-Left
        [ 0.02,  -0.035, -0.02], # Front-Right
        [-0.16,   0.035, -0.02], # Back-Left
        [-0.16,  -0.035, -0.02], # Back-Right
        # 正方向点
        [ 0.04,   0.0,   -0.02], # 正前方
        [-0.18,   0.0,   -0.02], # 正后方
        [-0.07,   0.04,  -0.02], # 正左侧
        [-0.07,  -0.04,  -0.02], # 正右侧
    ]

    # 虚拟节点配置  用于fit_ik时运算fk
    extend_node_dict = EasyDict({
        "extend_hand": {
            "using": False,
            "node_names": ["left_hand_link", "right_hand_link"],
            "parent_index": [20, 16],
            "pos": [[0.17, 0, 0], [0.17, 0, 0]],
            "quat": [[1, 0, 0, 0], [1, 0, 0, 0]]
        },
        "extend_foot_right": {
            "using": False,
            "node_names": ["left_foot_link_right", "right_foot_link_right"],
            "parent_index": [12, 6],
            "pos": [[0.1, -0.04, -0.03], [0.1, -0.04, -0.03]],
            "quat": [[1, 0, 0, 0], [1, 0, 0, 0]]
        },
        "extend_foot_left": {
            "using": False,
            "node_names": ["left_foot_link_left", "right_foot_link_left"],
            "parent_index": [23, 24],
            "pos": [[0, 0.08, 0], [0, 0.08, 0]],
            "quat": [[1, 0, 0, 0], [1, 0, 0, 0]]
        },
        "extend_head": {
            "using": False,
            "node_names": ["head_link"],
            "parent_index": [0],
            "pos": [[0, 0, 0.3]],
            "quat": [[1, 0, 0, 0]]
        },
        "virtual_nodes": ["foot_l1_link", "foot_r1_link"]  # 只包含实际使用的虚拟节点
    })