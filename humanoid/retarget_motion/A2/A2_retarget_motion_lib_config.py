import torch
from easydict import EasyDict
from ..base.motion_lib_cfg import MotionLibCfg

# A2配置文件
class A2RetargetMotionLibCfg(MotionLibCfg):
    motion_file = None
    device = "cuda:0"
    fix_height = False
    masterfoot_config = None
    min_length = -1
    im_eval=False
    multi_thread = False
    mjcf_file = 'resources/robots/A2/mjcf/A2_retarget.xml'
    urdf_file = 'resources/robots/A2/urdf/A2_retarget.urdf'

    # 机器人根节点位置偏移，x y z
    root_bias = torch.tensor([0, 0, -0.2]).float()
    # 脚踝节点位置偏移，x y z
    ankle_bias = torch.tensor([0, 0, 0.0]).float()

    # retarget用的xml跟imitate用的xml可能不一样，需要指定imitation_link_name
    imitation_link_name = ['base_link',
                  'leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link',
                  'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link', 
                  'left_arm_link01', 'left_arm_link02', 'left_arm_link03', 'left_arm_link04', 'left_arm_link05', 'left_arm_link06', 'left_arm_link07', 
                  'right_arm_link01', 'right_arm_link02', 'right_arm_link03', 'right_arm_link04', 'right_arm_link05', 'right_arm_link06', 'right_arm_link07']
    
    isaac_vis_link_name = ['leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link', 
                  'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link', 
                  'left_arm_link01', 'left_arm_link02', 'left_arm_link03', 'left_arm_link04', 'left_arm_link05', 'left_arm_link06', 'left_arm_link07', 
                  'right_arm_link01', 'right_arm_link02', 'right_arm_link03', 'right_arm_link04', 'right_arm_link05', 'right_arm_link06', 'right_arm_link07']


    # 所有的关节节点
    joint_names = ['base_link',
                        'leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link',
                        'foot_l1_link',
                        'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link',
                        'foot_r1_link',
                        'left_arm_link01', 'left_arm_link02', 'left_arm_link03', 'left_arm_link04', 'left_arm_link05',
                        'left_arm_link06', 'left_arm_link07',
                        'right_arm_link01', 'right_arm_link02', 'right_arm_link03', 'right_arm_link04',
                        'right_arm_link05', 'right_arm_link06', 'right_arm_link07',
                        'head_link']

    # 需要考察的节点
    pos_names = \
        ['leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link',  # 5
         'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link',  # 11
         'left_arm_link01', 'left_arm_link02', 'left_arm_link03', 'left_arm_link04', 'left_arm_link05',
         'left_arm_link06', 'left_arm_link07',  # 18
         'right_arm_link01', 'right_arm_link02', 'right_arm_link03', 'right_arm_link04', 'right_arm_link05',
         'right_arm_link06', 'right_arm_link07']  # 25

    # 关节链条
    limb_names = \
        {"leg_l": ['leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link'],
         "leg_r": ['leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link'],
         "arm_l": ['left_arm_link01', 'left_arm_link02', 'left_arm_link03', 'left_arm_link04', 'left_arm_link05',
                   'left_arm_link06', 'left_arm_link07'],
         "arm_r": ['right_arm_link01', 'right_arm_link02', 'right_arm_link03', 'right_arm_link04', 'right_arm_link05',
                   'right_arm_link06', 'right_arm_link07']}

    # 选中用于解算ik的节点
    joint_pick = ['base_link',
                       'leg_l4_link', 'leg_l5_link', 'foot_l1_link',
                       'leg_r4_link', 'leg_r5_link', 'foot_r1_link',
                       'left_arm_link02', 'left_arm_link04', 'left_arm_link07',
                       'right_arm_link02', 'right_arm_link04', 'right_arm_link07']

    # 选中用于解算ik的节点
    joint_pick_bias = [
        'leg_l4_link', 'leg_l5_link', 'foot_l1_link',
        'leg_r4_link', 'leg_r5_link', 'foot_r1_link',
        'left_arm_link02', 'left_arm_link04', 'left_arm_link07',
        'right_arm_link02', 'right_arm_link04', 'right_arm_link07'
    ]
    # 将xml中的默认位置转为T-Pose 需要添加的关节角度偏置:
    __dof_pos__ = {
        # 'left_shoulder_roll_link': torch.pi * 0.5,
        # 'right_shoulder_roll_link': -(torch.pi * 0.5),
        # 'left_elbow_link': torch.pi * 0.5,
        # 'right_elbow_link': torch.pi * 0.5,
        'left_arm_link05': torch.pi * 0.5,
        'right_arm_link05': torch.pi * 0.5,
    }
    dof_pos = torch.zeros((1, len(pos_names)))
    for key in list(__dof_pos__.keys()):
        dof_pos[:, pos_names.index(key)] = __dof_pos__[key]

    # 以下我没有用到，你可以尝试一下效果
    extend_node_dict = EasyDict({
        "extend_hand":{
            "using": False,  #是否为机器人添加手
            "node_names":["left_hand_link", "right_hand_link"], #添加虚拟body，xml中body的name
            "parent_index":[20,16],   #新增body的父节点
            "pos":[[0.17, 0, 0], [0.17, 0, 0]],  #xml中body的 pos
            "quat":[[1, 0, 0, 0], [1, 0, 0, 0]]  #xml中body的 quat
        },
        "extend_foot_right":{
            "using": False,
            "node_names":["left_foot_link_right", "right_foot_link_right"], #添加虚拟body，xml中body的name
            "parent_index":[12, 6],   #新增body的父节点
            "pos":[[0.1,-0.04,-0.03], [0.1,-0.04,-0.03]],  #xml中body的 pos
            "quat":[[1, 0, 0, 0], [1, 0, 0, 0]]  #xml中body的 quat
        },
        "extend_foot_left":{
            "using": False,
            "node_names":["left_foot_link_left", "right_foot_link_left"], #添加虚拟body，xml中body的name
            "parent_index":[23, 24],   #新增body的父节点
            "pos":[[0,0.08,0], [0,0.08,0]],  #xml中body的 pos
            "quat":[[1, 0, 0, 0], [1, 0, 0, 0]]  #xml中body的 quat
        },
        "extend_head":{
            "using": False,
            "node_names":["head_link"], #添加虚拟body，xml中body的name
            "parent_index":[0],   #新增body的父节点
            "pos":[[0, 0, 0.3]],  #xml中body的 pos
            "quat":[[1, 0, 0, 0]]  #xml中body的 quat
        },
    })
