import torch
from easydict import EasyDict
from ..base.motion_lib_cfg import MotionLibCfg

# dobot配置文件
class dobotRetargetMotionLibCfg(MotionLibCfg):
    motion_file = None
    device = "cuda:0"
    fix_height = False
    masterfoot_config = None
    min_length = -1
    im_eval = False
    multi_thread = False
    mjcf_file = 'resources/robots/yj/mjcf/dobot_hand.xml'
    urdf_file = 'resources/robots/yj/urdf/dobot_hand.urdf'

    root_bias = torch.tensor([0, 0, -0.1])
    ankle_bias = torch.tensor([0, 0, -0.05])
    # root_bias = torch.tensor([0.0, 0.0, 0.0])
    # ankle_bias = torch.tensor([0.0, 0.0, 0.0])

    # retarget用的xml跟imitate用的xml可能不一样，需要指定imitation_link_name
    imitation_link_name = ['pelvis',
                           'leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link',#6
                           'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link',#12
                           'uidx_arm_l1_link', 'left_shoulder_roll_link', 'left_shoulder_yaw_link', 'uidx_arm_l2_link',
                           'left_elbow_roll_link', 'left_wrist_pitch_link', 'left_wrist_roll_link',#19
                           'uidx_arm_r1_link', 'right_shoulder_roll_link', 'right_shoulder_yaw_link', 'uidx_arm_r2_link',
                           'right_elbow_roll_link', 'right_wrist_pitch_link', 'right_wrist_roll_link',]# 26

    isaac_vis_link_name = ['leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link',#5
                           'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link',#11
                           'uidx_arm_l1_link', 'left_shoulder_roll_link', 'left_shoulder_yaw_link', 'uidx_arm_l2_link',#15
                           'left_elbow_roll_link', 'left_wrist_pitch_link', 'left_wrist_roll_link',#18
                           'uidx_arm_r1_link', 'right_shoulder_roll_link', 'right_shoulder_yaw_link', 'uidx_arm_r2_link',#22
                           'right_elbow_roll_link', 'right_wrist_pitch_link', 'right_wrist_roll_link',]#25

    task = "dobot_retarget"
    joint_names = ['pelvis',
                        'leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link',
                        'foot_l1_link',
                        'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link',
                        'foot_r1_link',
                        'uidx_arm_l1_link', 'left_shoulder_roll_link', 'left_shoulder_yaw_link', 'uidx_arm_l2_link',
                        'left_elbow_roll_link',
                        'left_wrist_pitch_link', 'left_wrist_roll_link',
                        'uidx_arm_r1_link', 'right_shoulder_roll_link', 'right_shoulder_yaw_link', 'uidx_arm_r2_link',
                        'right_elbow_roll_link',
                        'right_wrist_pitch_link', 'right_wrist_roll_link',
                        'head_pitch_link']
    pos_names = \
        ['leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link',  # 5
         'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link',  # 11
         'uidx_arm_l1_link', 'left_shoulder_roll_link', 'left_shoulder_yaw_link', 'uidx_arm_l2_link',
         'left_elbow_roll_link', 'left_wrist_pitch_link', 'left_wrist_roll_link',  # 18
         'uidx_arm_r1_link', 'right_shoulder_roll_link', 'right_shoulder_yaw_link', 'uidx_arm_r2_link',
         'right_elbow_roll_link', 'right_wrist_pitch_link', 'right_wrist_roll_link']  # 25

    limb_names = \
        {"leg_l": ['leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link'],
         "leg_r": ['leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link'],
         "arm_l": ['uidx_arm_l1_link', 'left_shoulder_roll_link', 'left_shoulder_yaw_link', 'uidx_arm_l2_link',
                   'left_elbow_roll_link', 'left_wrist_pitch_link', 'left_wrist_roll_link', ],
         "arm_r": ['uidx_arm_r1_link', 'right_shoulder_roll_link', 'right_shoulder_yaw_link', 'uidx_arm_r2_link',
                   'right_elbow_roll_link', 'right_wrist_pitch_link', 'right_wrist_roll_link']}

    #
    joint_pick = ['pelvis',
                       'leg_l4_link', 'leg_l6_link', 'foot_l1_link',
                       'leg_r4_link', 'leg_r6_link', 'foot_r1_link',                           # 6
                       'left_shoulder_roll_link', 'uidx_arm_l2_link', 'left_wrist_roll_link',  # 9
                       'right_shoulder_roll_link', 'uidx_arm_r2_link', 'right_wrist_roll_link']

    joint_pick_bias = [
        'leg_l4_link', 'leg_l6_link', 'foot_l1_link',
        'leg_r4_link', 'leg_r6_link', 'foot_r1_link',
        'left_shoulder_roll_link', 'uidx_arm_l2_link', 'left_wrist_roll_link',
        'right_shoulder_roll_link', 'uidx_arm_r2_link', 'right_wrist_roll_link',
    ]


    # T-Pose bias:
    __dof_pos__ = {
                    'left_shoulder_roll_link':   torch.pi*0.5 -0.18,
                    'right_shoulder_roll_link': -(torch.pi*0.5 -0.18),
                    'uidx_arm_l2_link':         torch.pi*0.5 -0.1,
                    'uidx_arm_r2_link':         torch.pi*0.5 -0.1,
               }
    dof_pos = torch.zeros((1, len(pos_names)))
    for key in list(__dof_pos__.keys()):
        dof_pos[:,pos_names.index(key)] = __dof_pos__[key]

    extend_node_dict = EasyDict({
        "extend_hand": {
            "using": False,  # 是否为机器人添加手
            "node_names": ["L_hand_base_link", "R_hand_base_link"],  # 添加虚拟body，xml中body的name
            "parent_index": [20, 16],  # 新增body的父节点
            "pos": [[0.17, 0, 0], [0.17, 0, 0]],  # xml中body的 pos
            "quat": [[1, 0, 0, 0], [1, 0, 0, 0]]  # xml中body的 quat
        },
        "extend_foot_right": {
            "using": False,
            "node_names": ["left_foot_link_right", "right_foot_link_right"],  # 添加虚拟body，xml中body的name
            "parent_index": [12, 6],  # 新增body的父节点
            "pos": [[0.1, -0.04, -0.03], [0.1, -0.04, -0.03]],  # xml中body的 pos
            "quat": [[1, 0, 0, 0], [1, 0, 0, 0]]  # xml中body的 quat
        },
        "extend_foot_left": {
            "using": False,
            "node_names": ["left_foot_link_left", "right_foot_link_left"],  # 添加虚拟body，xml中body的name
            "parent_index": [23, 24],  # 新增body的父节点
            "pos": [[0, 0.08, 0], [0, 0.08, 0]],  # xml中body的 pos
            "quat": [[1, 0, 0, 0], [1, 0, 0, 0]]  # xml中body的 quat
        },
        "extend_head": {
            "using": False,
            "node_names": ["head_link"],  # 添加虚拟body，xml中body的name
            "parent_index": [0],  # 新增body的父节点
            "pos": [[0, 0, 0.3]],  # xml中body的 pos
            "quat": [[1, 0, 0, 0]]  # xml中body的 quat
        },
    })