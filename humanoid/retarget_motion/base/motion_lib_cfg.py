from easydict import EasyDict
class MotionLibCfg():
    motion_file = None
    device = "cuda:0"
    fix_height = False
    masterfoot_config = None
    min_length = -1
    im_eval=False
    multi_thread = False
    mjcf_file = 'resources/robots/A2/mjcf/A2_retarget.xml'
    urdf_file = 'resources/robots/A2/urdf/A2_retarget.urdf'
    
    # retarget用的xml跟imitate用的xml可能不一样，需要指定imitation_link_name
    imitation_link_name = [ 
                        'base_link',
                        'leg_l1_link',
                        'leg_l2_link',
                        'leg_l3_link',
                        'leg_l4_link',
                        'leg_l5_link',
                        'leg_l6_link',
                        'leg_r1_link',
                        'leg_r2_link',
                        'leg_r3_link',
                        'leg_r4_link',
                        'leg_r5_link',
                        'leg_r6_link',
                        'left_arm_link01',
                        'left_arm_link04',
                        'right_arm_link01',
                        'right_arm_link04',
                        ]
    isaac_vis_link_name = ['leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link', 
                  'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link', 
                  'left_arm_link01', 'left_arm_link02', 'left_arm_link03', 'left_arm_link04', 'left_arm_link05', 'left_arm_link06', 'left_arm_link07', 
                  'right_arm_link01', 'right_arm_link02', 'right_arm_link03', 'right_arm_link04', 'right_arm_link05', 'right_arm_link06', 'right_arm_link07']
    extend_node_dict = EasyDict({
        "extend_hand":{
            "using": False,  #是否为机器人添加手
            "node_names":["left_hand_link", "right_hand_link"], #添加虚拟body，xml中body的name
            "parent_index":[20,16],   #新增body的父节点
            "pos":[[0.17, 0, 0], [0.17, 0, 0]],  #xml中body的 pos
            "quat":[[1, 0, 0, 0], [1, 0, 0, 0]]  #xml中body的 quat
        },
        "extend_foot_right":{
            "using": False,
            "node_names":["left_foot_link_right", "right_foot_link_right"], #添加虚拟body，xml中body的name
            "parent_index":[12, 6],   #新增body的父节点
            "pos":[[0.1,-0.04,-0.03], [0.1,-0.04,-0.03]],  #xml中body的 pos
            "quat":[[1, 0, 0, 0], [1, 0, 0, 0]]  #xml中body的 quat
        },
        "extend_foot_left":{
            "using": False,
            "node_names":["left_foot_link_left", "right_foot_link_left"], #添加虚拟body，xml中body的name
            "parent_index":[23, 24],   #新增body的父节点
            "pos":[[0,0.08,0], [0,0.08,0]],  #xml中body的 pos
            "quat":[[1, 0, 0, 0], [1, 0, 0, 0]]  #xml中body的 quat
        },
        "extend_head":{
            "using": False,
            "node_names":["head_link"], #添加虚拟body，xml中body的name
            "parent_index":[0],   #新增body的父节点
            "pos":[[0, 0, 0.3]],  #xml中body的 pos
            "quat":[[1, 0, 0, 0]]  #xml中body的 quat
        },
    })