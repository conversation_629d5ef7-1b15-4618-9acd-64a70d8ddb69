import torch
from easydict import EasyDict
from ..base.motion_lib_cfg import MotionLibCfg

# T1配置文件
class x2RetargetMotionLibCfg(MotionLibCfg):
    motion_file = None
    device = "cuda:0"
    fix_height = False
    masterfoot_config = None
    min_length = -1
    im_eval=False
    multi_thread = False
    mjcf_file = 'resources/robots/x2/mjcf/x2_retarget.xml'
    urdf_file = 'resources/robots/x2/urdf/x2_retarget.urdf'

    # 是否优化根节点位置和旋转
    optimize_root = False

    # 机器人根节点位置偏移，x y z
    # root_bias = torch.tensor([0.06, 0, -0.055]).float()
    root_bias = torch.tensor([0.0, 0.0, -0.1]).float()
    # 脚踝节点位置偏移，x y z
    ankle_bias = torch.tensor([-0.015, 0, 0.02]).float()

    # --- 新增：脚趾关节名称配置 ---
    left_toe_name = 'foot_l1_link'
    right_toe_name = 'foot_r1_link'
    # --- 结束新增 ---

    # 选择偏置的旋转参考系，True: 相对于父关节旋转, False: 相对于子关节旋转
    bias_relative_to_parent = True

    # retarget用的xml跟imitate用的xml可能不一样，需要指定imitation_link_name
    imitation_link_name = ['base_link',
                  'leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link',
                  'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link', 
                  'left_arm_link01', 'left_arm_link02', 'left_arm_link03', 'left_arm_link04',
                  'right_arm_link01', 'right_arm_link02', 'right_arm_link03', 'right_arm_link04']
    
    isaac_vis_link_name = ['waist_link','leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link', 
                  'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link', 
                  'left_arm_link01', 'left_arm_link02', 'left_arm_link03', 'left_arm_link04',
                  'right_arm_link01', 'right_arm_link02', 'right_arm_link03', 'right_arm_link04']


    # 所有的关节节点
    joint_names = ['base_link',
                    'leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link',
                    'foot_l1_link',
                    'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link',
                    'foot_r1_link',
                    'waist_link',
                    'left_arm_link01', 'left_arm_link02', 'left_arm_link03', 'left_arm_link04', 'left_hand_link',
                    'right_arm_link01', 'right_arm_link02', 'right_arm_link03', 'right_arm_link04', 'right_hand_link']

    # 需要考察的节点
    pos_names = \
        ['leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link',  # 5
         'leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link',  # 11
         'waist_link',
         'left_arm_link01', 'left_arm_link02', 'left_arm_link03', 'left_arm_link04',   # 15
         'right_arm_link01', 'right_arm_link02', 'right_arm_link03', 'right_arm_link04']  # 19

    # 关节链条
    limb_names = \
        {"leg_l": ['leg_l1_link', 'leg_l2_link', 'leg_l3_link', 'leg_l4_link', 'leg_l5_link', 'leg_l6_link'],
         "leg_r": ['leg_r1_link', 'leg_r2_link', 'leg_r3_link', 'leg_r4_link', 'leg_r5_link', 'leg_r6_link'],
         "waist": ['waist_link'],
         "arm_l": ['left_arm_link01', 'left_arm_link02', 'left_arm_link03', 'left_arm_link04'],
         "arm_r": ['right_arm_link01', 'right_arm_link02', 'right_arm_link03', 'right_arm_link04']}

    # SMPL关键点与机器人关键点的对应关系配置
    # 格式: {机器人关键点名称: SMPL关键点名称}
    # SMPL关键点名称参考: 
    # - Pelvis: 骨盆
    # - L_Hip/R_Hip: 左右髋关节
    # - L_Knee/R_Knee: 左右膝关节
    # - L_Ankle/R_Ankle: 左右踝关节
    # - L_Toe/R_Toe: 左右脚趾
    # - Spine1/2/3: 脊椎1/2/3
    # - Neck: 颈部
    # - L_Collar/R_Collar: 左右锁骨
    # - L_Shoulder/R_Shoulder: 左右肩关节
    # - L_Elbow/R_Elbow: 左右肘关节
    # - L_Wrist/R_Wrist: 左右腕关节
    # - L_Hand/R_Hand: 左右手
    smpl_joint_correspondence = {
        # 躯干
        # 'base_link': 'Pelvis',  # 机器人基座对应SMPL骨盆
        
        # 左腿
        # 'leg_l1_link': 'L_Hip',     # 左髋关节
        'leg_l4_link': 'L_Knee',    # 左膝关节
        'leg_l6_link': 'L_Ankle',   # 左踝关节
        'foot_l1_link': 'L_Toe',    # 左脚趾
        
        # 右腿
        # 'leg_r1_link': 'R_Hip',     # 右髋关节
        'leg_r4_link': 'R_Knee',    # 右膝关节
        'leg_r6_link': 'R_Ankle',   # 右踝关节
        'foot_r1_link': 'R_Toe',    # 右脚趾
        
        # 左臂
        # 'left_arm_link01': 'L_Shoulder',  # 左肩关节
        'left_arm_link02': 'L_Shoulder',     # 左肘关节
        'left_arm_link04': 'L_Elbow',     # 左腕关节
        'left_hand_link': 'L_Wrist',     # 左腕关节
        
        # 右臂
        # 'right_arm_link01': 'R_Shoulder',  # 右肩关节
        'right_arm_link02': 'R_Shoulder',     # 右肘关节
        'right_arm_link04': 'R_Elbow',     # 右腕关节
        'right_hand_link': 'R_Wrist',     # 右腕关节
    }

    joint_pick = list(smpl_joint_correspondence.keys())
    joint_pick_bias = list(smpl_joint_correspondence.keys())

    # 将xml中的默认位置转为T-Pose 需要添加的关节角度偏置:
    __dof_pos__ = {
         'leg_l1_link' : 0, 
         'leg_l2_link' : 0, 
         'leg_l3_link' : 0, 
         'leg_l4_link' : 0, 
         'leg_l5_link' : 0, 
         'leg_l6_link' : 0,  # 5
         'leg_r1_link' : 0, 
         'leg_r2_link' : 0, 
         'leg_r3_link' : 0, 
         'leg_r4_link' : 0, 
         'leg_r5_link' : 0, 
         'leg_r6_link' : 0,
         'waist_link' : 0,
         'left_arm_link01' : 0, 
         'left_arm_link02' : 1.34, 
         'left_arm_link03' : 0, 
         'left_arm_link04' : 0.439,   # 15
         'right_arm_link01' : 0, 
         'right_arm_link02' : -1.34, 
         'right_arm_link03' : 0, 
         'right_arm_link04' : 0.439
    }
    dof_pos = torch.zeros((1, len(pos_names)))
    for key in list(__dof_pos__.keys()):
        dof_pos[:, pos_names.index(key)] = __dof_pos__[key]

    # 以下我没有用到，你可以尝试一下效果
    extend_node_dict = EasyDict({
        "extend_hand":{
            "using": False,  #是否为机器人添加手
            "node_names":["left_hand_link", "right_hand_link"], #添加虚拟body，xml中body的name
            "parent_index":[20,16],   #新增body的父节点
            "pos":[[0.17, 0, 0], [0.17, 0, 0]],  #xml中body的 pos
            "quat":[[1, 0, 0, 0], [1, 0, 0, 0]]  #xml中body的 quat
        },
        "extend_foot_right":{
            "using": False,
            "node_names":["left_foot_link_right", "right_foot_link_right"], #添加虚拟body，xml中body的name
            "parent_index":[12, 6],   #新增body的父节点
            "pos":[[0.1,-0.04,-0.03], [0.1,-0.04,-0.03]],  #xml中body的 pos
            "quat":[[1, 0, 0, 0], [1, 0, 0, 0]]  #xml中body的 quat
        },
        "extend_foot_left":{
            "using": False,
            "node_names":["left_foot_link_left", "right_foot_link_left"], #添加虚拟body，xml中body的name
            "parent_index":[23, 24],   #新增body的父节点
            "pos":[[0,0.08,0], [0,0.08,0]],  #xml中body的 pos
            "quat":[[1, 0, 0, 0], [1, 0, 0, 0]]  #xml中body的 quat
        },
        "extend_head":{
            "using": False,
            "node_names":["head_link"], #添加虚拟body，xml中body的name
            "parent_index":[0],   #新增body的父节点
            "pos":[[0, 0, 0.3]],  #xml中body的 pos
            "quat":[[1, 0, 0, 0]]  #xml中body的 quat
        },
        "virtual_nodes": ["foot_l1_link", "foot_r1_link", "left_hand_link", "right_hand_link"]
    })

    # 关节权重配置，用于损失函数计算
    joint_weights = {
        # 左腿
        'leg_l4_link': 1.0,    # 左膝关节
        'leg_l6_link': 1.0,    # 左踝关节
        'foot_l1_link': 1.0,   # 左脚趾
        
        # 右腿
        'leg_r4_link': 1.0,    # 右膝关节
        'leg_r6_link': 1.0,    # 右踝关节
        'foot_r1_link': 1.0,   # 右脚趾
        
        # 左臂
        'left_arm_link02': 1.0,     # 左肩关节
        'left_arm_link04': 1.0,     # 左肘关节
        'left_hand_link': 1.0,      # 左腕关节
        
        # 右臂
        'right_arm_link02': 1.0,    # 右肩关节
        'right_arm_link04': 1.0,    # 右肘关节
        'right_hand_link': 1.0      # 右腕关节
    }

    # --- 新增：脚底采样点局部偏移配置 ---
    # 相对于脚趾局部坐标系 (假设 +X 前, +Y 左, +Z 上)
    # [x, y, z]
    foot_sample_points_local_offsets = [
        # 角落点 (基于之前的 foot_sample_offsets)
        [ 0.01, 0.05, -0.014], # Front-Left 
        [ 0.01, -0.05, -0.014], # Front-Right
        [ -0.15, 0.05, -0.015], # Back-Left
        [ -0.15, -0.05, -0.015], # Back-Right
        # 正方向点 
        [  0.035, 0.0, -0.011], # 正前方
        [ -0.173, 0.0, -0.015], # 正后方
        [ -0.02, 0.06, -0.015], # 正左侧
        [ -0.02, -0.06, -0.015], # 正右侧
    ]
    # --- 结束新增 ---
