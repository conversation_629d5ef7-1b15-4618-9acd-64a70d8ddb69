# coding=utf-8
# Copyright 2020 The Google Research Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Utility functions for processing motion clips."""

import os
import inspect
currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(os.path.dirname(currentdir))
os.sys.path.insert(0, parentdir)

import numpy as np

from rsl_rl.datasets import pose3d
from pybullet_utils import transformations
import torch


def standardize_quaternion(q):
  """Returns a quaternion where q.w >= 0 to remove redundancy due to q = -q.

  Args:
    q: A quaternion to be standardized.

  Returns:
    A quaternion with q.w >= 0.

  """
  if q[-1] < 0:
    q = -q
  return q


def normalize_rotation_angle(theta):
  """Returns a rotation angle normalized between [-pi, pi].

  Args:
    theta: angle of rotation (radians).

  Returns:
    An angle of rotation normalized between [-pi, pi].

  """
  norm_theta = theta
  if np.abs(norm_theta) > np.pi:
    norm_theta = np.fmod(norm_theta, 2 * np.pi)
    if norm_theta >= 0:
      norm_theta += -2 * np.pi
    else:
      norm_theta += 2 * np.pi

  return norm_theta


def calc_heading(q):
  """Returns the heading of a rotation q, specified as a quaternion.

  The heading represents the rotational component of q along the vertical
  axis (z axis).

  Args:
    q: A quaternion that the heading is to be computed from.

  Returns:
    An angle representing the rotation about the z axis.

  """
  ref_dir = np.array([1, 0, 0])
  rot_dir = pose3d.QuaternionRotatePoint(ref_dir, q)
  heading = np.arctan2(rot_dir[1], rot_dir[0])
  return heading


def calc_heading_rot(q):
  """Return a quaternion representing the heading rotation of q along the vertical axis (z axis).

  Args:
    q: A quaternion that the heading is to be computed from.

  Returns:
    A quaternion representing the rotation about the z axis.

  """
  heading = calc_heading(q)
  q_heading = transformations.quaternion_about_axis(heading, [0, 0, 1])
  return q_heading

def extract_z_rotation(q):
  """
  Extract the z-axis rotation component from a quaternion.
  """
  x, y, z, w = q.unbind(-1)
  
  # 计算 yaw 角（绕 z 轴的旋转）
  yaw = torch.atan2(2 * (w * z + x * y), 1 - 2 * (y * y + z * z))
  
  # 从 yaw 角构建新的四元数
  z_rot_x = torch.zeros_like(x)
  z_rot_y = torch.zeros_like(y)
  z_rot_z = torch.sin(yaw / 2)
  z_rot_w = torch.cos(yaw / 2)
  
  return torch.stack([z_rot_x, z_rot_y, z_rot_z, z_rot_w], dim=-1)

def quat_mul_batch(q1, q2):
  """
  Multiply two batches of quaternions (x, y, z, w order).
  """
  x1, y1, z1, w1 = q1.unbind(-1)
  x2, y2, z2, w2 = q2.unbind(-1)
  w = w1 * w2 - x1 * x2 - y1 * y2 - z1 * z2
  x = w1 * x2 + x1 * w2 + y1 * z2 - z1 * y2
  y = w1 * y2 + y1 * w2 + z1 * x2 - x1 * z2
  z = w1 * z2 + z1 * w2 + x1 * y2 - y1 * x2
  return torch.stack([x, y, z, w], dim=-1)

def quat_rotate_batch(q, v):
  """
  Rotate a batch of 3D vectors v by quaternions q (x, y, z, w order).
  """
  qvec = q[..., :3]
  qw = q[..., 3:4]
  uv = torch.cross(qvec.expand(v.shape), v, dim=-1)
  uuv = torch.cross(qvec.expand(v.shape), uv, dim=-1)
  return v + 2 * (qw * uv + uuv)

@torch.jit.script
def quat_to_tan_norm(q):
  # type: (Tensor) -> Tensor
  # represents a rotation using the tangent and normal vectors
  ref_tan = torch.zeros_like(q[..., 0:3])
  ref_tan[..., 0] = 1
  tan = quat_rotate_batch(q, ref_tan)

  ref_norm = torch.zeros_like(q[..., 0:3])
  ref_norm[..., -1] = 1
  norm = quat_rotate_batch(q, ref_norm)

  norm_tan = torch.cat([tan, norm], dim=len(tan.shape) - 1)
  return norm_tan

@torch.jit.script
def my_quat_rotate(q, v):
  shape = q.shape
  q_w = q[:, -1]
  q_vec = q[:, :3]
  a = v * (2.0 * q_w**2 - 1.0).unsqueeze(-1)
  b = torch.cross(q_vec, v, dim=-1) * q_w.unsqueeze(-1) * 2.0
  c = q_vec * \
      torch.bmm(q_vec.view(shape[0], 1, 3), v.view(
          shape[0], 3, 1)).squeeze(-1) * 2.0
  return a + b + c
  
@torch.jit.script
def quat_conjugate(a):
  shape = a.shape
  a = a.reshape(-1, 4)
  return torch.cat((-a[:, :3], a[:, -1:]), dim=-1).view(shape)

@torch.jit.script
def normalize_angle(x):
  return torch.atan2(torch.sin(x), torch.cos(x))

@torch.jit.script
def quat_to_angle_axis(q):
  # type: (Tensor) -> Tuple[Tensor, Tensor]
  # computes axis-angle representation from quaternion q
  # q must be normalized
  min_theta = 1e-5
  qx, qy, qz, qw = 0, 1, 2, 3

  sin_theta = torch.sqrt(1 - q[..., qw] * q[..., qw])
  angle = 2 * torch.acos(q[..., qw])
  angle = normalize_angle(angle)
  sin_theta_expand = sin_theta.unsqueeze(-1)
  axis = q[..., qx:qw] / sin_theta_expand

  mask = torch.abs(sin_theta) > min_theta
  default_axis = torch.zeros_like(axis)
  default_axis[..., -1] = 1

  angle = torch.where(mask, angle, torch.zeros_like(angle))
  mask_expand = mask.unsqueeze(-1)
  axis = torch.where(mask_expand, axis, default_axis)
  return angle, axis