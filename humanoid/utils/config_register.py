import os

from humanoid.retarget_motion.A2.A2_retarget_motion_lib_config import A2RetargetMotionLibCfg
from humanoid.retarget_motion.T1.T1_retarget_motion_lib_config import T1RetargetMotionLibCfg
from humanoid.retarget_motion.dobot.dobot_retarget_motion_lib_config import dobotRetargetMotionLibCfg
from humanoid.retarget_motion.unitreeG1.unitree_G1_retarget_motion_lib_config import unitreeG1RetargetMotionLibCfg
from humanoid.retarget_motion.x2.x2_retarget_motion_lib_config import x2RetargetMotionLibCfg
from humanoid.retarget_motion.cdroid.cdroid_retarget_motion_lib_config import cdroidRetargetMotionLibCfg

class ConfigFactory:
    def __init__(self):
        self._configs = {}
        self.shape_path = None     

    def register_config(self, name, config):
        self._configs[name] = config

    def register_task(self, task, shape_path=None):
        # print(f"[ConfigFactory.register_task] Registering task: {task} with shape_path: {shape_path}")
        if task == "A2_retarget":
            self.register_config(task, A2RetargetMotionLibCfg)
            # self.shape_path = shape_path if shape_path else os.path.join("data/calc_beta", task.split('_')[0], "shape_scale_bias8.pkl")
            # print(f"[ConfigFactory.register_task] A2_retarget shape_path: {self.shape_path}")
        if task == "T1_retarget":
            self.register_config(task, T1RetargetMotionLibCfg)
            # self.shape_path = shape_path if shape_path else os.path.join("data/calc_beta", task.split('_')[0], "shape_scale_bias.json")
            # print(f"[ConfigFactory.register_task] T1_retarget shape_path: {self.shape_path}")
        elif task == "dobot_retarget":
            self.register_config(task, dobotRetargetMotionLibCfg)
            # self.shape_path = shape_path if shape_path else os.path.join("data/calc_beta", task.split('_')[0], "shape_scale_bias1.pkl")
            # print(f"[ConfigFactory.register_task] dobot_retarget shape_path: {self.shape_path}")
        elif task == "unitreeG1_retarget":
            self.register_config(task, unitreeG1RetargetMotionLibCfg)
            # self.shape_path = shape_path if shape_path else os.path.join("data/calc_beta", task.split('_')[0], "shape_scale_bias.pkl")
            # print(f"[ConfigFactory.register_task] unitreeG1_retarget shape_path: {self.shape_path}")
        elif task == "x2_retarget":
            self.register_config(task, x2RetargetMotionLibCfg)
            # self.shape_path = shape_path if shape_path else os.path.join("data/calc_beta", task.split('_')[0], "shape_scale_bias.json")
            # print(f"[ConfigFactory.register_task] x2_retarget shape_path: {self.shape_path}")
        elif task == "cdroid_retarget":
            self.register_config(task, cdroidRetargetMotionLibCfg)
            # self.shape_path = shape_path if shape_path else os.path.join("data/calc_beta", task.split('_')[0], "shape_scale_bias.json")
            # print(f"[ConfigFactory.register_task] cdroid_retarget shape_path: {self.shape_path}")
        # elif ...
        # print(f"[ConfigFactory.register_task] Final shape_path set to: {self.shape_path}")

    def set_configs(self, configs):
        self._configs = configs

    def get_config(self, name):
        config = self._configs.get(name)
        return config