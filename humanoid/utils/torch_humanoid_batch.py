import torch 
import numpy as np
import humanoid.utils.rotation_conversions as tRot
import xml.etree.ElementTree as ETree
from easydict import EasyDict
import scipy.ndimage.filters as filters
import poselib.core.rotation3d as pRot


# A2_ROTATION_AXIS = torch.tensor([
#     [
#         [0.0, 0.0, 1.0],  # leg_r1_joint 
#         [1.0, 0.0, 0.0],  # leg_r2_joint 
#         [0.0, 1.0, 0.0],  # leg_r3_joint 
#         [0.0, 1.0, 0.0],  # leg_r4_joint 
#         [0.0, 1.0, 0.0],  # leg_r5_joint 
#         [1.0, 0.0, 0.0],  # leg_r6_joint 
#         [0.0, 0.0, 1.0],  # leg_l1_joint 
#         [1.0, 0.0, 0.0],  # leg_l2_joint 
#         [0.0, 1.0, 0.0],  # leg_l3_joint
#         [0.0, 1.0, 0.0],  # leg_l4_joint
#         [0.0, 1.0, 0.0],  # leg_l5_joint
#         [1.0, 0.0, 0.0],  # leg_l6_joint
#         [0.0, 1.0, 0.0],  # r_shoulder_pitch_joint
#         [1.0, 0.0, 0.0],  # r_shoulder_roll_joint
#         [0.0, 0.0, 1.0],  # r_shoulder_yaw_joint
#         [0.0, 1.0, 0.0],  # r_elbow_joint
#         [0.0, 1.0, 0.0],  # l_shoulder_pitch_joint
#         [1.0, 0.0, 0.0],  # l_shoulder_roll_joint
#         [0.0, 0.0, 1.0],  # l_shoulder_yaw_joint
#         [0.0, 1.0, 0.0],  # l_elbow_joint
#     ]
# ])
class Humanoid_Batch:
    
    def __init__(self, mjcf_file, extend_node_dict, device=torch.device("cpu")):
        self.virtual_nodes = extend_node_dict.get("virtual_nodes", [])  # 从配置中获取虚拟节点列表
        self.mjcf_data = mjcf_data = self.from_mjcf(mjcf_file)  # 从mjcf文件中获取关节信息（tran、rot...）
        self._remove_idx = 0
        self._parents = mjcf_data['parent_indices'].to(device)  # 父节点索引
        self.model_names = mjcf_data['node_names']  # 节点名称
        self._offsets = mjcf_data['local_translation'][None, ].to(device)  # 关节位置
        self._local_rotation = mjcf_data['local_rotation'][None, ].to(device)  # 关节旋转
        self.virtual = mjcf_data['virtual'].to(device)  # 虚拟节点索引
        for key, value in list(extend_node_dict.items()):
            if "extend" in key:
                if value.using:
                    node_names = value.node_names
                    parent_index = value.parent_index
                    pos = value.pos
                    quat = value.quat
                    self.model_names = self.model_names + node_names
                    self._parents = torch.cat((self._parents.to(device), torch.tensor(parent_index, device=device))) 
                    self._offsets = torch.cat((self._offsets, torch.tensor(pos, device=device)[None, ]), dim=1)
                    self._local_rotation = torch.cat((self._local_rotation, torch.tensor(quat, device=device)[None, ]), dim=1)
                    self._remove_idx += len(parent_index)  # Adjust the remove index if necessary
        
        self.joints_range = mjcf_data['joints_range'].to(device)  #rad
        self.joints_axis = mjcf_data['joints_axis'].to(device) 
        self._local_rotation_mat = tRot.quaternion_to_matrix(self._local_rotation).float()  # w, x, y, z -> 3x3
        
    def from_mjcf(self, path):
        tree = ETree.parse(path)
        xml_doc_root = tree.getroot()
        xml_world_body = xml_doc_root.find("worldbody")
        if xml_world_body is None:
            raise ValueError("MJCF parsed incorrectly, please verify it.")
        
        xml_body_root = xml_world_body.find("body")
        if xml_body_root is None:
            raise ValueError("MJCF parsed incorrectly, please verify it.")
        
        node_names = []
        parent_indices = []
        local_translation = []
        local_rotation = []  # 四元数
        joints_range = []
        joints_axis = []
        virtual = []
        def _add_xml_node(xml_node, parent_index, node_index):
            if xml_node.attrib.get("name") in self.virtual_nodes:
                virtual.append(node_index)
            node_name = xml_node.attrib.get("name")
            pos = np.fromstring(xml_node.attrib.get("pos", "0 0 0"), dtype=float, sep=" ")
            quat = np.fromstring(xml_node.attrib.get("quat", "1 0 0 0"), dtype=float, sep=" ")
            node_names.append(node_name)
            parent_indices.append(parent_index)
            local_translation.append(pos)
            local_rotation.append(quat)
            curr_index = node_index
            node_index += 1

            all_joints = xml_node.findall("joint")
            for joint in all_joints:
                if joint.attrib.get("range"):
                    joints_range.append(np.fromstring(joint.attrib.get("range"), dtype=float, sep=" "))
                if joint.attrib.get("axis"):
                    joints_axis.append(np.fromstring(joint.attrib.get("axis"), dtype=float, sep=" "))
            for next_node in xml_node.findall("body"):
                node_index = _add_xml_node(next_node, curr_index, node_index)
            return node_index
        _add_xml_node(xml_body_root, -1, 0)
        # print('node_names: ',node_names)
        return {
            "node_names": node_names,
            "parent_indices": torch.from_numpy(np.array(parent_indices, dtype=np.int32)),
            "local_translation": torch.from_numpy(np.array(local_translation, dtype=np.float32)),
            "local_rotation": torch.from_numpy(np.array(local_rotation, dtype=np.float32)),
            "joints_range": torch.from_numpy(np.array(joints_range)),
            "joints_axis": torch.from_numpy(np.array([joints_axis])),
            "virtual": torch.from_numpy(np.array([virtual])),
        }

    def fk_batch(self, pose, trans, convert_to_mat=True, return_full=True, dt=1/30):
        for i in range(self.virtual.shape[1]):
            pose = torch.cat((pose[:,:,:self.virtual[0,i],:], torch.zeros_like(pose[:,:,[0],:]), pose[:,:,self.virtual[0,i]:,:]), dim=2)

        device, dtype = pose.device, pose.dtype
        pose_input = pose.clone()
        B, seq_len = pose.shape[:2]
        pose = pose[..., :len(self._parents), :]  # H1 fitted joints might have extra joints
        
        if self._remove_idx != 0 and pose.shape[-2] == len(self.model_names)-1:
            print("pose:", pose.shape)
            print("torch.zeros(B, seq_len, 1, 3):", torch.zeros(B, seq_len, 1, 3).shape)
            pose = torch.cat([pose, torch.zeros(B, seq_len, 1, 3).to(device).type(dtype)], dim=-2)  # adding hand and head joints

        if convert_to_mat:
            pose_quat = tRot.axis_angle_to_quaternion(pose)
            pose_mat = tRot.quaternion_to_matrix(pose_quat)
        else:
            pose_mat = pose

        if pose_mat.shape != 5:
            pose_mat = pose_mat.reshape(B, seq_len, -1, 3, 3)

        wbody_pos, wbody_mat = self.forward_kinematics_batch(pose_mat[:, :, 1:], pose_mat[:, :, 0:1], trans)

        return_dict = EasyDict()
        wbody_rot = tRot.wxyz_to_xyzw(tRot.matrix_to_quaternion(wbody_mat))

        if self._remove_idx != 0: # 不进
            if return_full:
                return_dict.global_velocity_extend = self._compute_velocity(wbody_pos, dt)
                return_dict.global_angular_velocity_extend = self._compute_angular_velocity(wbody_rot, dt)

            return_dict.global_translation_extend = wbody_pos.clone()
            return_dict.global_rotation_mat_extend = wbody_mat.clone()
            return_dict.global_rotation_extend = wbody_rot

            wbody_pos = wbody_pos[..., :-self._remove_idx, :]
            wbody_mat = wbody_mat[..., :-self._remove_idx, :, :]
            wbody_rot = wbody_rot[..., :-self._remove_idx, :]

        return_dict.global_translation = wbody_pos
        return_dict.global_rotation_mat = wbody_mat
        return_dict.global_rotation = wbody_rot

        with torch.no_grad():
            if return_full:
                rigidbody_linear_velocity = self._compute_velocity(wbody_pos, dt)
                rigidbody_angular_velocity = self._compute_angular_velocity(wbody_rot, dt)
                return_dict.local_rotation = tRot.wxyz_to_xyzw(pose_quat)
                return_dict.global_root_velocity = rigidbody_linear_velocity[..., 0, :]
                return_dict.global_root_angular_velocity = rigidbody_angular_velocity[..., 0, :]
                return_dict.global_angular_velocity = rigidbody_angular_velocity
                return_dict.global_velocity = rigidbody_linear_velocity

                if self._remove_idx != 0:
                    return_dict.dof_pos = pose.sum(dim=-1)[..., 1:][..., :-self._remove_idx]
                else:
                    return_dict.dof_pos = pose.sum(dim=-1)[..., 1:]
                    ### 这里需要注意，由于机器人的关节只有一个自由度，因此直接对轴角求和，就会得到关节旋转角度。

                dof_vel = (return_dict.dof_pos[:, 1:] - return_dict.dof_pos[:, :-1]) / dt
                return_dict.dof_vels = torch.cat([dof_vel, dof_vel[:, -2:-1]], dim=1)
                return_dict.fps = int(1 / dt)

        return return_dict

    def forward_kinematics_batch(self, rotations, root_rotations, root_positions):
        device, dtype = root_rotations.device, root_rotations.dtype
        B, seq_len = rotations.size()[0:2]
        J = self._offsets.shape[1]
        positions_world = []
        rotations_world = []

        expanded_offsets = (self._offsets[:, None].expand(B, seq_len, J, 3).to(device).type(dtype))

        for i in range(J):
            if self._parents[i] == -1:
                positions_world.append(root_positions)
                rotations_world.append(root_rotations)
            else:
                # print(f"J:{J}, i:{i}")
                # print(f"self._parents:{self._parents.shape} {self._parents}")
                # print(f"expanded_offsets:{expanded_offsets.shape}")
                jpos = (torch.matmul(rotations_world[self._parents[i]][:, :, 0].double(), expanded_offsets[:, :, i, :, None].double()).squeeze(-1) + positions_world[self._parents[i]].double())
                rot_mat = torch.matmul(rotations_world[self._parents[i]].double(), torch.matmul(self._local_rotation_mat[..., (i):(i + 1),:,:].double(), rotations[:, :, (i - 1):i, :].double()))

                positions_world.append(jpos)
                rotations_world.append(rot_mat)

        positions_world = torch.stack(positions_world, dim=2)
        rotations_world = torch.cat(rotations_world, dim=2)
        return positions_world, rotations_world

    @staticmethod
    def _compute_velocity(p, time_delta, guassian_filter=True):
        # 添加长度检查
        if p.shape[-3] < 3:
            # 对于太短的序列，可以用简单的差分来代替
            velocity = torch.zeros_like(p)
            if p.shape[-3] == 2:
                velocity[..., 1:, :, :] = (p[..., 1:, :, :] - p[..., :-1, :, :]) / time_delta
                velocity[..., 0:1, :, :] = velocity[..., 1:2, :, :]
            elif p.shape[-3] == 1:
                velocity[..., :, :, :] = 0
            return velocity
        
        velocity = np.gradient(p.cpu().detach().numpy(), axis=-3) / time_delta
        if guassian_filter:
            velocity = torch.from_numpy(filters.gaussian_filter1d(velocity, 2, axis=-3, mode="nearest")).to(p)
        else:
            velocity = torch.from_numpy(velocity).to(p)
        return velocity

    @staticmethod
    def _compute_angular_velocity(r, time_delta: float, guassian_filter=True):
        diff_quat_data = pRot.quat_identity_like(r).to(r.device)
        diff_quat_data[..., :-1, :, :] = pRot.quat_mul_norm(r[..., 1:, :, :], pRot.quat_inverse(r[..., :-1, :, :]))
        # here
        diff_angle, diff_axis = pRot.quat_angle_axis(diff_quat_data)
        angular_velocity = diff_axis * diff_angle.unsqueeze(-1) / time_delta
        if guassian_filter:
            angular_velocity = torch.from_numpy(filters.gaussian_filter1d(angular_velocity.cpu().detach().numpy(), 2, axis=-3, mode="nearest"))
        return angular_velocity
