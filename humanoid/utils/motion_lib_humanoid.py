import numpy as np
import os
import yaml
from tqdm import tqdm
import os.path as osp

from humanoid.utils import torch_utils
import joblib
import torch
import torch.multiprocessing as mp
import copy
import gc
from humanoid.smpllib.smpl_parser import (
    SMPL_Parser,
    SMPLH_Parser,
    SMPLX_Parser,
)
from scipy.spatial.transform import Rotation as sRot
import random
from humanoid.utils.flags import flags
from humanoid.utils.motion_lib_base import MotionLibBase, DeviceCache, compute_motion_dof_vels, FixHeightMode
from humanoid.utils.torch_humanoid_batch import Humanoid_Batch
from humanoid.retarget_motion.base.motion_lib_cfg import MotionLibCfg
from easydict import EasyDict

def to_torch(tensor):
    if torch.is_tensor(tensor):
        return tensor
    else:
        return torch.from_numpy(tensor)



USE_CACHE = False
print("MOVING MOTION DATA TO GPU, USING CACHE:", USE_CACHE)

if not USE_CACHE:
    old_numpy = torch.Tensor.numpy
    
    class Patch:

        def numpy(self):
            if self.is_cuda:
                return self.to("cpu").numpy()
            else:
                return old_numpy(self)

    torch.Tensor.numpy = Patch.numpy


class MotionLibHumanoid(MotionLibBase):

    def __init__(self, motion_lib_cfg:MotionLibCfg):
        super().__init__(motion_lib_cfg)
        self.mesh_parsers = Humanoid_Batch(motion_lib_cfg.mjcf_file, motion_lib_cfg.extend_node_dict)
        # !
        return
    
    # 该方法好像未完成
    @staticmethod
    def fix_trans_height(pose_aa, trans, curr_gender_betas, mesh_parsers, fix_height_mode):
        if fix_height_mode == FixHeightMode.no_fix:
            return trans, 0
        
        with torch.no_grad():
            raise NotImplementedError("Fix height is not implemented for Gaoqing")
            return trans, diff_fix
        
    def load_motions(self, num_motion_to_load, limb_weights, random_sample=True, start_idx=0, max_len=-1, target_heading = None):
        # load motion load the same number of motions as there are skeletons (humanoids)
        # 加载与骨架相同数量的动作，这就是为什么要为数据集里每一个动作赋予一个骨架树，用骨架树来指代动作
        # if "gts" in self.__dict__:
        #     del self.gts, self.grs, self.lrs, self.grvs, self.gravs, self.gavs, self.gvs, self.dvs
        #     del self._motion_lengths, self._motion_fps, self._motion_dt, self._motion_num_frames, self._motion_bodies, self._motion_aa
        #     if "gts_t" in self.__dict__:
        #         self.gts_t, self.grs_t, self.gvs_t
        #     if flags.real_traj:
        #         del self.q_gts, self.q_grs, self.q_gavs, self.q_gvs

        motions = []
        files = []
        _motion_lengths = []
        _motion_fps = []
        _motion_dt = []
        _motion_num_frames = []
        _motion_bodies = []
        _motion_aa = []
        
        # real_traj这个值没被定义过也没被传入，我草
        if flags.real_traj:
            self.q_gts, self.q_grs, self.q_gavs, self.q_gvs = [], [], [], []

        torch.cuda.empty_cache()
        gc.collect()

        total_len = 0.0
        self.num_joints = len(self.mesh_parsers.model_names) # xml有多少个joint

        # 确定给动作是随机采样还是顺序采样
        if random_sample:
            sample_idxes = torch.multinomial(self._sampling_prob, num_samples=num_motion_to_load, replacement=True).to(self._device)
        else:
            sample_idxes = torch.remainder(torch.arange(num_motion_to_load) + start_idx, self._num_unique_motions ).to(self._device)

        # import ipdb; ipdb.set_trace()
        
        
        # self.one_hot_motions = torch.nn.functional.one_hot(self._curr_motion_ids, num_classes = self._num_unique_motions).to(self._device)  # Testing for obs_v5 # 没用

        self._curr_motion_ids = sample_idxes # 储存当前动作的id
        self.curr_motion_keys = self._motion_data_keys[sample_idxes] #获取当前动作的键（名）
        self._sampling_batch_prob = self._sampling_prob[self._curr_motion_ids] / self._sampling_prob[self._curr_motion_ids].sum() # 计算采样概率

        ### 此处在训练的时候会出现
        print("\n****************************** Current motion keys ******************************")
        print("Sampling motion:", sample_idxes[:30])
        if len(self.curr_motion_keys) < 100:
            print(self.curr_motion_keys)
        else:
            print(self.curr_motion_keys[:30], ".....")
        print("*********************************************************************************\n")


        motion_data_list = self._motion_data_list[sample_idxes.cpu().numpy()]
        mp.set_sharing_strategy('file_descriptor') # 设置多进程共享策略

        manager = mp.Manager() # 创建进程管理器
        queue = manager.Queue() # 创建进程间通信队列
        num_jobs = min(mp.cpu_count(), 64) # 确定工作的数量

        # 如果核心数太少，或者明确不使用多线程，或者debug模式，就是用单线程
        if num_jobs <= 8 or not self.multi_thread:
            num_jobs = 1
        if flags.debug:
            num_jobs = 1
        
        
        res_acc = {}  # using dictionary ensures order of the results. 储存结果（字典）
        jobs = motion_data_list # 将采样得到的动作数据列表赋值给 jobs，记为任务
        chunk = np.ceil(len(jobs) / num_jobs).astype(int) # 将任务总数除以进程数并向上取整得到的每个进程需要处理的任务块（chunk）的大小
        ids = np.arange(len(jobs)) # 创建一个索引数组，包含从0到任务总数减1的整数。

        #将 motion_data_list 分割成多个任务块，每个任务块包含一部分动作数据以及对应的骨架树、性别参数等信息。这些任务块将被分配给不同的进程处理
        jobs = [(ids[i:i + chunk], jobs[i:i + chunk], self.fix_height, self.mesh_parsers, self._masterfoot_conifg, target_heading, max_len) for i in range(0, len(jobs), chunk)]
        
        job_args = [jobs[i] for i in range(len(jobs))] # 为每个任务块创建参数列表
        
        for i in range(1, len(jobs)): # 为每个任务块创建一个子进程，如果num_jobs == 1，则不进入此循环
            worker_args = (*job_args[i], queue, i)
            worker = mp.Process(target=self.load_motion_with_skeleton, args=worker_args)
            worker.start()
            
        res_acc.update(self.load_motion_with_skeleton(*jobs[0], None, 0)) # 主进程处理第一个任务块，，并将结果更新到 res_acc 字典中，update是字典类的一个方法，类似于数组的append
        # 这里传递 None 作为队列参数，因为主进程不需要通过队列通信

        # 从进程间通信队列中收集子进程的计算结果，合并到主线程的字典中
        for i in tqdm(range(len(jobs) - 1)):
            res = queue.get()
            res_acc.update(res)

        # 主进程处理和整理每个动作数据
        for f in tqdm(range(len(res_acc))):
            motion_file_data, curr_motion = res_acc[f]
            if USE_CACHE:
                curr_motion = DeviceCache(curr_motion, self._device)

            motion_fps = curr_motion.fps
            curr_dt = 1.0 / motion_fps

            num_frames = curr_motion.global_rotation.shape[0] # 得到总帧数
            curr_len = 1.0 / motion_fps * (num_frames - 1) # 计算动作的总长度（以秒为单位）
            
            
            if "beta" in motion_file_data: # 如果 motion_file_data 包含beta参数，重塑pose_aa，同时记录beta参数
                _motion_aa.append(motion_file_data['pose_aa'].reshape(-1, self.num_joints * 3))
                _motion_bodies.append(curr_motion.gender_beta)
            else:
                _motion_aa.append(np.zeros((num_frames, self.num_joints * 3)))
                _motion_bodies.append(torch.zeros(17)) #不含beta就添加零值占位符

            _motion_fps.append(motion_fps) # 记录动作的帧率
            _motion_dt.append(curr_dt) # 记录动作的时间间隔
            _motion_num_frames.append(num_frames) # 记录动作的帧数
            motions.append(curr_motion) # 将处理后的动作数据添加到 motions 列表中
            files.append(motion_file_data) # 将处理后的动作数据添加到 motions 列表中
            _motion_lengths.append(curr_len) # 记录动作的长度
            
            # real_traj没用啊
            if flags.real_traj:
                self.q_gts.append(curr_motion.quest_motion['quest_trans'])
                self.q_grs.append(curr_motion.quest_motion['quest_rot'])
                self.q_gavs.append(curr_motion.quest_motion['global_angular_vel'])
                self.q_gvs.append(curr_motion.quest_motion['linear_vel'])
                
            del curr_motion
            
        self._motion_lengths = torch.tensor(_motion_lengths, device=self._device, dtype=torch.float32)
        self.motion_fps = torch.tensor(_motion_fps, device=self._device, dtype=torch.float32)
        self._motion_bodies = torch.stack(_motion_bodies).to(self._device).type(torch.float32)
        self._motion_aa = torch.tensor(np.concatenate(_motion_aa), device=self._device, dtype=torch.float32)

        self._motion_dt = torch.tensor(_motion_dt, device=self._device, dtype=torch.float32)
        self._motion_num_frames = torch.tensor(_motion_num_frames, device=self._device)
        self._motion_limb_weights = torch.tensor(np.array(limb_weights), device=self._device, dtype=torch.float32)
        self._num_motions = len(motions)

        # 整理运动学信息
        self.gts = torch.cat([m.global_translation for m in motions], dim=0).float().to(self._device) #  将这些全局平移向量沿着第0维，拼接一个大的张量，以下同理
        # 类似于(帧数，关节数，维度) ---> (帧数*关节数，维度)
        self.grs = torch.cat([m.global_rotation for m in motions], dim=0).float().to(self._device)
        self.lrs = torch.cat([m.local_rotation for m in motions], dim=0).float().to(self._device)
        self.grvs = torch.cat([m.global_root_velocity for m in motions], dim=0).float().to(self._device)
        self.gravs = torch.cat([m.global_root_angular_velocity for m in motions], dim=0).float().to(self._device)
        self.gavs = torch.cat([m.global_angular_velocity for m in motions], dim=0).float().to(self._device)
        self.gvs = torch.cat([m.global_velocity for m in motions], dim=0).float().to(self._device)
        self.dvs = torch.cat([m.dof_vels for m in motions], dim=0).float().to(self._device)
        if "global_translation_extend" in motions[0].__dict__:
            self.gts_t = torch.cat([m.global_translation_extend for m in motions], dim=0).float().to(self._device)
            self.grs_t = torch.cat([m.global_rotation_extend for m in motions], dim=0).float().to(self._device)
            self.gvs_t = torch.cat([m.global_velocity_extend for m in motions], dim=0).float().to(self._device)
            self.gavs_t = torch.cat([m.global_angular_velocity_extend for m in motions], dim=0).float().to(self._device)
        if "dof_pos" in motions[0].__dict__:
            self.dof_pos = torch.cat([torch.tensor(m["dof_pos"]) for m in motions], dim=0).float().to(self._device)
        if flags.real_traj:# 没用
            self.q_gts = torch.cat(self.q_gts, dim=0).float().to(self._device)
            self.q_grs = torch.cat(self.q_grs, dim=0).float().to(self._device)
            self.q_gavs = torch.cat(self.q_gavs, dim=0).float().to(self._device)
            self.q_gvs = torch.cat(self.q_gvs, dim=0).float().to(self._device)
        

        lengths = self._motion_num_frames # 获取每个动作的帧长度
        lengths_shifted = lengths.roll(1) # 使用 roll 函数将 lengths 张量中的元素向左移动一位 [1,2,3,4,5] --> [2,3,4,5,1]
        lengths_shifted[0] = 0 # 第一个元素置零
        self.length_starts = lengths_shifted.cumsum(0) # 计算每个动作的起始帧索引
        self.motion_ids = torch.arange(len(motions), dtype=torch.long, device=self._device) # 创建动作ID张量
        motion = motions[0] # 从 motions 列表中获取第一个动作数据 
        self.num_bodies = self.num_joints

        num_motions = self.num_motions() #计算动作数量
        total_len = self.get_total_length() #得到所有动作的累积长度
        print(f"Loaded {num_motions:d} motions with a total length of {total_len:.3f}s and {self.gts.shape[0]} frames.")
        return motions

    def get_motion_state(self, motion_ids, motion_times, offset=None):
        n = len(motion_ids) # 没用
        num_bodies = self._get_num_bodies() # 没用

        motion_len = self._motion_lengths[motion_ids]
        num_frames = self._motion_num_frames[motion_ids]
        dt = self._motion_dt[motion_ids]

        frame_idx0, frame_idx1, blend = self._calc_frame_blend(motion_times, motion_len, num_frames, dt) # 动画补间，插值得到blend，即帧间值
        
        # print("non_interval", frame_idx0, frame_idx1)
        
        
        ##以下是计算两帧之间的动作数据，采用插值的方法
        
        f0l = frame_idx0 + self.length_starts[motion_ids] #得到该时间点的第一帧
        f1l = frame_idx1 + self.length_starts[motion_ids] #得到该时间点的第二帧

        # 提取以上两个时间点的数据
        if "dof_pos" in self.__dict__:
            local_rot0 = self.dof_pos[f0l].squeeze(-1)
            local_rot1 = self.dof_pos[f1l].squeeze(-1)
        else:
            local_rot0 = self.lrs[f0l]
            local_rot1 = self.lrs[f1l]
            
        body_vel0 = self.gvs[f0l]
        body_vel1 = self.gvs[f1l]

        body_ang_vel0 = self.gavs[f0l]
        body_ang_vel1 = self.gavs[f1l]

        body_pos0 = self.gts[f0l, :]
        body_pos1 = self.gts[f1l, :]

        dof_vel0 = self.dvs[f0l]
        dof_vel1 = self.dvs[f1l]

        vals = [local_rot0, local_rot1, body_vel0, body_vel1, body_ang_vel0, body_ang_vel1, body_pos0, body_pos1, dof_vel0, dof_vel1]
        for v in vals:
            assert v.dtype != torch.float64

        blend = blend.unsqueeze(-1)

        blend_exp = blend.unsqueeze(-1)

        if offset is None:
            body_pos = (1.0 - blend_exp) * body_pos0 + blend_exp * body_pos1  # ZL: apply offset
        else:
            body_pos = (1.0 - blend_exp) * body_pos0 + blend_exp * body_pos1 + offset[..., None, :]  # ZL: apply offset

        body_vel = (1.0 - blend_exp) * body_vel0 + blend_exp * body_vel1
        body_ang_vel = (1.0 - blend_exp) * body_ang_vel0 + blend_exp * body_ang_vel1
        

        if "dof_pos" in self.__dict__: # joints
            dof_vel = (1.0 - blend) * dof_vel0 + blend * dof_vel1
            dof_pos = (1.0 - blend) * local_rot0 + blend * local_rot1
        else:
            dof_vel = (1.0 - blend_exp) * dof_vel0 + blend_exp * dof_vel1
            local_rot = torch_utils.slerp(local_rot0, local_rot1, torch.unsqueeze(blend, axis=-1))
            dof_pos = self._local_rotation_to_dof_smpl(local_rot)

        body_rot0 = self.grs[f0l]
        body_rot1 = self.grs[f1l]
        body_rot = torch_utils.slerp(body_rot0, body_rot1, blend_exp)
        return_dict = {}
        
        if "gts_t" in self.__dict__:
            body_pos_t0 = self.gts_t[f0l]
            body_pos_t1 = self.gts_t[f1l]
            
            rg_rot_t0 = self.grs_t[f0l]
            rg_rot_t1 = self.grs_t[f1l]
            
            body_vel_t0 = self.gvs_t[f0l]
            body_vel_t1 = self.gvs_t[f1l]
            
            body_ang_vel_t0 = self.gavs_t[f0l]
            body_ang_vel_t1 = self.gavs_t[f1l]
            if offset is None:
                body_pos_t = (1.0 - blend_exp) * body_pos_t0 + blend_exp * body_pos_t1  
            else:
                body_pos_t = (1.0 - blend_exp) * body_pos_t0 + blend_exp * body_pos_t1 + offset[..., None, :]
            rg_rot_t = torch_utils.slerp(rg_rot_t0, rg_rot_t1, blend_exp)
            body_vel_t = (1.0 - blend_exp) * body_vel_t0 + blend_exp * body_vel_t1
            body_ang_vel_t = (1.0 - blend_exp) * body_ang_vel_t0 + blend_exp * body_ang_vel_t1
            
            return_dict['body_pos_t'] = body_pos_t
            return_dict['rg_rot_t'] = rg_rot_t
            return_dict['body_vel_t'] = body_vel_t
            return_dict['body_ang_vel_t'] = body_ang_vel_t
        
        if flags.real_traj: # 没用
            q_body_ang_vel0, q_body_ang_vel1 = self.q_gavs[f0l], self.q_gavs[f1l]
            q_body_rot0, q_body_rot1 = self.q_grs[f0l], self.q_grs[f1l]
            q_body_pos0, q_body_pos1 = self.q_gts[f0l, :], self.q_gts[f1l, :]
            q_body_vel0, q_body_vel1 = self.q_gvs[f0l], self.q_gvs[f1l]

            q_ang_vel = (1.0 - blend_exp) * q_body_ang_vel0 + blend_exp * q_body_ang_vel1
            q_body_rot = torch_utils.slerp(q_body_rot0, q_body_rot1, blend_exp)
            q_body_pos = (1.0 - blend_exp) * q_body_pos0 + blend_exp * q_body_pos1
            q_body_vel = (1.0 - blend_exp) * q_body_vel0 + blend_exp * q_body_vel1
            
            body_pos[:, self.track_idx] = q_body_pos
            body_rot[:, self.track_idx] = q_body_rot
            body_vel[:, self.track_idx] = q_body_vel
            body_ang_vel[:, self.track_idx] = q_ang_vel
            
        return_dict.update({
            "root_pos": body_pos[..., 0, :].clone(),
            "root_rot": body_rot[..., 0, :].clone(),
            "dof_pos": dof_pos.clone(),
            "root_vel": body_vel[..., 0, :].clone(),
            "root_ang_vel": body_ang_vel[..., 0, :].clone(),
            "dof_vel": dof_vel.view(dof_vel.shape[0], -1),
            "motion_aa": self._motion_aa[f0l],
            "body_pos": body_pos,
            "body_rot": body_rot,
            "body_vel": body_vel,
            "body_ang_vel": body_ang_vel,
            "motion_bodies": self._motion_bodies[motion_ids],
            "motion_limb_weights": self._motion_limb_weights[motion_ids],
        })
        return return_dict
        
    @staticmethod
    def load_motion_with_skeleton(ids, motion_data_list, fix_height, mesh_parsers:Humanoid_Batch, masterfoot_config,  target_heading, max_len, queue, pid):
        # ZL: loading motion with the specified skeleton. Perfoming forward kinematics to get the joint positions
        np.random.seed(np.random.randint(5000)* pid)
        res = {}
        assert (len(ids) == len(motion_data_list))
        for f in range(len(motion_data_list)):
            curr_id = ids[f]  # id for this datasample
            curr_file = motion_data_list[f]
            if not isinstance(curr_file, dict) and osp.isfile(curr_file):
                key = motion_data_list[f].split("/")[-1].split(".")[0]
                curr_file = joblib.load(curr_file)[key]

            seq_len = curr_file['root_trans_offset'].shape[0]
            if max_len == -1 or seq_len < max_len:
                start, end = 0, seq_len
            else:
                start = random.randint(0, seq_len - max_len)
                end = start + max_len

            trans = to_torch(curr_file['root_trans_offset']).clone()[start:end]
            pose_aa = to_torch(curr_file['pose_aa'][start:end]).clone()
            dt = 1/curr_file['fps']

            B, J, N = pose_aa.shape

            ##### ZL: randomize the heading ######
            # if (not flags.im_eval) and (not flags.test):
            #     # if True:
            #     random_rot = np.zeros(3)
            #     random_rot[2] = np.pi * (2 * np.random.random() - 1.0)
            #     random_heading_rot = sRot.from_euler("xyz", random_rot)
            #     pose_aa = pose_aa.reshape(B, -1)
            #     pose_aa[:, :3] = torch.tensor((random_heading_rot * sRot.from_rotvec(pose_aa[:, :3])).as_rotvec())
            #     trans = torch.matmul(trans, torch.from_numpy(random_heading_rot.as_matrix().T))
            ##### ZL: randomize the heading ######
            if not target_heading is None:
                start_root_rot = sRot.from_rotvec(pose_aa[0, 0])
                heading_inv_rot = sRot.from_quat(torch_utils.calc_heading_quat_inv(torch.from_numpy(start_root_rot.as_quat()[None, ])))
                heading_delta = sRot.from_quat(target_heading) * heading_inv_rot 
                pose_aa[:, 0] = torch.tensor((heading_delta * sRot.from_rotvec(pose_aa[:, 0])).as_rotvec())

                trans = torch.matmul(trans, torch.from_numpy(heading_delta.as_matrix().squeeze().T))


            # trans, trans_fix = MotionLibSMPL.fix_trans_height(pose_aa, trans, curr_gender_beta, mesh_parsers, fix_height_mode = fix_height)
            curr_motion = mesh_parsers.fk_batch(pose_aa[None, ], trans[None, ], return_full= True, dt = dt)
            curr_motion = EasyDict({k: v.squeeze() if torch.is_tensor(v) else v for k, v in curr_motion.items() })
            
            
            res[curr_id] = (curr_file, curr_motion)
            
        if not queue is None:
            queue.put(res)
        else:
            return res


    
