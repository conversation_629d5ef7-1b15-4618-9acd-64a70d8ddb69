# SPDX-FileCopyrightText: Copyright (c) 2021 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-FileCopyrightText: Copyright (c) 2021 ETH Zurich, Nikita Rudin
# SPDX-License-Identifier: BSD-3-Clause
# 
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# 1. Redistributions of source code must retain the above copyright notice, this
# list of conditions and the following disclaimer.
#
# 2. Redistributions in binary form must reproduce the above copyright notice,
# this list of conditions and the following disclaimer in the documentation
# and/or other materials provided with the distribution.
#
# 3. Neither the name of the copyright holder nor the names of its
# contributors may be used to endorse or promote products derived from
# this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
# FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
# DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
# CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
# OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
# Copyright (c) 2024 Beijing RobotEra TECHNOLOGY CO.,LTD. All rights reserved.

# from isaacgym.torch_utils import quat_apply, normalize  # css在CPU上运行需要去掉isaacgym，暂时注释掉
import torch
from torch import Tensor
import numpy as np
from typing import Tuple
from scipy.signal import butter, filtfilt
import matplotlib.pyplot as plt
import math
import torch
import torch.nn.functional as F
# @ torch.jit.script
def quat_apply_yaw(quat, vec):
    quat_yaw = quat.clone().view(-1, 4)
    quat_yaw[:, :2] = 0.
    quat_yaw = normalize(quat_yaw)
    return quat_apply(quat_yaw, vec)

# @ torch.jit.script
def wrap_to_pi(angles):
    angles %= 2*np.pi
    angles -= 2*np.pi * (angles > np.pi)
    return angles

# @ torch.jit.script
def torch_rand_sqrt_float(lower, upper, shape, device):
    # type: (float, float, Tuple[int, int], str) -> Tensor
    r = 2*torch.rand(*shape, device=device) - 1
    r = torch.where(r<0., -torch.sqrt(-r), torch.sqrt(r))
    r =  (r + 1.) / 2.
    return (upper - lower) * r + lower

# 四元数乘法，支持批量
def quaternion_multiply(q1, q2):
    x1, y1, z1, w1 = q1[..., 0], q1[..., 1], q1[..., 2], q1[..., 3]
    x2, y2, z2, w2 = q2[..., 0], q2[..., 1], q2[..., 2], q2[..., 3]
    
    x = w1 * x2 + x1 * w2 + y1 * z2 - z1 * y2
    y = w1 * y2 - x1 * z2 + y1 * w2 + z1 * x2
    z = w1 * z2 + x1 * y2 - y1 * x2 + z1 * w2
    w = w1 * w2 - x1 * x2 - y1 * y2 - z1 * z2
    
    return torch.stack((x, y, z, w), dim=-1)

# 四元数逆，支持批量
def quaternion_inverse(q):
    x, y, z, w = q[..., 0], q[..., 1], q[..., 2], q[..., 3]
    return torch.stack((-x, -y, -z, w), dim=-1)


def copysign_new(a, b):

    a = torch.tensor(a, device=b.device, dtype=torch.float)
    a = a.expand_as(b)
    return torch.abs(a) * torch.sign(b)

def get_euler_rpy(q):
    qx, qy, qz, qw = 0, 1, 2, 3
    # roll (x-axis rotation)
    sinr_cosp = 2.0 * (q[..., qw] * q[..., qx] + q[..., qy] * q[..., qz])
    cosr_cosp = q[..., qw] * q[..., qw] - q[..., qx] * \
        q[..., qx] - q[..., qy] * q[..., qy] + q[..., qz] * q[..., qz]
    roll = torch.atan2(sinr_cosp, cosr_cosp)

    # pitch (y-axis rotation)
    sinp = 2.0 * (q[..., qw] * q[..., qy] - q[..., qz] * q[..., qx])
    pitch = torch.where(torch.abs(sinp) >= 1, copysign_new(
        np.pi / 2.0, sinp), torch.asin(sinp))

    # yaw (z-axis rotation)
    siny_cosp = 2.0 * (q[..., qw] * q[..., qz] + q[..., qx] * q[..., qy])
    cosy_cosp = q[..., qw] * q[..., qw] + q[..., qx] * \
        q[..., qx] - q[..., qy] * q[..., qy] - q[..., qz] * q[..., qz]
    yaw = torch.atan2(siny_cosp, cosy_cosp)

    return roll % (2*np.pi), pitch % (2*np.pi), yaw % (2*np.pi)

def get_euler_xyz_tensor(quat):
    r, p, w = get_euler_rpy(quat)
    # stack r, p, w in dim1
    euler_xyz = torch.stack((r, p, w), dim=-1)
    euler_xyz[euler_xyz > np.pi] -= 2 * np.pi
    return euler_xyz

def euler_to_quaternion(euler_angles):
    # 假设输入是形状 [M, N, 3] 的张量，最后一维是 (roll, pitch, yaw)
    roll = euler_angles[..., 0] / 2
    pitch = euler_angles[..., 1] / 2
    yaw = euler_angles[..., 2] / 2

    # 计算正弦和余弦值
    c_roll = torch.cos(roll)
    s_roll = torch.sin(roll)
    c_pitch = torch.cos(pitch)
    s_pitch = torch.sin(pitch)
    c_yaw = torch.cos(yaw)
    s_yaw = torch.sin(yaw)

    # 计算四元数的分量，根据标准公式
    w = c_roll * c_pitch * c_yaw - s_roll * s_pitch * s_yaw
    x = s_roll * c_pitch * c_yaw + c_roll * s_pitch * s_yaw
    y = c_roll * s_pitch * c_yaw - s_roll * c_pitch * s_yaw
    z = c_roll * c_pitch * s_yaw + s_roll * s_pitch * c_yaw

    # 返回 [M, N, 4] 的四元数，最后一维是 [x, y, z, w]
    return torch.stack((x, y, z, w), dim=-1)

class ButterworthFilter:
    def __init__(self, cutoff, fs, input_shape, order=2):
        # 归一化截止频率
        normalized_cutoff = cutoff / (0.5 * fs)
        
        # 生成巴特沃斯滤波器的系数
        b, a = butter(N=order, Wn=normalized_cutoff, btype='low')
        self.b = torch.tensor(b, dtype=torch.float32)
        self.a = torch.tensor(a, dtype=torch.float32)
        self.order = order

        # 输入形状（不包含批次维度）
        self.input_shape = input_shape
        
        # 初始化状态，形状为 [*input_shape, order]
        state_shape = (*self.input_shape, len(self.b) - 1)
        self.prev_x = torch.zeros(state_shape, dtype=torch.float32)  # 存储之前的输入
        self.prev_y = torch.zeros(state_shape, dtype=torch.float32)  # 存储之前的输出

    def filter(self, x):
        """
        输入一个张量，返回滤波后的结果。

        :param x: 新输入的张量，形状为 [*input_shape]
        :return: 滤波后的张量，形状为 [*input_shape]
        """
        # 确保输入形状正确
        assert x.shape == self.input_shape, f"输入形状应为 {self.input_shape}, 但得到 {x.shape}"

        y = self.b[0] * x

        # 加上 b 系数对之前输入的影响
        if len(self.b) > 1:
            # 广播 b 系数到输入形状
            b_rest = self.b[1:].view(*([1] * len(self.input_shape)), -1)
            y += torch.sum(b_rest * self.prev_x, dim=-1)

        # 减去 a 系数对之前输出的影响
        if len(self.a) > 1:
            # 广播 a 系数到输入形状
            a_rest = self.a[1:].view(*([1] * len(self.input_shape)), -1)
            y -= torch.sum(a_rest * self.prev_y, dim=-1)

        # 更新状态
        self.prev_x = torch.cat((x.unsqueeze(-1), self.prev_x[..., :-1]), dim=-1)  # 更新输入状态
        self.prev_y = torch.cat((y.unsqueeze(-1), self.prev_y[..., :-1]), dim=-1)  # 更新输出状态

        return y

    def reset(self, rows=None):
        """
        重置滤波器的状态。

        :param rows: 要重置的行索引列表或张量。
        """
        if rows != None:
            # 重置指定行的状态
            self.prev_x[rows, ...] = 0
            self.prev_y[rows, ...] = 0


def euler_to_rotMat(input):
    # 输入顺序:x-y-z
    # 旋转顺序:z-y-x
    # input shape: [*, 3]
    yaw=input[...,2]
    pitch=input[...,1]
    roll=input[...,0]
    # Rz_yaw = torch.zeros_like(yaw).unsqueeze(dim=-1).repeat(1, 3, 3)
    # print(Rz_yaw.shape)
    Rz_yaw = torch.stack([
        torch.stack([torch.cos(yaw), -torch.sin(yaw), torch.zeros_like(yaw)], dim=-1),
        torch.stack([torch.sin(yaw), torch.cos(yaw), torch.zeros_like(yaw)], dim=-1),
        torch.stack([torch.zeros_like(yaw), torch.zeros_like(yaw), torch.ones_like(yaw)], dim=-1)], dim=-1)
    # print(Rz_yaw.shape)
    # Rz_yaw = torch.tensor([
    #     [torch.cos(yaw), -torch.sin(yaw), 0],
    #     [torch.sin(yaw), torch.cos(yaw), 0],
    #     [0, 0, 1]])
    Ry_pitch = torch.stack([
        torch.stack([torch.cos(pitch), torch.zeros_like(pitch), torch.sin(pitch)], dim=-1),
        torch.stack([torch.zeros_like(pitch), torch.ones_like(yaw), torch.zeros_like(pitch)], dim=-1),
        torch.stack([-torch.sin(pitch), torch.zeros_like(pitch), torch.cos(pitch)], dim=-1)], dim=-1)
    # Ry_pitch = torch.tensor([
    #     [torch.cos(pitch), 0, torch.sin(pitch)],
    #     [0, 1, 0],
    #     [-torch.sin(pitch), 0, torch.cos(pitch)]])
    Rx_roll = torch.stack([
        torch.stack([torch.ones_like(roll), torch.zeros_like(roll), torch.zeros_like(roll)], dim=-1),
        torch.stack([torch.zeros_like(roll), torch.cos(roll), -torch.sin(roll)], dim=-1),
        torch.stack([torch.zeros_like(roll), torch.sin(roll), torch.cos(roll)], dim=-1)], dim=-1)
    # Rx_roll = torch.tensor([
    #     [1, 0, 0],
    #     [0, torch.cos(roll), -torch.sin(roll)],
    #     [0, torch.sin(roll), torch.cos(roll)]])
    rotMat = torch.matmul(Rz_yaw, torch.matmul(Ry_pitch, Rx_roll))
    # print(rotMat.shape)
    return rotMat


def rotation_matrix_from_vectors(vec1_batch, vec2_batch):
    """
    批量计算多个向量对之间的旋转矩阵。

    参数:
    vec1_batch -- 多个方向向量批次 (PyTorch tensor)，形状为 (batch_size, 3)
    vec2_batch -- 多个目标向量批次 (PyTorch tensor)，形状为 (batch_size, 3)

    返回:
    R_batch -- 旋转矩阵批次 (PyTorch tensor)，形状为 (batch_size, 3, 3)
    """
    # 计算两个向量的点积
    dot_product = torch.sum(vec1_batch * vec2_batch, dim=1)

    # 计算两个向量的叉积
    cross_product = torch.cross(vec1_batch, vec2_batch, dim=1)

    # 计算叉积的模长
    norm_cross_product = torch.norm(cross_product, dim=1)

    # 如果两个向量平行，则没有唯一的旋转矩阵
    if torch.any(norm_cross_product < 1e-8):  # 使用一个小的阈值来避免除以零
        raise ValueError("至少有一对向量平行，无法计算唯一的旋转矩阵。")

    # 计算叉积的单位向量
    k = cross_product / norm_cross_product[:, None]

    # 计算旋转角度
    theta = torch.acos(dot_product / (torch.norm(vec1_batch, dim=1) * torch.norm(vec2_batch, dim=1)))

    # 使用罗德里格斯公式计算旋转矩阵
    cos_theta = torch.cos(theta[:, None])
    sin_theta = torch.sin(theta[:, None])

    # 展开单位向量 k
    kx, ky, kz = k[:, [0]], k[:, [1]], k[:, [2]]

    # 计算旋转矩阵的各个元素
    R_batch = torch.stack([
        cos_theta + kx ** 2 * (1 - cos_theta), kx * ky * (1 - cos_theta) - kz * sin_theta,
        kx * kz * (1 - cos_theta) + ky * sin_theta,
        kx * ky * (1 - cos_theta) + kz * sin_theta, cos_theta + ky ** 2 * (1 - cos_theta),
        ky * kz * (1 - cos_theta) - kx * sin_theta,
        kx * kz * (1 - cos_theta) - ky * sin_theta, ky * kz * (1 - cos_theta) + kx * sin_theta,
        cos_theta + kz ** 2 * (1 - cos_theta)
    ], dim=1).view(-1, 3, 3).to(vec2_batch.device)

    return R_batch

def gaussian_kernel_1d(size, sigma):
    size = int(size)
    sigma = float(sigma)
    kernel = torch.tensor([math.exp(-((x - size // 2) ** 2) / (2 * sigma ** 2)) for x in range(size)])
    kernel /= kernel.sum()
    return kernel

def gaussian_filter_1d_batch(input_data, kernel_size, sigma):
    # Create 1D Gaussian kernel
    kernel = gaussian_kernel_1d(kernel_size, sigma).to(input_data.device)

    # Reshape kernel for convolution
    kernel = kernel.view(1, 1, kernel_size)

    # Adjust kernel size for batch and channel
    kernel = kernel.repeat(input_data.size(1), 1, 1)

    padding_size = kernel_size // 2

    # Apply replicate padding
    padded_input = F.pad(input_data, (padding_size, padding_size), mode='replicate')

    # Apply convolution (gaussian filtering)
    filtered_data = F.conv1d(padded_input, kernel, padding="valid", groups=input_data.size(1))
    return filtered_data

if __name__ == "__main__":
    vec1 = torch.tensor([0, 1.57, 0]).unsqueeze(0).repeat(400,1)
    mat1 = euler_to_rotMat(vec1)
    print(mat1[0,...])
    # vec2 = torch.tensor([0, 1.0, 0]).unsqueeze(0).repeat(400,1)
    # R = rotation_matrix_from_vectors(vec1, vec2)
    # print(R)


def butter_lowpass_filter_np(data, cutoff, fs, dim,order=4):
    nyquist = 0.5 * fs
    normal_cutoff = cutoff / nyquist
    b, a = butter(order, normal_cutoff, btype='low', analog=False)
    return filtfilt(b, a, data)

def butter_lowpass_filter_torch(data, cutoff, fs,axis = 1, order=4):
    nyquist = 0.5 * fs
    normal_cutoff = cutoff / nyquist
    b, a = butter(order, normal_cutoff, btype='low', analog=False)
    return filtfilt(b, a, data,axis = axis)

class LowPassFilter1():
    # cutoff 截止频率
    def __init__(self, cutoff, fs, dim=1):
        # 归一化截止频率
        # normalized_cutoff = cutoff / (0.5 * fs)
        # a = 2pi *cutoff / f 采样频率
        # self.a = 2  * cutoff / fs
        # super(LowPassFilter1, self).__init__()
        self.a = 2 * np.pi * cutoff / fs
        self.dim = dim
    def filter(self, x,dim=2):
        """
        输入一个张量，返回滤波后的结果。
        :param x: 新输入的张量，形状为 [*input_shape]
        :return: 滤波后的张量，形状为 [*input_shape]
        """
        # 确保输入形状正确
        # assert x.shape == self.input_shape, f"输入形状应为 {self.input_shape}, 但得到 {x.shape}"
        y = torch.zeros_like(x)
        y[:,0,:,:] = x[:,0,:,:]
        for i in range(1, x.shape[dim]):
            y[:,i,:,:] = self.a * x[:,i,:,:] + (1 - self.a) * y[:,i-1,:,:]
        return y

    def reset(self, rows=None):
        return None