import argparse
import joblib
import numpy as np
import torch
import mujoco
import mujoco.viewer
import time
from scipy.spatial.transform import Rotation as sRot
import sys
import os
import matplotlib
matplotlib.use('TkAgg')
import matplotlib.pyplot as plt
import matplotlib.widgets as widgets
import matplotlib.font_manager as fm
import glfw
from matplotlib.widgets import Button
import json
import tkinter as tk       # 导入 tkinter
from tkinter import filedialog # 导入 filedialog
import datetime

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']  # 优先使用这些字体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# --- SMPL 解析器处理 ---
# 尝试导入用户环境中的 SMPL 解析器
# 假设 smpl_parser.py 在 humanoid/smpllib 目录下，相对于工作区根目录
try:
    # 动态添加 humanoid 包所在的路径到 sys.path
    # 这假设脚本位于像 scripts/retarget 这样的子目录中
    script_dir = os.path.dirname(os.path.abspath(__file__))
    workspace_root = os.path.abspath(os.path.join(script_dir, '../../')) # 回到工作区根目录
    humanoid_path = os.path.join(workspace_root)
    if humanoid_path not in sys.path:
        print(f"将 {humanoid_path} 添加到 sys.path")
        sys.path.insert(0, humanoid_path)

    from humanoid.smpllib.smpl_parser import SMPL_Parser, SMPL_BONE_ORDER_NAMES
    print("成功导入 humanoid.smpllib.smpl_parser 中的 SMPL_Parser。")
    SmplParserClass = SMPL_Parser
except ImportError as e:
    print(f"无法导入 humanoid.smpllib.smpl_parser: {e}")
    print("将使用 MockSMPLParser 作为替代。请确保您的环境中包含有效的 SMPL 实现。")

    # 定义 SMPL 骨骼顺序（如果导入失败）
    SMPL_BONE_ORDER_NAMES = [
        "Pelvis", "L_Hip", "R_Hip", "Spine1", "L_Knee", "R_Knee", "Spine2", "L_Ankle", "R_Ankle", "Spine3",
        "L_Toe", "R_Toe", "Neck", "L_Collar", "R_Collar", "Head", "L_Shoulder", "R_Shoulder", "L_Elbow",
        "R_Elbow", "L_Wrist", "R_Wrist", "L_Hand", "R_Hand"
    ]

    class MockSMPLParser:
        """用于演示的最小化模拟 SMPL 解析器。"""
        def __init__(self, model_path, gender, device):
            self.device = device
            self.num_joints = 24 # 标准 SMPL
            print(f"模拟 SMPL 解析器已在 {device} 上初始化。")
            # 在实际场景中，这里会加载 SMPL 模型参数

        def get_joints_verts(self, pose_aa, beta, trans):
            """
            模拟函数：返回虚拟的关节位置。
            实际上，这会基于 pose、beta、trans 计算 FK。
            输出形状：(num_frames, num_joints, 3)
            """
            num_frames = pose_aa.shape[0]
            mock_joints = np.zeros((num_frames, self.num_joints, 3))
            # 基础结构 (粗略估计)
            mock_joints[:, 0] = [0, 0, 0.9]   # Pelvis
            mock_joints[:, 1] = [-0.1, 0, 0.9] # L_Hip
            mock_joints[:, 2] = [0.1, 0, 0.9]  # R_Hip
            mock_joints[:, 4] = [-0.1, 0, 0.5] # L_Knee
            mock_joints[:, 5] = [0.1, 0, 0.5]  # R_Knee
            mock_joints[:, 7] = [-0.1, 0, 0.1] # L_Ankle
            mock_joints[:, 8] = [0.1, 0, 0.1]  # R_Ankle
            mock_joints[:, 16] = [-0.3, 0, 1.3]# L_Shoulder
            mock_joints[:, 17] = [0.3, 0, 1.3] # R_Shoulder
            # ... 其他关节

            # 应用全局平移
            trans_np = trans.cpu().numpy()
            mock_joints += trans_np[:, np.newaxis, :]

            # 基于根旋转应用简单"动画"
            root_rot_vec = pose_aa[:, :3].cpu().numpy()
            rots = sRot.from_rotvec(root_rot_vec)
            for i in range(num_frames):
                pelvis_pos = mock_joints[i, 0].copy() # 围绕骨盆旋转
                mock_joints[i] = rots[i].apply(mock_joints[i] - pelvis_pos) + pelvis_pos

            # 添加噪声模拟运动
            mock_joints += np.random.randn(num_frames, self.num_joints, 3) * 0.01

            # 确保脚踝可能低于零以进行接触测试
            time_vec = np.arange(num_frames) / num_frames * 2 * np.pi
            mock_joints[:, 7, 2] -= 0.15 * np.abs(np.sin(time_vec * 2)) # 让左脚踝下降
            mock_joints[:, 8, 2] -= 0.15 * np.abs(np.sin(time_vec * 2 + np.pi)) # 让右脚踝下降

            # 转换为 torch tensor
            mock_verts = torch.from_numpy(mock_joints * 1.1).float().to(self.device) # 虚拟顶点
            mock_joints = torch.from_numpy(mock_joints).float().to(self.device)

            print("警告：正在使用模拟的 SMPL 关节计算。")
            return mock_verts, mock_joints

    SmplParserClass = MockSMPLParser
# --- 结束 SMPL 解析器处理 ---

# SMPL 骨架定义 (24 个关节的父关节索引)
SMPL_PARENTS = np.array([
    -1, 0, 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9, 9, 12, 13, 14, 16, 17, 18, 19, 20, 21
], dtype=np.int32)

def get_bvh_parents(joint_names):
    """根据BVH关节名称生成父关节索引数组"""
    # 这里需要根据实际的BVH文件结构来定义
    # 基于jooging_optimized.bvh的层次结构
    parents = []
    for i, name in enumerate(joint_names):
        if name == "Hips":
            parents.append(-1)  # 根节点
        elif name == "LeftUpLeg":
            parents.append(joint_names.index("Hips"))
        elif name == "LeftLeg":
            parents.append(joint_names.index("LeftUpLeg"))
        elif name == "LeftFoot":
            parents.append(joint_names.index("LeftLeg"))
        elif name == "LeftToeBase":
            parents.append(joint_names.index("LeftFoot"))
        elif name == "RightUpLeg":
            parents.append(joint_names.index("Hips"))
        elif name == "RightLeg":
            parents.append(joint_names.index("RightUpLeg"))
        elif name == "RightFoot":
            parents.append(joint_names.index("RightLeg"))
        elif name == "RightToeBase":
            parents.append(joint_names.index("RightFoot"))
        elif name == "Spine":
            parents.append(joint_names.index("Hips"))
        elif name == "Spine1":
            parents.append(joint_names.index("Spine"))
        elif name == "Neck":
            parents.append(joint_names.index("Spine1"))
        elif name == "Head":
            parents.append(joint_names.index("Neck"))
        elif name == "LeftShoulder":
            parents.append(joint_names.index("Spine1"))
        elif name == "LeftArm":
            parents.append(joint_names.index("LeftShoulder"))
        elif name == "LeftForeArm":
            parents.append(joint_names.index("LeftArm"))
        elif name == "LeftHand":
            parents.append(joint_names.index("LeftForeArm"))
        elif name == "LeftHandThumb":
            parents.append(joint_names.index("LeftHand"))
        elif name == "L_Wrist_End":
            parents.append(joint_names.index("LeftHand"))
        elif name == "RightShoulder":
            parents.append(joint_names.index("Spine1"))
        elif name == "RightArm":
            parents.append(joint_names.index("RightShoulder"))
        elif name == "RightForeArm":
            parents.append(joint_names.index("RightArm"))
        elif name == "RightHand":
            parents.append(joint_names.index("RightForeArm"))
        elif name == "RightHandThumb":
            parents.append(joint_names.index("RightHand"))
        elif name == "R_Wrist_End":
            parents.append(joint_names.index("RightHand"))
        else:
            # 对于未知的关节，假设它是前一个关节的子关节
            parents.append(max(0, i - 1))
    
    return np.array(parents, dtype=np.int32)

def load_smpl_data(filepath, device):
    """加载 SMPL 数据并计算关节位置。"""
    try:
        # 尝试使用 allow_pickle=True 加载，因为 joblib 可能需要它
        data = joblib.load(filepath)
        print(f"已从 {filepath} 加载数据")
    except Exception as e:
        print(f"加载文件 {filepath} 时出错: {e}")
        return None, None, None

    # 假设 pkl 文件包含类似上下文脚本中的键
    # 可能需要处理键的变化
    if not data:
        print(f"错误：文件 {filepath} 为空或格式无法识别。")
        return None, None, None

    # 尝试查找第一个看起来像动作数据的键
    motion_key = None
    for key, value in data.items():
        if isinstance(value, dict) and 'pose_aa' in value and 'trans' in value:
            motion_key = key
            break

    if motion_key is None:
        print(f"错误：在 {filepath} 中未找到有效的动作数据键（例如包含 'pose_aa', 'trans'）。")
        return None, None, None

    motion_data = data[motion_key]
    print(f"正在处理动作键: {motion_key}")

    # 提取数据
    try:
        pose_aa_np = motion_data['pose_aa']
        trans_np = motion_data['trans']
    except KeyError as e:
        print(f"错误：动作数据中缺少键 {e}。")
        return None, None, None

    pose_aa = torch.from_numpy(pose_aa_np).float().to(device)
    trans = torch.from_numpy(trans_np).float().to(device)

    # 处理 beta
    if 'beta' in motion_data:
        betas_np = motion_data['beta']
        if betas_np.ndim == 1:
            betas_np = betas_np[np.newaxis, :] # 如果需要，添加批次维度
        # 使用第一个 beta，或者如果 beta 是每帧都有的，则使用所有
        if betas_np.shape[0] == pose_aa.shape[0]:
             beta = torch.from_numpy(betas_np).float().to(device)
        else:
             beta = torch.from_numpy(betas_np[0:1]).float().to(device) # 使用第一个 beta
             beta = beta.repeat(pose_aa.shape[0], 1) # 为所有帧重复 beta
    else:
        print("警告：数据中未找到 'beta'。使用默认零值。")
        beta = torch.zeros(pose_aa.shape[0], 10, device=device).float() # 默认 beta 形状

    fps = motion_data.get('fps', 30.0) # 如果未指定，默认为 30 fps
    if not isinstance(fps, (int, float)) or fps <= 0:
        print(f"警告：无效的 fps 值 ({fps})。使用默认值 30.0。")
        fps = 30.0

    # 初始化 SMPL 解析器
    try:
        # 修改SMPL模型路径的获取方式
        smpl_model_path = os.path.join(script_dir, "data", "smpl")
        if not os.path.isdir(smpl_model_path):
             print(f"警告：SMPL 模型路径 '{smpl_model_path}' 不存在或不是目录。")
             # 如果使用 Mock 解析器，这可能没问题
             if SmplParserClass == MockSMPLParser:
                 pass # Mock 不需要路径
             else:
                 raise FileNotFoundError(f"SMPL 模型路径 '{smpl_model_path}' 未找到。")

        smpl_parser = SmplParserClass(model_path=smpl_model_path, gender="neutral", device=device)
        smpl_parser.to(device) # 确保整个模型及其参数都在目标设备上
    except Exception as e:
        print(f"初始化 SMPL 解析器失败: {e}")
        print("请确保 SMPL 模型文件在 'data/smpl' 目录下可用。")
        return None, None, None

    # 计算关节位置
    with torch.no_grad():
        # 确保 pose_aa 形状为 (num_frames, 72)
        num_pose_dims = pose_aa.shape[1]
        if num_pose_dims > 72:
            print(f"警告：姿态数据有 {num_pose_dims} 个维度。截断为 72。")
            pose_aa = pose_aa[:, :72]
        elif num_pose_dims < 72:
            print(f"警告：姿态数据有 {num_pose_dims} 个维度。用零填充到 72。")
            padding = torch.zeros(pose_aa.shape[0], 72 - num_pose_dims, device=device)
            pose_aa = torch.cat([pose_aa, padding], dim=1)

        # 确保 trans 形状为 (num_frames, 3)
        if trans.shape[1] != 3:
            print(f"警告：平移数据有 {trans.shape[1]} 个维度。假设使用前 3 个。")
            trans = trans[:, :3]

        # 确保 beta 形状为 (num_frames, 10)
        if beta.shape[1] != 10:
            print(f"警告：Beta 数据有 {beta.shape[1]} 个维度。使用默认零值。")
            beta = torch.zeros(pose_aa.shape[0], 10, device=device).float()
        if beta.shape[0] != pose_aa.shape[0]:
             # 如果 beta 只有一个，重复它
             if beta.shape[0] == 1:
                 beta = beta.repeat(pose_aa.shape[0], 1)
             else: # 形状不匹配且不是单个 beta
                 print(f"错误：Beta 形状 ({beta.shape}) 与姿态形状 ({pose_aa.shape}) 不兼容。")
                 return None, None, None


        smpl_verts, smpl_joints = smpl_parser.get_joints_verts(pose_aa, beta, trans)

    # 确保返回 numpy array
    smpl_joints_np = smpl_joints.cpu().numpy()

    return smpl_joints_np, float(fps), SMPL_BONE_ORDER_NAMES


def load_bvh_data(bvh_file: str):
    from utils.bvh_utils import BVHMotion
    parser = BVHMotion(bvh_file)
    parser.batch_forward_kinematics()

    position_torch = torch.from_numpy(parser.joint_translation)
    bvh_positions_converted = torch.zeros_like(position_torch)
    bvh_positions_converted[..., 0] = position_torch[..., 2]  # x_robot = z_bvh
    bvh_positions_converted[..., 1] = position_torch[..., 0]  # y_robot = x_bvh
    bvh_positions_converted[..., 2] = position_torch[..., 1]  # z_robot = y_bvh

    # 偏移z坐标，让最小值为0.01
    bvh_positions_np = bvh_positions_converted.cpu().numpy()
    min_z = np.min(bvh_positions_np[..., 2])
    if min_z < 0.01:
        z_offset = 0.01 - min_z
        bvh_positions_np[..., 2] += z_offset
        print(f"已偏移z坐标: 最小值从 {min_z:.4f} 调整到 0.01 (偏移量: {z_offset:.4f})")

    return bvh_positions_np, parser.mocap_framerate, parser.joints_name


def calculate_foot_contacts(toe_heights, toe_velocities_z, num_frames, height_threshold, velocity_threshold, manual_overrides=None):
    """根据预计算的高度和速度以及手动覆盖计算二元足部接触序列 (包含滤波)。

    Args:
        toe_heights (np.ndarray): 脚趾高度 [帧数, 2].
        toe_velocities_z (np.ndarray): 脚趾垂直速度 [帧数, 2].
        num_frames (int): 总帧数.
        height_threshold (float): 高度阈值.
        velocity_threshold (float): 速度阈值.
        manual_overrides (np.ndarray | None): 手动覆盖数组 [帧数, 2], -1: 无覆盖, 0: 强制非接触, 1: 强制接触。

    Returns:
        np.ndarray: 最终接触序列 [帧数, 2], True 表示接触。
    """
    if num_frames < 1:
        return np.zeros((0, 2), dtype=bool)
    if toe_heights.shape != (num_frames, 2) or toe_velocities_z.shape != (num_frames, 2):
        raise ValueError("toe_heights 或 toe_velocities_z 的形状不正确")
    if manual_overrides is not None and manual_overrides.shape != (num_frames, 2):
        raise ValueError("manual_overrides 的形状不正确")

    # 1. 基于阈值确定初始接触
    contact_thresh = (toe_heights < height_threshold) & (np.abs(toe_velocities_z) < velocity_threshold)  # shape:(num_frames, True/False)

    # 2. 应用滤波逻辑
    if num_frames > 2:
        filtered_contact = contact_thresh.copy()
        for i in range(1, num_frames - 1):
            # 检查左脚
            if contact_thresh[i-1, 0] == contact_thresh[i+1, 0] and contact_thresh[i, 0] != contact_thresh[i-1, 0]:
                filtered_contact[i, 0] = contact_thresh[i-1, 0] # 恢复到前一帧的状态
            # 检查右脚
            if contact_thresh[i-1, 1] == contact_thresh[i+1, 1] and contact_thresh[i, 1] != contact_thresh[i-1, 1]:
                filtered_contact[i, 1] = contact_thresh[i-1, 1] # 恢复到前一帧的状态
        contact_calculated = filtered_contact
    else:
        contact_calculated = contact_thresh

    # 3. 应用手动覆盖
    if manual_overrides is not None:
        final_contact = contact_calculated.copy()
        override_indices_0 = np.where(manual_overrides == 0)
        override_indices_1 = np.where(manual_overrides == 1)
        final_contact[override_indices_0] = False # 强制非接触
        final_contact[override_indices_1] = True  # 强制接触
        return final_contact
    else:
        # 如果没有 manual_overrides，则返回计算和滤波后的结果
        return contact_calculated

def plot_contact_timeline(
    initial_contacts,        # 初始接触状态
    fps,
    num_frames,
    joint_positions,
    joint_names,
    toe_heights,
    toe_velocities_z,
    initial_height_threshold,
    initial_velocity_threshold
):
    """仅使用 Matplotlib 绘制足部接触时间线和高度曲线，返回绘图对象。
       不再包含键盘事件处理逻辑。
       现在包含一个保存按钮。
    """
    # --- 创建 figure 和 axes (增加一行给按钮，按钮行分两列) ---
    fig = plt.figure(figsize=(12, 9)) # 保持高度
    # 修改 gridspec，最后一行为按钮行，设为 2 列
    gs = fig.add_gridspec(5, 2, height_ratios=[1, 2, 0.2, 0.2, 0.1], width_ratios=[1, 1], hspace=0.4) # 5行2列
    ax1 = fig.add_subplot(gs[0, :]) # 第 0 行，跨所有列
    ax2 = fig.add_subplot(gs[1, :]) # 第 1 行，跨所有列
    ax_height_slider = fig.add_subplot(gs[2, :]) # 第 2 行，跨所有列
    ax_vel_slider = fig.add_subplot(gs[3, :]) # 第 3 行，跨所有列
    ax_load_button = fig.add_subplot(gs[4, 0]) # 第 4 行，第 0 列 (加载按钮)
    ax_save_button = fig.add_subplot(gs[4, 1]) # 第 4 行，第 1 列 (保存按钮)

    time_axis = np.arange(num_frames)

    # --- 绘制初始接触序列 ---
    left_fill = ax1.fill_between(time_axis, 1.1, 1.9, where=initial_contacts[:, 0],
                     color='green', alpha=0.5, step='mid', label='Left Foot Contact')
    right_fill = ax1.fill_between(time_axis, 0.1, 0.9, where=initial_contacts[:, 1],
                     color='red', alpha=0.5, step='mid', label='Right Foot Contact')

    # --- 添加垂直线, 设置 y 轴等 ---
    vline1 = ax1.axvline(0, color='blue', lw=2, label='Current Frame')
    vline2 = ax2.axvline(0, color='blue', lw=2)
    ax1.set_yticks([0.5, 1.5])
    ax1.set_yticklabels(['Right Foot', 'Left Foot'])
    ax1.set_xlim(0, num_frames - 1)
    ax1.set_ylim(0, 2)
    ax1.set_title("Foot Contact Sequence") # 移除按键提示
    ax1.legend(loc='upper right')

    # --- 绘制高度曲线 ---
    try:
        # 适配BVH关节名称
        left_ankle_idx = joint_names.index("LeftFoot")
        right_ankle_idx = joint_names.index("RightFoot")
        left_toe_idx = joint_names.index("LeftToeBase")
        right_toe_idx = joint_names.index("RightToeBase")
    except ValueError as e:
        try:
            left_ankle_idx = joint_names.index("L_Ankle")
            right_ankle_idx = joint_names.index("R_Ankle")
            left_toe_idx = joint_names.index("L_Toe")
            right_toe_idx = joint_names.index("R_Toe")
        except ValueError as e:
            print(f"错误：在 joint_names 中找不到脚踝或脚趾关节: {e}")
            print(f"可用的关节名称: {joint_names}")
            return None, None, None, None, None, None, None  # 返回 None 以指示错误

    left_ankle_heights = joint_positions[:, left_ankle_idx, 2]
    right_ankle_heights = joint_positions[:, right_ankle_idx, 2]
    left_toe_heights_for_plot = toe_heights[:, 0]
    right_toe_heights_for_plot = toe_heights[:, 1]

    left_ankle_line, = ax2.plot(time_axis, left_ankle_heights, 'g-', label='Left Ankle Height')
    right_ankle_line, = ax2.plot(time_axis, right_ankle_heights, 'r-', label='Right Ankle Height')
    left_toe_line, = ax2.plot(time_axis, left_toe_heights_for_plot, 'g--', label='Left Toe Height')
    right_toe_line, = ax2.plot(time_axis, right_toe_heights_for_plot, 'r--', label='Right Toe Height')

    height_threshold_line = ax2.axhline(y=initial_height_threshold, color='gray', linestyle=':', label='Height Threshold')

    ax2.set_xlabel("Frame Index")
    ax2.set_ylabel("Height (m)")
    ax2.set_xlim(0, num_frames - 1)
    ax2.set_title("Foot Heights")
    ax2.legend(loc='upper right')
    ax2.grid(True)

    # --- 添加滑块 (仅创建，不附加回调) ---
    height_slider = widgets.Slider(
        ax=ax_height_slider,
        label='Height Threshold (m)',
        valmin=0.01,
        valmax=0.2,
        valinit=initial_height_threshold,
        valstep=0.001
    )

    vel_slider = widgets.Slider(
        ax=ax_vel_slider,
        label='Velocity Threshold (m/s)',
        valmin=0.01,
        valmax=0.5,
        valinit=initial_velocity_threshold,
        valstep=0.01
    )

    # --- 创建加载和保存按钮 ---
    load_button = Button(ax_load_button, "Load Contacts")
    save_button = Button(ax_save_button, 'Save Contacts') # 改为英文标签

    # --- 添加说明文本和调整布局 ---
    fig.text(0.02, 0.02,
              "Controls (Plot Window): Space=Play/Pause, Left/Right=Toggle Contact (paused), "
              "./,=Prev/Next Frame (paused), +/-=Speed, R=Reset, Esc/Q=Quit",
              fontsize=8)
    fig.tight_layout(rect=[0, 0.05, 1, 1]) # 调整矩形区域为按钮和文本留出空间
    # fig.subplots_adjust(bottom=0.15) # 调整底部边距，由 tight_layout 处理

    plt.ion()  # 保持交互模式

    # 返回所有需要的绘图组件，包括两个按钮
    return fig, (ax1, ax2), (vline1, vline2), (height_slider, vel_slider), (left_fill, right_fill), time_axis, height_threshold_line, load_button, save_button

# 确定工作区根目录
script_dir = os.path.dirname(os.path.abspath(__file__))
# workspace_root = os.path.abspath(os.path.join(script_dir, '../../'))
# 定义默认的接触序列目录
default_contact_dir = os.path.join(script_dir, "data", "contact_seq")

def visualize_contact(
    joint_positions,
    fps,
    joint_names,
    toe_heights,
    toe_velocities_z,
    initial_height_threshold,
    initial_velocity_threshold,
    manual_overrides, # 接收 manual_overrides
    default_contact_dir, # 新增参数
    args  # 添加args参数
):
    """使用 MuJoCo 可视化 BVH 骨架和接触，并显示可交互的接触时间线。
       键盘事件现在由 Matplotlib 窗口处理。
    """
    num_frames, num_joints, _ = joint_positions.shape
    if num_frames == 0:
        print("没有帧可供可视化。")
        return None

    file_type = "BVH"
    try:
        # 适配BVH关节名称
        left_foot_idx = joint_names.index("LeftFoot")
        right_foot_idx = joint_names.index("RightFoot")
        left_toe_idx = joint_names.index("LeftToeBase")
        right_toe_idx = joint_names.index("RightToeBase")
    except ValueError as e:
        try:
            left_foot_idx = joint_names.index("L_Ankle")  # 7
            right_foot_idx = joint_names.index("R_Ankle")  # 8
            left_toe_idx = joint_names.index("L_Toe")  # 10
            right_toe_idx = joint_names.index("R_Toe")  # 11
            file_type = 'SMPL'
        except ValueError as e:
            print(f"错误：无法在 joint_names 中找到脚部关节: {e}")
            print(f"可用的关节名称: {joint_names}")
            return None

    # --- 初始化绘图对象变量 ---
    contact_fig, contact_axes, contact_vlines, sliders = None, None, None, (None, None)
    height_slider, vel_slider = None, None
    ax1, ax2 = None, None
    vline1, vline2 = None, None
    left_fill, right_fill = None, None
    time_axis = None
    height_threshold_line = None
    load_button = None # 初始化加载按钮变量
    save_button = None # 初始化保存按钮变量

    # --- 计算初始接触状态 三条准则：1. 高度阈值 2. 速度阈值 3. 手动覆盖 ---
    initial_contacts = calculate_foot_contacts(
        toe_heights, toe_velocities_z, num_frames,
        initial_height_threshold, initial_velocity_threshold,
        manual_overrides
    )
    print(f"\n--- 用于初始化绘图的{file_type}最终接触计算 (考虑初始阈值和覆盖) ---")
    print(f"左脚趾接触帧数: {np.sum(initial_contacts[:, 0])} / {num_frames}")
    print(f"右脚趾接触帧数: {np.sum(initial_contacts[:, 1])} / {num_frames}")
    print("-" * 30 + "\n")

    # --- 创建绘图 --- # (移除内部键盘处理)
    try:
        plot_results = plot_contact_timeline(
            initial_contacts=initial_contacts,
            fps=fps,
            num_frames=num_frames,
            joint_positions=joint_positions,
            joint_names=joint_names,
            toe_heights=toe_heights,
            toe_velocities_z=toe_velocities_z,
            initial_height_threshold=initial_height_threshold,
            initial_velocity_threshold=initial_velocity_threshold
        )
        # 解包绘图组件 (增加 load_button, save_button)
        (contact_fig, contact_axes, contact_vlines, sliders,
         plot_fills, time_axis, height_threshold_line,
         load_button, save_button) = plot_results # 获取 load_button 和 save_button

        if contact_fig is None: # 检查 plot_contact_timeline 是否成功
            raise RuntimeError("plot_contact_timeline 未能成功创建绘图对象。")

        ax1, ax2 = contact_axes
        vline1, vline2 = contact_vlines
        height_slider, vel_slider = sliders
        left_fill, right_fill = plot_fills

        # --- 添加说明文本到 Matplotlib 图形 --- #
        # contact_fig.text(0.02, 0.02,
        #           "Controls (Plot Window): Space=Play/Pause, Left/Right=Toggle Contact (paused), "
        #           "./,=Prev/Next Frame (paused), +/-=Speed, R=Reset, Esc/Q=Quit",
        #           fontsize=8)

    except Exception as e:
        print(f"创建接触时间线图失败: {e}")
        import traceback
        traceback.print_exc()
        return

    # --- MuJoCo 设置 (不变) ---
    xml = """
    <mujoco>
      <option gravity="0 0 0"/>
      <worldbody>
        <light diffuse=".8 .8 .8" pos="0 0 30" dir="0 0 -1"/>
        <geom type="plane" size="10 10 0.1" rgba=".9 .9 .9 1" contype="0" conaffinity="0"/>
      </worldbody>
      <visual>
        <map znear="0.01"/>
      </visual>
    </mujoco>
    """
    try:
        model = mujoco.MjModel.from_xml_string(xml)
        data = mujoco.MjData(model)
    except Exception as e:
        print(f"从 XML 创建 MuJoCo 模型失败: {e}")
        if contact_fig and plt.fignum_exists(contact_fig.number):
            plt.close(contact_fig)
        return

    viewer = None
    joint_size = 0.018
    bone_size = 0.012
    colors = {
        "default_joint": np.array([0.3, 0.5, 0.9, 1.0]),
        "default_bone": np.array([0.5, 0.5, 0.5, 1.0]),
        "contact_joint": np.array([0.1, 0.8, 0.1, 1.0]),
        "no_contact_joint": np.array([0.9, 0.2, 0.2, 1.0])
    }

    # --- 共享状态变量 (移到这里，方便下面的辅助函数访问) ---
    frame_idx = [0]
    paused = [False]
    playback_speed = [1.0]
    last_render_time = time.time()
    is_running = [True]
    plot_update_needed = [False]

    # --- 辅助函数：计算显示宽度和填充 (移到循环外部) ---
    def get_display_width(text):
        """计算字符串在终端中的大致显示宽度，中文字符算 2，其他算 1。"""
        width = 0
        for char in text:
            # 使用简单的 Unicode 范围判断常见 CJK 字符
            if ('\u4e00' <= char <= '\u9fff' or  # CJK Unified Ideographs
                '\u3000' <= char <= '\u303f' or  # CJK Symbols and Punctuation
                '\uff00' <= char <= '\uffef'):   # Fullwidth Forms
                width += 2
            else:
                width += 1
        return width

    def pad_string_to_width(s, target_width):
        """使用空格将字符串填充到目标显示宽度。"""
        current_width = get_display_width(s)
        padding_needed = max(0, target_width - current_width)
        return s + " " * padding_needed
    # -------------------------------------------------------

    final_contacts = initial_contacts.copy() # Keep track of the full filtered+override sequence

    # --- 定义滑块更新回调函数 --- #
    def update_plot_from_sliders(val):
        # Use nonlocal to modify the final_contacts defined in visualize_smpl scope
        nonlocal left_fill, right_fill, manual_overrides, height_threshold_line
        nonlocal height_slider, vel_slider, toe_heights, toe_velocities_z, num_frames
        nonlocal ax1, time_axis, contact_fig, plot_update_needed

        if not all([contact_fig, plt.fignum_exists(contact_fig.number), ax1, height_slider, vel_slider]):
             return

        # Just set the flag, recalculation happens in the main loop
        plot_update_needed[0] = True

        # Optional: Update the threshold line immediately for visual feedback
        new_height_threshold = height_slider.val
        if height_threshold_line:
            height_threshold_line.set_ydata([new_height_threshold, new_height_threshold])
            if contact_fig and plt.fignum_exists(contact_fig.number):
                 try:
                      contact_fig.canvas.draw_idle() # Draw only the line update
                 except Exception as slider_draw_e:
                     print(f"从滑块回调绘制阈值线时出错: {slider_draw_e}")

        # The full final_contacts recalculation and plot update happens
        # in the main loop when plot_update_needed is checked.

    # --- 注册滑块回调 (不变) ---
    if height_slider: height_slider.on_changed(update_plot_from_sliders)
    if vel_slider: vel_slider.on_changed(update_plot_from_sliders)

    # --- 定义加载按钮的回调函数 ---
    def load_callback(event):
        nonlocal manual_overrides, height_slider, vel_slider, num_frames, plot_update_needed

        # --- 使用文件对话框让用户选择文件 --- #
        try:
            # 确保初始目录存在
            os.makedirs(default_contact_dir, exist_ok=True)
            root = tk.Tk()
            root.withdraw() # 隐藏主窗口
            filepath = filedialog.askopenfilename(
                title="选择要加载的接触序列 JSON 文件",
                initialdir=default_contact_dir, # 使用默认目录
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            root.destroy() # 销毁临时窗口
        except Exception as tk_error:
             print(f"\n打开文件对话框时出错: {tk_error}")
             # 尝试不创建 root 窗口再次打开 (某些环境可能不需要)
             try:
                 filepath = filedialog.askopenfilename(
                    title="选择要加载的接触序列 JSON 文件",
                    initialdir=default_contact_dir, # 仍然使用默认目录
                    filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
                 )
             except Exception as fallback_tk_error:
                 print(f"备用方法打开文件对话框也失败: {fallback_tk_error}")
                 return

        if not filepath: # 用户取消选择
            print("\n加载操作已取消。")
            return

        load_filename = filepath # 使用用户选择的文件路径
        # 注释掉固定的加载文件名
        # load_filename = "manual_saved_contacts.json" # 固定加载文件名

        if not os.path.exists(load_filename):
            print(f"\n错误：无法加载，文件不存在: {load_filename}") # 文件对话框通常会保证存在，但以防万一
            return

        try:
            with open(load_filename, 'r') as f:
                loaded_data = json.load(f)

            # 验证加载的数据
            if not all(k in loaded_data for k in ['overrides', 'height_threshold', 'velocity_threshold']):
                print(f"\n错误：加载的文件 {load_filename} 缺少必要的键 ('overrides', 'height_threshold', 'velocity_threshold')。")
                return

            loaded_overrides_list = loaded_data['overrides']
            loaded_height_thresh = float(loaded_data['height_threshold'])
            loaded_vel_thresh = float(loaded_data['velocity_threshold'])

            # 转换并验证 overrides
            try:
                loaded_overrides_np = np.array(loaded_overrides_list, dtype=int)
                if loaded_overrides_np.shape != (num_frames, 2):
                    print(f"\n错误：加载的 'overrides' 形状 ({loaded_overrides_np.shape}) 与当前动画帧数 ({num_frames}) 不匹配。")
                    return
                if not np.all(np.isin(loaded_overrides_np, [-1, 0, 1])):
                    print("\n错误：加载的 'overrides' 包含无效值 (只允许 -1, 0, 1)。")
                    return
            except Exception as convert_e:
                print(f"\n错误：无法将加载的 'overrides' 转换为 NumPy 数组: {convert_e}")
                return

            # --- 更新状态 --- #
            # 直接修改全局 manual_overrides 数组
            np.copyto(manual_overrides, loaded_overrides_np)

            # 更新滑块值 (这通常会触发 update_plot_from_sliders)
            if height_slider: height_slider.set_val(loaded_height_thresh)
            if vel_slider: vel_slider.set_val(loaded_vel_thresh)

            print(f"\n已成功从 {load_filename} 加载接触状态和阈值。")
            plot_update_needed[0] = True # 确保即使滑块值没变也更新绘图

        except json.JSONDecodeError as e:
            print(f"\n加载 JSON 文件 {load_filename} 时出错：无效的 JSON 格式 - {e}")
        except Exception as e:
            print(f"\n加载接触状态文件 {load_filename} 时发生意外错误: {e}")

    # --- 定义保存按钮的回调函数 ---
    def save_callback(event):
        # This still needs access to the up-to-date final_contacts
        nonlocal final_contacts # Keep nonlocal here for reading in save callback
        nonlocal toe_heights, toe_velocities_z, num_frames, height_slider, vel_slider, manual_overrides
        if height_slider is None or vel_slider is None:
            print("\n错误：滑块未初始化，无法保存。")
            return

        current_height_threshold = height_slider.val
        current_vel_threshold = vel_slider.val

        # 使用当前阈值和覆盖状态计算最终接触
        final_contacts = calculate_foot_contacts(
            toe_heights, toe_velocities_z, num_frames,
            current_height_threshold, current_vel_threshold,
            manual_overrides
        )

        # 定义保存文件路径 (使用默认目录)
        fixed_filename = args.save_filename  # 使用命令行参数指定的文件名
        save_filename = os.path.join(default_contact_dir, fixed_filename)
        try:
            # 确保目录存在
            os.makedirs(default_contact_dir, exist_ok=True)

            # 手动构建 JSON 字符串以控制格式
            output_lines = []
            output_lines.append("{")
            output_lines.append(f'  "height_threshold": {json.dumps(float(current_height_threshold))},')
            output_lines.append(f'  "velocity_threshold": {json.dumps(float(current_vel_threshold))},')

            output_lines.append('  "contacts": [')
            contact_list = final_contacts.tolist()
            for i, frame_contact in enumerate(contact_list):
                line = f'    {json.dumps(frame_contact)}'
                if i < len(contact_list) - 1:
                    line += ","
                output_lines.append(line)
            output_lines.append('  ],')

            output_lines.append('  "overrides": [')
            override_list = manual_overrides.tolist()
            for i, frame_override in enumerate(override_list):
                line = f'    {json.dumps(frame_override)}'
                if i < len(override_list) - 1:
                    line += ","
                output_lines.append(line)
            output_lines.append('  ]') # 最后一个列表后面没有逗号

            output_lines.append("}")

            # 将构建好的行写入文件
            with open(save_filename, 'w') as f:
                f.write("\n".join(output_lines))

            print(f"最终足部接触序列和覆盖状态已保存到 {save_filename}")
        except Exception as e:
            print(f"保存最终接触序列到 {save_filename} 失败: {e}")

    # --- 连接按钮的回调 ---
    if load_button: load_button.on_clicked(load_callback)
    if save_button: save_button.on_clicked(save_callback)

    # --- 定义统一的键盘回调 (在 visualize_smpl 内部) ---
    def on_key_press(event):
        # This function only modifies manual_overrides and sets the update flag.
        # It does NOT recalculate final_contacts directly.
        nonlocal is_running, paused, frame_idx, playback_speed, manual_overrides, plot_update_needed
        # Note: No nonlocal final_contacts needed here as it's not assigned directly

        key = event.key
        # print(f"Matplotlib Key: {key}") # Debug

        # --- 导航和播放控制 (使用 Matplotlib key names) ---
        if key == ' ': # Space
            paused[0] = not paused[0]
        elif key == '.': # . (下一帧)
            if paused[0]:
                frame_idx[0] = (frame_idx[0] + 1) % num_frames
                plot_update_needed[0] = True
        elif key == ',': # , (上一帧)
            if paused[0]:
                frame_idx[0] = (frame_idx[0] - 1 + num_frames) % num_frames
                plot_update_needed[0] = True
        elif key == '=' or key == '+': # = or +
            playback_speed[0] = min(5.0, playback_speed[0] * 1.5)
        elif key == '-': # -
            playback_speed[0] = max(0.1, playback_speed[0] / 1.5)
        elif key == 'r': # R
            frame_idx[0] = 0
            paused[0] = False # 重置后开始播放
            plot_update_needed[0] = True
        elif key == 'escape' or key == 'q': # Esc/Q
            is_running[0] = False
            print("\n请求退出...")
            return

        # --- 接触状态修改 (使用 Matplotlib key names) ---
        elif key == 'left' or key == 'right':
            if not paused[0]:
                print("\r请先按空格键暂停以修改接触状态。           ", end="")
                return

            current_frame_int = int(frame_idx[0])
            foot_index = 0 if key == 'left' else 1
            foot_name = "左脚" if foot_index == 0 else "右脚"
            current_override = manual_overrides[current_frame_int, foot_index]

            if current_override == -1:
                new_override = 0; new_mode_str = "强制不接触"
            elif current_override == 0:
                new_override = 1; new_mode_str = "强制接触"
            else: # current_override == 1
                new_override = -1; new_mode_str = "自动"

            manual_overrides[current_frame_int, foot_index] = new_override
            print(f"\r帧 {current_frame_int}: {foot_name} 接触状态切换为 -> {new_mode_str}           ", end="")
            plot_update_needed[0] = True # Signal that plot needs update in the main loop

    # --- 连接键盘事件到 Matplotlib Figure ---
    if contact_fig:
        contact_fig.canvas.mpl_connect('key_press_event', on_key_press)

    # --- 启动查看器 (移除 key_callback) ---
    print("\n启动 MuJoCo 查看器 (键盘控制在 Matplotlib 窗口)...")
    print(f"正在可视化BVH文件: {args.bvh_file}")
    # print 控制信息已移到 Matplotlib 图形中

    if contact_fig:
        contact_fig.show()
        try:
            contact_fig.canvas.draw_idle()
            plt.pause(0.5)
        except Exception as draw_err:
            print(f"尝试强制绘制 Matplotlib 窗口时出错: {draw_err}")

    try:
        # 不再传递 key_callback
        viewer = mujoco.viewer.launch_passive(model, data)
        if viewer is None:
             raise RuntimeError("无法启动 MuJoCo 查看器")

        viewer.cam.distance = 3.5
        viewer.cam.azimuth = 90
        viewer.cam.elevation = -15
        initial_lookat = joint_positions[0, 0, :]
        viewer.cam.lookat[:] = initial_lookat

        # --- 主循环 --- #
        while viewer.is_running() and is_running[0]:
            loop_start_time = time.time()

            # --- 更新帧索引 (由键盘回调或自动播放处理) ---
            time_since_last_render = loop_start_time - last_render_time
            if not paused[0] and time_since_last_render > 0:
                frame_increment = time_since_last_render * fps * playback_speed[0]
                new_frame_idx = (frame_idx[0] + frame_increment)
                # 如果帧索引因为播放而更新，也需要更新绘图
                if int(new_frame_idx) != int(frame_idx[0]):
                     plot_update_needed[0] = True
                frame_idx[0] = new_frame_idx % num_frames

            current_frame_int = int(frame_idx[0])
            frame_pos = joint_positions[current_frame_int]

            # --- 获取当前滑块阈值 (不变) ---
            current_height_thresh = initial_height_threshold
            current_vel_thresh = initial_velocity_threshold
            if height_slider is not None: current_height_thresh = height_slider.val
            if vel_slider is not None: current_vel_thresh = vel_slider.val

            # --- 获取当前帧的最终接触状态 (从 potentially updated final_contacts) ---
            # final_contacts is updated above if plot_update_needed was true
            if current_frame_int < len(final_contacts):
                frame_contacts = final_contacts[current_frame_int, :]
            else: # Should not happen if logic is correct, but safe guard
                frame_contacts = [False, False] # Default if out of bounds

            # --- 更新接触时间线图 (如果需要) ---
            if plot_update_needed[0]:
                plot_update_needed[0] = False # Reset flag
                if contact_fig and plt.fignum_exists(contact_fig.number) and ax1 and time_axis is not None:
                    try:
                        # Recalculate final_contacts here, using current slider values and overrides
                        current_height_thresh_plot = height_slider.val if height_slider else initial_height_threshold
                        current_vel_thresh_plot = vel_slider.val if vel_slider else initial_velocity_threshold

                        # Assign to the final_contacts in the outer scope
                        final_contacts = calculate_foot_contacts(
                            toe_heights, toe_velocities_z, num_frames,
                            current_height_thresh_plot, current_vel_thresh_plot,
                            manual_overrides
                        )

                        # Update vertical lines
                        vline1.set_xdata([current_frame_int])
                        vline2.set_xdata([current_frame_int])

                        # Update plot fills using the newly calculated final_contacts
                        if left_fill: left_fill.remove()
                        if right_fill: right_fill.remove()
                        left_fill = ax1.fill_between(time_axis, 1.1, 1.9, where=final_contacts[:, 0],
                                                     color='green', alpha=0.5, step='mid')
                        right_fill = ax1.fill_between(time_axis, 0.1, 0.9, where=final_contacts[:, 1],
                                                     color='red', alpha=0.5, step='mid')

                    except Exception as plot_update_e:
                        print(f"\n在主循环中更新 Matplotlib 图表时出错: {plot_update_e}") # Added newline
                else:
                    # Use \r only if plot exists
                    print("无法更新 Matplotlib：图表未初始化或已关闭。" + (" " * 20) , end=("\r" if contact_fig else "\n"))

            # --- 在主循环的这个位置统一执行绘图更新 ---
            if contact_fig and plt.fignum_exists(contact_fig.number):
                try:
                    contact_fig.canvas.draw_idle()
                    plt.pause(0.001) # 需要短暂暂停以处理事件
                except Exception as final_draw_e:
                     print(f"在主循环末尾绘制 Matplotlib 时出错: {final_draw_e}")
                     if not plt.fignum_exists(contact_fig.number):
                         contact_fig = None # 标记绘图已关闭

            # --- 将几何体添加到场景 (不变) ---
            viewer.user_scn.ngeom = 0
            geom_count = 0

            # --- 添加接触指示器 (不变) ---
            indicator_radius = 0.04
            indicator_height = 0.002
            indicator_color = np.array([0.1, 1.0, 0.1, 0.8])
            indicator_color_transparent = np.array([0.0, 0.0, 0.0, 0.0], dtype=np.float32)

            if geom_count < viewer.user_scn.maxgeom:
                left_toe_pos = frame_pos[left_toe_idx]
                indicator_pos = np.array([left_toe_pos[0], left_toe_pos[1], indicator_height / 2.0])
                current_indicator_color = indicator_color if frame_contacts[0] else indicator_color_transparent
                mujoco.mjv_initGeom(
                    viewer.user_scn.geoms[geom_count], type=mujoco.mjtGeom.mjGEOM_CYLINDER,
                    size=np.array([indicator_radius, indicator_height / 2.0, 0.0]), pos=indicator_pos.astype(np.float64),
                    mat=np.eye(3).flatten(), rgba=current_indicator_color
                )
                geom_count += 1

            if geom_count < viewer.user_scn.maxgeom:
                right_toe_pos = frame_pos[right_toe_idx]
                indicator_pos = np.array([right_toe_pos[0], right_toe_pos[1], indicator_height / 2.0])
                current_indicator_color = indicator_color if frame_contacts[1] else indicator_color_transparent
                mujoco.mjv_initGeom(
                    viewer.user_scn.geoms[geom_count], type=mujoco.mjtGeom.mjGEOM_CYLINDER,
                    size=np.array([indicator_radius, indicator_height / 2.0, 0.0]), pos=indicator_pos.astype(np.float64),
                    mat=np.eye(3).flatten(), rgba=current_indicator_color
                )
                geom_count += 1

            # --- 绘制骨架 (不变) ---
            for j_idx in range(num_joints):
                if geom_count >= viewer.user_scn.maxgeom: break
                color = colors["default_joint"].copy()
                if j_idx == left_foot_idx:
                    color = colors["contact_joint"] if frame_contacts[0] else colors["no_contact_joint"]
                elif j_idx == right_foot_idx:
                    color = colors["contact_joint"] if frame_contacts[1] else colors["no_contact_joint"]
                mujoco.mjv_initGeom(
                    viewer.user_scn.geoms[geom_count], type=mujoco.mjtGeom.mjGEOM_SPHERE,
                    size=np.array([joint_size, 0, 0]), pos=frame_pos[j_idx].astype(np.float64),
                    mat=np.eye(3).flatten(), rgba=color.astype(np.float32)
                )
                geom_count += 1

            # 使用BVH的父关节索引
            if file_type == 'BVH':
                bvh_parents = get_bvh_parents(joint_names)
            else:
                bvh_parents = SMPL_PARENTS
            for j_idx in range(num_joints):
                if geom_count >= viewer.user_scn.maxgeom: break
                parent_idx = bvh_parents[j_idx]
                if parent_idx != -1:
                    start_pos = frame_pos[parent_idx]
                    end_pos = frame_pos[j_idx]
                    vec = end_pos - start_pos
                    length = np.linalg.norm(vec)
                    if length > 1e-6:
                        mid_point = (start_pos + end_pos) / 2.0
                        direction = vec / length
                        z_axis = np.array([0, 0, 1.0])
                        dot_prod = np.clip(np.dot(z_axis, direction), -1.0, 1.0)
                        angle = np.arccos(dot_prod)
                        axis = np.cross(z_axis, direction)
                        if np.linalg.norm(axis) < 1e-6:
                             rot_mat = np.eye(3) if dot_prod > 0 else sRot.from_euler('y', np.pi).as_matrix()
                        else:
                            axis /= np.linalg.norm(axis)
                            rot_mat = sRot.from_rotvec(angle * axis).as_matrix()
                        mujoco.mjv_initGeom(
                            viewer.user_scn.geoms[geom_count], type=mujoco.mjtGeom.mjGEOM_CAPSULE,
                            size=np.array([bone_size, length / 2.0, 0.0], dtype=np.float64),
                            pos=mid_point.astype(np.float64), mat=rot_mat.flatten(),
                            rgba=colors["default_bone"].astype(np.float32)
                        )
                        geom_count += 1

            viewer.user_scn.ngeom = geom_count

            # --- 更新相机焦点和同步 (不变) ---
            target_lookat = frame_pos[0]
            viewer.cam.lookat[:] = 0.8 * viewer.cam.lookat[:] + 0.2 * target_lookat
            viewer.sync()
            last_render_time = loop_start_time

            # --- 打印状态行 (不变) ---
            if is_running[0]:
                try:
                    left_mode_val = manual_overrides[current_frame_int, 0]
                    right_mode_val = manual_overrides[current_frame_int, 1]
                    left_mode_str = "自动" if left_mode_val == -1 else ("强制不接触" if left_mode_val == 0 else "强制接触")
                    right_mode_str = "自动" if right_mode_val == -1 else ("强制不接触" if right_mode_val == 0 else "强制接触")
                    left_contact_str = "接触" if frame_contacts[0] else "未接触"
                    right_contact_str = "接触" if frame_contacts[1] else "未接触"
                    pause_str = "(暂停)" if paused[0] else "(播放)"

                    # 计算左右脚的状态字符串
                    left_status_str = f"{left_contact_str} ({left_mode_str})"
                    right_status_str = f"{right_contact_str} ({right_mode_str})"

                    # --- 使用辅助函数进行宽度计算和填充 ---
                    # 确定一个足够大的固定显示宽度 (基于显示宽度计算)
                    target_display_width = 24 # 重新计算 "未接触 (强制不接触)" 宽度约为21，留余量

                    # 填充左右状态字符串到目标宽度
                    padded_left_status = pad_string_to_width(left_status_str, target_display_width)
                    padded_right_status = pad_string_to_width(right_status_str, target_display_width)
                    # ---------------------------------------

                    status_line = (
                        f"\r帧: {current_frame_int:04d}/{num_frames-1} {pause_str} | "
                        f"左: {padded_left_status} | "
                        f"右: {padded_right_status}"
                    )
                    print(status_line, end="")
                except IndexError:
                    pass # 帧索引可能暂时超出范围
                except Exception as print_err:
                    # 避免使用 \r 如果可能导致问题
                    print(f"打印状态时出错: {print_err}") # 移除 \r

            # --- 控制循环速率 (不变) --
            elapsed = time.time() - loop_start_time
            sleep_time = max(0, (1.0 / 60.0) - elapsed)
            time.sleep(sleep_time)

    except Exception as e:
        print(f"\n可视化过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print() # 结束状态行打印
        final_height_thresh = initial_height_threshold
        final_vel_thresh = initial_velocity_threshold
        if height_slider is not None: final_height_thresh = height_slider.val
        if vel_slider is not None: final_vel_thresh = vel_slider.val

        if viewer and viewer.is_running():
            viewer.close()
        if contact_fig and plt.fignum_exists(contact_fig.number):
             plt.close(contact_fig)
        print("BVH可视化结束。")
        # Return the final state
        return final_height_thresh, final_vel_thresh, manual_overrides


def main():
    parser = argparse.ArgumentParser(description="使用 MuJoCo 可视化 BVH/SMPL 数据并计算/编辑足部接触。")
    parser.add_argument("--bvh_file", default="data/bvh/walk_forward.bvh", type=str, help="BVH 文件路径。")
    parser.add_argument("--smpl_file", default="", type=str, help="SMPL 文件路径。")
    parser.add_argument("--device", type=str, default="cuda:0" if torch.cuda.is_available() else "cpu", help="使用的设备 (cpu 或 cuda:0)。")
    parser.add_argument("--height_thresh", type=float, default=0.12, help="接触检测的高度阈值 (米)。")
    parser.add_argument("--vel_thresh", type=float, default=0.10, help="接触检测的垂直速度阈值 (米/秒)。")
    parser.add_argument("--save_contacts", action="store_true", help="结束程序后，自动保存接触序列到data/contact_seq/日期_manual_contacts.json")
    parser.add_argument("--load_overrides", type=str, default=None, help="从 .json 文件加载手动覆盖状态 (可选)。")
    parser.add_argument("--save_filename", type=str, default="manual_saved_contacts.json", help="指定保存接触序列的文件名（默认：manual_saved_contacts.json）。")
    args = parser.parse_args()

    # --- 在 main 函数中也确定根目录和默认接触目录 --- #
    main_script_dir = os.path.dirname(os.path.abspath(__file__))
    main_default_contact_dir = os.path.join(main_script_dir, "data", "contact_seq")  


    if args.bvh_file == "" and args.smpl_file == "":
        print("No input files!")
        return

    is_bvh = False
    if args.bvh_file != "":
        is_bvh = True


    device = torch.device(args.device)
    print(f"使用设备: {device}")


    if is_bvh:
        if not os.path.exists(args.bvh_file):
            print(f"错误：输入文件 '{args.bvh_file}' 不存在。")
            return

        # 使用BVH数据而不是SMPL数据
        joint_positions, fps, joint_names = load_bvh_data(args.bvh_file)
        if joint_positions is None:
            print("加载或处理 BVH 数据失败。")
            return
    else:
        if not os.path.exists(args.smpl_file):
            print(f"错误：输入文件 '{args.smpl_file}' 不存在。")
            return

        joint_positions, fps, joint_names = load_smpl_data(args.smpl_file, device)
        if joint_positions is None:
            print("加载或处理 smpl 数据失败。")
            return

    num_frames = joint_positions.shape[0]
    if num_frames == 0:
        print("错误：加载的数据没有帧。")
        return


    try:
        left_toe_idx = joint_names.index("LeftToeBase")
        right_toe_idx = joint_names.index("RightToeBase")
    except ValueError as e:
        try:
            left_toe_idx = joint_names.index("L_Toe")
            right_toe_idx = joint_names.index("R_Toe")
        except ValueError as e:
            print(f"错误：在 bvh_joint_names 中找不到脚趾关节: {e}")
            print(f"可用的关节名称: {joint_names}")
            return

    toe_positions = joint_positions[:, [left_toe_idx, right_toe_idx], :]
    toe_heights = toe_positions[:, :, 2]
    toe_velocities_z = np.zeros_like(toe_heights)

    if num_frames >= 2:
        dt = 1.0 / fps
        toe_velocities_z[1:-1] = (toe_heights[2:] - toe_heights[:-2]) / (2.0 * dt)
        toe_velocities_z[0] = (toe_heights[1] - toe_heights[0]) / dt
        toe_velocities_z[-1] = (toe_heights[-1] - toe_heights[-2]) / dt

    # --- 初始化或加载 manual_overrides (加载逻辑改进为 JSON) ---
    manual_overrides = np.full((num_frames, 2), -1, dtype=int)  # -1：表示未手动修改（使用自动检测的接触状态）0：表示手动设置为未接触1：表示手动设置为接触
    if args.load_overrides:
        # --- 确定最终加载路径 --- #
        load_path = args.load_overrides
        # 如果用户提供的路径不包含分隔符，则假定是相对于默认目录的文件名
        if os.sep not in load_path and (not os.path.isabs(load_path)):
             load_path = os.path.join(main_default_contact_dir, load_path)
             print(f"信息：将加载路径解析为默认目录下的文件: {load_path}")
        # ------------------------- #

        if os.path.exists(load_path):
            try:
                # 从 JSON 文件加载
                with open(load_path, 'r') as f:
                    loaded_data = json.load(f)

                if 'overrides' in loaded_data:
                    loaded_overrides_list = loaded_data['overrides']
                    # 尝试将列表转换为 NumPy 数组
                    try:
                        loaded_overrides = np.array(loaded_overrides_list, dtype=int)
                        if loaded_overrides.shape == (num_frames, 2):
                            if np.all(np.isin(loaded_overrides, [-1, 0, 1])):
                                manual_overrides = loaded_overrides
                                print(f"已从 {load_path} 加载 'overrides' 状态。")
                                # 可选：如果 JSON 文件包含阈值，也加载它们
                                if 'height_threshold' in loaded_data:
                                    args.height_thresh = float(loaded_data['height_threshold'])
                                    print(f"  - 同时加载 height_threshold: {args.height_thresh:.3f}")
                                if 'velocity_threshold' in loaded_data:
                                    args.vel_thresh = float(loaded_data['velocity_threshold'])
                                    print(f"  - 同时加载 velocity_threshold: {args.vel_thresh:.3f}")
                            else:
                                print("警告：加载的覆盖文件包含无效值([-1,0,1]之外)，将忽略。")
                        else:
                            print(f"警告：加载的覆盖文件 {load_path} ['overrides'] 形状不匹配 ({loaded_overrides.shape} vs {(num_frames, 2)})，将忽略。")
                    except Exception as convert_e:
                         print(f"警告：无法将加载的 'overrides' 列表转换为 NumPy 数组: {convert_e}")
                else:
                     print(f"警告：加载的 JSON 文件 {load_path} 中未找到 'overrides' 键。")
            except json.JSONDecodeError as e:
                print(f"加载 JSON 覆盖文件 {load_path} 时出错：无效的 JSON 格式 - {e}")
            except Exception as e:
                print(f"加载 JSON 覆盖文件 {load_path} 时出错: {e}")
        else:
            print(f"警告：指定的覆盖文件 {load_path} 不存在。")

    # --- 可视化 --- #
    # Capture the final state returned by visualize_bvh_contact
    final_h_thresh, final_v_thresh, final_manual_overrides = visualize_contact(
        joint_positions=joint_positions,
        fps=fps,
        joint_names=joint_names,
        toe_heights=toe_heights,
        toe_velocities_z=toe_velocities_z,
        initial_height_threshold=args.height_thresh, # Pass initial thresholds
        initial_velocity_threshold=args.vel_thresh,
        manual_overrides=manual_overrides, # Pass the potentially loaded overrides
        default_contact_dir=main_default_contact_dir, # 传递默认目录给可视化函数
        args=args  # 添加args参数
    )

    # --- 可视化结束后计算并保存最终状态 --- #
    # Use the FINAL state returned from the visualization
    final_contacts_for_saving = calculate_foot_contacts(
        toe_heights, toe_velocities_z, num_frames,
        final_h_thresh, final_v_thresh, # Use final thresholds from vis
        final_manual_overrides # Use final overrides from vis
    )

    print(f"\n--- BVH可视化结束后的最终接触计算 ({final_h_thresh=:.3f}m, {final_v_thresh=:.3f}m/s, 含手动修改) ---")
    print(f"左脚趾最终接触帧数: {np.sum(final_contacts_for_saving[:, 0])} / {num_frames}")
    print(f"右脚趾最终接触帧数: {np.sum(final_contacts_for_saving[:, 1])} / {num_frames}")
    print("-" * 60)

    # --- 保存最终的接触序列和覆盖状态到 .json 文件 ---
    if args.save_contacts:
        # 生成带日期的文件名
        current_time = datetime.datetime.now()
        date_str = current_time.strftime("%Y-%m-%d_%H-%M-%S")
        save_filename = f"{date_str}_manual_contacts.json"
        save_path = os.path.join(main_default_contact_dir, save_filename)

        try:
            # 确保目标目录存在
            os.makedirs(main_default_contact_dir, exist_ok=True)

            # 手动构建 JSON 字符串以控制格式
            output_lines = []
            output_lines.append("{")
            output_lines.append(f'  "height_threshold": {json.dumps(float(final_h_thresh))},')
            output_lines.append(f'  "velocity_threshold": {json.dumps(float(final_v_thresh))},')

            output_lines.append('  "contacts": [')
            contact_list = final_contacts_for_saving.tolist()
            for i, frame_contact in enumerate(contact_list):
                line = f'    {json.dumps(frame_contact)}'
                if i < len(contact_list) - 1:
                    line += ","
                output_lines.append(line)
            output_lines.append('  ],')

            output_lines.append('  "overrides": [')
            override_list = final_manual_overrides.tolist()
            for i, frame_override in enumerate(override_list):
                line = f'    {json.dumps(frame_override)}'
                if i < len(override_list) - 1:
                    line += ","
                output_lines.append(line)
            output_lines.append('  ]')

            output_lines.append("}")

            # 将构建好的行写入文件
            with open(save_path, 'w') as f:
                f.write("\n".join(output_lines))

            print(f"最终足部接触序列和覆盖状态已保存到 {save_path}")
        except Exception as e:
            print(f"保存最终接触序列到 {save_path} 失败: {e}")


if __name__ == "__main__":
    main()