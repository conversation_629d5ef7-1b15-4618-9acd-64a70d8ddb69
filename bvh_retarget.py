#!/usr/bin/env python3
"""
基于BVH的动作重定向脚本

使用拟合过骨骼长度的bvh数据进行重定向

"""

import argparse
import os
import sys
import json
import numpy as np
import torch
import time
from datetime import datetime
from tqdm import tqdm
import matplotlib.pyplot as plt
from scipy.spatial.transform import Rotation as sRot
import joblib
from humanoid.utils import humanoid_batch_register
from humanoid.utils.torch_humanoid_batch import Humanoid_Batch
from humanoid.retarget_motion.base.motion_lib_cfg import MotionLibCfg
from humanoid.utils.math_tool import butter_lowpass_filter_torch, gaussian_filter_1d_batch
from utils.bvh_utils import BVHMotion
from utils.mujoco_viewer import MujocoViwer




class BVHMotionRetargeter:
    """BVH动作重定向器"""
    
    def __init__(self, args):
        """初始化重定向器
        
        Args:
            args: 命令行参数
        """
        self.args = args
        self.device = torch.device(args.device)
        
        # 设置基础参数
        self.setPrama()  

        # 加载骨骼拟合结果配置
        print(f"加载骨骼拟合配置: {self.config_file}")
         # 解析json，返回值包含优化后的offset，机器人t_pose dof，关节映射，机器人 keypoint关节索引，bvh keypoint关节索引等
        self.skeleton_params = self.load_skeleton_params(self.config_file) 
        
        # 注册机器人和初始化
        self.registryRobot()
        self.initRobotBody()
        
        # 加载优化后的BVH动作数据
        print(f"加载优化后的BVH文件: {self.bvh_file}")
        # BVHMotion获取局部位置和旋转等信息
        self.bvh_motion = BVHMotion(self.bvh_file)  
        
        # 检查并转换BVH数据单位（从厘米转换为米）
        self.convert_bvh_units_to_meters()
        
        # # 🔧 根据命令行参数决定是否启用角度解包处理
        # if getattr(args, 'unwrap_angles', True):
        #     self.unwrap_bvh_rotations()
        #     print("✅ 已应用角度解包处理，解决旋转不连续问题")
        # else:
        #     print("⚠️ 跳过角度解包处理（用户禁用），可能存在旋转跳跃问题")
        
        # 初始化BVH播放器 - 在此之前先设置正确的机器人关节索引
        if not args.no_vis:
            # 从配置文件中获取机器人关节索引，确保在播放器初始化时就有正确的索引
            robot_joint_indices = self.skeleton_params['joint_correspondence'].get('robot_joint_indices', [])
            if robot_joint_indices:
                self.skeleton_params['robot_joint_indices'] = robot_joint_indices
                print(f"✅ 预设机器人关节索引到skeleton_params: {robot_joint_indices}")
            else:
                print("⚠️ 配置文件中没有robot_joint_indices，将使用原始计算")
                
            self.mujoco_viewer = MujocoViwer(self.motion_lib_cfg)
        
        # 初始化脚趾索引用于接触检测
        try:
            self.left_toe_idx = self.motion_lib_cfg.joint_names.index(self.motion_lib_cfg.left_toe_name)
            self.right_toe_idx = self.motion_lib_cfg.joint_names.index(self.motion_lib_cfg.right_toe_name)
            print(f"✅ 脚趾索引初始化成功: Left={self.left_toe_idx} ('{self.motion_lib_cfg.left_toe_name}'), Right={self.right_toe_idx} ('{self.motion_lib_cfg.right_toe_name}')")
        except ValueError as e:
            print(f"⚠️ 脚趾索引初始化失败: {e}")
            self.left_toe_idx = -1
            self.right_toe_idx = -1
        
        # 损失函数
        self.mse_loss = torch.nn.MSELoss()
        
        # 初始化关节权重
        self.init_joint_weights()
        
        # 设置接触序列文件路径
        if hasattr(args, 'contact_file') and args.contact_file:
            self.contact_file_path = args.contact_file
            print(f"✅ 使用指定的接触序列文件: {self.contact_file_path}")
        else:
            print(f"⚠️ 未指定接触序列文件")
        
        print("BVH动作重定向器初始化完成")

    def convert_bvh_units_to_meters(self):
        """
        BVH单位转换 - 检查并转换运动数据单位
        
        注意：虽然bone_optimizer_gui.py处理了骨架结构，
        但运动数据可能仍需要单位转换（厘米->米）。
        """
        import numpy as np
        
        # 检查位置数据的数值范围来判断单位
        if self.bvh_motion.joint_position is not None:
            pos_range = np.abs(self.bvh_motion.joint_position).max()
            
            print(f"BVH运动数据单位检查:")
            print(f"  - 最大position值: {pos_range:.3f}")
            
            # 如果数值很大（>10），很可能是厘米单位，需要转换为米
            if pos_range > 10.0:
                print(f"  - 检测到厘米单位，转换运动数据为米单位")
                
                # 转换所有position数据（厘米 -> 米）
                self.bvh_motion.joint_position *= 0.01
                
                # 如果已经计算了全局位置，也需要转换
                if self.bvh_motion.joint_translation is not None:
                    self.bvh_motion.joint_translation *= 0.01
                
                # 重新检查转换后的范围
                new_range = np.abs(self.bvh_motion.joint_position).max()
                print(f"  - 转换后最大position值: {new_range:.3f}")
                
                return 0.01  # 返回转换因子
            else:
                print(f"  - 数据已经是米单位，无需转换")
                return 1.0
        else:
            print("  - 警告: BVH joint_position为空")
            return 1.0

    def setPrama(self):
        """设置可调参数"""
        self.bvh_file = self.args.optimized_bvh
        self.config_file = self.args.config
        self.device = self.args.device
        self.kernel_size = self.args.kernel_size
        self.sigma = self.args.sigma
        self.iterations = self.args.iterations
        self.learning_rate = self.args.learning_rate
        
        # 损失权重
        self.keypoint_loss_weight = self.args.keypoint_loss_weight
        self.smoothness_loss_weight = self.args.smoothness_loss_weight
        self.acceleration_loss_weight = self.args.acceleration_loss_weight
        self.contact_loss_weight = self.args.contact_loss_weight
        self.foot_height_loss_weight = self.args.foot_height_loss_weight
        

    def load_skeleton_params(self, config_path):
        """加载骨骼拟合参数 - 从bone_optimizer_gui的输出中读取"""
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 验证配置文件格式
        if not self.validate_config_file(config_path):
            raise ValueError(f"无效的配置文件格式: {config_path}")
        
        # 提取task_name
        self.task_name = config['metadata']['task_name']
        
        # 提取关节对应关系
        joint_correspondence = config['joint_correspondence']
        
        # 提取BVH关节信息
        bvh_info = config.get('bvh_info', {})
        bvh_joint_names = bvh_info.get('joint_names', [])
        bvh_selected_joints = bvh_info.get('selected_joints', [])
        
        # 提取机器人关节信息  
        robot_info = config.get('robot_info', {})
        robot_joint_names = robot_info.get('joint_names', [])
        robot_selected_joints = robot_info.get('selected_joints', [])
        
        robot_joint_indices = joint_correspondence['robot_joint_indices']
        bvh_joint_indices = joint_correspondence['bvh_joint_indices']
             
        # 构建完整的参数结构
        params = {
            'metadata': {
                'task_name': self.task_name,
                'source_file': config_path,
                'optimization_version': config['metadata'].get('optimization_version', 'BVH_Direct_v1.0')
            },
            'joint_correspondence': {
                'robot_to_bvh': joint_correspondence.get('robot_to_bvh', {}),
                'bvh_to_robot': joint_correspondence.get('bvh_to_robot', {}),
                'robot_joint_indices': robot_joint_indices,  # selected_joints
                'bvh_joint_indices': bvh_joint_indices  
            },
            'skeleton_params': config.get('skeleton_params', {}),  # 优化后的offset
            't_pose_config': config.get('t_pose_config', {}),  # 机器人t_pose dof
            'coordinate_transform': config.get('coordinate_transform', {}),  # 坐标系变换信息
            'bvh_info': {
                'joint_names': bvh_joint_names, 
                'selected_joints': bvh_selected_joints
            },
            'robot_info': {
                'joint_names': robot_joint_names,
                'selected_joints': robot_selected_joints
            }
        }
        
        print("骨骼参数加载成功:")
        print(f"  - 任务: {self.task_name}")
        print(f"  - 关节映射: {len(joint_correspondence.get('robot_to_bvh', {}))} 对")
        
        
        skeleton_params = params['skeleton_params']
        if 'scale_factor' in skeleton_params:
            print(f"  - 缩放因子: {skeleton_params['scale_factor']:.4f}")
        
        return params


    def validate_config_file(self, config_path):
        """验证配置文件是否是有效的bone_optimizer_gui输出
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            bool: 是否是有效的配置文件
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查必需的字段
            required_fields = [
                'metadata', 
                'joint_correspondence', 
                'skeleton_params', 
                't_pose_config'
            ]
            
            for field in required_fields:
                if field not in config:
                    return False
            
            # 检查metadata中是否有task_name
            if 'task_name' not in config['metadata']:
                return False
            
            # 检查是否是bone_optimizer_gui的输出
            if 'optimization_version' in config['metadata']:
                version = config['metadata']['optimization_version']
                if 'BVH' in version or 'Direct' in version:
                    return True
            
            return True
            
        except (json.JSONDecodeError, KeyError, Exception):
            return False

    def registryRobot(self):
        """注册机器人信息 - 使用从配置中提取的task_name"""
        # 注册机器人任务
        humanoid_batch_register.register_task(self.task_name)
        self.motion_lib_cfg: MotionLibCfg = humanoid_batch_register.get_config(self.task_name)
        self.motion_lib_cfg.device = self.device
        
        # 直接从motion_lib_cfg中读取optimize_root配置
        self.optimize_root = getattr(self.motion_lib_cfg, 'optimize_root', False)
        print(f"optimize_root: {self.optimize_root}")
        
        # 初始化前向运动学
        self.Humanoid_fk = Humanoid_Batch(
            self.motion_lib_cfg.mjcf_file, 
            self.motion_lib_cfg.extend_node_dict, 
            device=self.device
        )
        
    def initRobotBody(self):
        """初始化机器人关节信息"""
        # 获取关节信息
        self.robot_joint_names = self.motion_lib_cfg.joint_names
        self.robot_pos_names = self.motion_lib_cfg.pos_names
        self.limb_names = self.motion_lib_cfg.limb_names
        self.robot_joint_pick = self.motion_lib_cfg.joint_pick
        self.robot_joint_pick_idx = [self.robot_joint_names.index(j) for j in self.robot_joint_pick]

        # BVH关节信息
        self.bvh_joint_pick = self.skeleton_params['bvh_info']['selected_joints']

        if 'bvh_joint_indices' in self.skeleton_params['joint_correspondence']:
            self.bvh_joint_pick_idx = self.skeleton_params['joint_correspondence']['bvh_joint_indices']
        else:
            raise ValueError("配置中没有BVH关节索引")

        
        # 创建关节链条
        self.limb_index = {}
        for key in self.limb_names:
            limb_name = self.limb_names[key]
            self.limb_index[key] = [self.robot_pos_names.index(i) for i in limb_name if i in self.robot_pos_names]
 
        print(f"机器人模型初始化完成: {len(self.robot_joint_names)} 个关节")
        print(f"机器人Key-point数: {len(self.robot_joint_pick)}")
        print(f"BVH Key-point数: {len(self.bvh_joint_pick)}")

    def init_joint_weights(self):
        """初始化关节权重"""
        # 创建关节权重张量 - 基于实际的机器人关节数量
        self.joint_weights = torch.ones(len(self.robot_joint_pick), device=self.device)
        
        # 如果配置中有关节权重设置，应用它们
        if hasattr(self.motion_lib_cfg, 'joint_weights'):
            for i, joint_name in enumerate(self.robot_joint_pick):
                if joint_name in self.motion_lib_cfg.joint_weights:
                    self.joint_weights[i] = self.motion_lib_cfg.joint_weights[joint_name]
        
        # 扩展权重到3D - 这里先用原始数量，后面会根据有效关节调整
        self.joint_weights = self.joint_weights.unsqueeze(0).unsqueeze(-1).expand(1, -1, 3)

    def update_joint_weights_for_valid_joints(self, num_valid_joints):
        """根据有效关节数量更新关节权重"""
        if num_valid_joints != len(self.robot_joint_pick):
            print(f"调整关节权重: {len(self.robot_joint_pick)} -> {num_valid_joints}")
            # 只保留有效关节的权重
            if num_valid_joints <= len(self.robot_joint_pick):
                self.joint_weights = self.joint_weights[:, :num_valid_joints, :]
            else:
                # 如果有效关节更多，扩展权重
                additional_weights = torch.ones(1, num_valid_joints - len(self.robot_joint_pick), 3, device=self.device)
                self.joint_weights = torch.cat([self.joint_weights, additional_weights], dim=1)

    def apply_skeleton_transform(self, bvh_positions):
        """
        应用坐标系变换 - 直接从JSON配置文件中获取4x4变换矩阵
        """
        print("应用坐标系转换...")
        
        # 从配置文件中获取变换信息
        coord_transform = self.skeleton_params.get('coordinate_transform', {})
        
        if 'transform_matrix' in coord_transform:
            # 直接使用配置文件中的4x4变换矩阵
            transform_matrix = coord_transform['transform_matrix']
            transform_4x4 = torch.tensor(transform_matrix, device=self.device, dtype=torch.float32)
            
            print(f"使用配置文件中的4x4变换矩阵")
            # 应用齐次变换
            transformed_positions = self._apply_homogeneous_transform(bvh_positions, transform_4x4)
        else:
            print("配置文件中没有变换矩阵，使用默认变换")
            transformed_positions = self._apply_default_transform(bvh_positions)
        
        # print(f"变换后位置范围: X[{transformed_positions[:,:,0].min():.3f}, {transformed_positions[:,:,0].max():.3f}], "
        #       f"Y[{transformed_positions[:,:,1].min():.3f}, {transformed_positions[:,:,1].max():.3f}], "
        #       f"Z[{transformed_positions[:,:,2].min():.3f}, {transformed_positions[:,:,2].max():.3f}]")
        
        return transformed_positions



    def _apply_homogeneous_transform(self, positions, transform_matrix):
        """应用齐次坐标变换"""
        original_shape = positions.shape
        
        # 将位置转换为齐次坐标 [x, y, z, 1]
        positions_flat = positions.reshape(-1, 3)
        ones = torch.ones(positions_flat.shape[0], 1, device=self.device, dtype=torch.float32)
        positions_homogeneous = torch.cat([positions_flat, ones], dim=1)  # [N, 4]
        
        # 应用变换: new_pos = transform_matrix @ old_pos.T
        transformed_homogeneous = (transform_matrix @ positions_homogeneous.T).T[..., :3]# [4, N]
        transformed_positions = transformed_homogeneous.reshape(original_shape)
        
        return transformed_positions

    def _apply_default_transform(self, bvh_positions):
        """应用默认的坐标系转换"""
        print("使用默认坐标系转换: BVH(x=左右,y=上下,z=前后) -> Robot(x=前后,y=左右,z=上下)")
        transformed_positions = torch.zeros_like(bvh_positions)
        transformed_positions[..., 0] = bvh_positions[..., 2]  # x_robot = z_bvh (前后)
        transformed_positions[..., 1] = bvh_positions[..., 0]  # y_robot = x_bvh (左右)
        transformed_positions[..., 2] = bvh_positions[..., 1]  # z_robot = y_bvh (上下)
        return transformed_positions



    def apply_rotation_coordinate_transform(self, bvh_rotations):
        """
        简单的旋转坐标系转换 - 直接从JSON配置文件中获取变换矩阵
        
        Args:
            bvh_rotations: [num_frames, 3] 轴角表示的旋转
            
        Returns:
            transformed_rotations: [num_frames, 3] 转换后的旋转
        """
        from scipy.spatial.transform import Rotation as R
        import numpy as np
        
        coord_cfg = self.skeleton_params.get('coordinate_transform', {})
        if 'transform_matrix' not in coord_cfg:
            # 未配置时不变
            return bvh_rotations

        T = np.asarray(coord_cfg['transform_matrix'], dtype=np.float32)[:3, :3]  # (3,3)
        T_inv = T.T    # 因为 T 是纯正交旋转，T⁻¹ = Tᵀ

        # ── 2. 轴角 → 旋转矩阵
        rot_mat = sRot.from_rotvec(
            bvh_rotations.detach().cpu().numpy()     # (T,3)
        ).as_matrix()                                 # (T,3,3)

        # ── 3. 坐标系变换  R' = T · R · Tᵀ 
        #    等价于把 R 表示的姿态从 "参考系" 改写到 "机器人系"
        rot_mat_tr = np.einsum('ij,njk,kl->nil', T, rot_mat, T_inv)  # (T,3,3)

        # ── 4. 再转回轴角 
        rotvec_tr = sRot.from_matrix(rot_mat_tr).as_rotvec()         # (T,3) rad

        return torch.tensor(
            rotvec_tr, device=bvh_rotations.device, dtype=bvh_rotations.dtype
        )



    def caluBVH2Robot(self):
        """
        计算BVH到机器人的映射
        
        Returns:
            dof_pos: 初始化的关节角度（T-pose）
            bvh_target_positions: BVH目标关节位置（已处理）
            root_pos: 根节点位置（已处理）
            root_rot: 根节点旋转（已处理）
        """
        # 获取BVH动作数据
        self.num_frames = self.bvh_motion.joint_position.shape[0]
        self.fps = self.bvh_motion.mocap_framerate
        
        print(f"BVH动作数据:")
        print(f"  - 帧数: {self.num_frames}")
        print(f"  - 帧率: {self.fps:.1f} fps")
        print(f"  - 时长: {self.num_frames / self.fps:.2f} 秒")
        print(f"  - 关节数: {len(self.bvh_motion.joint_name)}")
        
        # 获取关节对应关系
        joint_correspondence = self.skeleton_params.get('joint_correspondence', {})
        if 'bvh_joint_indices' in joint_correspondence and 'robot_joint_indices' in joint_correspondence:
            self.valid_bvh_joint_indices = joint_correspondence['bvh_joint_indices']
            self.valid_robot_joint_indices = joint_correspondence['robot_joint_indices']
            print(f"✅ 加载关节对应关系: {len(self.valid_bvh_joint_indices)} 对")
        else:
            raise ValueError("配置文件中缺少关节索引信息！")
        
        # 直接从配置文件获取参数
        skeleton_params = self.skeleton_params.get('skeleton_params', {})
        scale_factor = skeleton_params.get('scale_factor', 1.0)
        bvh_root_trans = skeleton_params.get('bvh_root_trans', [0.0, 0.0])
        
        print(f"✅ 从配置文件加载参数:")
        print(f"  - 缩放因子: {scale_factor}")
        print(f"  - 根节点变换: {bvh_root_trans}")
        
        # 对于动态运动，我们需要为每一帧计算FK，然后应用相同的坐标转换
        print("计算所有帧的BVH前向运动学...")
        self.bvh_motion.batch_forward_kinematics()
        bvh_joint_positions = self.bvh_motion.joint_translation  # [num_frames, num_joints, 3]
        bvh_joint_positions = torch.tensor(bvh_joint_positions, device=self.device, dtype=torch.float32)
        
        # 直接应用坐标系转换（与bone_optimizer_gui.py保持一致）
        print("应用坐标系转换...")
        
        # 坐标系转换：BVH坐标系 -> 机器人坐标系
        # BVH: z向前，y向上，x向左 -> 机器人: z向上，y向前，x向右
        bvh_positions_converted = torch.zeros_like(bvh_joint_positions)
        bvh_positions_converted[..., 0] = bvh_joint_positions[..., 2]   # x_robot = z_bvh
        bvh_positions_converted[..., 1] = bvh_joint_positions[..., 0]   # y_robot = x_bvh
        bvh_positions_converted[..., 2] = bvh_joint_positions[..., 1]   # z_robot = y_bvh
        
        # 应用缩放因子
        bvh_positions_converted *= scale_factor
        
        # 应用根节点变换（如果需要）
        if bvh_root_trans != [0.0, 0.0]:
            root_trans = torch.zeros(3, device=self.device)
            root_trans[0] = bvh_root_trans[0]  # x方向
            root_trans[2] = bvh_root_trans[1]  # z方向
            bvh_positions_converted += root_trans
        
        bvh_joint_positions = bvh_positions_converted
        print(f"✅ 完成坐标系转换和缩放，缩放因子: {scale_factor}")
        
        # 提取目标关节位置
        bvh_target_positions = bvh_joint_positions[:, self.valid_bvh_joint_indices, :]
        
        # 处理根节点位置和旋转
        p_root = bvh_joint_positions[:, 0, :].clone()
        
        
        # 处理根节点旋转 - 使用备份脚本的方式，直接从BVH文件读取原始数据
        print("处理根节点旋转...")
        
        # 🔧 修复：使用已经解包的根节点旋转数据，避免角度跳跃问题
        print("使用已解包的根节点旋转数据，确保角度连续性...")
        
        # 使用已经解包的根节点旋转数据（索引0是根节点）
        bvh_root_euler_degrees = self.bvh_motion.joint_rotation[:, 0, :]  # [num_frames, 3] 欧拉角（度）
        
        print(f"原始BVH根节点欧拉角范围:")
        print(f"  - X: [{bvh_root_euler_degrees[:, 0].min():.3f}°, {bvh_root_euler_degrees[:, 0].max():.3f}°]")
        print(f"  - Y: [{bvh_root_euler_degrees[:, 1].min():.3f}°, {bvh_root_euler_degrees[:, 1].max():.3f}°]")
        print(f"  - Z: [{bvh_root_euler_degrees[:, 2].min():.3f}°, {bvh_root_euler_degrees[:, 2].max():.3f}°]")
        
        # 检查相邻帧之间的角度跳跃
        angle_diffs = np.abs(np.diff(bvh_root_euler_degrees, axis=0))
        max_diff = np.max(angle_diffs, axis=0)
        print(f"相邻帧最大角度变化: X={max_diff[0]:.3f}°, Y={max_diff[1]:.3f}°, Z={max_diff[2]:.3f}°")
        
        # 🔧 添加额外的角度连续性检查
        large_jumps = np.any(angle_diffs > 150, axis=1)  # 检测仍然存在的大跳跃
        num_large_jumps = np.sum(large_jumps)
        if num_large_jumps > 0:
            print(f"⚠️ 检测到 {num_large_jumps} 帧仍有大角度跳跃，应用额外平滑处理...")
            # 对这些帧应用更激进的角度解包
            bvh_root_euler_degrees = self.unwrap_euler_angles(bvh_root_euler_degrees)
            print("✅ 已应用额外的角度解包处理")
        
        # 转换为弧度
        bvh_root_euler_radians = np.deg2rad(bvh_root_euler_degrees)
        
        # 直接转换为轴角表示（避免四元数）
        root_rotations_scipy = sRot.from_euler(self.bvh_motion.euler, bvh_root_euler_radians)
        root_rotations_aa = root_rotations_scipy.as_rotvec()  # 轴角表示
        
        # 应用坐标变换（如果需要）
        coord_transform = self.skeleton_params.get('coordinate_transform', {})
        if 'transform_matrix' in coord_transform:
            # 如果有坐标变换矩阵，也需要对旋转进行相应变换
            transform_matrix = np.array(coord_transform['transform_matrix'])
            
            # 🔧 修复：从4x4变换矩阵中提取3x3旋转部分
            rotation_transform = transform_matrix[:3, :3]  # 提取3x3旋转部分
            
            # 将轴角旋转转换为旋转矩阵
            rotation_matrices = root_rotations_scipy.as_matrix()  # [num_frames, 3, 3]
            
            # 应用坐标变换: R_new = T * R_old * T^(-1)
            transformed_matrices = np.matmul(np.matmul(rotation_transform, rotation_matrices), rotation_transform.T)
            
            # 转换回轴角表示
            transformed_rotations = sRot.from_matrix(transformed_matrices)
            root_rotations_aa = transformed_rotations.as_rotvec()
            
            print("应用了旋转的坐标变换")
        else:
            # 使用默认的坐标系转换：BVH: z前,y上,x左 -> Robot: z上,y前,x右
            print("应用默认的旋转坐标变换（矩阵共轭）…")
            # ① 把 axis–angle 转回旋转矩阵
            rotation_matrices = root_rotations_scipy.as_matrix()    # (T,3,3)

            # ② 构造你的坐标系旋转 R_axes，col-vect 记号
            #    BVH: x←左,y←上,z←前  → Robot: x←前,y←左,z←上
            R_axes = np.array([
                [0, 0, 1],   # x_robot = +z_bvh
                [0, 1, 0],   # y_robot = +y_bvh
                [1, 0, 0],   # z_robot = +x_bvh
            ], dtype=np.float32)

            # ③ 共轭变换：R_new = R_axes @ R_old @ R_axes^T
            R_new = np.einsum('ij,njk,kl->nil', R_axes, rotation_matrices, R_axes.T)

            # ④ 拆回轴–角
            root_rotations_aa = sRot.from_matrix(R_new).as_rotvec()  # (T,3) rad
        
        r_root = torch.tensor(root_rotations_aa, device=self.device, dtype=torch.float32)
        
        print(f"根节点旋转初始化:")
        print(f"  - 旋转范围: X[{r_root[:, 0].min():.3f}, {r_root[:, 0].max():.3f}]")
        print(f"  - 旋转范围: Y[{r_root[:, 1].min():.3f}, {r_root[:, 1].max():.3f}]")
        print(f"  - 旋转范围: Z[{r_root[:, 2].min():.3f}, {r_root[:, 2].max():.3f}]")
        
        # 初始化DOF位置
        t_pose_config = self.skeleton_params.get('t_pose_config', {})
        if 'robot_dof_pos' in t_pose_config:
            t_pose_dof = torch.tensor(t_pose_config['robot_dof_pos'], device=self.device, dtype=torch.float32)
            print("✅ 使用配置文件中的T-pose DOF")
        elif hasattr(self.motion_lib_cfg, 'dof_pos') and self.motion_lib_cfg.dof_pos is not None:
            t_pose_dof = self.motion_lib_cfg.dof_pos.to(self.device)
            print("✅ 使用motion_lib_cfg中的T-pose DOF")
        else:
            t_pose_dof = torch.zeros(self.Humanoid_fk.joints_axis.shape[1], device=self.device)
            print("⚠️ 使用零DOF位置")
        
        # 为所有帧设置初始DOF值
        dof_pos = torch.zeros((1, self.num_frames, self.Humanoid_fk.joints_axis.shape[1], 1), device=self.device)
        if t_pose_dof.dim() == 1:
            t_pose_expanded = t_pose_dof.unsqueeze(0).repeat(self.num_frames, 1)
        else:
            t_pose_expanded = t_pose_dof.repeat(self.num_frames, 1)
        dof_pos[0, :, :, 0] = t_pose_expanded
        
        print(f"✅ BVH到机器人映射完成，数据已就绪")
        print(f"  - 目标位置形状: {bvh_target_positions.shape}")
        print(f"  - DOF形状: {dof_pos.shape}")

        # 自适应高度调整：找到脚趾最低点并调整根节点高度
        foot_joint_names = ['LeftToeBase', 'RightToeBase', 'LeftFoot', 'RightFoot']
        foot_indices = []
        for name in foot_joint_names:
            if name in self.bvh_motion.joint_name:
                foot_indices.append(self.bvh_motion.joint_name.index(name))
        
        if foot_indices:
            foot_positions = bvh_joint_positions[:, foot_indices, :]  # [frames, foot_joints, 3]
            min_foot_height = foot_positions[:, :, 2].min().item()  # z坐标最小值
            height_offset = -min_foot_height + 0.02  # 提升到略高于地面
            p_root[:, 2] += height_offset  # 调整根节点z坐标
        
        return dof_pos, bvh_target_positions, p_root, r_root
    


    def parse_bvh_motion_data(self):
        """
        解析BVH动作数据，获取所有帧的关节位置
        
        Returns:
            joint_positions: [num_frames, num_joints, 3] 关节位置
        """
        # 使用BVHMotion类计算前向运动学
        self.bvh_motion.batch_forward_kinematics()
        
        # 获取全局关节位置
        joint_positions = self.bvh_motion.joint_translation  # [num_frames, num_joints, 3]
        
        # 转换为torch张量
        joint_positions = torch.tensor(joint_positions, device=self.device, dtype=torch.float32)
        
        return joint_positions

    def calcAA(self, dof_pos, root_rot):
        """计算轴角表示，参考备份脚本的实现"""
        # 获取关节轴
        axes = self.Humanoid_fk.joints_axis
        # 获取关节角度
        angles = dof_pos
        
        # 计算所有非根关节的轴角
        joint_aa = angles * axes.unsqueeze(1)
        
        # 调整根旋转的形状以进行拼接
        root_rot_reshaped = root_rot.view(1, -1, 1, 3)
        
        # 拼接根旋转和关节轴角
        pose_aa_new = torch.cat([root_rot_reshaped, joint_aa], dim=2).to(self.device)
        
        return pose_aa_new

    def calcJointPosition(self, fk_return):
        """计算关节位置并应用偏置修正，参考备份脚本的实现"""
        # 获取关节位置
        joint_positions = fk_return['global_translation']
        
        # 如果有偏置配置，应用偏置修正
        if hasattr(self.motion_lib_cfg, 'joint_pick_bias') and self.motion_lib_cfg.joint_pick_bias:
            # 这里可以添加偏置修正逻辑
            # 暂时直接返回原始位置
            pass
        
        return joint_positions

    def compute_robot_fk(self, dof_pos, root_rot, root_pos):
        """计算机器人前向运动学 - 使用备份脚本的正确方式"""
        # 计算轴角表示 - 参考备份脚本的实现
        axes = self.Humanoid_fk.joints_axis  # [1, num_dofs, 3]
        angles = dof_pos  # [1, num_frames, num_dofs, 1]
        
        # 🔧 修复：使用备份脚本的正确计算方式
        # 去掉不必要的维度变换，直接计算轴角
        joint_aa = angles * axes.unsqueeze(1)  # [1, num_frames, num_dofs, 3]
        
        # 调整根旋转的形状以进行拼接
        # root_rot: [num_frames, 3] -> [1, num_frames, 1, 3]
        root_rot_reshaped = root_rot.view(1, -1, 1, 3)
        
        # 拼接根旋转和关节轴角
        pose_aa_new = torch.cat([root_rot_reshaped, joint_aa], dim=2).to(self.device)
        
        # 使用FK计算
        # root_pos: [num_frames, 3] -> [1, num_frames, 3]
        root_pos_batch = root_pos.unsqueeze(0) if root_pos.dim() == 2 else root_pos
        
        fk_return = self.Humanoid_fk.fk_batch(
            pose_aa_new, 
            root_pos_batch, 
            return_full=True
        )
        
        # 返回关节位置
        return fk_return['global_translation']

    def calcKeyPointDiffLoss(self, robot_positions, target_positions):
        """计算关键点差异损失"""
        # robot_positions: [1, num_frames, num_joints, 3]
        # target_positions: [num_frames, num_joints, 3]
        
        # 提取关键点位置
        robot_key_pos = robot_positions[0]  # [num_frames, num_joints, 3]
        
        # 计算加权损失
        diff = robot_key_pos - target_positions
        weighted_diff = diff * self.joint_weights
        loss = self.mse_loss(weighted_diff, torch.zeros_like(weighted_diff))
        
        return loss

    def calcSmoothnessLoss(self, robot_positions):
        """计算平滑损失"""
        # robot_positions: [1, num_frames, num_joints, 3]
        positions = robot_positions[0]  # [num_frames, num_joints, 3]
        
        # 计算一阶差分（速度）
        pos_diff = positions[1:] - positions[:-1]
        pos_diff_norm = torch.norm(pos_diff, p=2, dim=-1)
        total_loss = self.mse_loss(pos_diff_norm, torch.zeros_like(pos_diff_norm))
        expanded_loss = torch.zeros(positions.shape[0], device=self.device)
        
        return total_loss, expanded_loss
    
    def calcAccelerationLoss(self, robot_positions):
        """
        计算加速度损失，直接最小化加速度的大小
        Args:
            T1_key_pos: 机器人关键点位置 [batch_size, num_frames, num_key_points, 3]
        Returns:
            total_loss: 加速度损失
            per_frame_loss: 每帧加速度损失 [num_frames-2] (用于可视化)
        """
        # 计算速度
        v = robot_positions[:, 1:, :, :] - robot_positions[:, 0:-1, :, :]
        # 计算加速度
        a = v[:, 1:, :, :] - v[:, 0:-1, :, :]
        # 计算加速度的范数
        a_norm = torch.norm(a, p=2, dim=-1) # [batch_size, num_frames-2, num_key_points]
        
        # 使用MSE损失直接最小化加速度
        total_loss = self.mse_loss(a_norm, torch.zeros_like(a_norm))
        return total_loss

    def calcFootHeightLoss(self, robot_positions):
        """计算脚部高度损失"""
        # robot_positions: [1, num_frames, num_joints, 3]
        positions = robot_positions[0]  # [num_frames, num_joints, 3]
        
        # 查找脚部相关关节
        foot_indices = []
        for i, joint_name in enumerate(self.robot_pos_names):
            if any(keyword in joint_name.lower() for keyword in ['foot', 'ankle', 'toe']):
                foot_indices.append(i)
        
        if not foot_indices:
            return torch.tensor(0.0, device=self.device)
        
        # 提取脚部位置
        foot_positions = positions[:, foot_indices, :]
        
        # 计算脚部高度损失（防止穿地）
        foot_heights = foot_positions[:, :, 2]  # z坐标
        target_foot_height = 0.0
        height_violations = torch.clamp(target_foot_height - foot_heights, min=0.0)
        
        foot_height_loss = torch.mean(height_violations ** 2)
        
        return foot_height_loss

    def calcJointLimitLoss(self, dof_pos):
        """计算关节限制损失 - 防止关节角度超出物理限制"""
        if not hasattr(self.Humanoid_fk, 'joints_range') or self.Humanoid_fk.joints_range is None:
            return torch.tensor(0.0, device=self.device)
        
        # dof_pos: [1, num_frames, num_joints, 1]
        joint_angles = dof_pos[0, :, :, 0]  # [num_frames, num_joints]
        
        # 获取关节限制 [num_joints, 2]
        min_limits = self.Humanoid_fk.joints_range[:, 0]  # [num_joints]
        max_limits = self.Humanoid_fk.joints_range[:, 1]  # [num_joints]
        
        # 计算违反限制的程度
        # 使用软约束：当接近限制时损失逐渐增加
        margin = 0.1  # 10%的安全边际
        range_size = max_limits - min_limits
        safe_min = min_limits + margin * range_size
        safe_max = max_limits - margin * range_size
        
        # 计算超出安全范围的损失
        under_penalty = torch.clamp(safe_min - joint_angles, min=0.0) ** 2
        over_penalty = torch.clamp(joint_angles - safe_max, min=0.0) ** 2
        
        # 总的关节限制损失
        joint_limit_loss = torch.mean(under_penalty + over_penalty)
        
        return joint_limit_loss

    def retargetBVHData(self):
        """重定向BVH数据 - 简化版本，专注于dof_pos优化"""
        print("="*50)
        print("开始BVH动作重定向")
        print("="*50)
        
        # 1. 获取预处理的BVH数据作为目标参考
        dof_pos, bvh_target_positions, root_pos, root_rot = self.caluBVH2Robot()
        
        print(f"目标位置数据形状: {bvh_target_positions.shape}")
        print(f"关节角度数据形状: {dof_pos.shape}")
        print(f"根节点位置形状: {root_pos.shape}")
        print(f"根节点旋转形状: {root_rot.shape}")
        
        # 2. 更新关节权重以匹配有效关节数量
        num_valid_joints = bvh_target_positions.shape[1]
        self.update_joint_weights_for_valid_joints(num_valid_joints)
        
        # 使用配置中定义的有效关节索引
        if hasattr(self, 'valid_robot_joint_indices'):
            self.current_robot_joint_pick_idx = self.valid_robot_joint_indices
            print(f"使用配置文件中的机器人关节索引: {len(self.current_robot_joint_pick_idx)} 个")
            print(f"BVH关节索引数量: {len(self.valid_bvh_joint_indices)} 个")
            
            # 验证数量一致性
            if len(self.current_robot_joint_pick_idx) != len(self.valid_bvh_joint_indices):
                print(f"警告: 机器人关节数({len(self.current_robot_joint_pick_idx)}) != BVH关节数({len(self.valid_bvh_joint_indices)})")
        else:
            # 回退方案
            self.current_robot_joint_pick_idx = self.robot_joint_pick_idx
            print(f"使用默认机器人关节索引: {len(self.current_robot_joint_pick_idx)} 个")
        
        # 加载接触序列（如果存在）
        self.contact_sequence = None
        if os.path.exists(self.contact_file_path):
            try:
                with open(self.contact_file_path, 'r') as f:
                    contact_data = json.load(f)
                # 验证键
                if all(k in contact_data for k in ['contacts', 'overrides', 'height_threshold', 'velocity_threshold']):
                    self.contact_sequence = np.array(contact_data['contacts'], dtype=bool)
                    print(f"✅ 成功加载接触序列: {self.contact_file_path}")
                    print(f"  - 接触序列形状: {self.contact_sequence.shape}")
                    print(f"  - 左脚接触帧数: {self.contact_sequence[:, 0].sum()}")
                    print(f"  - 右脚接触帧数: {self.contact_sequence[:, 1].sum()}")
                else:
                    print(f"⚠️ 接触文件 {self.contact_file_path} 缺少必要的键，将使用默认接触序列")
            except json.JSONDecodeError:
                print(f"⚠️ 无法解码接触文件 JSON: {self.contact_file_path}，将使用默认接触序列")
            except Exception as e:
                print(f"⚠️ 加载接触文件时出错 {self.contact_file_path}: {e}，将使用默认接触序列")
        else:
            print(f"⚠️ 接触文件不存在: {self.contact_file_path}，将在后续使用默认接触序列")
        
        # 3. 创建优化变量
        from torch.autograd import Variable
        
        dof_pos_new = Variable(dof_pos.clone(), requires_grad=True)
        
        if self.optimize_root:
            print("开启根节点优化")
            root_pos_tensor = torch.as_tensor(root_pos, device=self.device, dtype=torch.float32)
            root_rot_tensor = torch.as_tensor(root_rot, device=self.device, dtype=torch.float32)
            root_pos_new = Variable(root_pos_tensor.clone(), requires_grad=True)
            root_rot_new = Variable(root_rot_tensor.clone(), requires_grad=True)
        else:
            print("关闭根节点优化")
            root_pos_new = torch.as_tensor(root_pos, device=self.device, dtype=torch.float32)
            root_rot_new = torch.as_tensor(root_rot, device=self.device, dtype=torch.float32)
        
        # 4. 创建优化器 - 参考备份脚本的设置
        optimizer_params = [{'params': dof_pos_new, 'lr': 0.02}]
        if self.optimize_root:
            optimizer_params.extend([
                {'params': root_pos_new, 'lr': 0.005},
                {'params': root_rot_new, 'lr': 0.005}
            ])
        
        optimizer = torch.optim.Adam(optimizer_params)
        scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=500, gamma=0.5)
        
        # 5. 优化循环 - 核心任务：调整dof_pos使机器人FK结果匹配BVH目标
        loss_history = []
        
        print(f"\n开始优化循环: {self.iterations} 次迭代")
        print(f"学习率: {self.learning_rate}")
        print(f"优化目标: 使机器人FK结果匹配预处理的BVH目标")
        
        # 简化的关节限制信息
        if hasattr(self.Humanoid_fk, 'joints_range') and self.Humanoid_fk.joints_range is not None:
            print(f"\n📐 关节限制信息: {self.Humanoid_fk.joints_range.shape[0]} 个关节")
        else:
            print("⚠️ 警告: 没有找到关节限制信息！")
        
        for iteration in range(self.iterations):
            optimizer.zero_grad()
            
            # 🔒 限制关节角度范围 - 参考备份脚本的实现
            if hasattr(self.Humanoid_fk, 'joints_range') and self.Humanoid_fk.joints_range is not None:
                dof_pos_new.data.clamp_(
                    self.Humanoid_fk.joints_range[:, 0, None], 
                    self.Humanoid_fk.joints_range[:, 1, None]
                )
            
            # 计算前向运动学 - 使用备份脚本的正确方式
            pose_aa_new = self.calcAA(dof_pos_new, root_rot_new)
            fk_return = self.Humanoid_fk.fk_batch(
                pose_aa_new, 
                root_pos_new[None,], 
                return_full=True
            )
            
            # 计算关节位置
            robot_joint_positions = self.calcJointPosition(fk_return)
            robot_key_positions = robot_joint_positions[:, :, self.current_robot_joint_pick_idx]
            
            # 第一次迭代时打印详细的对应关系调试信息
            if iteration == 0:
                print(f"\n🔍 关键点对应关系验证 (第{iteration}次迭代):")
                print(f"  - 机器人关节索引: {self.current_robot_joint_pick_idx}")
                print(f"  - BVH关节索引: {self.valid_bvh_joint_indices}")
                print(f"  - robot_key_positions形状: {robot_key_positions.shape}")
                print(f"  - bvh_target_positions形状: {bvh_target_positions.shape}")
                
                print(f"\n  详细对应关系:")
                min_joints = min(len(self.current_robot_joint_pick_idx), len(self.valid_bvh_joint_indices))
                for i in range(min_joints):
                    robot_joint_idx = self.current_robot_joint_pick_idx[i]
                    robot_joint_name = self.robot_joint_names[robot_joint_idx] if robot_joint_idx < len(self.robot_joint_names) else f"未知关节{robot_joint_idx}"
                    
                    bvh_joint_idx = self.valid_bvh_joint_indices[i]
                    bvh_joint_name = self.bvh_motion.joint_name[bvh_joint_idx]
                    
                    # 获取第一帧的位置进行距离计算
                    robot_pos = robot_key_positions[0, 0, i].detach().cpu().numpy() if robot_key_positions.shape[2] > i else [0,0,0]
                    bvh_pos = bvh_target_positions[0, i].detach().cpu().numpy() if bvh_target_positions.shape[1] > i else [0,0,0]
                    distance = np.linalg.norm(robot_pos - bvh_pos)
                    
                    print(f"    [{i}] Robot[{robot_joint_idx}]:{robot_joint_name} ↔ BVH[{bvh_joint_idx}]:{bvh_joint_name}")
                    print(f"        位置: Robot{robot_pos} vs BVH{bvh_pos}, 距离: {distance:.3f}")
                
                # 检查是否有明显的对应错误
                problematic_pairs = []
                for i in range(min_joints):
                    robot_joint_idx = self.current_robot_joint_pick_idx[i]
                    robot_joint_name = self.robot_joint_names[robot_joint_idx] if robot_joint_idx < len(self.robot_joint_names) else f"未知关节{robot_joint_idx}"
                    
                    bvh_joint_idx = self.valid_bvh_joint_indices[i]
                    bvh_joint_name = self.bvh_motion.joint_name[bvh_joint_idx]
                    
                    # 简单的名称匹配检查
                    if ("left" in robot_joint_name.lower() and "right" in bvh_joint_name.lower()) or \
                       ("right" in robot_joint_name.lower() and "left" in bvh_joint_name.lower()) or \
                       ("leg" in robot_joint_name.lower() and "arm" in bvh_joint_name.lower()) or \
                       ("arm" in robot_joint_name.lower() and "leg" in bvh_joint_name.lower()):
                        problematic_pairs.append((i, robot_joint_name, bvh_joint_name))
                
                if problematic_pairs:
                    print(f"\n  ⚠️ 发现可疑的对应关系:")
                    for i, robot_name, bvh_name in problematic_pairs:
                        print(f"    [{i}] {robot_name} ↔ {bvh_name} (可能不匹配)")
                
                print("") # 空行分隔
            
            # 计算各项损失：机器人FK结果 vs BVH目标
            keypoint_loss = self.calcKeyPointDiffLoss(robot_key_positions, bvh_target_positions)
            smoothness_loss_total, per_frame_smoothness_loss = self.calcSmoothnessLoss(robot_key_positions)
            acceleration_loss = self.calcAccelerationLoss(robot_key_positions)
            foot_height_loss = self.calcFootHeightLoss(robot_joint_positions)
            
            # 计算关节限制损失
            joint_limit_loss = torch.tensor(0.0, device=self.device)  # 简化为零损失
            # joint_limit_loss = self.calcJointLimitLoss(dof_pos_new)
            
            # 总损失
            total_loss = (
                self.keypoint_loss_weight * keypoint_loss +
                self.smoothness_loss_weight * smoothness_loss_total +
                self.acceleration_loss_weight * acceleration_loss +
                self.foot_height_loss_weight * foot_height_loss +
                0.1 * joint_limit_loss  # 关节限制损失权重
            )
            
            # 反向传播和优化
            total_loss.backward()
            optimizer.step()
            scheduler.step()
            

            
            # 记录损失
            loss_dict = {
                'total': total_loss.item(),
                'keypoint': keypoint_loss.item(),
                'smoothness': smoothness_loss_total.item(),
                'acceleration': acceleration_loss.item(),
                'foot_height': foot_height_loss.item(),
                'joint_limit': joint_limit_loss.item()
            }
            loss_history.append(loss_dict)
            
            # 输出进度
            if iteration % 100 == 0 or iteration == self.iterations - 1:
                print(f"迭代 {iteration:4d}: 总损失={total_loss.item():.6f}, "
                      f"关键点={keypoint_loss.item():.6f}, "
                      f"平滑={smoothness_loss_total.item():.6f}, "
                      f"加速度={acceleration_loss.item():.6f}, "
                      f"脚高={foot_height_loss.item():.6f}, "
                      f"关节限制={joint_limit_loss.item():.6f}, "
                      f"学习率={scheduler.get_last_lr()[0]:.6f}")
        
        print("\n优化完成!")
        
        # 6. 应用平滑滤波（可选）
        if self.args.apply_filter:
            print("应用高斯平滑滤波...")
            try:
                original_shape = dof_pos_new.shape
                dof_reshaped = dof_pos_new.squeeze(-1)
                dof_transposed = dof_reshaped.transpose(1, 2)
                
                dof_filtered = gaussian_filter_1d_batch(
                    dof_transposed, 
                kernel_size=self.kernel_size, 
                sigma=self.sigma
            )
                
                dof_pos_new = dof_filtered.transpose(1, 2).unsqueeze(-1)
                print(f"滤波完成: {original_shape} -> {dof_pos_new.shape}")
                
            except Exception as e:
                print(f"滤波失败: {e}")
                print("跳过平滑滤波步骤，使用原始优化结果")
        
        # 7. 计算最终FK结果（避免重复计算）
        print("计算最终FK结果...")
        final_pose_aa = self.calcAA(dof_pos_new, root_rot_new)
        final_fk_return = self.Humanoid_fk.fk_batch(
            final_pose_aa, 
            root_pos_new[None,], 
            return_full=True
        )
        final_joint_positions = self.calcJointPosition(final_fk_return)
        
        # 8. 保存结果
        output_path = self.save_results(dof_pos_new, root_pos_new, root_rot_new, loss_history, 
                                      final_joint_positions)
        
        # 9. 播放优化后的动作
        if not self.args.no_vis:
            self.play_optimized_motion(dof_pos_new, root_pos_new, root_rot_new, bvh_target_positions,
                                     final_joint_positions)
        
        print("="*50)
        print("BVH动作重定向完成!")
        print(f"结果保存在: {output_path}")
        print("="*50)
        
        return output_path

    def save_results(self, dof_pos, root_pos, root_rot, loss_history, final_joint_positions):
        """保存重定向结果为pkl文件 - 参考grad_rotation_fit_ik.py的格式"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"data/calc_bvh/{self.skeleton_params['metadata']['task_name']}"
        os.makedirs(output_dir, exist_ok=True)
        
        # 按照grad_rotation_fit_ik.py的格式准备数据
        bvh_key = os.path.splitext(os.path.basename(self.args.optimized_bvh))[0]
        
        # 使用最终的pose_aa用于保存
        final_pose_aa = self.calcAA(dof_pos, root_rot)
        
        data_dump = {
            bvh_key: {
                "root_trans_offset": root_pos.detach().cpu().numpy(),
                "pose_aa": final_pose_aa.squeeze(0).detach().cpu().numpy(),
                "dof_pos": dof_pos.squeeze(0).detach().cpu().numpy(),
                "root_rot": sRot.from_rotvec(root_rot.detach().cpu().numpy()).as_quat(),
                "global_translation": final_joint_positions.squeeze(0).detach().cpu().numpy(),
                "fps": float(self.fps),
                # BVH特有的元数据
                "metadata": {
                'timestamp': timestamp,
                    'source_bvh': self.args.optimized_bvh,
                    'skeleton_params': self.skeleton_params,
                'task_name': self.skeleton_params['metadata']['task_name'],
                    'optimization_version': 'BVH_Retarget_v2.0',
                'iterations': self.iterations,
                'final_loss': loss_history[-1]['total'] if loss_history else None,
                'parameters': {
                    'learning_rate': self.learning_rate,
                    'keypoint_loss_weight': self.keypoint_loss_weight,
                    'smoothness_loss_weight': self.smoothness_loss_weight,
                    'foot_height_loss_weight': self.foot_height_loss_weight,
                    'joint_limit_loss_weight': 0.1
                    }
                }
            }
        }
        
        # 保存为pkl文件
        output_path = os.path.join(output_dir, f"bvh_retarget_result_{timestamp}.pkl")
        joblib.dump(data_dump, output_path)
        
        # 保存为BVH文件
        bvh_output_path = os.path.join(output_dir, f"bvh_retarget_result_{timestamp}.bvh")
        self.save_bvh_file(bvh_output_path, dof_pos, root_pos, root_rot)
        
        print(f"重定向结果已保存到: {output_path}")
        print(f"BVH文件已保存到: {bvh_output_path}")
        
        return output_path

    def save_bvh_file(self, output_path, dof_pos, root_pos, root_rot):
        """只保存motion数据，不要结构"""
        from scipy.spatial.transform import Rotation as sRot
        import numpy as np
        
        with open(output_path, 'w') as f:
            f.write(f"Frames: {self.num_frames}\n")
            f.write(f"Frame Time: {1.0/self.fps:.6f}\n")
            
            for frame_idx in range(self.num_frames):
                frame_data = []
                
                # 根节点位置
                root_position = root_pos[frame_idx].detach().cpu().numpy()
                frame_data.extend([root_position[0], root_position[1], root_position[2]])
                
                # 根节点旋转（轴角转XYZ欧拉角）
                root_rotation = root_rot[frame_idx].detach().cpu().numpy()
                if np.linalg.norm(root_rotation) > 1e-6:
                    root_euler = sRot.from_rotvec(root_rotation).as_euler('XYZ', degrees=True)
                else:
                    root_euler = np.zeros(3)
                frame_data.extend([root_euler[0], root_euler[1], root_euler[2]])
                
                # 关节角度（弧度转度数，每个关节3个旋转角度）
                frame_dof = dof_pos[0, frame_idx].detach().cpu().numpy().flatten()
                frame_dof_degrees = np.degrees(frame_dof)
                
                # 每个关节输出3个旋转角度（Z, X, Y），只有第一个轴有实际值
                for joint_angle in frame_dof_degrees:
                    frame_data.extend([joint_angle, 0.0, 0.0])  # Z, X, Y旋转
                
                f.write(" " + " ".join(f"{val:.6f}" for val in frame_data) + "\n")
    


    def play_optimized_motion(self, dof_pos, root_pos, root_rot, bvh_target_positions, final_joint_positions):
        """使用新的BVH播放器播放优化后的动作"""
        print("开始播放优化后的动作...")
        
        # 🔧 数据验证
        print(f"📊 数据验证:")
        print(f"  - dof_pos形状: {dof_pos.shape}")
        print(f"  - root_pos形状: {root_pos.shape}")
        print(f"  - root_rot形状: {root_rot.shape}")
        print(f"  - bvh_target_positions形状: {bvh_target_positions.shape}")
        print(f"  - final_joint_positions形状: {final_joint_positions.shape}")
        
        # 检查数据是否为空
        if bvh_target_positions.numel() == 0:
            print("❌ 错误: bvh_target_positions为空")
            return
        
        if final_joint_positions.numel() == 0:
            print("❌ 错误: final_joint_positions为空")
            return
        
        # 使用预计算的FK结果
        with torch.no_grad():
            # 重新计算FK以获得最终的关节位置 - 使用备份脚本的方式
            pose_aa_final = self.calcAA(dof_pos, root_rot)
            fk_return_final = self.Humanoid_fk.fk_batch(
                pose_aa_final, 
                root_pos[None,], 
                return_full=True
            )
            
            # 获取关节位置
            robot_joint_positions = self.calcJointPosition(fk_return_final)
            
            # 计算关键点位置 - 使用有效的机器人关节索引
            if hasattr(self, 'current_robot_joint_pick_idx'):
                robot_joint_indices = self.current_robot_joint_pick_idx
            else:
                robot_joint_indices = self.robot_joint_pick_idx
                
            # 🔧 验证关节索引
            if not robot_joint_indices or len(robot_joint_indices) == 0:
                print("❌ 错误: robot_joint_indices为空")
                return
                
            print(f"播放器关节索引验证:")
            print(f"  - 使用的机器人关节索引: {robot_joint_indices}")
            print(f"  - 机器人关节位置形状: {robot_joint_positions.shape}")
            
            # 🔧 验证关节索引是否在有效范围内
            max_joint_idx = max(robot_joint_indices)
            if max_joint_idx >= robot_joint_positions.shape[2]:
                print(f"❌ 错误: 关节索引超出范围，最大索引 {max_joint_idx} >= 总关节数 {robot_joint_positions.shape[2]}")
                # 过滤掉超出范围的索引
                valid_indices = [idx for idx in robot_joint_indices if idx < robot_joint_positions.shape[2]]
                if not valid_indices:
                    print("❌ 错误: 没有有效的关节索引")
                    return
                print(f"🔧 使用有效的关节索引: {valid_indices}")
                robot_joint_indices = valid_indices
            
            # 提取关键点位置
            robot_key_pos = robot_joint_positions[:, :, robot_joint_indices]
            
            print(f"  - 提取的关键点形状: {robot_key_pos.shape}")
            print(f"  - BVH目标位置形状: {bvh_target_positions.shape}")
            
            # 🔧 确保维度匹配
            if robot_key_pos.shape[2] != bvh_target_positions.shape[1]:
                print(f"⚠️ 警告: 关键点数量不匹配: robot={robot_key_pos.shape[2]}, bvh={bvh_target_positions.shape[1]}")
                # 取最小值进行匹配
                min_joints = min(robot_key_pos.shape[2], bvh_target_positions.shape[1])
                robot_key_pos = robot_key_pos[:, :, :min_joints]
                bvh_target_positions = bvh_target_positions[:, :min_joints, :]
                print(f"🔧 调整后: robot_key_pos={robot_key_pos.shape}, bvh_target_positions={bvh_target_positions.shape}")
            
            # 🔧 确保帧数匹配
            if robot_key_pos.shape[1] != bvh_target_positions.shape[0]:
                print(f"⚠️ 警告: 帧数不匹配: robot={robot_key_pos.shape[1]}, bvh={bvh_target_positions.shape[0]}")
                min_frames = min(robot_key_pos.shape[1], bvh_target_positions.shape[0])
                robot_key_pos = robot_key_pos[:, :min_frames, :]
                bvh_target_positions = bvh_target_positions[:min_frames, :, :]
                print(f"🔧 调整后: robot_key_pos={robot_key_pos.shape}, bvh_target_positions={bvh_target_positions.shape}")
            
            # 计算损失
            try:
                final_frame_losses = torch.norm(robot_key_pos - bvh_target_positions, p=2, dim=-1).mean(dim=-1).squeeze().cpu().numpy()
                print(f"  - 损失形状: {final_frame_losses.shape}")
            except Exception as e:
                print(f"❌ 计算损失失败: {e}")
                # 创建默认损失数组
                final_frame_losses = np.zeros(bvh_target_positions.shape[0])
        
        # 使用预加载的接触序列
        try:
            if hasattr(self, 'contact_sequence') and self.contact_sequence is not None:
                contact_sequence = self.contact_sequence
                # 确保帧数匹配
                expected_frames = bvh_target_positions.shape[0]
                if contact_sequence.shape[0] != expected_frames:
                    if contact_sequence.shape[0] > expected_frames:
                        # 截断
                        contact_sequence = contact_sequence[:expected_frames]
                        print(f"⚠️ 接触序列帧数过多，已截断至{expected_frames}帧")
                    else:
                        # 填充（重复最后一帧）
                        last_frame = contact_sequence[-1:] if len(contact_sequence) > 0 else np.array([[False, False]])
                        padding = np.repeat(last_frame, expected_frames - contact_sequence.shape[0], axis=0)
                        contact_sequence = np.concatenate([contact_sequence, padding], axis=0)
                        print(f"⚠️ 接触序列帧数不足，已填充至{expected_frames}帧")
                
                print(f"✅ 使用预加载的接触序列，形状: {contact_sequence.shape}")
            else:
                print("⚠️ 未找到预加载的接触序列，创建默认序列")
                contact_sequence = np.zeros((bvh_target_positions.shape[0], 2), dtype=bool)
        except Exception as e:
            print(f"⚠️ 接触序列处理失败: {e}")
            # 创建默认接触序列
            contact_sequence = np.zeros((bvh_target_positions.shape[0], 2), dtype=bool)
        
        # 计算BVH骨架连接关系
        try:
            self._calculate_bvh_skeleton_connections()
        except Exception as e:
            print(f"⚠️ BVH骨架连接计算失败: {e}")
            self.skeleton_params['bvh_skeleton_connections'] = {}
        
        # 向skeleton_params添加正确的机器人关节索引信息
        self.skeleton_params['robot_joint_indices'] = robot_joint_indices
        
        # 🔧 修复：确保MuJoCo可视化器能访问有效的关节索引
        if hasattr(self.mujoco_viewer, 'valid_robot_joint_indices'):
            self.mujoco_viewer.valid_robot_joint_indices = self.valid_robot_joint_indices
        else:
            # 为MuJoCo可视化器添加有效关节索引属性
            setattr(self.mujoco_viewer, 'valid_robot_joint_indices', self.valid_robot_joint_indices)
        
        if hasattr(self.mujoco_viewer, 'valid_bvh_joint_indices'):
            self.mujoco_viewer.valid_bvh_joint_indices = self.valid_bvh_joint_indices
        else:
            # 为MuJoCo可视化器添加有效BVH关节索引属性
            setattr(self.mujoco_viewer, 'valid_bvh_joint_indices', self.valid_bvh_joint_indices)
        
        # 🔧 修复：确保MuJoCo可视化器能访问BVH运动数据（用于获取关节名称）
        if hasattr(self.mujoco_viewer, 'bvh_motion'):
            self.mujoco_viewer.bvh_motion = self.bvh_motion
        else:
            setattr(self.mujoco_viewer, 'bvh_motion', self.bvh_motion)
        
        print(f"[Debug] 已向MuJoCo可视化器传递关节索引:")
        print(f"  - 有效机器人关节索引: {self.valid_robot_joint_indices}")
        print(f"  - 有效BVH关节索引: {self.valid_bvh_joint_indices}")
        
        # 使用MujocoViwer播放动作
        try:
            # 准备数据以匹配MujocoViwer.visRefPoint的参数格式
            # bvh_joints_scaled: 使用BVH目标位置作为参考关节位置
            bvh_joints_scaled = bvh_target_positions.unsqueeze(0)  # [1, num_frames, num_joints, 3]
            
            # T1_joint_dump: 机器人原始FK结果（所有关节）
            T1_joint_dump = final_joint_positions  # [1, num_frames, num_joints, 3]
            
            # T1_joint: 机器人修正后的FK结果（关键关节）
            T1_joint = robot_key_pos  # [1, num_frames, num_key_joints, 3]
            
            # bvh_key_joints: 关键关节位置（使用BVH目标位置）
            bvh_key_joints = bvh_target_positions.unsqueeze(0)  # [1, num_frames, num_joints, 3]
            
            # 获取BVH骨架连接关系
            bvh_skeleton_connections = self.skeleton_params.get('bvh_skeleton_connections', {})
            
            print(f"📊 传递给MujocoViwer的数据:")
            print(f"  - bvh_joints_scaled形状: {bvh_joints_scaled.shape}")
            print(f"  - T1_joint_dump形状: {T1_joint_dump.shape}")
            print(f"  - T1_joint形状: {T1_joint.shape}")
            print(f"  - bvh_key_joints形状: {bvh_key_joints.shape}")
            print(f"  - contact_sequence形状: {contact_sequence.shape}")
            print(f"  - bvh_skeleton_connections数量: {len(bvh_skeleton_connections)}")
            
            # 调用MujocoViwer的visRefPoint方法
            self.mujoco_viewer.visRefPoint(
                bvh_joints_scaled=bvh_joints_scaled,
                T1_joint_dump=T1_joint_dump,
                T1_joint=T1_joint,
                bvh_key_joints=bvh_key_joints,
                contact_sequence=contact_sequence,
                root_trans_offset=root_pos,
                dof_pos_new=dof_pos,
                root_rot_new=root_rot,
                final_frame_losses=final_frame_losses,
                left_foot_samples=None,
                right_foot_samples=None,
                bvh_skeleton_connections=bvh_skeleton_connections
            )
            
        except Exception as e:
            print(f"❌ MuJoCo播放错误: {e}")
            import traceback
            traceback.print_exc()

    def generate_contact_sequence(self, robot_joint_positions):
        """生成接触序列 - 直接使用脚趾关节进行接触检测
        
        Args:
            robot_joint_positions: [1, num_frames, num_joints, 3] 机器人关节位置
            
        Returns:
            contact_sequence: [num_frames, 2] 接触序列 (左脚, 右脚)
        """
        try:
            # 检查脚趾索引是否有效
            if self.left_toe_idx == -1 or self.right_toe_idx == -1:
                print("⚠️ 脚趾索引无效，使用默认接触序列")
                num_frames = robot_joint_positions.shape[1]
                contact_sequence = np.zeros((num_frames, 2), dtype=bool)
                # 简单的交替接触模式
                for i in range(num_frames):
                    if i % 20 < 10:  # 每20帧交替一次
                        contact_sequence[i, 0] = True  # 左脚接触
                    else:
                        contact_sequence[i, 1] = True  # 右脚接触
                return contact_sequence
            
            positions = robot_joint_positions[0]  # [num_frames, num_joints, 3]
            num_frames = positions.shape[0]
            contact_sequence = torch.zeros((num_frames, 2), dtype=torch.bool)
            
            # 接触检测参数
            contact_height_threshold = self.motion_lib_cfg.contact_height_threshold
            velocity_threshold = self.motion_lib_cfg.contact_velocity_threshold
            
            # 直接使用脚趾关节位置
            left_toe_positions = positions[:, self.left_toe_idx, :]  # [num_frames, 3]
            right_toe_positions = positions[:, self.right_toe_idx, :]  # [num_frames, 3]
            
            # 提取脚趾高度（z坐标）
            left_toe_heights = left_toe_positions[:, 2]  # [num_frames]
            right_toe_heights = right_toe_positions[:, 2]  # [num_frames]
            
            # 计算脚趾垂直速度
            left_toe_velocity = torch.zeros_like(left_toe_heights)
            right_toe_velocity = torch.zeros_like(right_toe_heights)
            
            if num_frames > 1:
                left_toe_velocity[1:] = torch.diff(left_toe_heights) * self.fps  # 垂直速度
                right_toe_velocity[1:] = torch.diff(right_toe_heights) * self.fps  # 垂直速度
            
            # 综合高度和速度判断接触
            # 左脚接触检测
            left_height_contact = left_toe_heights < contact_height_threshold
            left_low_velocity = torch.abs(left_toe_velocity) < velocity_threshold
            contact_sequence[:, 0] = left_height_contact & left_low_velocity
            
            # 右脚接触检测
            right_height_contact = right_toe_heights < contact_height_threshold
            right_low_velocity = torch.abs(right_toe_velocity) < velocity_threshold
            contact_sequence[:, 1] = right_height_contact & right_low_velocity
            
            # 后处理：消除短暂的接触间隙
            contact_sequence = self.smooth_contact_sequence(contact_sequence)
            
            # 转换为numpy数组
            contact_sequence_np = contact_sequence.detach().cpu().numpy()
            
            print(f"接触序列生成完成:")
            print(f"  - 使用脚趾关节: Left[{self.left_toe_idx}], Right[{self.right_toe_idx}]")
            print(f"  - 左脚接触帧数: {contact_sequence_np[:, 0].sum()}")
            print(f"  - 右脚接触帧数: {contact_sequence_np[:, 1].sum()}")
            print(f"  - 左脚高度范围: [{left_toe_heights.min():.3f}, {left_toe_heights.max():.3f}]")
            print(f"  - 右脚高度范围: [{right_toe_heights.min():.3f}, {right_toe_heights.max():.3f}]")
            
            return contact_sequence_np
            
        except Exception as e:
            print(f"接触序列生成失败: {e}")
            import traceback
            traceback.print_exc()
            # 返回默认的接触序列（交替接触）
            import numpy as np
            num_frames = robot_joint_positions.shape[1]
            contact_sequence = np.zeros((num_frames, 2), dtype=bool)
            # 简单的交替接触模式
            for i in range(num_frames):
                if i % 20 < 10:  # 每20帧交替一次
                    contact_sequence[i, 0] = True  # 左脚接触
                else:
                    contact_sequence[i, 1] = True  # 右脚接触
            return contact_sequence
    
    def smooth_contact_sequence(self, contact_sequence, min_contact_duration=3):
        """平滑接触序列，消除短暂的接触间隙
        
        Args:
            contact_sequence: [num_frames, 2] 原始接触序列
            min_contact_duration: 最小接触持续时间（帧数）
            
        Returns:
            smoothed_sequence: [num_frames, 2] 平滑后的接触序列
        """
        smoothed_sequence = contact_sequence.clone()
        num_frames = contact_sequence.shape[0]
        
        for foot_idx in range(2):  # 左脚和右脚
            # 找到所有接触段
            contact_changes = torch.diff(contact_sequence[:, foot_idx].float())
            start_frames = torch.where(contact_changes > 0)[0] + 1
            end_frames = torch.where(contact_changes < 0)[0] + 1
            
            # 处理边界情况
            if contact_sequence[0, foot_idx]:
                start_frames = torch.cat([torch.tensor([0]), start_frames])
            if contact_sequence[-1, foot_idx]:
                end_frames = torch.cat([end_frames, torch.tensor([num_frames])])
            
            # 删除过短的接触段
            for start, end in zip(start_frames, end_frames):
                if end - start < min_contact_duration:
                    smoothed_sequence[start:end, foot_idx] = False
            
            # 填补短暂的间隙
            for i in range(len(start_frames) - 1):
                gap_start = end_frames[i]
                gap_end = start_frames[i + 1]
                if gap_end - gap_start < min_contact_duration:
                    smoothed_sequence[gap_start:gap_end, foot_idx] = True
        
        return smoothed_sequence

    def _calculate_bvh_skeleton_connections(self):
        """计算BVH骨架连接关系 - 基于选中的关键关节计算正确的连接关系"""
        try:
            # 🔧 修复：基于BVH文件的实际关节父子关系计算连接
            if not hasattr(self, 'bvh_motion') or not hasattr(self.bvh_motion, 'joint_parent'):
                print("⚠️ BVH运动数据或关节父子关系不可用")
                self.skeleton_params['bvh_skeleton_connections'] = {}
                return
            
            # 获取BVH的全局关节父子关系
            joint_parents = self.bvh_motion.joint_parent  # [num_joints] 数组，包含每个关节的父关节索引
            joint_names = self.bvh_motion.joint_name      # [num_joints] 数组，包含关节名称
            
            print(f"[Debug] BVH关节总数: {len(joint_names)}")
            print(f"[Debug] 选中的BVH关节索引: {self.valid_bvh_joint_indices}")
            print(f"[Debug] 选中的BVH关节名称: {[joint_names[i] for i in self.valid_bvh_joint_indices]}")
            
            # 基于选中的关节计算连接关系
            connections = {}
            for i, child_global_idx in enumerate(self.valid_bvh_joint_indices):
                # 获取该关节在全局BVH中的父关节索引
                parent_global_idx = joint_parents[child_global_idx]
                
                # 检查父关节是否也在选中的关节列表中
                if parent_global_idx >= 0 and parent_global_idx in self.valid_bvh_joint_indices:
                    # 找到父关节在选中关节列表中的局部索引
                    parent_local_idx = self.valid_bvh_joint_indices.index(parent_global_idx)
                    connections[i] = parent_local_idx  # child_local_idx -> parent_local_idx
                    
                    child_name = joint_names[child_global_idx]
                    parent_name = joint_names[parent_global_idx]
                    print(f"[Debug] 连接: [{i}]{child_name} -> [{parent_local_idx}]{parent_name}")
            
            # 保存连接关系
            self.skeleton_params['bvh_skeleton_connections'] = connections
            print(f"✅ BVH骨架连接关系构建完成: {len(connections)} 个连接")
            print(f"[Debug] 最终连接关系: {connections}")
            
        except Exception as e:
            print(f"❌ 构建BVH骨架连接失败: {e}")
            import traceback
            traceback.print_exc()
            self.skeleton_params['bvh_skeleton_connections'] = {}
    
    def _add_missing_arm_connections(self, connections):
        """添加缺失的手臂连接 - 基于关节名称的逻辑推断"""
        missing_connections = {}
        
        # 建立关节名称到索引的映射
        joint_name_to_idx = {}
        for i, joint_idx in enumerate(self.valid_bvh_joint_indices):
            joint_name = self.bvh_motion.joint_name[joint_idx]
            joint_name_to_idx[joint_name] = i
        
        # 定义期望的连接关系
        expected_connections = [
            ('LeftForeArm', 'LeftShoulder'),
            ('RightForeArm', 'RightShoulder'),
            ('LeftHand', 'LeftForeArm'),
            ('RightHand', 'RightForeArm'),
            ('LeftFoot', 'LeftLeg'),
            ('RightFoot', 'RightLeg'),
            ('LeftToeBase', 'LeftFoot'),
            ('RightToeBase', 'RightFoot'),
        ]
        
        # 检查每个期望的连接
        for child_name, parent_name in expected_connections:
            if child_name in joint_name_to_idx and parent_name in joint_name_to_idx:
                child_idx = joint_name_to_idx[child_name]
                parent_idx = joint_name_to_idx[parent_name]
                
                # 如果这个连接还不存在，添加它
                if child_idx not in connections:
                    missing_connections[child_idx] = parent_idx
                    print(f"    + 添加缺失连接: [{child_idx}]{child_name} -> [{parent_idx}]{parent_name}")
        
        return missing_connections
    
    def unwrap_euler_angles(self, euler_angles_deg):
        """
        对欧拉角序列进行角度解包，消除-179°到179°的跳跃
        
        Args:
            euler_angles_deg: [num_frames, 3] 欧拉角数据（度）
            
        Returns:
            unwrapped_angles_deg: [num_frames, 3] 解包后的欧拉角数据（度）
        """
        import numpy as np
        
        print("🔄 开始角度解包处理...")
        
        # 转换为numpy数组以便处理
        if isinstance(euler_angles_deg, torch.Tensor):
            angles_np = euler_angles_deg.detach().cpu().numpy()
        else:
            angles_np = np.array(euler_angles_deg)
        
        # 检查角度范围，确认是否存在跳跃
        angle_ranges = []
        jump_detected = False
        
        for axis in range(3):
            angle_sequence = angles_np[:, axis]
            angle_min = np.min(angle_sequence)
            angle_max = np.max(angle_sequence)
            angle_ranges.append((angle_min, angle_max))
            
            # 检测大幅度跳跃（>300度表示可能存在-179到179的跳跃）
            angle_diffs = np.abs(np.diff(angle_sequence))
            max_jump = np.max(angle_diffs)
            
            if max_jump > 300:
                jump_detected = True
                print(f"  - 轴{axis}: 检测到角度跳跃，最大跳跃 {max_jump:.1f}°")
                print(f"    范围: [{angle_min:.1f}°, {angle_max:.1f}°]")
        
        if not jump_detected:
            print("  - 未检测到明显的角度跳跃，跳过解包")
            return euler_angles_deg
        
        # 对每个轴分别进行解包
        unwrapped_angles = np.zeros_like(angles_np)
        
        for axis in range(3):
            # 将角度转换为弧度进行解包
            angles_rad = np.deg2rad(angles_np[:, axis])
            
            # 使用numpy的unwrap函数进行角度解包
            unwrapped_rad = np.unwrap(angles_rad)
            
            # 转换回度
            unwrapped_deg = np.rad2deg(unwrapped_rad)
            unwrapped_angles[:, axis] = unwrapped_deg
            
            # 报告解包结果
            original_range = angle_ranges[axis]
            new_min = np.min(unwrapped_deg)
            new_max = np.max(unwrapped_deg)
            
            print(f"  - 轴{axis}: [{original_range[0]:.1f}°, {original_range[1]:.1f}°] -> [{new_min:.1f}°, {new_max:.1f}°]")
            
            # 检查解包后的连续性
            max_jump_after = np.max(np.abs(np.diff(unwrapped_deg)))
            print(f"    解包后最大跳跃: {max_jump_after:.1f}°")
        
        print("✅ 角度解包完成")
        
        # 返回相同类型的数据
        if isinstance(euler_angles_deg, torch.Tensor):
            return torch.tensor(unwrapped_angles, dtype=euler_angles_deg.dtype)
        else:
            return unwrapped_angles

    def unwrap_bvh_rotations(self):
        """
        对所有BVH关节的旋转数据进行角度解包处理
        """
        import numpy as np
        
        print("🔄 开始对所有BVH关节进行角度解包...")
        
        if self.bvh_motion.joint_rotation is None:
            print("⚠️ 警告: BVH joint_rotation为空，跳过角度解包")
            return
        
        original_rotations = self.bvh_motion.joint_rotation  # [num_frames, num_joints, 3]
        num_frames, num_joints, _ = original_rotations.shape
        
        print(f"  - 处理 {num_joints} 个关节，共 {num_frames} 帧")
        
        # 统计需要解包的关节数量
        joints_needing_unwrap = []
        
        # 对每个关节分别进行角度解包
        unwrapped_rotations = original_rotations.copy()
        
        for joint_idx in range(num_joints):
            joint_name = self.bvh_motion.joint_name[joint_idx] if joint_idx < len(self.bvh_motion.joint_name) else f"Joint_{joint_idx}"
            joint_rotations = original_rotations[:, joint_idx, :]  # [num_frames, 3]
            
            # 检测是否需要解包
            needs_unwrap = False
            for axis in range(3):
                angle_diffs = np.abs(np.diff(joint_rotations[:, axis]))
                max_jump = np.max(angle_diffs) if len(angle_diffs) > 0 else 0
                if max_jump > 300:  # 大于300度的跳跃
                    needs_unwrap = True
                    break
            
            if needs_unwrap:
                joints_needing_unwrap.append((joint_idx, joint_name))
                # 对这个关节进行角度解包
                unwrapped_joint_rotations = self.unwrap_euler_angles(joint_rotations)
                unwrapped_rotations[:, joint_idx, :] = unwrapped_joint_rotations
        
        if joints_needing_unwrap:
            print(f"  - 对 {len(joints_needing_unwrap)} 个关节进行了角度解包:")
            for joint_idx, joint_name in joints_needing_unwrap[:5]:  # 只显示前5个
                print(f"    [{joint_idx}] {joint_name}")
            if len(joints_needing_unwrap) > 5:
                print(f"    ... 还有 {len(joints_needing_unwrap) - 5} 个关节")
            
            # 计算整体改善效果
            total_jumps_before = 0
            total_jumps_after = 0
            
            for joint_idx, _ in joints_needing_unwrap:
                original_joint = original_rotations[:, joint_idx, :]
                unwrapped_joint = unwrapped_rotations[:, joint_idx, :]
                
                for axis in range(3):
                    # 计算解包前的跳跃
                    jumps_before = np.sum(np.abs(np.diff(original_joint[:, axis])) > 300)
                    total_jumps_before += jumps_before
                    
                    # 计算解包后的跳跃
                    jumps_after = np.sum(np.abs(np.diff(unwrapped_joint[:, axis])) > 300)
                    total_jumps_after += jumps_after
            
            print(f"  - 角度跳跃统计: {total_jumps_before} -> {total_jumps_after} (减少了 {total_jumps_before - total_jumps_after} 个跳跃)")
        else:
            print("  - 所有关节的角度数据都是连续的，无需解包")
        
        # 更新BVH运动数据
        self.bvh_motion.joint_rotation = unwrapped_rotations
        
        print("✅ BVH旋转数据角度解包完成")



    def run_retargeting(self):
        """运行完整的重定向流程"""
        return self.retargetBVHData()

    def load_bvh_raw_euler_data(self):
        """
        直接从BVH文件中读取原始的根节点欧拉角数据，避免四元数转换
        参考备份脚本的实现
        
        Returns:
            root_euler_degrees: [num_frames, 3] 根节点欧拉角（度）
        """
        bvh_file_path = self.args.optimized_bvh
        
        with open(bvh_file_path, 'r') as f:
            lines = f.readlines()
        
        # 找到MOTION数据开始位置
        motion_start_idx = -1
        for i, line in enumerate(lines):
            if line.strip() == "MOTION":
                motion_start_idx = i
                break
        
        if motion_start_idx == -1:
            raise ValueError("未找到MOTION数据")
        
        # 跳过Frames和Frame Time行，找到数据开始行
        data_start_idx = -1
        for i in range(motion_start_idx + 1, len(lines)):
            line = lines[i].strip()
            if line.startswith("Frames:") or line.startswith("Frame Time:"):
                continue
            if line and not line.startswith("#"):  # 找到第一行实际数据
                data_start_idx = i
                break
        
        if data_start_idx == -1:
            raise ValueError("未找到运动数据")
        
        # 读取所有运动数据
        motion_data = []
        for i in range(data_start_idx, len(lines)):
            line = lines[i].strip()
            if line:
                try:
                    values = [float(x) for x in line.split()]
                    motion_data.append(values)
                except ValueError:
                    break  # 遇到非数字行，停止读取
        
        motion_data = np.array(motion_data)
        print(f"读取到原始运动数据形状: {motion_data.shape}")
        
        # 根据BVH文件结构，前3个通道是根节点位置，接下来3个是根节点旋转
        # 我们需要根节点的旋转数据
        if motion_data.shape[1] < 6:
            raise ValueError(f"运动数据通道数不足，期望至少6个通道，实际得到 {motion_data.shape[1]} 个")
        
        # 提取根节点欧拉角数据（位置通道3-5，即索引3,4,5）
        root_euler_degrees = motion_data[:, 3:6]  # [num_frames, 3]
        
        print(f"提取根节点欧拉角数据形状: {root_euler_degrees.shape}")
        
        return root_euler_degrees


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="BVH动作重定向 - 使用bone_optimizer_gui.py的输出进行动作重定向",
        epilog="""
使用示例:
  python bvh_motion_retarget.py --optimized-bvh data/calc_bvh/unitreeG1/walk_forward_optimized.bvh --iterations 1000

输入要求:
  - 优化后的BVH文件：由bone_optimizer_gui.py生成的BVH文件，包含已经缩放到机器人尺寸的骨骼长度
  - 配置文件：脚本会自动在BVH文件同目录下查找对应的JSON配置文件
  
工作流程:
  1. 运行bone_optimizer_gui.py优化骨骼结构
  2. 使用本脚本对优化后的BVH文件进行动作重定向
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # 必需参数
    parser.add_argument("--optimized-bvh", type=str, default="data/calc_bvh/unitreeG1/jooging_optimized.bvh",
                       help="优化后的BVH文件路径 (由bone_optimizer_gui.py生成)")
    parser.add_argument("--config", type=str, default="data/calc_bvh/unitreeG1/jooging.json",
                       help="优化后的配置文件 (由bone_optimizer_gui.py生成)")
    
    # 优化参数
    parser.add_argument("--iterations", type=int, default=200,
                       help="优化迭代次数 (默认: 500)")
    parser.add_argument("--learning-rate", type=float, default=0.01,
                       help="学习率 (默认: 0.01)")
    
    # 损失权重
    parser.add_argument("--keypoint-loss-weight", type=float, default=1.0,
                       help="关键点损失权重 (默认: 1.0)")
    parser.add_argument("--smoothness-loss-weight", type=float, default=0.1,
                       help="平滑损失权重 (默认: 0.1)")
    parser.add_argument("--acceleration-loss-weight", type=float, default=0.0,
                       help="加速度损失权重 (默认: 0.05)")
    parser.add_argument("--contact-loss-weight", type=float, default=0.0,
                       help="接触损失权重 (默认: 0.05)")
    parser.add_argument("--foot-height-loss-weight", type=float, default=0.1,
                       help="脚部高度损失权重 (默认: 0.2)")
    
    # 平滑滤波
    parser.add_argument("--apply-filter", action="store_true", default=True,
                       help="应用高斯平滑滤波 (默认: True)")
    parser.add_argument("--kernel-size", type=int, default=7,
                       help="高斯核大小 (默认: 7)")
    parser.add_argument("--sigma", type=float, default=2.0,
                       help="高斯核标准差 (默认: 2.0)")
    
    # 其他选项
    parser.add_argument("--optimize-root", action="store_true", default=False,
                       help="优化根节点位置和旋转 (默认: True)")
    parser.add_argument("--unwrap-angles", action="store_true", default=False,
                        help="启用角度解包处理，解决±179°跳跃问题 (默认: True)")
    parser.add_argument("--contact-file", type=str, default="data/contact_seq/SFU_0005_jogging_contact.json",
                        help="接触序列JSON文件路径，如 data/contact_seq/SFU_0005_jogging_contact.json")
    parser.add_argument("--device", type=str, default="cpu", choices=["cpu", "cuda"],
                        help="计算设备 (默认: cpu)")
    parser.add_argument("--no-vis", action="store_true", default=False,
                       help="不显示可视化")
    
    args = parser.parse_args()
    
    # 验证输入文件
    if not os.path.exists(args.optimized_bvh):
        print(f"❌ 错误: 优化后的BVH文件不存在: {args.optimized_bvh}")
        print("\n💡 使用提示:")
        print("1. 请先运行 bone_optimizer_gui.py 进行骨骼拟合和优化")
        print("2. 确保输入的是优化后的BVH文件路径（通常以_optimized.bvh结尾）")
        print("3. 确保在BVH文件同目录下存在相应的配置JSON文件")
        sys.exit(1)
    
    print("🚀 BVH动作重定向")
    print("=" * 50)
    print(f"📁 优化后的BVH文件: {args.optimized_bvh}")
    print(f"💻 计算设备: {args.device}")
    print(f"🔄 优化迭代次数: {args.iterations}")
    print("=" * 50)
    
    try:
        # 创建重定向器并运行
        retargeter = BVHMotionRetargeter(args)
        output_path = retargeter.run_retargeting()
        
        print("\n" + "=" * 50)
        print("✅ 重定向成功完成!")
        print(f"📁 输出文件: {output_path}")
        print("=" * 50)
        
    except FileNotFoundError as e:
        print(f"\n❌ 文件错误: {e}")
        print("\n💡 解决方案:")
        print("1. 检查BVH文件路径是否正确")
        print("2. 确保已运行bone_optimizer_gui.py生成配置文件")
        print("3. 检查配置文件是否在BVH文件同目录下")
        sys.exit(1)
    except ValueError as e:
        print(f"\n❌ 配置错误: {e}")
        print("\n💡 解决方案:")
        print("1. 确保使用的是bone_optimizer_gui.py的输出文件")
        print("2. 检查配置文件格式是否正确")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 重定向失败: {e}")
        import traceback
        print("\n🔍 详细错误信息:")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 