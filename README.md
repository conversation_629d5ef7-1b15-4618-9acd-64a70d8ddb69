# SMPL 参数优化与动作重定向系统

本系统包含三个主要组件：
1. SMPL Beta 优化器（beta_optimizer_gui.py）：用于优化 SMPL 模型的 Beta 和 Scale 参数，并获取bias
2. 接触序列编辑器（visualize_smpl_contact.py）：用于编辑和生成脚部接触序列
3. 动作重定向器（grad_rotation_fit_ik.py）：使用优化后的参数进行动作重定向和 IK 求解

## 系统架构

```
smpl_beta_optimizer2fit_ik/
├── beta_optimizer_gui.py     # SMPL 参数优化器主程序
├── visualize_smpl_contact.py # 接触序列编辑器主程序
├── grad_rotation_fit_ik.py   # 动作重定向器主程序
├── setup.py                  # 项目安装配置文件
├── requirements.txt          # 项目依赖文件
├── configs/                  # 配置文件目录
│   └── default_beta_config.json  # 默认 Beta 优化配置
├── data/                    # 数据文件目录
│   ├── calc_beta/          # Beta优化结果保存目录
│   ├── processed_data/     # 处理后的数据目录
│   │   └── amass/         # AMASS数据集处理结果
│   │       ├── CMU/       # CMU数据集处理结果
│   │       └── ACCAD/     # ACCAD数据集处理结果
│   ├── smpl/              # SMPL模型文件目录
│   ├── amass_raw_data/    # AMASS原始数据目录
│   └── contact_seq/       # 接触序列数据
├── resources/              # 资源文件目录
├── utils/                  # 工具函数目录
│   ├── __init__.py        # 工具包初始化文件
│   ├── mujoco_viewer.py   # MuJoCo 可视化工具
│   ├── smpl_beta_core.py  # SMPL Beta 优化核心功能
│   └── smpl_data_procee/  # SMPL数据处理工具
│       ├── process_amass_raw.py  # 原始数据处理
│       └── process_amass_db.py   # 数据库处理
├── poselib/               # 姿态库
├── smplx/                 # SMPL 模型相关代码
└── humanoid/             # 人形机器人相关代码
```

## 安装

```bash
# 克隆仓库
git clone http://************/development/research/imitation_rl/humanoid_retarget.git
cd smpl_beta_optimizer2fit_ik

# 安装依赖
pip install -r requirements.txt

# 开发模式安装
pip install -e .

# 下载SMPL_NEUTRAL.pkl文件并解压至data/smpl文件夹下
https://download.is.tue.mpg.de/download.php?domain=smpl&sfile=SMPL_python_v.1.1.0.zip
```
## 数据预处理
在使用SMPL Beta优化器之前，需要先对AMASS原始数据进行处理：

1. 将AMASS原始数据（.npz格式）转换为PyTorch格式（.pt）：
```bash
python utils/smpl_data_procee/process_amass_raw.py --raw_dir data/amass_raw_data --out_dir data/processed_data
```

2. 将PyTorch格式数据（.pt）转换为数据库格式（.pkl）：
```bash
python utils/smpl_data_procee/process_amass_db.py --pt_path data/processed_data --out_dir data/processed_data --target_fr=90
```

## 1. SMPL Beta 优化器 (beta_optimizer_gui.py)

### 功能特点
- 图形化界面，实时可视化 SMPL 模型和目标姿态
- 动态调整损失权重
- 实时显示优化曲线
- 保存/加载配置
- 支持多种机器人平台（A2、T1、dobot、unitreeG1、x2、cdroid）

### 使用方法

```bash
python beta_optimizer_gui.py [参数]
```

参数说明：
- `--config`: 配置文件路径（默认：configs/default_beta_config.json）
- `--save-result`: 优化结果保存文件名称（默认：shape_scale_bias.json）
- `--save-config`: 配置保存路径（默认：configs/default_beta_config.json）
- `--device`: 运行设备，可选 "cpu" 或 "cuda"（默认：cpu）
- `--task`: 任务名称，可选值包括：
  - A2_retarget
  - T1_retarget
  - dobot_retarget
  - unitreeG1_retarget
  - x2_retarget
  - cdroid_retarget
  （默认：cdroid_retarget）

### 配置文件说明

配置文件（JSON 格式）包含以下主要参数：
```json
{
    "joint_loss_weight": 1.0,        // 关节损失权重
    "beta_reg_weight": 0.001,        // Beta正则化权重
    "scale_reg_weight": 0.001,       // Scale正则化权重
    "symmetry_loss_weight": 0.7,     // 对称性损失权重
    "joint_angle_weight": 0.02,      // 关节角度损失权重
    "bone_length_weight": 0.02,      // 骨骼长度损失权重
    "endpoint_loss_weight": 0.5,     // 末端位置损失权重
    "learning_rate": 0.005,          // 学习率
    "shape": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],  // SMPL形状参数
    "scale": 0.656856                // 缩放参数
}
```

## 2. 接触序列编辑器 (visualize_smpl_contact.py)

### 功能特点
- 可视化 SMPL 动作序列中的脚部接触状态
- 交互式编辑接触序列
- 支持自动检测和手动调整接触状态
- 实时预览接触效果
- 保存/加载接触序列

### 使用方法

```bash
python visualize_smpl_contact.py [参数]
```

参数说明：
- `smpl_file`: 输入的动作文件路径（.pkl格式）,必选参数
- `--height_threshold`: 高度阈值，用于自动检测接触（默认：0.12）
- `--velocity_threshold`: 速度阈值，用于自动检测接触（默认：0.1）
- `--save_filename`: 保存接触序列的文件名（默认：manual_saved_contacts.json），所有结果均保存在data/contact_seq/下
- `--save_contacts`: 是否在程序结束时自动保存接触序列（默认：False）

### 交互功能
- 使用空格键暂停/继续播放，暂停时使用左右方向键分别标记左右脚趾是否接触
- 调整高度和速度阈值来优化自动检测
- 实时预览接触状态的变化
- UI界面中可加载，保存接触序列，若指定--save_contacts，程序结束后会自动保存接触序列

## 3. 动作重定向器 (grad_rotation_fit_ik.py)

### 功能特点
- 使用优化后的 SMPL 参数进行动作重定向
- 支持逆运动学（IK）求解
- 生成机器人可学习的动作序列
- 实时可视化优化过程
- 支持多种损失函数的优化

### 使用方法

```bash
python grad_rotation_fit_ik.py [参数]
```

参数说明：
- `--task`: 任务名称，可选值包括：
  - A2_retarget
  - T1_retarget
  - dobot_retarget
  - unitreeG1_retarget
  - x2_retarget
  - cdroid_retarget
- `--shape-path`: 自定义shape_scale_bias文件路径
- `--source_data_path`: 源数据路径（文件）
- `--dataset_path`: 保存路径（文件夹）
- `--no_show`: 禁用可视化显示，默认为False
- `--device`: 设备选择，可选"cuda:0"或"cpu"
- `--kernel_size`: 高斯核大小
- `--sigma`: 高斯核的标准差
- `--iterations`: 迭代次数
- `--show_frequency`: 损失函数可视化间隔
- `--t_pose_mode`: 启用T-pose模式
- `--target_foot_height`: 目标脚部高度(Z轴)
- `--contact_file_path`: 接触文件路径

## 工作流程

1. 使用 beta_optimizer_gui.py 优化 SMPL 参数：
   - 加载目标姿态数据
   - 调整损失权重
   - 运行优化
   - 保存优化结果（shape_scale_bias.json）

2. 使用 visualize_smpl_contact.py 编辑接触序列：
   - 加载动作数据
   - 调整接触检测参数
   - 手动编辑接触状态
   - 保存接触序列（manual_saved_contacts.json）

3. 使用 grad_rotation_fit_ik.py 进行动作重定向：
   - 加载优化后的 SMPL 参数
   - 加载动作数据和接触序列
   - 运行 IK 求解
   - 生成机器人动作序列

## 注意事项

1. 环境要求：
   - Python >= 3.7
   - CUDA 支持（可选，但推荐）
   - 足够的系统内存（建议 16GB 以上）

2. 数据准备：
   - 确保数据文件格式正确
   - 检查文件路径权限

3. 优化建议：
   - 先使用默认配置进行测试
   - 根据实际效果调整损失权重
   - 注意观察优化曲线

## 常见问题

1. **OpenGL 相关警告**
   - 这些警告通常不会影响程序的主要功能
   - 如果遇到显示问题，可以尝试更新显卡驱动

2. **可视化窗口无响应**
   - 检查系统是否支持 OpenGL
   - 尝试使用不同的显示后端

3. **优化效果不理想**
   - 调整损失权重
   - 增加优化迭代次数
   - 检查输入数据的质量

# BVH骨骼优化器

## 使用方法

### 基本用法
```bash
python bone_optimizer_gui.py --bvh data/bvh/walk_forward.bvh --task unitreeG1_retarget
```

### Scale参数控制
```bash
# 只使用offset优化（默认）
python bone_optimizer_gui.py --bvh data/bvh/walk_forward.bvh

# 使用scale和offset一起优化
python bone_optimizer_gui.py --bvh data/bvh/walk_forward.bvh --use-scale
```

### 完整参数示例
```bash
python bone_optimizer_gui.py \
    --bvh data/bvh/walk_forward.bvh \
    --task unitreeG1_retarget \
    --device cpu \
    --use-scale \
    --config configs/default_bvh_config.json \
    --save-result my_optimization_result.json
```

## 命令行参数说明

- `--bvh`: BVH文件路径
- `--task`: 任务名称（A2_retarget, T1_retarget, dobot_retarget, unitreeG1_retarget, x2_retarget, cdroid_retarget）
- `--device`: 运行设备（cpu 或 cuda）
- `--use-scale`: 使用scale参数优化（默认: 只使用offset优化）
- `--config`: 优化器配置文件路径（可选）
- `--save-result`: 优化结果保存文件名称
- `--save-config`: 配置保存路径

## 优化模式说明

### 只使用offset优化（默认）
- scale固定为1.0，不参与优化
- 只优化各个关节的offset参数
- 适合只想调整骨骼形状而不改变整体大小的情况

### 使用scale和offset一起优化
- scale初始值为1.0，参与优化
- 同时优化scale和offset参数
- 适合需要调整整体骨骼大小和形状的情况