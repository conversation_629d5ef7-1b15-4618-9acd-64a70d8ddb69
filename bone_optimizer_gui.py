import sys  
import os  

# 导入PyQt5 GUI相关模块
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QSlider, QPushButton, QGroupBox,
                            QFileDialog, QLineEdit, QDoubleSpinBox, QTabWidget, QCheckBox)
# QApplication: 应用程序类，管理GUI程序的控制流和主要设置
# QMainWindow: 主窗口类，提供主应用程序窗口
# QWidget: 所有用户界面对象的基类
# QVBoxLayout: 垂直布局管理器
# QHBoxLayout: 水平布局管理器
# QLabel: 文本或图像显示标签
# QSlider: 滑块控件
# QPushButton: 按钮控件
# QGroupBox: 分组框控件
# QFileDialog: 文件对话框
# QLineEdit: 单行文本输入框
# QDoubleSpinBox: 浮点数输入框
# QTabWidget: 标签页控件
# QCheckBox: 复选框控件

# 导入PyQt5核心模块
from PyQt5.QtCore import Qt, QTimer
# Qt: 包含Qt的核心非GUI功能
# QTimer: 定时器类，用于定时触发事件

# 导入PyQt5图表模块
from PyQt5.QtChart import QChart, QChartView, QLineSeries, QValueAxis
# QChart: 图表基类
# QChartView: 图表视图类
# QLineSeries: 线图数据系列
# QValueAxis: 数值坐标轴

# 导入PyQt5绘图模块
from PyQt5.QtGui import QPainter, QColor, QPen
# QPainter: 用于绘制图形和颜色
# QPen: 用于设置线条样式

# 导入数值计算模块
import numpy as np  # 用于数值计算

# 导入自定义模块
from humanoid.utils import humanoid_batch_register  # 导入人形机器人环境注册模块
import torch  # 导入PyTorch深度学习框架
import argparse  # 用于解析命令行参数
import json  # 用于处理JSON数据
import time  # 用于时间相关操作
import queue  # 用于线程间通信的队列
import threading
import open3d as o3d
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.animation as animation

# 导入自定义的BVH模型相关模块
from utils.bvh_bone_core import BVHBoneOptimizer, BVHVisualizer, BVHOptimizationParams
from utils.bvh_parser import BVHParser
from utils.get_bvh_joints import parse_bvh_joints

class BoneOptimizerGUI(QMainWindow):
    """BVH骨骼优化器的GUI类"""
    
    def __init__(self, bvh_file_path, default_config_path=None, save_result="bvh_optimization_result.json", 
                 save_config_path="configs/default_bvh_config.json",
                 device="cpu", task_name="cdroid_retarget", args=None):
        """初始化函数
        
        Args:
            bvh_file_path (str): BVH文件路径
            default_config_path (str): 默认配置文件的路径，默认为 None
            save_result (str): 优化结果保存的文件名称，默认为 "bvh_optimization_result.json"
            save_config_path (str): 配置保存的完整文件路径，默认为 "configs/default_bvh_config.json"
            device (str): 运行设备，可选 "cpu" 或 "cuda"，默认为 "cpu"
            task_name (str): 任务名称，可选值包括：
                - A2_retarget
                - T1_retarget
                - dobot_retarget
                - unitreeG1_retarget
                - x2_retarget
                - cdroid_retarget
                默认为 "cdroid_retarget"
            args: 命令行参数对象
        """
        super().__init__()
        self.setWindowTitle("BVH骨骼优化器")  # 设置窗口标题
        self.setGeometry(100, 100, 1600, 1000)  # 设置窗口位置和大小
        
        self.default_config_path = default_config_path
        self.save_result = save_result
        self.save_config_path = save_config_path
        self.device = device
        self.task_name = task_name
        self.args = args
        self.core_optimizer = None
        self.optimizer = None
        self.params = BVHOptimizationParams()  # 初始化优化参数:各种损失权重、学习率、迭代次数、scale
        self.is_optimizing = False
        self.current_iteration = 0
        self.bvh_parser = None
        self.robot_positions = None
        self.optimization_timer = QTimer(self)
        self.optimization_timer.timeout.connect(self.optimization_step)
        self.max_data_points = 100  # 最大数据点数，保证图表性能
        self.bvh_file_path = bvh_file_path
        
        # 初始化损失数据存储
        self.loss_data = []
        
        # 创建图表
        self.init_charts()  # 图表
        
        # 如果有配置文件就加载，否则使用默认参数
        if self.default_config_path:
            self.load_default_config()
        else:
            print("使用默认优化器参数")
        
        # 如果命令行参数中有use_scale设置，覆盖配置文件中的值
        if self.args:
            if hasattr(self.args, 'use_scale'):
                if self.args.use_scale:
                    # 使用scale优化：scale初始值为1.0，optimize_scale为True
                    self.params.scale = 1.0
                    self.params.optimize_scale = True
                    print(f"命令行设置: 使用scale优化 (scale=1.0, optimize_scale=True)")
                else:
                    # 不使用scale优化：scale初始值为1.0，optimize_scale为False
                    self.params.scale = 1.0
                    self.params.optimize_scale = False
                    print(f"命令行设置: 只使用offset优化 (scale=1.0, optimize_scale=False)")
            
            # 如果命令行参数中有adaptive_root_height设置，覆盖配置文件中的值
            if hasattr(self.args, 'adaptive_root_height'):
                self.params.adaptive_root_height = self.args.adaptive_root_height
                if self.args.adaptive_root_height:
                    print(f"命令行设置: 启用自适应根节点高度调整")
                else:
                    print(f"命令行设置: 禁用自适应根节点高度调整")
            
            # 如果命令行参数中有optimize_root_translation设置，覆盖配置文件中的值
            if hasattr(self.args, 'optimize_root_translation'):
                self.params.optimize_root_translation = self.args.optimize_root_translation
                if self.args.optimize_root_translation:
                    print(f"命令行设置: 启用根节点平移优化")
                else:
                    print(f"命令行设置: 禁用根节点平移优化")
        
        # 先初始化优化器和可视化器（和SMPL流程一致）
        self.init_optimizer()
        
        # 再创建用户界面
        self.init_ui()  # 按钮和滑块
        
        self.status_label.setText(f"使用设备: {self.device}")
        print(f"使用设备: {self.device}")
        # 自动加载BVH文件
        if self.bvh_file_path:
            self.load_bvh_file(self.bvh_file_path)  # 核心的地方
        else:
            self.status_label.setText("请通过参数指定BVH文件路径")

    def init_optimizer(self):
        """初始化优化器和可视化器"""
        if self.bvh_parser is None:
            print("请先选择BVH文件")
            return False
            
        # 使用传入的args参数，如果没有则创建默认参数对象
        if self.args is not None:
            args = self.args
            # 确保必要的属性存在
            if not hasattr(args, 'task'):
                args.task = self.task_name
            if not hasattr(args, 'no_show'):
                args.no_show = False
            if not hasattr(args, 'iterations'):
                args.iterations = 1500
                
            # 如果args中有save_result和save_config参数，更新实例变量
            if hasattr(args, 'save_result'):
                self.save_result = args.save_result
                print(f"使用命令行指定的保存结果路径: {args.save_result}")
            if hasattr(args, 'save_config'):
                self.save_config_path = args.save_config
                print(f"使用命令行指定的配置保存路径: {args.save_config}")
        else:
            # 创建默认参数对象
            args = argparse.Namespace()
            args.task = self.task_name  # 使用类属性中的任务名称
            args.no_show = False  # 设置是否显示可视化
            args.iterations = 1500  # 设置最大迭代次数
        
        try:
            # 注册任务到人形机器人环境
            humanoid_batch_register.register_task(args.task)
            # 获取任务配置
            motion_lib_cfg = humanoid_batch_register.get_config(args.task)
            
            # 检查bvh_joint_correspondence是否完整
            if hasattr(motion_lib_cfg, 'bvh_joint_correspondence'):
                # 检查哪些映射是空的
                empty_mappings = [joint for joint, bvh_joint in motion_lib_cfg.bvh_joint_correspondence.items() if not bvh_joint]
                
                if empty_mappings:
                    print(f"发现空的关节映射: {empty_mappings}")
                    print("正在自动填充BVH关节对应关系...")
                    
                    # 获取BVH关节名称
                    bvh_joints = self.bvh_parser.get_joint_names()
                    print(f"BVH关节名称: {bvh_joints}")
                    
                    # 使用智能匹配逻辑
                    try:
                        # 使用utils/get_bvh_joints.py中的智能匹配逻辑
                        smart_mapping = parse_bvh_joints(self.bvh_file_path)
                        print("智能匹配结果:")
                        for robot_joint, bvh_joint in smart_mapping.items():
                            print(f"  {robot_joint} -> {bvh_joint}")
                        
                        # 更新配置中的对应关系
                        for robot_joint, bvh_joint in smart_mapping.items():
                            if robot_joint in motion_lib_cfg.bvh_joint_correspondence:
                                motion_lib_cfg.bvh_joint_correspondence[robot_joint] = bvh_joint
                        
                    except Exception as e:
                        print(f"智能匹配失败: {e}")
                        print("使用简单匹配作为备选方案...")
                        
                        # 备选方案：使用简单的字符串匹配
                        for robot_joint in empty_mappings:
                            if robot_joint in motion_lib_cfg.bvh_joint_correspondence:
                                # 根据关节名称匹配BVH关节
                                for bvh_joint in bvh_joints:
                                    if robot_joint.lower().replace('_', '') in bvh_joint.lower().replace('_', '') or bvh_joint.lower().replace('_', '') in robot_joint.lower().replace('_', ''):
                                        motion_lib_cfg.bvh_joint_correspondence[robot_joint] = bvh_joint
                                        print(f"  简单匹配: {robot_joint} -> {bvh_joint}")
                                        break
                    
                    # 打印最终的对应关系
                    print("最终的关节对应关系:")
                    for robot_joint, bvh_joint in motion_lib_cfg.bvh_joint_correspondence.items():
                        print(f"  {robot_joint} -> {bvh_joint}")
            else:
                print("   错误: 配置中没有bvh_joint_correspondence")
                return
            
            # 将bvh_joint_correspondence复制到bvh_joint_correspondence（保持兼容性）
            motion_lib_cfg.bvh_joint_correspondence = motion_lib_cfg.bvh_joint_correspondence.copy()
            
            # 创建BVH骨骼优化器，将offset, root_trans, root_rot, root_height_adjustment, scale都初始化为0,开启梯度
            self.core_optimizer = BVHBoneOptimizer(motion_lib_cfg, self.bvh_parser, self.device)
            
            # 设置关节对应关系
            self.core_optimizer.setup_joint_correspondence()
            
        except Exception as e:
            error_msg = f"任务 '{args.task}' 注册失败: {str(e)}"
            print(error_msg)
            self.status_label.setText(error_msg)
            raise ValueError(error_msg)
        
        # 创建可视化器并初始化Open3D查看器
        self.visualizer = BVHVisualizer(self.core_optimizer)
        self.visualizer.init_mujoco_viewer()  # 新增：先初始化MuJoCo窗口
        self.visualizer.start_visualization_thread()  # 新增：再启动Open3D线程
        
        # 获取机器人key-points world frame关节位置 - 计算一次，重复使用
        self.robot_positions = self.core_optimizer.get_robot_positions(selected_only=True)
        
        # 更新初始可视化
        self.update_initial_visualization()  # 设置T-pose，绘制bvh骨架树和连线
        
        return True

    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # 文件信息组
        file_group = QGroupBox("BVH文件信息")
        file_layout = QHBoxLayout()
        self.bvh_file_label = QLabel(f"BVH文件: {os.path.basename(self.bvh_file_path) if self.bvh_file_path else '未指定'}")
        file_layout.addWidget(self.bvh_file_label)
        file_group.setLayout(file_layout)
        layout.addWidget(file_group)

        # 创建滑块组
        slider_group = QGroupBox("优化参数")
        slider_layout = QVBoxLayout()
        
        # 创建关节损失权重滑块
        self.joint_loss_slider, self.joint_loss_slider_widget, self.joint_loss_spinbox = self.create_slider_with_label(
            "关节损失权重", 0.0, 10.0, 1.0, self.update_joint_loss_weight)
        slider_layout.addLayout(self.joint_loss_slider)
        
        # 创建对称性损失权重滑块
        self.symmetry_loss_slider, self.symmetry_loss_slider_widget, self.symmetry_loss_spinbox = self.create_slider_with_label(
            "对称性损失权重", 0.0, 2.0, 0.1, self.update_symmetry_loss_weight)
        slider_layout.addLayout(self.symmetry_loss_slider)
        
        # 创建末端位置损失权重滑块
        self.endpoint_loss_slider, self.endpoint_loss_slider_widget, self.endpoint_loss_spinbox = self.create_slider_with_label(
            "末端位置损失权重", 0.0, 2.0, 0.5, self.update_endpoint_loss_weight)
        slider_layout.addLayout(self.endpoint_loss_slider)
        
        # 创建学习率滑块
        self.learning_rate_slider, self.learning_rate_slider_widget, self.learning_rate_spinbox = self.create_slider_with_label(
            "学习率", 0.001, 0.1, 0.005, self.update_learning_rate)
        slider_layout.addLayout(self.learning_rate_slider)
        
        # 创建优化scale复选框
        scale_checkbox_layout = QHBoxLayout()
        scale_label = QLabel("优化Scale参数:")
        self.optimize_scale_checkbox = QCheckBox()
        self.optimize_scale_checkbox.setChecked(self.params.optimize_scale)
        self.optimize_scale_checkbox.stateChanged.connect(self.update_optimize_scale)
        scale_checkbox_layout.addWidget(scale_label)
        scale_checkbox_layout.addWidget(self.optimize_scale_checkbox)
        scale_checkbox_layout.addStretch()
        slider_layout.addLayout(scale_checkbox_layout)
        
        # 创建优化根节点平移复选框
        root_trans_checkbox_layout = QHBoxLayout()
        root_trans_label = QLabel("优化根节点平移:")
        self.optimize_root_translation_checkbox = QCheckBox()
        self.optimize_root_translation_checkbox.setChecked(self.params.optimize_root_translation)
        self.optimize_root_translation_checkbox.stateChanged.connect(self.update_optimize_root_translation)
        root_trans_checkbox_layout.addWidget(root_trans_label)
        root_trans_checkbox_layout.addWidget(self.optimize_root_translation_checkbox)
        root_trans_checkbox_layout.addStretch()
        slider_layout.addLayout(root_trans_checkbox_layout)
        
        # 创建自适应根节点高度复选框
        root_height_checkbox_layout = QHBoxLayout()
        root_height_label = QLabel("自适应根节点高度:")
        self.adaptive_root_height_checkbox = QCheckBox()
        self.adaptive_root_height_checkbox.setChecked(self.params.adaptive_root_height)
        self.adaptive_root_height_checkbox.stateChanged.connect(self.update_adaptive_root_height)
        root_height_checkbox_layout.addWidget(root_height_label)
        root_height_checkbox_layout.addWidget(self.adaptive_root_height_checkbox)
        root_height_checkbox_layout.addStretch()
        slider_layout.addLayout(root_height_checkbox_layout)
        
        # 创建根节点高度权重滑块
        self.root_height_weight_slider, self.root_height_weight_slider_widget, self.root_height_weight_spinbox = self.create_slider_with_label(
            "根节点高度权重", 0.0, 1.0, 0.1, self.update_root_height_weight)
        slider_layout.addLayout(self.root_height_weight_slider)
        
        slider_group.setLayout(slider_layout)
        layout.addWidget(slider_group)

        # 添加图表标签页控件
        layout.addWidget(self.tab_widget)

        # 创建控制按钮布局
        button_layout = QHBoxLayout()
        
        # 开始/停止优化按钮
        self.optimize_button = QPushButton("开始优化")
        self.optimize_button.clicked.connect(self.toggle_optimization)
        button_layout.addWidget(self.optimize_button)
        
        # 重置按钮
        self.reset_button = QPushButton("重置")
        self.reset_button.clicked.connect(self.reset_optimization)
        button_layout.addWidget(self.reset_button)
        
        # 保存配置按钮
        self.save_config_button = QPushButton("保存配置")
        self.save_config_button.clicked.connect(self.save_config)
        button_layout.addWidget(self.save_config_button)
        
        # 加载配置按钮
        self.load_config_button = QPushButton("加载配置")
        self.load_config_button.clicked.connect(self.load_config)
        button_layout.addWidget(self.load_config_button)
        
        # 保存结果按钮
        self.save_result_button = QPushButton("保存优化结果")
        self.save_result_button.clicked.connect(self.save_optimization_result)
        button_layout.addWidget(self.save_result_button)
        
        # 添加按钮布局到主布局
        layout.addLayout(button_layout)

        # 创建状态显示组
        status_group = QGroupBox("状态")
        status_layout = QVBoxLayout()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        
        # 迭代次数标签
        self.iteration_label = QLabel("迭代次数: 0")
        status_layout.addWidget(self.iteration_label)
        
        # 当前损失标签
        self.current_loss_label = QLabel("当前损失: 0.0")
        status_layout.addWidget(self.current_loss_label)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)

    def create_slider_with_label(self, name, min_val, max_val, default_val, callback):
        """创建带标签的滑块控件"""
        # 创建水平布局
        layout = QHBoxLayout()
        
        # 标签
        label = QLabel(name + ":")
        layout.addWidget(label)
        
        # 创建数值输入框
        spinbox = QDoubleSpinBox()
        spinbox.setMinimum(min_val)
        spinbox.setMaximum(max_val)
        spinbox.setValue(default_val)
        spinbox.setDecimals(6)
        spinbox.setKeyboardTracking(False)
        spinbox.editingFinished.connect(
            lambda: self.on_spinbox_change(spinbox.value(), slider, name, callback)
        )
        layout.addWidget(spinbox)
        
        # 滑块
        slider = QSlider(Qt.Horizontal)
        slider.setMinimum(int(min_val * 1000))
        slider.setMaximum(int(max_val * 1000))
        slider.setValue(int(default_val * 1000))
        slider.valueChanged.connect(
            lambda value: self.on_slider_change(value, spinbox, name, callback)
        )
        layout.addWidget(slider)
        
        # 设置布局中各部分的拉伸比例
        layout.setStretch(0, 1)  # 标签
        layout.setStretch(1, 1)  # 输入框
        layout.setStretch(2, 4)  # 滑块
        
        return layout, slider, spinbox

    def get_param_name(self, display_name):
        """获取参数名称"""
        param_mapping = {
            "关节损失权重": "joint_loss_weight",
            "对称性损失权重": "symmetry_loss_weight",
            "末端位置损失权重": "endpoint_loss_weight",
            "学习率": "learning_rate",
        }
        return param_mapping.get(display_name, display_name)

    def on_slider_change(self, value, spinbox, name, callback):
        """滑块值改变时的回调"""
        float_value = value / 1000.0
        spinbox.setValue(float_value)
        self.update_param(self.get_param_name(name), float_value)
        if callback:
            callback(float_value)

    def on_spinbox_change(self, value, slider, name, callback):
        """数值输入框值改变时的回调"""
        slider.setValue(int(value * 1000))
        self.update_param(self.get_param_name(name), value)
        if callback:
            callback(value)

    def update_param(self, param_name, value):
        """更新参数值"""
        if hasattr(self.params, param_name):
            setattr(self.params, param_name, value)
            if self.optimizer:
                self.update_optimizer()

    def update_optimizer(self):
        """更新优化器"""
        if self.core_optimizer:
            # 更新优化器参数
            self.core_optimizer.params = self.params
            
            # 更新根节点偏移量的优化状态
            self.core_optimizer.update_root_offset_optimization_state()
            
            # 基础优化参数：始终包含偏移量
            optimizer_params = [
                {'params': list(self.core_optimizer.optimizable_offsets.values())},
            ]
            
            # 根据optimize_root_translation参数决定是否优化根节点平移
            if self.params.optimize_root_translation:
                # 确保根节点平移是可优化的
                if not self.core_optimizer.bvh_root_trans.requires_grad:
                    self.core_optimizer.bvh_root_trans.requires_grad_(True)
                optimizer_params.append({'params': [self.core_optimizer.bvh_root_trans]})
            else:
                # 确保根节点平移不可优化
                if self.core_optimizer.bvh_root_trans.requires_grad:
                    self.core_optimizer.bvh_root_trans.requires_grad_(False)
            
            # 根据optimize_scale参数决定是否优化scale
            if self.params.optimize_scale:
                # 确保scale是可优化的
                if not self.core_optimizer.scale.requires_grad:
                    self.core_optimizer.scale.requires_grad_(True)
                optimizer_params.append({'params': [self.core_optimizer.scale]})
            else:
                # 确保scale不可优化
                if self.core_optimizer.scale.requires_grad:
                    self.core_optimizer.scale.requires_grad_(False)
            
            # 根据adaptive_root_height参数决定是否优化根节点高度
            if self.params.adaptive_root_height:
                # 确保根节点高度调整是可优化的
                if not self.core_optimizer.root_height_adjustment.requires_grad:
                    self.core_optimizer.root_height_adjustment.requires_grad_(True)
                optimizer_params.append({'params': [self.core_optimizer.root_height_adjustment]})
            else:
                # 确保根节点高度调整不可优化
                if self.core_optimizer.root_height_adjustment.requires_grad:
                    self.core_optimizer.root_height_adjustment.requires_grad_(False)
            
            # 重新创建优化器
            self.optimizer = torch.optim.Adam(optimizer_params, lr=self.params.learning_rate)

    def init_charts(self):
        """初始化图表"""
        # 创建标签页控件
        self.tab_widget = QTabWidget()
        
        # 创建损失图表页
        loss_tab = QWidget()
        loss_layout = QVBoxLayout(loss_tab)
        
        # 创建损失曲线图表
        self.loss_chart = QChart()
        self.loss_chart.setTitle("优化损失曲线")
        
        # 创建数据系列
        self.total_loss_series = QLineSeries()
        self.total_loss_series.setName("加权总损失")
        self.joint_loss_series = QLineSeries()
        self.joint_loss_series.setName("关节损失")
        self.symmetry_loss_series = QLineSeries()
        self.symmetry_loss_series.setName("对称性损失")
        self.endpoint_loss_series = QLineSeries()
        self.endpoint_loss_series.setName("末端位置损失")
        
        # 添加数据系列到图表
        self.loss_chart.addSeries(self.total_loss_series)
        self.loss_chart.addSeries(self.joint_loss_series)
        self.loss_chart.addSeries(self.symmetry_loss_series)
        self.loss_chart.addSeries(self.endpoint_loss_series)
        
        # 创建坐标轴
        self.axis_x = QValueAxis()
        self.axis_x.setTitleText("迭代次数")
        self.axis_y = QValueAxis()
        self.axis_y.setTitleText("损失值")
        
        # 添加坐标轴到图表
        self.loss_chart.addAxis(self.axis_x, Qt.AlignBottom)
        self.loss_chart.addAxis(self.axis_y, Qt.AlignLeft)
        
        # 将系列附加到坐标轴
        self.total_loss_series.attachAxis(self.axis_x)
        self.total_loss_series.attachAxis(self.axis_y)
        self.joint_loss_series.attachAxis(self.axis_x)
        self.joint_loss_series.attachAxis(self.axis_y)
        self.symmetry_loss_series.attachAxis(self.axis_x)
        self.symmetry_loss_series.attachAxis(self.axis_y)
        self.endpoint_loss_series.attachAxis(self.axis_x)
        self.endpoint_loss_series.attachAxis(self.axis_y)
        
        # 创建图表视图
        self.loss_chart_view = QChartView(self.loss_chart)
        self.loss_chart_view.setRenderHint(QPainter.Antialiasing)
        
        # 设置初始范围
        self.axis_x.setRange(0, 1000)
        self.axis_y.setRange(0, 1)
        
        # 设置网格
        self.axis_x.setGridLineVisible(True)
        self.axis_y.setGridLineVisible(True)
        
        # 设置刻度数量
        self.axis_x.setTickCount(10)
        self.axis_y.setTickCount(10)
        
        # 添加图表视图到布局
        loss_layout.addWidget(self.loss_chart_view)
        
        # 创建关节距离差值图表页
        joint_distance_tab = QWidget()
        joint_distance_layout = QVBoxLayout(joint_distance_tab)
        
        # 创建关节距离差值图表
        self.joint_distance_chart = QChart()
        self.joint_distance_chart.setTitle("关节距离差值")
        
        # 创建关节距离差值数据系列
        self.joint_distance_series = {}
        
        # 创建关节距离差值图表视图
        self.joint_distance_chart_view = QChartView(self.joint_distance_chart)
        self.joint_distance_chart_view.setRenderHint(QPainter.Antialiasing)
        
        # 创建坐标轴
        self.joint_distance_axis_x = QValueAxis()
        self.joint_distance_axis_x.setTitleText("迭代次数")
        self.joint_distance_axis_y = QValueAxis()
        self.joint_distance_axis_y.setTitleText("距离差值 (米)")
        
        # 添加坐标轴到图表
        self.joint_distance_chart.addAxis(self.joint_distance_axis_x, Qt.AlignBottom)
        self.joint_distance_chart.addAxis(self.joint_distance_axis_y, Qt.AlignLeft)
        
        # 设置初始范围
        self.joint_distance_axis_x.setRange(0, 1000)
        self.joint_distance_axis_y.setRange(0, 0.1)
        
        # 设置网格
        self.joint_distance_axis_x.setGridLineVisible(True)
        self.joint_distance_axis_y.setGridLineVisible(True)
        
        # 设置刻度数量
        self.joint_distance_axis_x.setTickCount(10)
        self.joint_distance_axis_y.setTickCount(10)
        
        # 添加图表视图到布局
        joint_distance_layout.addWidget(self.joint_distance_chart_view)
        
        # 添加标签页到标签页控件
        self.tab_widget.addTab(loss_tab, "损失曲线")
        self.tab_widget.addTab(joint_distance_tab, "关节距离差值")

    def update_chart(self, iteration, total_loss, joint_loss, symmetry_loss, endpoint_loss):
        """更新图表"""
        # 添加数据点
        self.total_loss_series.append(iteration, total_loss)
        self.joint_loss_series.append(iteration, joint_loss)
        self.symmetry_loss_series.append(iteration, symmetry_loss)
        self.endpoint_loss_series.append(iteration, endpoint_loss)
        
        # 动态调整X轴范围
        x_min = max(0, iteration - 500)  # 显示最近500个点
        x_max = max(1000, iteration + 100)  # 至少显示1000个点，并留有余量
        
        # 计算当前视野内的最大最小值
        y_min = float('inf')
        y_max = float('-inf')
        
        # 遍历所有系列的可见数据点
        for series in [self.total_loss_series, self.joint_loss_series, 
                      self.symmetry_loss_series, self.endpoint_loss_series]:
            points = series.pointsVector()
            for point in points:
                if x_min <= point.x() <= x_max:  # 只考虑视野内的点
                    y_min = min(y_min, point.y())
                    y_max = max(y_max, point.y())
        
        # 如果没有数据点，使用默认范围
        if y_min == float('inf'):
            y_min = 0
            y_max = 1
        
        # 添加边距
        y_margin = (y_max - y_min) * 0.1  # 10%的边距
        y_min = max(0, y_min - y_margin)  # 下限不小于0
        y_max = y_max + y_margin
        
        # 设置新的范围
        self.axis_x.setRange(x_min, x_max)
        self.axis_y.setRange(y_min, y_max)
        
        # 每500个点清理一次旧数据以提高性能
        if iteration % 500 == 0:
            points_to_keep = 500
            if self.total_loss_series.count() > points_to_keep:
                for _ in range(self.total_loss_series.count() - points_to_keep):
                    self.total_loss_series.remove(0)
                    self.joint_loss_series.remove(0)
                    self.symmetry_loss_series.remove(0)
                    self.endpoint_loss_series.remove(0)

    def load_bvh_file(self, file_path):
        try:
            self.bvh_parser = BVHParser(file_path, if_scale=True)
            self.bvh_parser.parse()  # 解析BVH文件 self.bvh_parser.joints self.bvh_parser.motion_data
            self.bvh_file_label.setText(f"BVH文件: {os.path.basename(file_path)}")
            if self.init_optimizer():
                self.status_label.setText("BVH文件加载成功，优化器已初始化")
                self.update_optimizer()
            else:
                self.status_label.setText("优化器初始化失败")
        except Exception as e:
            self.status_label.setText(f"BVH文件加载失败: {str(e)}")
            print(f"BVH文件加载错误: {e}")

    def toggle_optimization(self):
        """切换优化状态"""
        if self.is_optimizing:
            self.stop_optimization()
        else:
            self.start_optimization()

    def start_optimization(self):
        """开始优化"""
        if self.core_optimizer is None:
            self.status_label.setText("请先选择BVH文件并初始化优化器")
            return
            
        self.is_optimizing = True
        self.optimize_button.setText("停止优化")
        self.optimization_timer.start(50)  # 50ms间隔
        self.status_label.setText("优化进行中...")

    def stop_optimization(self):
        """停止优化"""
        self.is_optimizing = False
        self.optimize_button.setText("开始优化")
        self.optimization_timer.stop()
        self.status_label.setText("优化已停止")

    def reset_optimization(self):
        """重置优化"""
        if self.core_optimizer:
            # 重置优化变量
            for joint_name, offset_tensor in self.core_optimizer.optimizable_offsets.items():
                original_offset = self.core_optimizer.original_offsets[joint_name]
                offset_tensor.data.copy_(original_offset)
            
            # 重置根节点变换
            self.core_optimizer.bvh_root_trans.data.zero_()
            
            # 重置根节点高度调整
            self.core_optimizer.root_height_adjustment.data.zero_()
            
            # 重置缩放因子
            self.core_optimizer.scale.data.fill_(self.params.scale)
            
            # 重置T-pose设置
            if hasattr(self.core_optimizer, 'dof_pos'):
                # 检查配置文件中是否有T-pose设置
                if hasattr(self.core_optimizer.motion_lib_cfg, 'dof_pos') and self.core_optimizer.motion_lib_cfg.dof_pos is not None:
                    # 使用配置文件中的T-pose设置
                    self.core_optimizer.dof_pos = self.core_optimizer.motion_lib_cfg.dof_pos.to(self.core_optimizer.device)
                    # print("重置时应用配置文件中的T-pose设置")
                else:
                    # 使用默认T-pose（所有关节角度为0）
                    self.core_optimizer.dof_pos.zero_()
                    print("应用默认T-pose设置")
                
                # 重新计算姿态角度
                self.core_optimizer.calculate_pose_angles()
            
            # 重置迭代计数器
            self.current_iteration = 0
            self.iteration_label.setText("迭代次数: 0")
            
            # 清空图表
            self.total_loss_series.clear()
            self.joint_loss_series.clear()
            self.symmetry_loss_series.clear()
            self.endpoint_loss_series.clear()
            
            # 清空关节距离差值图表
            for series in self.joint_distance_series.values():
                series.clear()
            
            # 重置坐标轴范围
            self.axis_x.setRange(0, 1000)
            self.axis_y.setRange(0, 1)
            
            # 更新可视化
            self.update_initial_visualization()
            
            self.status_label.setText("优化已重置")

    def optimization_step(self):
        """优化步骤"""
        if not self.is_optimizing or self.core_optimizer is None:
            return
        
        try:
            # 获取选择后的BVH关节位置（用于与机器人关节比较）
            selected_bvh_positions = self.core_optimizer.get_selected_bvh_joints(self.core_optimizer.optimizable_offsets)
            
            # 计算所有损失 - 包括对称性损失
            loss_dict = self.core_optimizer.compute_losses(
                self.robot_positions, selected_bvh_positions, 
                self.core_optimizer.optimizable_offsets, self.params
            )
            
            # 计算总损失
            total_loss = self.core_optimizer.compute_total_loss(loss_dict)
            
            # 反向传播
            self.optimizer.zero_grad()
            total_loss.backward()
            self.optimizer.step()
            
            # 更新迭代计数器
            self.current_iteration += 1
            self.iteration_label.setText(f"迭代次数: {self.current_iteration}")
            self.current_loss_label.setText(f"当前损失: {total_loss.item():.6f}")
            
            # 更新损失图表
            self.update_chart(
                self.current_iteration,
                total_loss.item(),
                loss_dict['joint_loss'].item(),
                loss_dict['symmetry_loss'].item(),
                loss_dict['endpoint_loss'].item()
            )
            
            # 更新关节距离差值图表
            self.update_joint_distance_chart(self.current_iteration, self.robot_positions, selected_bvh_positions)
            
            # 更新可视化（使用完整的BVH位置）
            if self.visualizer:
                # 获取所有机器人关节位置和选中的机器人关节位置
                all_robot_positions = self.core_optimizer.get_robot_positions(selected_only=False)
                selected_robot_positions = self.core_optimizer.get_robot_positions(selected_only=True)
                
                # 获取完整的BVH关节位置用于可视化
                full_bvh_positions = self.core_optimizer.get_bvh_joints(self.core_optimizer.optimizable_offsets)
                
                # 添加调试信息：显示骨骼长度变化
                # if self.current_iteration % 10 == 0:  # 每10次迭代打印一次
                #     print(f"\n=== 迭代 {self.current_iteration} 骨骼偏移量变化 ===")
                #     for joint_name, offset_tensor in self.core_optimizer.optimizable_offsets.items():
                #         original_offset = self.core_optimizer.original_offsets[joint_name]
                #         current_offset = offset_tensor.detach()
                #         original_length = torch.norm(original_offset).item()
                #         current_length = torch.norm(current_offset).item()
                #         length_change = current_length - original_length
                #         offset_change = (current_offset - original_offset).cpu().numpy()
                #         print(f"{joint_name}:")
                #         print(f"  原始: {original_offset.cpu().numpy()} (长度: {original_length:.4f})")
                #         print(f"  当前: {current_offset.cpu().numpy()} (长度: {current_length:.4f})")
                #         print(f"  变化: {offset_change} (长度变化: {length_change:+.4f})")
                    
                #     # 显示scale变化
                #     if self.params.optimize_scale:
                #         current_scale = self.core_optimizer.scale.item()
                #         original_scale = self.params.scale
                #         scale_change = current_scale - original_scale
                #         print(f"Scale变化:")
                #         print(f"  原始: {original_scale:.4f}")
                #         print(f"  当前: {current_scale:.4f}")
                #         print(f"  变化: {scale_change:+.4f}")
                #     else:
                #         print(f"Scale固定为: {self.core_optimizer.scale.item():.4f} (未优化)")
                    
                #     # 显示根节点高度调整变化
                #     if self.params.adaptive_root_height:
                #         current_height = self.core_optimizer.root_height_adjustment.item()
                #         print(f"根节点高度调整:")
                #         print(f"  当前调整: {current_height:+.4f}")
                #     else:
                #         print(f"根节点高度调整: 禁用")
                    # print("=" * 50)
                
                # 确保数据类型正确并添加batch维度（与SMPL版本一致）
                if isinstance(all_robot_positions, torch.Tensor):
                    all_robot_positions = all_robot_positions.detach().cpu().numpy()
                if isinstance(selected_robot_positions, torch.Tensor):
                    selected_robot_positions = selected_robot_positions.detach().cpu().numpy()
                if isinstance(full_bvh_positions, torch.Tensor):
                    full_bvh_positions = full_bvh_positions.detach().cpu().numpy()
                
                # 检查数据形状并确保与SMPL版本一致
                # get_robot_positions已经返回了包含batch维度的数据
                # all_robot_positions: [1, num_joints, 3] (已有batch维度)
                # selected_robot_positions: [1, num_selected_joints, 3] (已有batch维度)
                # full_bvh_positions: [num_bvh_joints, 3] -> [1, num_bvh_joints, 3]
                
                # 为selected_robot_positions添加额外的batch维度以匹配SMPL版本
                if selected_robot_positions.ndim == 3:
                    selected_robot_positions = selected_robot_positions[None, :, :, :]  # [1, 1, num_selected_joints, 3]
                
                # 为full_bvh_positions添加batch维度
                if full_bvh_positions.ndim == 2:
                    full_bvh_positions = full_bvh_positions[None, :, :]  # [1, num_bvh_joints, 3]
                
                self.visualizer.update_visualization_data(
                    all_robot_positions, 
                    selected_robot_positions, 
                    full_bvh_positions, 
                    self.current_iteration
                )
            
            # 检查是否达到最大迭代次数
            if self.current_iteration >= self.params.iterations:
                self.stop_optimization()
                self.save_optimization_result()
                
        except Exception as e:
            print(f"优化步骤错误: {e}")
            self.stop_optimization()
            self.status_label.setText(f"优化错误: {str(e)}")

    def load_default_config(self):
        """加载默认配置"""
        try:
            if self.default_config_path and os.path.exists(self.default_config_path):
                with open(self.default_config_path, 'r') as f:
                    config_data = json.load(f)
                
                # 更新优化参数
                if 'optimization_params' in config_data:
                    params = config_data['optimization_params']
                    for key, value in params.items():
                        if hasattr(self.params, key):
                            setattr(self.params, key, value)
                
                print(f"配置已从 {self.default_config_path} 加载")
            else:
                print("使用默认优化参数")
        except Exception as e:
            print(f"加载配置文件失败: {e}，使用默认参数")
        
        # 如果命令行参数中有use_scale设置，覆盖配置文件中的值
        if self.args:
            if hasattr(self.args, 'use_scale'):
                if self.args.use_scale:
                    # 使用scale优化：scale初始值为1.0，optimize_scale为True
                    self.params.scale = 1.0
                    self.params.optimize_scale = True
                    print(f"命令行设置: 使用scale优化 (scale=1.0, optimize_scale=True)")
                else:
                    # 不使用scale优化：scale初始值为1.0，optimize_scale为False
                    self.params.scale = 1.0
                    self.params.optimize_scale = False
                    print(f"命令行设置: 只使用offset优化 (scale=1.0, optimize_scale=False)")
            
            # 如果命令行参数中有adaptive_root_height设置，覆盖配置文件中的值
            if hasattr(self.args, 'adaptive_root_height'):
                self.params.adaptive_root_height = self.args.adaptive_root_height
                if self.args.adaptive_root_height:
                    print(f"命令行设置: 启用自适应根节点高度调整")
                else:
                    print(f"命令行设置: 禁用自适应根节点高度调整")
            
            # 如果命令行参数中有optimize_root_translation设置，覆盖配置文件中的值
            if hasattr(self.args, 'optimize_root_translation'):
                self.params.optimize_root_translation = self.args.optimize_root_translation
                if self.args.optimize_root_translation:
                    print(f"命令行设置: 启用根节点平移优化")
                else:
                    print(f"命令行设置: 禁用根节点平移优化")

    def save_config(self):
        """保存配置"""
        try:
            config_data = {
                'optimization_params': {
                    'joint_loss_weight': self.params.joint_loss_weight,
                    'symmetry_loss_weight': self.params.symmetry_loss_weight,
                    'endpoint_loss_weight': self.params.endpoint_loss_weight,
                    'learning_rate': self.params.learning_rate,
                    'iterations': self.params.iterations,
                    'scale': self.params.scale,
                    'optimize_scale': self.params.optimize_scale,
                    'optimize_root_translation': self.params.optimize_root_translation,
                    'adaptive_root_height': self.params.adaptive_root_height,
                    'root_height_weight': self.params.root_height_weight,
                }
            }
            
            with open(self.save_config_path, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            self.status_label.setText(f"配置已保存到 {self.save_config_path}")
        except Exception as e:
            self.status_label.setText(f"保存配置失败: {str(e)}")

    def load_config(self, file_path=None):
        """加载配置"""
        if file_path is None:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择配置文件", "", "JSON Files (*.json);;All Files (*)"
            )
        
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    config_data = json.load(f)
                
                # 更新优化参数
                if 'optimization_params' in config_data:
                    params = config_data['optimization_params']
                    for key, value in params.items():
                        if hasattr(self.params, key):
                            setattr(self.params, key, value)
                
                # 更新滑块和输入框
                self.update_sliders_from_params()
                
                # 更新优化器
                if self.core_optimizer:
                    self.update_optimizer()
                
                self.status_label.setText(f"配置已从 {file_path} 加载")
            except Exception as e:
                self.status_label.setText(f"加载配置失败: {str(e)}")

    def update_sliders_from_params(self):
        """从参数更新滑块"""
        # 更新滑块和输入框的值
        self.joint_loss_spinbox.setValue(self.params.joint_loss_weight)
        self.symmetry_loss_spinbox.setValue(self.params.symmetry_loss_weight)
        self.endpoint_loss_spinbox.setValue(self.params.endpoint_loss_weight)
        self.learning_rate_spinbox.setValue(self.params.learning_rate)
        self.root_height_weight_spinbox.setValue(self.params.root_height_weight)
        
        # 更新复选框状态
        self.optimize_scale_checkbox.setChecked(self.params.optimize_scale)
        self.optimize_root_translation_checkbox.setChecked(self.params.optimize_root_translation)
        self.adaptive_root_height_checkbox.setChecked(self.params.adaptive_root_height)

    def save_optimization_result(self, save_path=None):
        """保存优化结果"""
        if self.core_optimizer is None:
            return
            
        try:
            # 获取最终的BVH关节位置
            bvh_positions = self.core_optimizer.get_bvh_joints(self.core_optimizer.optimizable_offsets)
            
            save_path = self.save_result
            
            
            # 保存优化结果
            save_path = self.core_optimizer.save_optimization_result(
                self.core_optimizer.optimizable_offsets,
                self.robot_positions,
                bvh_positions,
                self.task_name,
                save_path=save_path
            )
            
            # 保存优化后的BVH文件
            if self.bvh_parser:
                optimized_offsets = {}
                for joint_name, offset_tensor in self.core_optimizer.optimizable_offsets.items():
                    optimized_offsets[joint_name] = offset_tensor.detach().cpu().numpy()
                
                output_bvh_path = os.path.splitext(self.save_result)[0] + "_optimized.bvh"
                scale_val = float(self.core_optimizer.scale.item())          # 缩放
                
                # bvh_root_trans 只有 x 和 z 方向 (2,)，需要扩展为 (3,) 包含 y 方向
                root_trans_xz = self.core_optimizer.bvh_root_trans.detach().cpu().numpy()  # (2,)
                root_trans = np.array([root_trans_xz[0], 0.0, root_trans_xz[1]])  # (3,) [x, y, z]
                
                root_rot  = self.core_optimizer.bvh_root_rot.detach().cpu().numpy()     # (3,) 已转度
                root_h    = float(getattr(self.core_optimizer,
                                        "root_height_adjustment", 0.0))

                self.bvh_parser.save_optimized_bvh(
                    output_bvh_path,
                    optimized_offsets
                )
                print(f"优化后的BVH文件已保存到: {output_bvh_path}")
            
            self.status_label.setText(f"优化结果已保存到 {save_path}")
        except Exception as e:
            self.status_label.setText(f"保存优化结果失败: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止优化
        if self.is_optimizing:
            self.stop_optimization()
        
        # 停止可视化
        if hasattr(self, 'visualizer') and self.visualizer:
            self.visualizer.stop_visualization()
        
        # 保存优化结果
        if self.core_optimizer and self.current_iteration > 0:
            self.save_optimization_result()
        
        event.accept()

    def update_initial_visualization(self):
        """在 GUI 启动时更新可视化以显示初始状态"""
        print("正在更新初始可视化...")
        try:
            with torch.no_grad():  # 使用 no_grad 避免梯度计算
                # 确保使用正确的T-pose设置
                if hasattr(self.core_optimizer, 'dof_pos'):
                    # 检查配置文件中是否有T-pose设置
                    if hasattr(self.core_optimizer.motion_lib_cfg, 'dof_pos') and self.core_optimizer.motion_lib_cfg.dof_pos is not None:
                        # 使用配置文件中的T-pose设置
                        self.core_optimizer.dof_pos = self.core_optimizer.motion_lib_cfg.dof_pos.to(self.core_optimizer.device)
                        print("应用配置文件中的T-pose设置")
                    else:
                        # 使用默认T-pose（所有关节角度为0）
                        self.core_optimizer.dof_pos.zero_()
                        print("应用默认T-pose设置")
                    
                    # 重新计算姿态角度
                    self.core_optimizer.calculate_pose_angles()
                
                # 获取机器人位置数据
                all_robot_positions = self.core_optimizer.get_robot_positions(selected_only=False)
                if isinstance(all_robot_positions, torch.Tensor):
                    all_robot_positions = all_robot_positions.detach().cpu().numpy()
                
                selected_robot_positions = self.core_optimizer.get_robot_positions(selected_only=True)
                if isinstance(selected_robot_positions, torch.Tensor):
                    selected_robot_positions = selected_robot_positions.detach().cpu().numpy()
                
                # 获取BVH关节位置
                bvh_positions = self.core_optimizer.get_bvh_joints()
                if isinstance(bvh_positions, torch.Tensor):
                    bvh_positions = bvh_positions.detach().cpu().numpy()
                
                # 确保数据类型正确并添加batch维度（与SMPL版本一致）
                if isinstance(all_robot_positions, torch.Tensor):
                    all_robot_positions = all_robot_positions.detach().cpu().numpy()
                if isinstance(selected_robot_positions, torch.Tensor):
                    selected_robot_positions = selected_robot_positions.detach().cpu().numpy()
                if isinstance(bvh_positions, torch.Tensor):
                    bvh_positions = bvh_positions.detach().cpu().numpy()
                
                # 检查数据形状并确保与SMPL版本一致
                # get_robot_positions已经返回了包含batch维度的数据
                # all_robot_positions: [1, num_joints, 3] (已有batch维度)
                # selected_robot_positions: [1, num_selected_joints, 3] (已有batch维度)
                # bvh_positions: [num_bvh_joints, 3] -> [1, num_bvh_joints, 3]
                
                # 为selected_robot_positions添加额外的batch维度以匹配SMPL版本
                if selected_robot_positions.ndim == 3:
                    selected_robot_positions = selected_robot_positions[None, :, :, :]  # [1, 1, num_selected_joints, 3]
                
                # 为bvh_positions添加batch维度
                if bvh_positions.ndim == 2:
                    bvh_positions = bvh_positions[None, :, :]  # [1, num_bvh_joints, 3]
                
                # 为all_robot_positions添加batch维度
                if all_robot_positions.ndim == 2:
                    all_robot_positions = all_robot_positions[None, :, :]  # [1, num_joints, 3]
                
                # 更新可视化数据  SMPL中传入的只有smpl全部joint的position（1,24,3），和选中的robot position（1,1,12,3）
                if self.visualizer:
                    print("更新可视化数据...")
                    self.visualizer.update_visualization_data(
                        all_robot_positions, 
                        selected_robot_positions, 
                        bvh_positions, 
                        iteration=0  # 初始迭代次数为 0
                    )
                else:
                    print("可视化器未准备好进行初始更新。")
                
                # 更新初始关节距离差值图表
                selected_bvh_positions = self.core_optimizer.get_selected_bvh_joints()
                self.update_joint_distance_chart(0, selected_robot_positions, selected_bvh_positions)

        except Exception as e:
            print(f"更新初始可视化时出错: {e}")
            import traceback
            traceback.print_exc()
        print("初始可视化更新尝试完成。")

    # 参数更新回调函数
    def update_joint_loss_weight(self, value):
        self.params.joint_loss_weight = value
        if self.optimizer:
            self.update_optimizer()

    def update_symmetry_loss_weight(self, value):
        self.params.symmetry_loss_weight = value
        if self.optimizer:
            self.update_optimizer()

    def update_endpoint_loss_weight(self, value):
        self.params.endpoint_loss_weight = value
        if self.optimizer:
            self.update_optimizer()

    def update_learning_rate(self, value):
        self.params.learning_rate = value
        if self.optimizer:
            self.update_optimizer()

    def update_optimize_scale(self, state):
        self.params.optimize_scale = state == Qt.Checked
        if self.optimizer:
            self.update_optimizer()

    def update_optimize_root_translation(self, state):
        self.params.optimize_root_translation = state == Qt.Checked
        if self.optimizer:
            self.update_optimizer()

    def update_adaptive_root_height(self, state):
        self.params.adaptive_root_height = state == Qt.Checked
        if self.optimizer:
            self.update_optimizer()

    def update_root_height_weight(self, value):
        self.params.root_height_weight = value
        if self.optimizer:
            self.update_optimizer()

    def update_joint_distance_chart(self, iteration, robot_positions, bvh_positions):
        """更新关节距离差值图表"""
        try:
            # 确保数据是numpy数组
            if isinstance(robot_positions, torch.Tensor):
                robot_positions = robot_positions.detach().cpu().numpy()
            if isinstance(bvh_positions, torch.Tensor):
                bvh_positions = bvh_positions.detach().cpu().numpy()
            
            # 处理batch维度
            if robot_positions.ndim == 3:
                robot_positions = robot_positions[0]  # [num_joints, 3]
            if bvh_positions.ndim == 3:
                bvh_positions = bvh_positions[0]  # [num_joints, 3]
            
            # 确保形状匹配
            if robot_positions.shape != bvh_positions.shape:
                # 处理robot_positions的额外维度
                if robot_positions.ndim == 4:  # (1, 1, num_joints, 3)
                    robot_positions = robot_positions[0, 0]  # 移除前两个batch维度
                elif robot_positions.ndim == 3:  # (1, num_joints, 3)
                    robot_positions = robot_positions[0]  # 移除batch维度
                
                # 确保bvh_positions是2D的 (num_joints, 3)
                if bvh_positions.ndim == 3:  # (1, num_joints, 3)
                    bvh_positions = bvh_positions[0]  # 移除batch维度
                
                # 现在两个数组都应该是 (num_joints, 3) 的形状
                min_joints = min(robot_positions.shape[0], bvh_positions.shape[0])
                robot_positions = robot_positions[:min_joints]
                bvh_positions = bvh_positions[:min_joints]
            
            # 计算每个关节的距离差值
            distances = np.linalg.norm(robot_positions - bvh_positions, axis=1)
            
            # 获取关节名称
            joint_names = self.core_optimizer.get_selected_joint_names()
            
            # 确保关节名称数量与距离数量匹配
            if len(joint_names) != len(distances):
                min_count = min(len(joint_names), len(distances))
                joint_names = joint_names[:min_count]
                distances = distances[:min_count]
            
            # 为每个关节创建或更新数据系列
            for i, joint_name in enumerate(joint_names):
                if joint_name not in self.joint_distance_series:
                    # 创建新的数据系列
                    series = QLineSeries()
                    series.setName(joint_name)
                    pen = series.pen()
                    pen.setWidth(2)  # 设置线条宽度为2
                    series.setPen(pen)
                    
                    # 设置关节颜色 - 对称关节使用统一颜色，一个实线一个虚线
                    if i == 0:  # 左髋
                        series.setColor(QColor(255, 165, 0))  # 橙色
                        series.setPen(QPen(QColor(255, 165, 0), 2, Qt.SolidLine))  # 实线
                    elif i == 5:  # 右髋
                        series.setColor(QColor(255, 165, 0))  # 橙色
                        series.setPen(QPen(QColor(255, 165, 0), 2, Qt.DashLine))  # 虚线
                    elif i == 1:  # 左膝
                        series.setColor(QColor(128, 0, 128))  # 紫色
                        series.setPen(QPen(QColor(128, 0, 128), 2, Qt.SolidLine))  # 实线
                    elif i == 6:  # 右膝
                        series.setColor(QColor(128, 0, 128))  # 紫色
                        series.setPen(QPen(QColor(128, 0, 128), 2, Qt.DashLine))  # 虚线
                    elif i == 2:  # 左踝
                        series.setColor(QColor(128, 0, 255))  # 蓝紫色
                        series.setPen(QPen(QColor(128, 0, 255), 2, Qt.SolidLine))  # 实线
                    elif i == 7:  # 右踝
                        series.setColor(QColor(128, 0, 255))  # 蓝紫色
                        series.setPen(QPen(QColor(128, 0, 255), 2, Qt.DashLine))  # 虚线
                    elif i == 3:  # 左脚趾
                        series.setColor(QColor(0, 0, 255))  # 蓝色
                        series.setPen(QPen(QColor(0, 0, 255), 2, Qt.SolidLine))  # 实线
                    elif i == 8:  # 右脚趾
                        series.setColor(QColor(0, 0, 255))  # 蓝色
                        series.setPen(QPen(QColor(0, 0, 255), 2, Qt.DashLine))  # 虚线
                    elif i == 4:  # 右髋（非对称）
                        series.setColor(QColor(0, 128, 0))  # 绿色
                        series.setPen(QPen(QColor(0, 128, 0), 2, Qt.SolidLine))  # 实线
                    elif i == 9:  # 脊柱（非对称）
                        series.setColor(QColor(0, 200, 200))  # 青色
                        series.setPen(QPen(QColor(0, 200, 200), 2, Qt.SolidLine))  # 实线
                    elif i == 10:  # 脊柱1（非对称）
                        series.setColor(QColor(255, 0, 0))  # 红色
                        series.setPen(QPen(QColor(255, 0, 0), 2, Qt.SolidLine))  # 实线
                    elif i == 11:  # 颈部（非对称）
                        series.setColor(QColor(255, 255, 0))  # 黄色
                        series.setPen(QPen(QColor(255, 255, 0), 2, Qt.SolidLine))  # 实线
                    
                    # 添加系列到图表
                    self.joint_distance_chart.addSeries(series)
                    series.attachAxis(self.joint_distance_axis_x)
                    series.attachAxis(self.joint_distance_axis_y)
                    self.joint_distance_series[joint_name] = series
                
                # 添加数据点 - 确保distances[i]是标量
                distance_value = distances[i]
                if hasattr(distance_value, '__len__') and len(distance_value) > 1:
                    distance_value = float(np.mean(distance_value))  # 取平均值作为标量
                else:
                    distance_value = float(distance_value)
                
                self.joint_distance_series[joint_name].append(float(iteration), distance_value)
            
            # 动态调整X轴范围
            x_min = max(0, iteration - 500)  # 显示最近500个点
            x_max = max(1000, iteration + 100)  # 至少显示1000个点，并留有余量
            
            # 计算当前视野内的最大最小值
            y_min = float('inf')
            y_max = float('-inf')
            
            # 遍历所有系列的可见数据点
            for series in self.joint_distance_series.values():
                points = series.pointsVector()
                for point in points:
                    if x_min <= point.x() <= x_max:  # 只考虑视野内的点
                        y_min = min(y_min, point.y())
                        y_max = max(y_max, point.y())
            
            # 如果没有数据点，使用默认范围
            if y_min == float('inf'):
                y_min = 0
                y_max = 0.1
            
            # 添加边距
            y_margin = (y_max - y_min) * 0.1  # 10%的边距
            y_min = max(0, y_min - y_margin)  # 下限不小于0
            y_max = y_max + y_margin
            
            # 设置新的范围
            self.joint_distance_axis_x.setRange(x_min, x_max)
            self.joint_distance_axis_y.setRange(y_min, y_max)
            
            # 每500个点清理一次旧数据以提高性能
            if iteration % 500 == 0:
                points_to_keep = 500
                for series in self.joint_distance_series.values():
                    if series.count() > points_to_keep:
                        for _ in range(series.count() - points_to_keep):
                            series.remove(0)
                            
        except Exception as e:
            print(f"更新关节距离差值图表时出错: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BVH骨骼优化GUI")
    parser.add_argument('--bvh', type=str, default="data/bvh/walk_forward.bvh", help='BVH文件路径')
    parser.add_argument("--config", type=str, help="优化器配置文件路径（可选）")
    parser.add_argument("--save-result", type=str, default="data/calc_bvh/unitreeG1/walking_forward.json",
                       help="优化结果保存文件名称")
    parser.add_argument("--save-config", type=str, default="configs/default_bvh_config.json",
                       help="配置保存路径")
    parser.add_argument("--device", type=str, default="cuda", choices=["cpu", "cuda"],
                       help="运行设备")
    parser.add_argument("--task", type=str, default="unitreeG1_retarget",
                       choices=["A2_retarget", "T1_retarget", "dobot_retarget", 
                               "unitreeG1_retarget", "x2_retarget", "cdroid_retarget"],
                       help="任务名称")
    parser.add_argument("--use-scale", action="store_true", default=False,
                       help="使用scale参数优化 (默认: 只使用offset优化)")
    parser.add_argument("--adaptive-root-height", action="store_true", default=False,
                       help="启用自适应根节点高度调整 (默认: 不启用)")
    parser.add_argument("--optimize-root-translation", action="store_true", default=False,
                       help="启用根节点平移优化 (默认: 不启用)")
    
    args = parser.parse_args()
    
    # 打印参数信息
    print(f"BVH文件: {args.bvh}")
    print(f"设备: {args.device}")
    print(f"任务: {args.task}")
    print(f"使用Scale优化: {args.use_scale}")
    print(f"自适应根节点高度: {args.adaptive_root_height}")
    print(f"优化根节点平移: {args.optimize_root_translation}")
    print("=" * 50)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = BoneOptimizerGUI(
        bvh_file_path=args.bvh,
        default_config_path=args.config,
        save_result=args.save_result,
        save_config_path=args.save_config,
        device=args.device,
        task_name=args.task,
        args=args
    )
    
    # 显示窗口
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main() 