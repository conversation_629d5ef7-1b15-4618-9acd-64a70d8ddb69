<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>) 
     Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot name="dobot">
  <link name="pelvis">
    <inertial>
      <origin xyz="-0.017758068235645 -2.03042042244043E-05 0.0368383397019517" rpy="0 0 0" />
      <mass value="2.78648232852559" />
      <!-- <inertia ixx="0.00668746588131038" ixy="-1.88665710936529E-07" ixz="3.61219311770208E-09" iyy="0.00810414554476941" iyz="4.42480848281071E-10" izz="0.00675983925879659" />
    </inertial> -->
      <inertia ixx="0.0101063" ixy="-0.0000002" ixz="0" iyy="0.0115230" iyz="0" izz="0.0067598" />
    </inertial>

    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/pelvis.STL" />
      </geometry>
      <material name="">
        <color rgba="0.698039215686274 0.698039215686274 0.698039215686274 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/pelvis.STL" />
      </geometry>
    </collision>
  </link>
  <link name="left_hip_pitch_link">
    <inertial>
      <origin xyz="0.000265881718701262 0.0510862129187268 -0.0159051990790692" rpy="0 0 0" />
      <mass value="0.558809942135873" />
      <inertia ixx="0.000766222208278133" ixy="-4.49439883354161E-06" ixz="2.51232012994156E-06" iyy="0.00178657218246286" iyz="0.000137464536808775" izz="0.00195303611657463" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_hip_pitch_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.866666666666667 0.909803921568627 1 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_hip_pitch_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="left_hip_pitch_joint" type="revolute">
    <origin xyz="-0.017758 0.0794797 -0.0522786" rpy="0 -0.113446401379624 0" />
    <parent link="pelvis" />
    <child link="left_hip_pitch_link" />
    <axis xyz="0 1 0" />
    <limit lower="-1.7" upper="1.8" effort="140.25" velocity="10.48" />
  </joint>
  <link name="left_hip_roll_link">
    <inertial>
      <origin xyz="2.06492849577113E-08 -0.00199015147676726 -0.00816730088358032" rpy="0 0 0" />
      <mass value="2.02244773835182" />
      <inertia ixx="0.00299821514409779" ixy="-3.43095628696618E-10" ixz="4.86891788334647E-10" iyy="0.00318101728044916" iyz="-0.000189383505683228" izz="0.00254014073273939" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_hip_roll_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.498039215686275 0.498039215686275 0.498039215686275 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_hip_roll_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="left_hip_roll_joint" type="revolute">
    <origin xyz="0 0.0675 -0.0249999999999997" rpy="0 0 0" />
    <parent link="left_hip_pitch_link" />
    <child link="left_hip_roll_link" />
    <axis xyz="1 0 0" />
    <limit lower="-0.36" upper="3.05" effort="140.25" velocity="10.48" />
  </joint>
  <link name="left_hip_yaw_link">
    <inertial>
      <origin xyz="-0.00127987563943656 0.0218516779038797 -0.124711331604185" rpy="0 0 0" />
      <mass value="5.72616381619725" />
      <inertia ixx="0.0365839126378662" ixy="-0.000186528762831785" ixz="-0.00118788831439506" iyy="0.0361480669237865" iyz="0.0010615263809862" izz="0.0138547438781937" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_hip_yaw_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.898039215686275 0.92156862745098 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_hip_yaw_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="left_hip_yaw_joint" type="revolute">
    <origin xyz="0 -0.011 -0.069" rpy="0 0 0" />
    <parent link="left_hip_roll_link" />
    <child link="left_hip_yaw_link" />
    <axis xyz="0 0 1" />
    <limit lower="-2.75" upper="2.75" effort="72.93" velocity="7.97" />
  </joint>
  <link name="left_knee_link">
    <inertial>
      <origin xyz="-0.0206880186605882 -6.44823071010436E-05 -0.10615290458906" rpy="0 0 0" />
      <mass value="3.05150217237149" />
      <inertia ixx="0.0288392292605362" ixy="5.22419296697065E-06" ixz="-0.00315840993164102" iyy="0.029444703846237" iyz="2.9303802895114E-05" izz="0.00396227795445308" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_knee_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.898039215686275 0.92156862745098 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_knee_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="left_knee_joint" type="revolute">
    <origin xyz="-0.0468661304999114 0 -0.317338748250108" rpy="0 0 0" />
    <parent link="left_hip_yaw_link" />
    <child link="left_knee_link" />
    <axis xyz="0 1 0" />
    <limit lower="-0.17" upper="2" effort="218.8" velocity="11.1" />
  </joint>
  <link name="left_ankle_pitch_link">
    <inertial>
      <origin xyz="4.11688488287395E-18 -2.77555756156289E-17 -0.0111659143581497" rpy="0 0 0" />
      <mass value="0.109068073178334" />
      <inertia ixx="2.59558742013528E-05" ixy="-5.59332459125912E-21" ixz="-1.47763628614106E-21" iyy="2.29503380113484E-05" iyz="1.22658358361253E-21" izz="1.3279810144691E-05" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_ankle_pitch_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.898039215686275 0.92156862745098 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_ankle_pitch_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="left_ankle_pitch_joint" type="revolute">
    <origin xyz="-0.0379230766122461 0 -0.332846571651657" rpy="0 0.113446401379623 0" />
    <parent link="left_knee_link" />
    <child link="left_ankle_pitch_link" />
    <axis xyz="0 1 0" />
    <limit lower="-0.5" upper="0.4" effort="44.03" velocity="9.3" />
  </joint>
  <link name="left_ankle_roll_link">
    <inertial>
      <origin xyz="0.0374622188590751 -5.08612783431395E-05 -0.0347043622522566" rpy="0 0 0" />
      <mass value="0.533313667711222" />
      <inertia ixx="0.000299144148874536" ixy="-5.98703795000402E-07" ixz="9.46270349678938E-05" iyy="0.00164912007292675" iyz="-1.09106503269095E-07" izz="0.00170073299385329" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_ankle_roll_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.898039215686275 0.92156862745098 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_ankle_roll_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="left_ankle_roll_joint" type="revolute">
    <origin xyz="0 0 -0.0249999999999998" rpy="0 0 0" />
    <parent link="left_ankle_pitch_link" />
    <child link="left_ankle_roll_link" />
    <axis xyz="1 0 0" />
    <limit lower="-0.24" upper="0.24" effort="44.038" velocity="9.3" />
  </joint>
  <link name="pelvis_contour_link">
    <inertial>
      <origin xyz="-1.55871114746237E-18 0.000268314834597214 -0.1445" rpy="0 0 0" />
      <mass value="3.47652179743598" />
      <inertia ixx="0.00412650944699933" ixy="5.90692930911391E-20" ixz="3.10930406792721E-20" iyy="0.00578865415324813" iyz="3.97161786291353E-20" izz="0.00412650944699933" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/pelvis_contour_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.698039215686274 0.698039215686274 0.698039215686274 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/pelvis_contour_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="pelvis_contour_joint" type="fixed">
    <origin xyz="-0.017758 -2.03E-05 0.0922214" rpy="0 0 0" />
    <parent link="pelvis" />
    <child link="pelvis_contour_link" />
    <axis xyz="0 0 0" />
  </joint>
  <link name="right_hip_pitch_link">
    <inertial>
      <origin xyz="0.000265858218325792 -0.0510860714904277 -0.0159052731310949" rpy="0 0 0" />
      <mass value="0.558811216078568" />
      <inertia ixx="0.000766227011089324" ixy="4.55351893924487E-06" ixz="2.49483261587899E-06" iyy="0.00178658282929306" iyz="-0.000137466344854015" izz="0.00195305067201943" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_hip_pitch_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.866666666666667 0.909803921568627 1 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_hip_pitch_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="right_hip_pitch_joint" type="revolute">
    <origin xyz="-0.017758 -0.0795203 -0.0522786" rpy="0 -0.113446401379624 0" />
    <parent link="pelvis" />
    <child link="right_hip_pitch_link" />
    <axis xyz="0 1 0" />
    <limit lower="-1.7" upper="1.8" effort="140.25" velocity="10.48" />
  </joint>
  <link name="right_hip_roll_link">
    <inertial>
      <origin xyz="-2.50586503439815E-08 0.00199014387057647 -0.00816729848469916" rpy="0 0 0" />
      <mass value="2.02244730495008" />
      <inertia ixx="0.00299821643436386" ixy="5.42713556003939E-10" ixz="-9.4333407865731E-11" iyy="0.00318101512610832" iyz="0.000189383503249488" izz="0.00254014212139077" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_hip_roll_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.498039215686275 0.498039215686275 0.498039215686275 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_hip_roll_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="right_hip_roll_joint" type="revolute">
    <origin xyz="0 -0.0675 -0.0249999999999997" rpy="0 0 0" />
    <parent link="right_hip_pitch_link" />
    <child link="right_hip_roll_link" />
    <axis xyz="1 0 0" />
    <limit lower="-3.05" upper="0.36" effort="140.25" velocity="10.48" />
  </joint>
  <link name="right_hip_yaw_link">
    <inertial>
      <origin xyz="-0.00128137278641304 -0.0218522896178993 -0.124718429223776" rpy="0 0 0" />
      <mass value="5.7263126615388" />
      <inertia ixx="0.0365829353018472" ixy="0.000186345497267204" ixz="-0.00118781911211244" iyy="0.0361472723056794" iyz="-0.00106070560814139" izz="0.0138539372350225" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_hip_yaw_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.898039215686275 0.92156862745098 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_hip_yaw_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="right_hip_yaw_joint" type="revolute">
    <origin xyz="0 0.011 -0.069" rpy="0 0 0" />
    <parent link="right_hip_roll_link" />
    <child link="right_hip_yaw_link" />
    <axis xyz="0 0 1" />
    <limit lower="-2.75" upper="2.75" effort="72.93" velocity="7.97" />
  </joint>
  <link name="right_knee_link">
    <inertial>
      <origin xyz="-0.0206873313885294 6.47390304362172E-05 -0.106153292303331" rpy="0 0 0" />
      <mass value="3.05152688418895" />
      <inertia ixx="0.0288390495339488" ixy="-5.24553722522047E-06" ixz="-0.00315840449455152" iyy="0.0294445458715992" iyz="-2.93186359583693E-05" izz="0.00396228892679692" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_knee_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.898039215686275 0.92156862745098 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_knee_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="right_knee_joint" type="revolute">
    <origin xyz="-0.0468661304999114 0 -0.317338748250108" rpy="0 0 0" />
    <parent link="right_hip_yaw_link" />
    <child link="right_knee_link" />
    <axis xyz="0 1 0" />
    <limit lower="-0.17" upper="2" effort="218.8" velocity="11.1" />
  </joint>
  <link name="right_ankle_pitch_link">
    <inertial>
      <origin xyz="2.5923006870902E-18 0 -0.0111659143581498" rpy="0 0 0" />
      <mass value="0.109068073178335" />
      <inertia ixx="2.59558742013529E-05" ixy="-1.86600313964599E-22" ixz="4.90813719854753E-21" iyy="2.29503380113485E-05" iyz="1.38633423081141E-21" izz="1.3279810144691E-05" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_ankle_pitch_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.898039215686275 0.92156862745098 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_ankle_pitch_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="right_ankle_pitch_joint" type="revolute">
    <origin xyz="-0.037923076612246 0.000400000000000011 -0.332846571651657" rpy="0 0.113446401379624 0" />
    <parent link="right_knee_link" />
    <child link="right_ankle_pitch_link" />
    <axis xyz="0 1 0" />
    <limit lower="-0.5" upper="0.4" effort="44.038" velocity="9.3" />
  </joint>
  <link name="right_ankle_roll_link">
    <inertial>
      <origin xyz="0.0374625399921509 5.18167145186488E-05 -0.0347044864620756" rpy="0 0 0" />
      <mass value="0.533316771514289" />
      <inertia ixx="0.000299139835624768" ixy="5.95076407369514E-07" ixz="9.46275041865658E-05" iyy="0.00164912722820401" iyz="1.03441013592447E-07" izz="0.0017007360310126" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_ankle_roll_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.898039215686275 0.92156862745098 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_ankle_roll_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="right_ankle_roll_joint" type="revolute">
    <origin xyz="0 0 -0.025" rpy="0 0 0" />
    <parent link="right_ankle_pitch_link" />
    <child link="right_ankle_roll_link" />
    <axis xyz="1 0 0" />
    <limit lower="-0.24" upper="0.24" effort="44.038" velocity="9.3" />
  </joint>
  <link name="torso_link">
    <inertial>
      <origin xyz="-0.0078166 -3.3836E-07 0.17476" rpy="0 0 0" />
      <mass value="18.06" />
      <inertia ixx="0.19943" ixy="-2.3152E-06" ixz="-0.0010534" iyy="0.19118" iyz="4.8615E-06" izz="0.084175" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/torso_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.69804 0.69804 0.69804 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/torso_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="torso_joint" type="revolute">
    <origin xyz="-0.017758 -2.03E-05 0.092221" rpy="0 0 0" />
    <parent link="pelvis" />
    <child link="torso_link" />
    <axis xyz="0 0 1" />
    <limit lower="-3.14" upper="3.14" effort="140.25" velocity="10.48" />
  </joint>
  <link name="left_shoulder_pitch_link">
    <inertial>
      <origin xyz="-7.19147899617904E-08 0.0501077533572008 -0.00342725772808683" rpy="0 0 0" />
      <mass value="1.61443533540382" />
      <inertia ixx="0.00223446137998964" ixy="8.21375930884592E-09" ixz="1.61467002461629E-08" iyy="0.00324410558687894" iyz="0.000173171072953077" izz="0.0039708921060695" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_shoulder_pitch_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.698039215686274 0.698039215686274 0.698039215686274 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_shoulder_pitch_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="left_shoulder_pitch_joint" type="revolute">
    <origin xyz="0 0.130999999999999 0.2865000087899" rpy="0 0 0" />
    <parent link="torso_link" />
    <child link="left_shoulder_pitch_link" />
    <axis xyz="0 1 0" />
    <limit lower="-3.05" upper="3.05" effort="76" velocity="2.6" />
  </joint>
  <link name="left_shoulder_roll_link">
    <inertial>
      <origin xyz="0.000966371020925686 0.00818026702006544 -0.0532762469359421" rpy="0 0 0" />
      <mass value="1.03280187622854" />
      <inertia ixx="0.00382623177037051" ixy="-9.08684038208586E-08" ixz="-4.17352012014123E-06" iyy="0.00374783792874104" iyz="0.00011767365930524" izz="0.00159567538718294" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_shoulder_roll_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.890196078431372 0.890196078431372 0.913725490196078 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_shoulder_roll_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="left_shoulder_roll_joint" type="revolute">
    <origin xyz="0 0.069 -0.00700000000000001" rpy="0 0 0" />
    <parent link="left_shoulder_pitch_link" />
    <child link="left_shoulder_roll_link" />
    <axis xyz="1 0 0" />
    <limit lower="-0.44" upper="3.6" effort="76" velocity="2.6" />
  </joint>
  <link name="left_shoulder_yaw_link">
    <inertial>
      <origin xyz="0.0120432130530842 -4.84728532590406E-08 -0.0652838006324341" rpy="0 0 0" />
      <mass value="0.976694991116597" />
      <inertia ixx="0.00200032119726538" ixy="1.31597943855741E-09" ixz="0.000262768781550788" iyy="0.0020153754538946" iyz="-1.78070458510602E-09" izz="0.00150551579043055" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_shoulder_yaw_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.890196078431372 0.890196078431372 0.913725490196078 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_shoulder_yaw_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="left_shoulder_yaw_joint" type="revolute">
    <origin xyz="0.00100000000000008 0.00800000000029996 -0.1796999999997" rpy="0 0 0" />
    <parent link="left_shoulder_roll_link" />
    <child link="left_shoulder_yaw_link" />
    <axis xyz="0 0 1" />
    <limit lower="-3.05" upper="3.05" effort="55" velocity="2.6" />
  </joint>
  <link name="left_elbow_pitch_link">
    <inertial>
      <origin xyz="0.0304494439165575 -1.22595412321846E-07 -0.0243765462063093" rpy="0 0 0" />
      <mass value="0.857362880496379" />
      <inertia ixx="0.00168709626539218" ixy="-6.85450715067522E-09" ixz="0.000210133804136126" iyy="0.00123513684918356" iyz="-2.3532846659714E-09" izz="0.00199970367617907" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_elbow_pitch_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.890196078431372 0.890196078431372 0.913725490196078 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_elbow_pitch_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="left_elbow_pitch_joint" type="revolute">
    <origin xyz="0.02 0 -0.0843000000003" rpy="0 0 0" />
    <parent link="left_shoulder_yaw_link" />
    <child link="left_elbow_pitch_link" />
    <axis xyz="0 1 0" />
    <limit lower="-1.5708" upper="1.4835" effort="55" velocity="2.6" />
  </joint>
  <link name="left_elbow_roll_link">
    <inertial>
      <origin xyz="0.0620892969480309 -3.9116559280572E-06 -0.000413047474401407" rpy="0 0 0" />
      <mass value="0.802863781440973" />
      <inertia ixx="0.00095226174498612" ixy="2.58034527096277E-08" ixz="-1.65551845033844E-05" iyy="0.00116536927026212" iyz="-6.84977914861065E-09" izz="0.00155236785238142" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_elbow_roll_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.890196078431372 0.890196078431372 0.913725490196078 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_elbow_roll_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="left_elbow_roll_joint" type="revolute">
    <origin xyz="0.128931201493914 0 -0.0399339059811434" rpy="0 0 0" />
    <parent link="left_elbow_pitch_link" />
    <child link="left_elbow_roll_link" />
    <axis xyz="1 0 0" />
    <limit lower="-3.05" upper="3.05" effort="31" velocity="2.6" />
  </joint>
  <link name="left_wrist_pitch_link">
    <inertial>
      <origin xyz="0.0632131962203847 -0.000488218734539053 4.69885502674128E-07" rpy="0 0 0" />
      <mass value="0.830390542669822" />
      <inertia ixx="0.000396768322907097" ixy="-5.71548845204812E-09" ixz="-2.89238622029398E-08" iyy="0.000679255521744611" iyz="6.17956305561777E-09" izz="0.000670359377265212" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_wrist_pitch_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.890196078431372 0.890196078431372 0.913725490196078 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_wrist_pitch_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="left_wrist_pitch_joint" type="revolute">
    <origin xyz="0.0777 0 -1.32406013299835E-05" rpy="0 0 0" />
    <parent link="left_elbow_roll_link" />
    <child link="left_wrist_pitch_link" />
    <axis xyz="0 1 0" />
    <limit lower="-1.9" upper="1.9" effort="31" velocity="2.6" />
  </joint>
  <link name="left_wrist_roll_link">
    <inertial>
      <origin xyz="0.0131554657921927 1.11022302462516E-16 0" rpy="0 0 0" />
      <mass value="0.0702710752736341" />
      <inertia ixx="4.12024699697545E-05" ixy="1.66471696156063E-19" ixz="1.05159981434758E-19" iyy="3.03893776649961E-05" iyz="7.72356655978906E-20" izz="3.0389377664996E-05" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_wrist_roll_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.890196078431372 0.890196078431372 0.913725490196078 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/left_wrist_roll_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="left_wrist_roll_joint" type="revolute">
    <origin xyz="0.104300000000008 -0.000499999999982098 0" rpy="0 0 0" />
    <parent link="left_wrist_pitch_link" />
    <child link="left_wrist_roll_link" />
    <axis xyz="1 0 0" />
    <limit lower="-3.05" upper="3.05" effort="31" velocity="2.6" />
  </joint>
  <link name="right_shoulder_pitch_link">
    <inertial>
      <origin xyz="2.0231132766438E-07 -0.050107936758677 -0.00342764366403048" rpy="0 0 0" />
      <mass value="1.61445832334524" />
      <inertia ixx="0.00223446811724256" ixy="1.27055702017373E-08" ixz="-2.68721245581516E-08" iyy="0.00324414965531285" iyz="-0.000173148664273807" izz="0.00397086638241496" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_shoulder_pitch_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.698039215686274 0.698039215686274 0.698039215686274 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_shoulder_pitch_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="right_shoulder_pitch_joint" type="revolute">
    <origin xyz="0 -0.130999999999999 0.2865000087899" rpy="0 0 0" />
    <parent link="torso_link" />
    <child link="right_shoulder_pitch_link" />
    <axis xyz="0 1 0" />
    <limit lower="-3.05" upper="3.05" effort="76" velocity="2.6" />
  </joint>
  <link name="right_shoulder_roll_link">
    <inertial>
      <origin xyz="0.000966891604508046 -0.00818041770349123 -0.0532754411104884" rpy="0 0 0" />
      <mass value="1.03279613645944" />
      <inertia ixx="0.00382617137257058" ixy="8.29579819612005E-08" ixz="-4.17103562163156E-06" iyy="0.00374774793869728" iyz="-0.000117677908575203" izz="0.00159565879652578" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_shoulder_roll_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.890196078431372 0.890196078431372 0.913725490196078 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_shoulder_roll_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="right_shoulder_roll_joint" type="revolute">
    <origin xyz="0 -0.069 -0.00700000000000006" rpy="0 0 0" />
    <parent link="right_shoulder_pitch_link" />
    <child link="right_shoulder_roll_link" />
    <axis xyz="1 0 0" />
    <limit lower="-3.6" upper="0.44" effort="76" velocity="2.6" />
  </joint>
  <link name="right_shoulder_yaw_link">
    <inertial>
      <origin xyz="0.0120429790120024 -5.81427909984722E-08 -0.0652838832904268" rpy="0 0 0" />
      <mass value="0.976693111167598" />
      <inertia ixx="0.00200032394220664" ixy="-1.01396426286019E-08" ixz="0.000262770091488321" iyy="0.00201535709385311" iyz="3.89871987302152E-09" izz="0.00150550209958323" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_shoulder_yaw_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.890196078431372 0.890196078431372 0.913725490196078 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_shoulder_yaw_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="right_shoulder_yaw_joint" type="revolute">
    <origin xyz="0.000999999999999949 -0.00800000000029996 -0.1796999999997" rpy="0 0 0" />
    <parent link="right_shoulder_roll_link" />
    <child link="right_shoulder_yaw_link" />
    <axis xyz="0 0 1" />
    <limit lower="-3.05" upper="3.05" effort="55" velocity="2.6" />
  </joint>
  <link name="right_elbow_pitch_link">
    <inertial>
      <origin xyz="0.0304493448586067 -1.34480310526319E-07 -0.0243766759708701" rpy="0 0 0" />
      <mass value="0.85736434083235" />
      <inertia ixx="0.00168709639716048" ixy="-6.64119171441659E-09" ixz="0.000210134634098589" iyy="0.00123514128295616" iyz="1.15476518973081E-08" izz="0.00199970977268714" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_elbow_pitch_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.890196078431372 0.890196078431372 0.913725490196078 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_elbow_pitch_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="right_elbow_pitch_joint" type="revolute">
    <origin xyz="0.02 0 -0.0843000000003" rpy="0 0 0" />
    <parent link="right_shoulder_yaw_link" />
    <child link="right_elbow_pitch_link" />
    <axis xyz="0 1 0" />
    <limit lower="-1.5708" upper="1.4835" effort="55" velocity="2.6" />
  </joint>
  <link name="right_elbow_roll_link">
    <inertial>
      <origin xyz="0.062088959697085 3.6628973463515E-06 -0.000413193387340027" rpy="0 0 0" />
      <mass value="0.802866471933735" />
      <inertia ixx="0.000952260748559672" ixy="-2.51989758112534E-08" ixz="-1.65555635638327E-05" iyy="0.00116537779769882" iyz="3.43143892679879E-09" izz="0.00155237522176579" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_elbow_roll_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.890196078431372 0.890196078431372 0.913725490196078 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_elbow_roll_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="right_elbow_roll_joint" type="revolute">
    <origin xyz="0.128931201493914 0 -0.0399339059811434" rpy="0 0 0" />
    <parent link="right_elbow_pitch_link" />
    <child link="right_elbow_roll_link" />
    <axis xyz="1 0 0" />
    <limit lower="-3.05" upper="3.05" effort="31" velocity="2.6" />
  </joint>
  <link name="right_wrist_pitch_link">
    <inertial>
      <origin xyz="0.0632132703132792 0.000488038106961269 4.47562464395146E-07" rpy="0 0 0" />
      <mass value="0.830387590439563" />
      <inertia ixx="0.000396763894739542" ixy="3.98887417581871E-09" ixz="-3.04125147806717E-08" iyy="0.000679252596349105" iyz="2.77648679538775E-10" izz="0.000670357451793362" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_wrist_pitch_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.890196078431372 0.890196078431372 0.913725490196078 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_wrist_pitch_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="right_wrist_pitch_joint" type="revolute">
    <origin xyz="0.0777 0 -1.32406013300113E-05" rpy="0 0 0" />
    <parent link="right_elbow_roll_link" />
    <child link="right_wrist_pitch_link" />
    <axis xyz="0 1 0" />
    <limit lower="-1.9" upper="1.9" effort="31" velocity="2.6" />
  </joint>
  <link name="right_wrist_roll_link">
    <inertial>
      <origin xyz="0.0131554657921929 -2.22044604925031E-16 -1.66533453693773E-16" rpy="0 0 0" />
      <mass value="0.070271075273634" />
      <inertia ixx="4.12024699697545E-05" ixy="-1.59585276256965E-19" ixz="1.15195741382066E-20" iyy="3.03893776649961E-05" iyz="1.92855302795215E-20" izz="3.03893776649959E-05" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_wrist_roll_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.890196078431372 0.890196078431372 0.913725490196078 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/right_wrist_roll_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="right_wrist_roll_joint" type="revolute">
    <origin xyz="0.104300000000007 0.000499999999982181 0" rpy="0 0 0" />
    <parent link="right_wrist_pitch_link" />
    <child link="right_wrist_roll_link" />
    <axis xyz="1 0 0" />
    <limit lower="-3.05" upper="3.05" effort="31" velocity="2.6" />
  </joint>
  <link name="head_yaw_link">
    <inertial>
      <origin xyz="-0.0256453616663468 -4.63886633771252E-10 0.0372687948464679" rpy="0 0 0" />
      <mass value="0.763812702017935" />
      <inertia ixx="0.000535189673379948" ixy="-6.46906570915607E-11" ixz="4.38714729376982E-05" iyy="0.000446415913201309" iyz="5.61390206059147E-11" izz="0.000557682812404017" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/head_yaw_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.898039215686275 0.92156862745098 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/head_yaw_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="head_yaw_joint" type="revolute">
    <origin xyz="0 0 0.3734997463443" rpy="0 0 0.0205182760550021" />
    <parent link="torso_link" />
    <child link="head_yaw_link" />
    <axis xyz="0 0 1" />
    <limit lower="-3.14" upper="3.14" effort="31" velocity="2.6" />
  </joint>
  <link name="head_pitch_link">
    <inertial>
      <origin xyz="0.0360909860336386 0.00224836938905089 0.102904615331724" rpy="0 0 0" />
      <mass value="0.825360879948076" />
      <inertia ixx="0.00192523426025253" ixy="1.008618703704E-06" ixz="0.000394021077522683" iyy="0.00182493697562949" iyz="-7.50366158348484E-07" izz="0.00200813842771529" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/head_pitch_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.898039215686275 0.92156862745098 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://dobot/meshes/head_pitch_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="head_pitch_joint" type="revolute">
    <origin xyz="-0.030000000000001 0 0.0349999999999999" rpy="0 0 0" />
    <parent link="head_yaw_link" />
    <child link="head_pitch_link" />
    <axis xyz="0 1 0" />
    <limit lower="-1.57" upper="1.57" effort="31" velocity="2.6" />
  </joint>

  <!-- imu -->
  <link name="imu_link">
  </link>
  <joint name="imu_joint" type="fixed">
    <origin xyz="0.02465 0 0.0165" rpy="0 0 0" />
    <parent link="torso_link" />
    <child link="imu_link" />
  </joint>

</robot>