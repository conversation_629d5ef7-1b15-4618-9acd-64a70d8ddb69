<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>) 
     Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot
  name="T1">
  <mujoco>
    <compiler meshdir="../meshes/" balanceinertia="true" discardvisual="false"/>
  </mujoco>

  <!-- <link name="world" />
  <joint name ="weld" type="floating">
    <parent link="world"/>
    <child link="base_link"/>
    <origin xyz="0 0 0.3"/>
  </joint> -->

  <link
    name="base_link">
    <inertial>
      <origin
        xyz="0.0551365401093076 -1.42058017623659E-06 0.105062332707657"
        rpy="0 0 0" />
      <mass
        value="11.7" />
      <inertia
        ixx="0.0915287235057927"
        ixy="-4.25369739206781E-07"
        ixz="0.000646360369011163"
        iyy="0.076778716903413"
        iyz="5.82340020271393E-07"
        izz="0.0556171053368987" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/Trunk.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.76 0.76 0.76 1.0" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0.06 0 0.12"
        rpy="0 0 0" />
      <geometry>
        <box size="0.15 0.2 0.3" />
      </geometry>
    </collision>
  </link>

  <link
    name="head_link01">
    <inertial>
      <origin
        xyz="-0.000508 -0.001403 0.057432"
        rpy="0 0 0" />
      <mass
        value="0.443910" />
      <inertia
        ixx="0.000224"
        ixy="0.000003"
        ixz="0.000001"
        iyy="0.000241"
        iyz="-0.000002"
        izz="0.000150" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/H1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
  </link>
  <joint
    name="idx22_head_joint1"
    type="fixed">
    <origin
      xyz="0.0625 0 0.243"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="head_link01" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-1.57"
      upper="1.57"
      effort="7"
      velocity="12.56" />
  </joint>
  <link
    name="head_link02">
    <inertial>
      <origin
        xyz="0.007802 0.001262 0.098631"
        rpy="0 0 0" />
      <mass
        value="0.631019" />
      <inertia
        ixx="0.002025"
        ixy="-0.000025"
        ixz="0.000046"
        iyy="0.001920"
        iyz="0.000036"
        izz="0.001739" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/H2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0.01 0 0.11"
        rpy="0 0 0" />
      <geometry>
        <sphere radius="0.08" />
      </geometry>
    </collision>
  </link>
  <joint
    name="idx23_head_joint2"
    type="fixed">
    <origin
      xyz="0 0 0.06185"
      rpy="0 0 0" />
    <parent
      link="head_link01" />
    <child
      link="head_link02" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.35"
      upper="1.22"
      effort="7"
      velocity="12.56" />
  </joint>
  <link
    name="left_arm_link01">
    <inertial>
      <origin
        xyz="-0.000677 0.044974 0.000000"
        rpy="0 0 0" />
      <mass
        value="0.53" />
      <inertia
        ixx="0.001293"
        ixy="-0.000017"
        ixz="0.000000"
        iyy="0.000293"
        iyz="0.000000"
        izz="0.001367" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/AL1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.76 0.76 0.76 1.0" />
      </material>
    </visual>
  </link>
  <joint
    name="idx13_left_arm_joint1"
    type="revolute">
    <origin
      xyz="0.0575 0.1063 0.219"
      rpy="0 0.00088113 0" />
    <parent
      link="base_link" />
    <child
      link="left_arm_link01" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-3.31"
      upper="1.22"
      effort="18"
      velocity="18.84" />
  </joint>
  <link
    name="left_arm_link02">
    <inertial>
      <origin
        xyz="0.003862 0.037976 0.000000"
        rpy="0 0 0" />
      <mass
        value="0.160" />
      <inertia
        ixx="0.000345"
        ixy="0.000008"
        ixz="0.000000"
        iyy="0.000177"
        iyz="0.000000"
        izz="0.000401" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/AL2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
  </link>
  <joint
    name="idx14_left_arm_joint2"
    type="revolute">
    <origin
      xyz="0 0.047 0"
      rpy="0 0 0" />
    <parent
      link="left_arm_link01" />
    <child
      link="left_arm_link02" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-1.74"
      upper="1.57"
      effort="18"
      velocity="18.84" />
  </joint>
  <link
    name="left_arm_link03">
    <inertial>
      <origin
        xyz="0.000000 0.085353 -0.000099"
        rpy="0 0 0" />
      <mass
        value="1.02" />
      <inertia
        ixx="0.012869"
        ixy="0.000000"
        ixz="0.000000"
        iyy="0.000621"
        iyz="-0.000024"
        izz="0.012798" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/AL3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0.05 0"
        rpy="1.5708 0 0" />
      <geometry>
        <cylinder radius="0.03" length="0.15" />
      </geometry>
    </collision>
  </link>
  <joint
    name="idx15_left_arm_joint3"
    type="revolute">
    <origin
      xyz="0.00025 0.0605 0"
      rpy="0 0 0" />
    <parent
      link="left_arm_link02" />
    <child
      link="left_arm_link03" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.27"
      upper="2.27"
      effort="18"
      velocity="18.84" />
  </joint>
  <link
    name="left_arm_link04">
    <inertial>
      <origin
        xyz="-0.000108 0.109573 0.000591"
        rpy="0 0 0" />
      <mass
        value="0.327214390850251" />
      <inertia
        ixx="0.008159"
        ixy="-0.000003"
        ixz="0.000000"
        iyy="0.000215"
        iyz="0.000017"
        izz="0.008131" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/left_hand_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0.13 0"
        rpy="1.5708 0 0" />
      <geometry>
        <cylinder radius="0.03" length="0.175" />
      </geometry>
    </collision>
  </link>
  <joint
    name="idx16_left_arm_joint4"
    type="revolute">
    <origin
      xyz="0 0.1471 0"
      rpy="0 0 0" />
    <parent
      link="left_arm_link03" />
    <child
      link="left_arm_link04" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-2.44"
      upper="0"
      effort="18"
      velocity="18.84" />
  </joint>
  <link
    name="right_arm_link01">
    <inertial>
      <origin
        xyz="-0.000677 -0.044974 0.000000"
        rpy="0 0 0" />
      <mass
        value="0.53" />
      <inertia
        ixx="0.001293"
        ixy="-0.000017"
        ixz="0.000000"
        iyy="0.000293"
        iyz="0.000000"
        izz="0.001367" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/AR1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.76 0.76 0.76 1.0" />
      </material>
    </visual>
  </link>
  <joint
    name="idx17_right_arm_joint1"
    type="revolute">
    <origin
      xyz="0.0575 -0.1063 0.219"
      rpy="0 0.00088113 0" />
    <parent
      link="base_link" />
    <child
      link="right_arm_link01" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-3.31"
      upper="1.22"
      effort="18"
      velocity="18.84" />
  </joint>
  <link
    name="right_arm_link02">
    <inertial>
      <origin
        xyz="0.003862 -0.037976 0.000000"
        rpy="0 0 0" />
      <mass
        value="0.160" />
      <inertia
        ixx="0.000345"
        ixy="-0.000008"
        ixz="0.000000"
        iyy="0.000177"
        iyz="0.000000"
        izz="0.000401" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/AR2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
  </link>
  <joint
    name="idx18_right_arm_joint2"
    type="revolute">
    <origin
      xyz="0 -0.047 0"
      rpy="0 0 0" />
    <parent
      link="right_arm_link01" />
    <child
      link="right_arm_link02" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-1.57"
      upper="1.74"
      effort="18"
      velocity="18.84" />
  </joint>
  <link
    name="right_arm_link03">
    <inertial>
      <origin  
        xyz="0.000000 -0.085353 -0.000099"
        rpy="0 0 0" />
      <mass
        value="1.02" />
      <inertia
        ixx="0.012869"
        ixy="0.000000"
        ixz="0.000000"
        iyy="0.000621"
        iyz="0.000024"
        izz="0.012798" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/AR3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 -0.05 0"
        rpy="1.5708 0 0" />
      <geometry>
        <cylinder radius="0.03" length="0.15" />
      </geometry>
    </collision>
  </link>
  <joint
    name="idx19_right_arm_joint3"
    type="revolute">
    <origin
      xyz="0.00025 -0.0605 0"
      rpy="0 0 0" />
    <parent
      link="right_arm_link02" />
    <child
      link="right_arm_link03" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.27"
      upper="2.27"
      effort="18"
      velocity="18.84" />
  </joint>
  <link
    name="right_arm_link04">
    <inertial>
      <origin     
xyz="-0.000108 -0.109573 0.000591"
        rpy="0 0 0" />
      <mass
        value="0.327214390850251" />
      <inertia
        ixx="0.008159"
        ixy="0.000003"
        ixz="0.000000"
        iyy="0.000215"
        iyz="-0.000017"
        izz="0.008131" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/right_hand_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 -0.13 0"
        rpy="1.5708 0 0" />
      <geometry>
        <cylinder radius="0.03" length="0.175" />
      </geometry>
    </collision>
  </link>
  <joint
    name="idx20_right_arm_joint4"
    type="revolute">
    <origin
      xyz="0 -0.1471 0"
      rpy="0 0 0" />
    <parent
      link="right_arm_link03" />
    <child
      link="right_arm_link04" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="0"
      upper="2.44"
      effort="18"
      velocity="18.84" />
  </joint>
  <link
    name="waist_link">
    <inertial>
      <origin
        xyz="0.002284 0.000003 0.007301"
        rpy="0 0 0" />
      <mass
        value="2.581" />
      <inertia
        ixx="0.005289"
        ixy="0.000000"
        ixz="0.000207"
        iyy="0.005299"
        iyz="0.000001"
        izz="0.004821" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/Waist.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
  </link>
  <joint
    name="idx21_waist_joint"
    type="revolute">
    <origin
      xyz="0.0625 0 -0.1155"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="waist_link" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-1.57"
      upper="1.57"
      effort="30"
      velocity="10.88" />
  </joint>
  <link
    name="leg_l1_link">
    <inertial>
      <origin
        xyz="0.000534 -0.007296 -0.018083"
        rpy="0 0 0" />
      <mass
        value="1.021" />
      <inertia
        ixx="0.001805"
        ixy="0.000006"
        ixz="-0.000015"
        iyy="0.001421"
        iyz="0.000080"
        izz="0.001292" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/Hip_Pitch_Left.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.76 0.76 0.76 1.0" />
      </material>
    </visual>
  </link>
  <joint
    name="leg_l1_joint"
    type="revolute">
    <origin
      xyz="0 0.106 0"
      rpy="0 0 0" />
    <parent
      link="waist_link" />
    <child
      link="leg_l1_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-1.8"
      upper="1.57"
      effort="45"
      velocity="12.5" />
  </joint>
  <link
    name="leg_l2_link">
    <inertial>
      <origin
        xyz="0.001101 0.000024 -0.053750"
        rpy="0 0 0" />
      <mass
        value="0.385" />
      <inertia
        ixx="0.001517"
        ixy="0.000000"
        ixz="0.000017"
        iyy="0.001743"
        iyz="0.000000"
        izz="0.000515" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/Hip_Roll_Left.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
  </link>
  <joint
    name="leg_l2_joint"
    type="revolute">
    <origin
      xyz="0 0 -0.02"
      rpy="0 0 0" />
    <parent
      link="leg_l1_link" />
    <child
      link="leg_l2_link" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.2"
      upper="1.57"
      effort="30"
      velocity="10.9" />
  </joint>
  <link
    name="leg_l3_link">
    <inertial>
      <origin
        xyz="-0.007233 0.000206 -0.089184"
        rpy="0 0 0" />
      <mass
        value="2.166" />
      <inertia
        ixx="0.025108"
        ixy="-0.000007"
        ixz="0.002094"
        iyy="0.025733"
        iyz="-0.000050"
        izz="0.002787" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/Hip_Yaw_Left.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <cylinder radius="0.05" length="0.16" />
      </geometry>
    </collision>
  </link>
  <joint
    name="leg_l3_joint"
    type="revolute">
    <origin
      xyz="0 0 -0.081854"
      rpy="0 0 0" />
    <parent
      link="leg_l2_link" />
    <child
      link="leg_l3_link" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-1"
      upper="1"
      effort="30"
      velocity="10.9" />
  </joint>
  <link
    name="leg_l4_link">
    <inertial>
      <origin
        xyz="-0.006012 0.000259 -0.124318"
        rpy="0 0 0" />
      <mass
        value="1.73" />
      <inertia
        ixx="0.034618"
        ixy="0.000011"
        ixz="0.001561"
        iyy="0.034539"
        iyz="0.000197"
        izz="0.001934" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/Shank_Left.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 -0.12"
        rpy="0 0 0" />
      <geometry>
        <cylinder radius="0.05" length="0.15" />
      </geometry>
    </collision>
  </link>
  <joint
    name="leg_l4_joint"
    type="revolute">
    <origin
      xyz="-0.014 0 -0.134"
      rpy="0 0 0" />
    <parent
      link="leg_l3_link" />
    <child
      link="leg_l4_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="0"
      upper="2.34"
      effort="60"
      velocity="11.7" />
  </joint>
  <link
    name="leg_l5_link">
    <inertial>
      <origin
        xyz="-0.003722 0.000000 -0.007981"
        rpy="0 0 0" />
      <mass
        value="0.073" />
      <inertia
        ixx="0.000012"
        ixy="0.000000"
        ixz="0.000003"
        iyy="0.000029"
        iyz="0.000000"
        izz="0.000025" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/Ankle_Cross_Left.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
  </link>
  <joint
    name="leg_l5_joint"
    type="revolute">
    <origin
      xyz="0 0 -0.28"
      rpy="0 0 0" />
    <parent
      link="leg_l4_link" />
    <child
      link="leg_l5_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.87"
      upper="0.35"
      effort="20"
      velocity="18.8" />
  </joint>
  <link
    name="leg_l6_link">
    <inertial>
      <origin
        xyz="-0.000249 0.000000 -0.009140"
        rpy="0 0 0" />
      <mass
        value="0.685" />
      <inertia
        ixx="0.002214"
        ixy="0"
        ixz="-0.000114"
        iyy="0.002385"
        iyz="0"
        izz="0.002671" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/left_foot_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
    <collision>
      <origin rpy="0.0 0.0 0.0" xyz="-0.0 0.0 0.0"/>
      <geometry>
        <mesh filename="../meshes/left_foot_link.STL" />
      </geometry>
    </collision> 
    <!-- <collision>
      <origin
        xyz="0.01 0 -0.015"
        rpy="0 0 0" />
      <geometry>
        <box size="0.15 0.09 0.03" />
      </geometry>
    </collision> -->
    <!-- <collision>
      <origin
        xyz="0.01 0 -0.015"
        rpy="0 0 0" />
      <geometry>
        <box size="0.223 0.1 0.03" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="leg_l6_joint"
    type="revolute">
    <origin
      xyz="0 0.00025 -0.012"
      rpy="0 0 0" />
    <parent
      link="leg_l5_link" />
    <child
      link="leg_l6_link" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.44"
      upper="0.44"
      effort="15"
      velocity="12.4" />
  </joint>
  <link
    name="leg_r1_link">
    <inertial>
      <origin
        xyz="0.000534 0.007514 -0.018082"
        rpy="0 0 0" />
      <mass
        value="1.021" />
      <inertia
        ixx="0.001805"
        ixy="-0.000008"
        ixz="-0.000015"
        iyy="0.001421"
        iyz="-0.000085"
        izz="0.001292" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/Hip_Pitch_Right.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.76 0.76 0.76 1.0" />
      </material>
    </visual>
  </link>
  <joint
    name="leg_r1_joint"
    type="revolute">
    <origin
      xyz="0 -0.106 0"
      rpy="0 0 0" />
    <parent
      link="waist_link" />
    <child
      link="leg_r1_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-1.8"
      upper="1.57"
      effort="45"
      velocity="12.5" />
  </joint>
  <link
    name="leg_r2_link">
    <inertial>
      <origin
        xyz="0.001099 0.000024 -0.053748"
        rpy="0 0 0" />
      <mass
        value="0.385" />
      <inertia
        ixx="0.001517"
        ixy="0.000000"
        ixz="0.000017"
        iyy="0.001743"
        iyz="0.000000"
        izz="0.000515" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/Hip_Roll_Right.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
  </link>
  <joint
    name="leg_r2_joint"
    type="revolute">
    <origin
      xyz="0 0 -0.02"
      rpy="0 0 0" />
    <parent
      link="leg_r1_link" />
    <child
      link="leg_r2_link" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-1.57"
      upper="0.2"
      effort="30"
      velocity="10.9" />
  </joint>
  <link
    name="leg_r3_link">
    <inertial>
      <origin
        xyz="-0.007191 -0.000149 -0.089220"
        rpy="0 0 0" />
      <mass
        value="2.17" />
      <inertia
        ixx="0.025137"
        ixy="0.000006"
        ixz="0.002086"
        iyy="0.025762"
        iyz="0.000044"
        izz="0.002787" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/Hip_Yaw_Right.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <cylinder radius="0.05" length="0.16" />
      </geometry>
    </collision>
  </link>
  <joint
    name="leg_r3_joint"
    type="revolute">
    <origin
      xyz="0 0 -0.081854"
      rpy="0 0 0" />
    <parent
      link="leg_r2_link" />
    <child
      link="leg_r3_link" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-1"
      upper="1"
      effort="30"
      velocity="10.9" />
  </joint>
  <link
    name="leg_r4_link">
    <inertial>
      <origin
        xyz="-0.005741 -0.000541 -0.122602"
        rpy="0 0 0" />
      <mass
        value="1.79" />
      <inertia
        ixx="0.035098"
        ixy="-0.000009"
        ixz="0.001554"
        iyy="0.034958"
        iyz="-0.000086"
        izz="0.002039" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/Shank_Right.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 -0.12"
        rpy="0 0 0" />
      <geometry>
        <cylinder radius="0.05" length="0.15" />
      </geometry>
    </collision>
  </link>
  <joint
    name="leg_r4_joint"
    type="revolute">
    <origin
      xyz="-0.014 0 -0.134"
      rpy="0 0 0" />
    <parent
      link="leg_r3_link" />
    <child
      link="leg_r4_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="0"
      upper="2.34"
      effort="60"
      velocity="11.7" />
  </joint>
  <link
    name="leg_r5_link">
    <inertial>
      <origin
        xyz="-0.003722 0.000000 -0.007981"
        rpy="0 0 0" />
      <mass
        value="0.073" />
      <inertia
        ixx="0.000012"
        ixy="0.000000"
        ixz="0.000003"
        iyy="0.000029"
        iyz="0.000000"
        izz="0.000025" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/Ankle_Cross_Right.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
  </link>
  <joint
    name="leg_r5_joint"
    type="revolute">
    <origin
      xyz="0 0 -0.28"
      rpy="0 0 0" />
    <parent
      link="leg_r4_link" />
    <child
      link="leg_r5_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.87"
      upper="0.35"
      effort="20"
      velocity="18.8" />
  </joint>
  <link
    name="leg_r6_link">
    <inertial>
      <origin
        xyz="-0.000248 0.000000 -0.009140"
        rpy="0 0 0" />
      <mass
        value="0.685" />
      <inertia
        ixx="0.002214"
        ixy="0"
        ixz="-0.000114"
        iyy="0.002385"
        iyz="0"
        izz="0.002671" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/right_foot_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.4 0.4 0.4 1.0" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0.01 0 -0.015"
        rpy="0 0 0" />
      <geometry>
        <box size="0.223 0.1 0.03" />
      </geometry>
    </collision> -->

    <collision>
      <origin rpy="0.0 0.0 0.0" xyz="-0.0 0.0 0.0"/>
      <geometry>
        <mesh filename="../meshes/right_foot_link.STL" />
      </geometry>
    </collision> 
  </link>
  <joint
    name="leg_r6_joint"
    type="revolute">
    <origin
      xyz="0 -0.00025 -0.012"
      rpy="0 0 0" />
    <parent
      link="leg_r5_link" />
    <child
      link="leg_r6_link" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.44"
      upper="0.44"
      effort="15"
      velocity="12.4" />
  </joint>

  <!-- Gazebo参数, 左腿 -->
  <gazebo reference="leg_l1_link">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>1</self_collide>
  <material>Gazebo/RedBright</material>
  </gazebo>
  <gazebo reference="leg_l2_link">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>1</self_collide>
  <material>Gazebo/Yellow</material>
  </gazebo>
  <gazebo reference="leg_l3_link">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>1</self_collide>
  <material>Gazebo/Green</material>
  </gazebo>
  <gazebo reference="leg_l4_link">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>0</self_collide>
  <material>Gazebo/Blue</material>
  </gazebo>
  <gazebo reference="leg_l5_link">
    <mu1>1.6</mu1>
    <mu2>1.6</mu2>
    <self_collide>0</self_collide>
    <kp>1000000.0</kp>
    <kd>100.0</kd>
    <maxVel>1.0</maxVel>
    <minDepth>0.00</minDepth>
  <material>Gazebo/Purple</material>
  </gazebo>
  <gazebo reference="leg_l6_link">
    <mu1>1.6</mu1>
    <mu2>1.6</mu2>
    <self_collide>0</self_collide>
    <kp>1000000.0</kp>
    <kd>100.0</kd>
    <maxVel>1.0</maxVel>
    <minDepth>0.00</minDepth>
  <material>Gazebo/Purple</material>
  </gazebo>

  <!-- 传动装置, 左腿 -->
  <transmission name="leg_l1_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="leg_l1_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="leg_l1_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>101</mechanicalReduction>
      <maxVelocity>12</maxVelocity>
      <maxEffort>120</maxEffort>
    </actuator>
  </transmission>
  <transmission name="leg_l2_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="leg_l2_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="leg_l2_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>101</mechanicalReduction>
      <maxVelocity>4.188790205</maxVelocity> <!-- 40rpm -->
      <maxEffort>64.0</maxEffort>
    </actuator>
  </transmission>
  <transmission name="leg_l3_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="leg_l3_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="leg_l3_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>51</mechanicalReduction>
      <maxVelocity>5.759586532</maxVelocity>  <!-- 55rpm -->
      <maxEffort>188.0</maxEffort>
    </actuator>
  </transmission>
  <transmission name="leg_l4_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="leg_l4_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="leg_l4_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>51</mechanicalReduction>
      <maxVelocity>5.759586532</maxVelocity> <!-- 55rpm -->
      <maxEffort>188.0</maxEffort>
    </actuator>
  </transmission>
  <transmission name="leg_l5_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="leg_l5_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="leg_l5_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>51</mechanicalReduction>
      <maxVelocity>8.58701992</maxVelocity> <!-- 82rpm -->
      <maxEffort>40.0</maxEffort>
    </actuator>
  </transmission>
  <transmission name="leg_l6_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="leg_l6_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="leg_l6_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>51</mechanicalReduction>
      <maxVelocity>9.58</maxVelocity> <!-- 97rpm -->
      <maxEffort>20.2</maxEffort>
    </actuator>
  </transmission>

  <!-- Gazebo参数, 右腿 -->
  <gazebo reference="leg_r1_link">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>1</self_collide>
      <material>Gazebo/RedBright</material>
  </gazebo>

  <gazebo reference="leg_r2_link">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>1</self_collide>
      <material>Gazebo/Yellow</material>
  </gazebo>
  <gazebo reference="leg_r3_link">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>1</self_collide>
      <material>Gazebo/Green</material>
  </gazebo>
  <gazebo reference="leg_r4_link">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>0</self_collide>
      <material>Gazebo/Blue</material>
  </gazebo>
  <gazebo reference="leg_r5_link">
    <mu1>1.6</mu1>
    <mu2>1.6</mu2>
    <self_collide>0</self_collide>
    <kp>1000000.0</kp>
    <kd>100.0</kd>
    <maxVel>1.0</maxVel>
    <minDepth>0.00</minDepth>
    <material>Gazebo/Purple</material>
  </gazebo>
  <gazebo reference="leg_r6_link">
    <mu1>1.6</mu1>
    <mu2>1.6</mu2>
    <self_collide>0</self_collide>
    <kp>1000000.0</kp>
    <kd>100.0</kd>
    <maxVel>1.0</maxVel>
    <minDepth>0.00</minDepth>
    <material>Gazebo/Purple</material>
  </gazebo>

  <!-- 传动装置, 右腿 -->
  <transmission name="leg_r1_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="leg_r1_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="leg_r1_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>101</mechanicalReduction>
      <maxVelocity>12</maxVelocity>
      <maxEffort>120</maxEffort>
    </actuator>
  </transmission>
  <transmission name="leg_r2_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="leg_r2_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="leg_r2_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>101</mechanicalReduction>
      <maxVelocity>12</maxVelocity>
      <maxEffort>120</maxEffort>
    </actuator>
  </transmission>
  <transmission name="leg_r3_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="leg_r3_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="leg_r3_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>51</mechanicalReduction>
      <maxVelocity>12</maxVelocity>
      <maxEffort>140</maxEffort>
    </actuator>
  </transmission>
  <transmission name="leg_r4_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="leg_r4_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="leg_r4_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>51</mechanicalReduction>
      <maxVelocity>12</maxVelocity>
      <maxEffort>140</maxEffort>
    </actuator>
  </transmission>
  <transmission name="leg_r5_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="leg_r5_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="leg_r5_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>51</mechanicalReduction>
      <maxVelocity>12</maxVelocity>
      <maxEffort>50</maxEffort>
    </actuator>
  </transmission>
  <transmission name="leg_r6_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="leg_r6_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="leg_r6_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>51</mechanicalReduction>
      <maxVelocity>12</maxVelocity>
      <maxEffort>50</maxEffort>
    </actuator>
  </transmission>

  <!-- Gazebo 参数, 左臂 -->
  <gazebo reference="left_arm_link01">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/RedBright</material>
  </gazebo>
  <gazebo reference="left_arm_link02">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>1</self_collide>
  <material>Gazebo/Yellow</material>
  </gazebo>
  <gazebo reference="left_arm_link03">
    <mu1>1.6</mu1>
    <mu2>1.6</mu2>
    <self_collide>0</self_collide>
    <kp>1000000.0</kp>
    <kd>100.0</kd>
    <maxVel>1.0</maxVel>
    <minDepth>0.00</minDepth>
  <material>Gazebo/Purple</material>
  </gazebo>
  <gazebo reference="left_arm_link04">
    <mu1>1.6</mu1>
    <mu2>1.6</mu2>
    <self_collide>0</self_collide>
    <kp>1000000.0</kp>
    <kd>100.0</kd>
    <maxVel>1.0</maxVel>
    <minDepth>0.00</minDepth>
  <material>Gazebo/Indigo</material>
  </gazebo>

  <!-- 传动装置, 左臂 -->
  <transmission name="left_arm_link01_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="idx13_left_arm_joint1">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="idx13_left_arm_joint1_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>101</mechanicalReduction>
      <maxVelocity>12</maxVelocity>
      <maxEffort>100</maxEffort>
    </actuator>
  </transmission>
  <transmission name="left_arm_link02_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="idx14_left_arm_joint2">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="idx14_left_arm_joint2_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>101</mechanicalReduction>
      <maxVelocity>12</maxVelocity>
      <maxEffort>100</maxEffort>
    </actuator>
  </transmission>

  <transmission name="left_arm_link03_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="idx15_left_arm_joint3">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="idx15_left_arm_joint3_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>101</mechanicalReduction>
      <maxVelocity>12</maxVelocity> 
      <maxEffort>100</maxEffort>
    </actuator>
  </transmission>
  <transmission name="left_arm_link04_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="idx16_left_arm_joint4">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="idx16_left_arm_joint4_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>51</mechanicalReduction>
      <maxVelocity>12</maxVelocity> 
      <maxEffort>100</maxEffort>
    </actuator>
  </transmission>

  <!-- Gazebo 参数, 右臂 -->
  <gazebo reference="right_arm_link01">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/RedBright</material>
  </gazebo>
  <gazebo reference="right_arm_link02">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>1</self_collide>
  <material>Gazebo/Yellow</material>
  </gazebo>
  <gazebo reference="right_arm_link03">
    <mu1>1.6</mu1>
    <mu2>1.6</mu2>
    <self_collide>0</self_collide>
    <kp>1000000.0</kp>
    <kd>100.0</kd>
    <maxVel>1.0</maxVel>
    <minDepth>0.00</minDepth>
  <material>Gazebo/Purple</material>
  </gazebo>
  <gazebo reference="right_arm_link04">
    <mu1>1.6</mu1>
    <mu2>1.6</mu2>
    <self_collide>0</self_collide>
    <kp>1000000.0</kp>
    <kd>100.0</kd>
    <maxVel>1.0</maxVel>
    <minDepth>0.00</minDepth>
  <material>Gazebo/Indigo</material>
  </gazebo>

  <!-- 传动装置, 左臂 -->
  <transmission name="right_arm_link01_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="idx17_right_arm_joint1">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="idx17_right_arm_joint1_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>101</mechanicalReduction>
      <maxVelocity>12</maxVelocity>
      <maxEffort>100</maxEffort>
    </actuator>
  </transmission>
  <transmission name="right_arm_link02_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="idx18_right_arm_joint2">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="idx18_right_arm_joint2_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>101</mechanicalReduction>
      <maxVelocity>12</maxVelocity>
      <maxEffort>100</maxEffort>
    </actuator>
  </transmission>

  <transmission name="right_arm_link03_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="idx19_right_arm_joint3">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="idx19_right_arm_joint3_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>101</mechanicalReduction>
      <maxVelocity>12</maxVelocity> 
      <maxEffort>100</maxEffort>
    </actuator>
  </transmission>
  <transmission name="right_arm_link04_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="idx20_right_arm_joint4">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="idx20_right_arm_joint4_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>51</mechanicalReduction>
      <maxVelocity>12</maxVelocity> 
      <maxEffort>100</maxEffort>
    </actuator>
  </transmission>

  <!-- Gazebo参数, 头 (fixed) -->
  <gazebo reference="head_link01">
    <material>Gazebo/GreyTransparent</material>
  </gazebo>

  <gazebo reference="head_link02">
    <material>Gazebo/GreyTransparent</material>
  </gazebo>

  <!-- Gazebo参数, 腰 -->
  <gazebo reference="waist_link">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>0</self_collide>
    <kp>1000000.0</kp>
    <kd>100.0</kd>
    <maxVel>1.0</maxVel>
    <minDepth>0.00</minDepth>
  <material>Gazebo/Turquoise</material>
  </gazebo>

  <!-- 传动装置, 腰 -->
  <transmission name="waist_link_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="idx21_waist_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="idx21_waist_joint_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>101</mechanicalReduction>
      <maxVelocity>12</maxVelocity>
      <maxEffort>100</maxEffort>
    </actuator>
  </transmission>

</robot>