<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>) 
     Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot
  name="urdf_viewer">

  <mujoco>
    <compiler meshdir="../meshes_v1_simp/" balanceinertia="true" discardvisual="false"/>
  </mujoco>

    <!-- [CAUTION] uncomment when convert to mujoco -->
    <link name="world"></link>
    <joint name="floating_base_joint" type="floating">
      <parent link="world"/>
      <child link="base_link"/>
    </joint>

  <link
    name="base_link">
    <inertial>
      <origin
        xyz="-0.00776259471195972 -0.00140197731838065 0.0336297701715775"
        rpy="0 0 0" />
      <mass
        value="7.30658284724595" />
      <inertia
        ixx="0.0465892379306531"
        ixy="1.48640099151913E-05"
        ixz="-5.58433170937688E-05"
        iyy="0.0274488106130694"
        iyz="0.000142308242338203"
        izz="0.0303559386455778" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 -0.11601077"  
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/base_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 -0.10"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/base_link.STL" />
      </geometry>
    </collision>
  </link>
  <link
    name="xleft_arm_link01">
    <inertial>
      <origin
        xyz="-0.00460634530062598 0.0507543528865328 0.0029396320756323"
        rpy="0 0 0" />
      <mass
        value="1.55528587803699" />
      <inertia
        ixx="0.00249816682945256"
        ixy="2.15393016013029E-06"
        ixz="0.000302460906477747"
        iyy="0.00216900686207713"
        iyz="-4.12760982317053E-05"
        izz="0.00219280382260358" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="1 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/left_arm_link01.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/left_arm_link01.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint name="xleft_arm_joint01" type="revolute">
    <origin xyz="0 0.12424 0" rpy="0 0 0"/>
    <parent link="base_link"/>
    <child  link="xleft_arm_link01"/>
    <axis   xyz="0 1 0"/>
    <limit  lower="-3" upper="3" effort="1000" velocity="100"/>
  </joint>
  <link
    name="xleft_arm_link02">
    <inertial>
      <origin
        xyz="0.0524279591837606 0.0106624167111036 -0.0252554345020881"
        rpy="0 0 0" />
      <mass
        value="0.77099757152996" />
      <inertia
        ixx="0.00163551871151054"
        ixy="1.41485986902138E-05"
        ixz="-4.94026698694809E-05"
        iyy="0.00103215324733097"
        iyz="0.00012235726050536"
        izz="0.000871532034319005" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="-1 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/left_arm_link02.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/left_arm_link02.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="xleft_arm_joint02"
    type="revolute">
    <origin
      xyz="-0.0436499999959411 0.0694997321979529 0"
      rpy="-1.26 0 0" />
    <parent
      link="xleft_arm_link01" />
    <child
      link="xleft_arm_link02" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="xleft_arm_link03">
    <inertial>
      <origin
        xyz="0.00421272579907894 -0.00145964846842034 -0.0991693709233346"
        rpy="0 0 0" />
      <mass
        value="1.54473250236892" />
      <inertia
        ixx="0.00278663739476603"
        ixy="7.50256357747923E-06"
        ixz="4.03334093377822E-05"
        iyy="0.00334605346539581"
        iyz="-5.00494800391242E-05"
        izz="0.00136474017057747" />
    </inertial>
    <visual>
      <origin
        xyz="0. 0.14 0"
        rpy="0 1.57 0" />
      <geometry>
        <box size="0.1 0.33 0.08"/>
      </geometry>
      <!-- <origin
        xyz="0 0 0"
        rpy="1 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/left_arm_link03.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material> -->
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/left_arm_link03.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="xleft_arm_joint03"
    type="revolute">
    <origin
      xyz="0.0437325467541395 0.0716172282129588 0.0167023554242665"
      rpy="0 0 0" />
    <parent
      link="xleft_arm_link02" />
    <child
      link="xleft_arm_link03" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="xleft_arm_link04">
    <inertial>
      <origin
        xyz="-0.0150790809792842 0.0332023864696083 -0.0302613208626026"
        rpy="0 0 0" />
      <mass
        value="0.407704961031808" />
      <inertia
        ixx="0.000293116737231582"
        ixy="8.12005107026881E-06"
        ixz="-2.87449850416113E-05"
        iyy="0.000490514953816382"
        iyz="1.18479782176887E-05"
        izz="0.000345584869408161" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="1 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/left_arm_link04.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/left_arm_link04.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="xleft_arm_joint04"
    type="revolute">
    <origin
      xyz="-0.0377000000000302 0.186748041823299 -0.0174198008752784"
      rpy="0 0 0" />
    <parent
      link="xleft_arm_link03" />
    <child
      link="xleft_arm_link04" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="xleft_arm_link05">
    <inertial>
      <origin
        xyz="0.00226079685730163 0.000314521154911651 -0.0965876755490127"
        rpy="0 0 0" />
      <mass
        value="0.873926438504206" />
      <inertia
        ixx="0.00235873205732084"
        ixy="-4.14580252894015E-06"
        ixz="1.91132710632377E-05"
        iyy="0.00199084174204352"
        iyz="1.54983511491909E-05"
        izz="0.00068424667251946" />
    </inertial>
    <visual>
      <origin
        xyz="0. 0.18 0"
        rpy="0 1.57 0" />
      <geometry>
        <box size="0.1 0.2 0.08"/>
      </geometry>
      <!-- <origin
        xyz="0 0 0"
        rpy="1 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/left_arm_link05.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material> -->
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/left_arm_link05.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="xleft_arm_joint05"
    type="revolute">
    <origin
      xyz="0.0376999419728888 0.0589711214539149 0.0174143628791139"
      rpy="0 0 0" />
    <parent
      link="xleft_arm_link04" />
    <child
      link="xleft_arm_link05" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="xleft_arm_link06">
    <inertial>
      <origin
        xyz="-0.032317688067383 -0.000750150272981087 -0.00024894854439772"
        rpy="0 0 0" />
      <mass
        value="0.512799713144791" />
      <inertia
        ixx="0.000181579444640215"
        ixy="7.84976431050537E-07"
        ixz="-5.48867576147434E-06"
        iyy="0.000319453883212228"
        iyz="-4.69448007365072E-06"
        izz="0.000193151627324984" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 -1" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/left_arm_link06.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/left_arm_link06.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="xleft_arm_joint06"
    type="revolute">
    <origin
      xyz="0 0.215499434127755 0.0328955526694363"
      rpy="0 0 0" />
    <parent
      link="xleft_arm_link05" />
    <child
      link="xleft_arm_link06" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="xleft_arm_link07">
        <inertial>
      <origin
        xyz="-0.00141524479328536 -0.124344426298111 -0.106888331062468"
        rpy="0 0 0" />
      <mass
        value="0.66492987084843" />
      <inertia
        ixx="0.000786550557480451"
        ixy="-7.80232359961781E-09"
        ixz="-1.37172276684966E-06"
        iyy="0.000919604383491564"
        iyz="-3.74728160822078E-05"
        izz="0.000315349684905891" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="1 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/left_arm_link07.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/left_arm_link07.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="xleft_arm_joint07"
    type="revolute">
    <origin
      xyz="-0.0389838139556652 -0.00112349876295814 -0.0328999999999615"
      rpy="0 0 0.028811646563689" />
    <parent
      link="xleft_arm_link06" />
    <child
      link="xleft_arm_link07" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="xright_arm_link01">
    <inertial>
      <origin
        xyz="-0.00491667599321899 -0.0519635559822305 -0.000222147088871694"
        rpy="0 0 0" />
      <mass
        value="0.442133480846084" />
      <inertia
        ixx="0.00059523390155258"
        ixy="-2.83897354294574E-06"
        ixz="7.35657138718999E-05"
        iyy="0.000528248481361658"
        iyz="1.05185491306054E-05"
        izz="0.000563158491468821" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/right_arm_link01.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/right_arm_link01.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint name="xright_arm_joint01" type="revolute">
    <origin xyz="0 -0.12436 0" rpy="0 0 0"/>
    <parent link="base_link"/>
    <child  link="xright_arm_link01"/>
    <axis   xyz="0 1 0"/>
    <limit  lower="-3" upper="3" effort="1000" velocity="100"/>
  </joint>
  <link
    name="xright_arm_link02">
    <inertial>
      <origin
        xyz="0.0417022225124919 -0.011129785844041 -0.0302676823147117"
        rpy="0 0 0" />
      <mass
        value="0.249491940759189" />
      <inertia
        ixx="0.000420299531109716"
        ixy="8.90343117595319E-07"
        ixz="1.95470099973506E-06"
        iyy="0.000271874433496591"
        iyz="-2.98244360474642E-05"
        izz="0.00024245572035315" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="1 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/right_arm_link02.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/right_arm_link02.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="xright_arm_joint02"
    type="revolute">
    <origin
      xyz="-0.0436499999999997 -0.0694997321821278 0"
      rpy="1.26 0 0" />
    <parent
      link="xright_arm_link01" />
    <child
      link="xright_arm_link02" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="xright_arm_link03">
    <inertial>
      <origin
        xyz="0.00537943014728004 0.00145778957913157 -0.0983684606571273"
        rpy="0 0 0" />
      <mass
        value="0.693570556157521" />
      <inertia
        ixx="0.00108265862687132"
        ixy="-2.47401859656579E-06"
        ixz="1.24195149252842E-05"
        iyy="0.00128723067425676"
        iyz="1.37448618531314E-05"
        izz="0.000596407369451779" />
    </inertial>
    <visual>
      <origin
        xyz="0. -0.14 0"
        rpy="0 1.57 0" />
      <geometry>
        <box size="0.1 0.33 0.08"/>
      </geometry>
      <!-- <origin
        xyz="0 0 0"
        rpy="-1 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/right_arm_link03.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material> -->
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/right_arm_link03.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="xright_arm_joint03"
    type="revolute">
    <origin
      xyz="0.0437324887300019 -0.0716172282089956 0.0167023554809989"
      rpy="0 0 0" />
    <parent
      link="xright_arm_link02" />
    <child
      link="xright_arm_link03" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="xright_arm_link04">
    <inertial>
      <origin
        xyz="-0.0145178188751376 -0.0360903291338528 -0.0303147127863168"
        rpy="0 0 0" />
      <mass
        value="0.187848339174813" />
      <inertia
        ixx="0.000118197270145918"
        ixy="-2.87201153479753E-06"
        ixz="-1.02004997556922E-05"
        iyy="0.000193678869164449"
        iyz="-4.20667327611963E-06"
        izz="0.000144067188180217" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="-1 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/right_arm_link04.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/right_arm_link04.STL" />
      </geometry>
    </collision> -->
    <!-- <collision>
      <origin
        xyz="0.04 -0.07 0"
        rpy="0 1.57 0" />
      <geometry>
        <box size="0.1 0.28 0.08"/>
      </geometry>
    </collision> -->
  </link>
  <joint
    name="xright_arm_joint04"
    type="revolute">
    <origin
      xyz="-0.0377000000000002 -0.186748041822978 -0.017419800874718"
      rpy="0 0 0" />
    <parent
      link="xright_arm_link03" />
    <child
      link="xright_arm_link04" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="xright_arm_link05">
    <inertial>
      <origin
        xyz="0.00163246495154556 -0.00012064661878225 -0.0978985526537954"
        rpy="0 0 0" />
      <mass
        value="0.912130938837617" />
      <inertia
        ixx="0.00240457954010546"
        ixy="5.18550275076368E-06"
        ixz="2.40542477194718E-05"
        iyy="0.00200757577749112"
        iyz="-9.81832775572871E-06"
        izz="0.000716095001957378" />
    </inertial>
    <visual>
      <origin
        xyz="0. -0.18 0"
        rpy="0 1.57 0" />
      <geometry>
        <box size="0.1 0.2 0.08"/>
      </geometry>
      <!-- <origin
        xyz="0 0 0"
        rpy="-1 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/right_arm_link05.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material> -->
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/right_arm_link05.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="xright_arm_joint05"
    type="revolute">
    <origin
      xyz="0.0377000000001358 -0.0589711214611882 0.0174143628949901"
      rpy="0 0 0" />
    <parent
      link="xright_arm_link04" />
    <child
      link="xright_arm_link05" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="xright_arm_link06">
    <inertial>
      <origin
        xyz="-0.0325720156805041 0.000999289754471042 -9.31528169441087E-05"
        rpy="0 0 0" />
      <mass
        value="0.467624265289788" />
      <inertia
        ixx="0.000132424626105876"
        ixy="1.42969492451583E-07"
        ixz="-3.54005743673942E-06"
        iyy="0.00025117779604613"
        iyz="7.50869087749932E-07"
        izz="0.000147846317755639" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="-1 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/right_arm_link06.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/right_arm_link06.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="xright_arm_joint06"
    type="revolute">
    <origin
      xyz="0 -0.215499434132 -0.0328955526690011"
      rpy="0 0 0" />
    <parent
      link="xright_arm_link05" />
    <child
      link="xright_arm_link06" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="xright_arm_link07">
    <inertial>
      <origin
        xyz="-0.000649836256861658 0.0320095871825944 -0.0913463435257631"
        rpy="0 0 0" />
      <mass
        value="0.421386763676359" />
      <inertia
        ixx="0.000457267230479397"
        ixy="6.94354045190022E-07"
        ixz="5.96382941619643E-07"
        iyy="0.000520830362869238"
        iyz="2.86287095749119E-05"
        izz="0.000206654306643201" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="-1 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/right_arm_link07.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/right_arm_link07.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="xright_arm_joint07"
    type="revolute">
    <origin
      xyz="-0.0390000000001423 0 0.0328999999999988"
      rpy="0 0 0" />
    <parent
      link="xright_arm_link06" />
    <child
      link="xright_arm_link07" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>

  <link
    name="waist_pitch_link">
    <inertial>
      <origin
        xyz="0.00164752359582665 -2.71420459579952E-07 -0.0144566387243632"
        rpy="0 0 0" />
      <mass
        value="0.0676057089302612" />
      <inertia
        ixx="8.92163776137529E-06"
        ixy="1.92235058255855E-10"
        ixz="3.4305545391069E-07"
        iyy="4.45741612898802E-05"
        iyz="2.95254017089921E-10"
        izz="4.28719068416202E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/waist_pitch_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/waist_pitch_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="waist_pitch_joint"
    type="revolute">
    <origin
      xyz="0.010347 -5.7324E-05 -0.28159"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="waist_pitch_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="waist_roll_link">
    <inertial>
      <origin
        xyz="0.00709263811487226 9.52016243616072E-15 -0.0102892340759588"
        rpy="0 0 0" />
      <mass
        value="0.257081977299114" />
      <inertia
        ixx="0.000148177659450841"
        ixy="2.78871431280619E-19"
        ixz="-3.12489251818429E-05"
        iyy="0.000499445718998584"
        iyz="5.53947688465851E-20"
        izz="0.000522790307432068" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/waist_roll_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/waist_roll_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="waist_roll_joint"
    type="revolute">
    <origin
      xyz="0 0 -0.017"
      rpy="0 0 0" />
    <parent
      link="waist_pitch_link" />
    <child
      link="waist_roll_link" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="waist_yaw_link">
    <inertial>
      <origin
        xyz="-0.0025031381732189 -1.57471168990009E-05 -0.117999339725699"
        rpy="0 0 0" />
      <mass
        value="2.60252549864933" />
      <inertia
        ixx="0.00920186649090081"
        ixy="-3.9067181904524E-07"
        ixz="3.48296601338555E-05"
        iyy="0.00736575206292818"
        iyz="1.9628937522287E-06"
        izz="0.00674896763072709" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/waist_yaw_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/waist_yaw_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="waist_yaw_joint"
    type="revolute">
    <origin
      xyz="0 0 -0.0246700474501687"
      rpy="0 0 0" />
    <parent
      link="waist_roll_link" />
    <child
      link="waist_yaw_link" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="leg_l1_link">
    <inertial>
      <origin
        xyz="-0.00881197939235434 0.0310888199196844 0.00238261720803923"
        rpy="0 0 0" />
      <mass
        value="0.37674012826592" />
      <inertia
        ixx="0.00125701997054514"
        ixy="6.51663823482127E-05"
        ixz="-7.89130064281747E-05"
        iyy="0.00193868077150748"
        iyz="-1.10459406305355E-05"
        izz="0.00136935723989124" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_l1_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_l1_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="leg_l1_joint"
    type="revolute">
    <origin
      xyz="0.0074999999999974 0.0906813368884017 -0.142967548137533"
      rpy="-0.349066110567974 0 0" />
    <parent
      link="waist_yaw_link" />
    <child
      link="leg_l1_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="leg_l2_link">
    <inertial>
      <origin
        xyz="0.0661873752685818 -0.00607784760220758 -0.0359050070899851"
        rpy="0 0 0" />
      <mass
        value="0.581142325874767" />
      <inertia
        ixx="0.00122410137872669"
        ixy="-6.41644436188587E-08"
        ixz="-1.65087074340261E-05"
        iyy="0.000988307077641917"
        iyz="2.51900469386748E-08"
        izz="0.00129433977025134" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_l2_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_l2_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="leg_l2_joint"
    type="revolute">
    <origin
      xyz="-0.0660000000000009 0.063117683820026 -0.0251075228082858"
      rpy="0.349065850398876 0 0" />
    <parent
      link="leg_l1_link" />
    <child
      link="leg_l2_link" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="leg_l3_link">
    <inertial>
      <origin
        xyz="0.00397812601315528 -0.00344221912675036 -0.150161437404954"
        rpy="0 0 0" />
      <mass
        value="1.53376040095287" />
      <inertia
        ixx="0.0109714455212212"
        ixy="1.0054224594304E-05"
        ixz="-0.00021822990962813"
        iyy="0.0138253353130918"
        iyz="2.07027853125766E-05"
        izz="0.00339975528350425" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_l3_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_l3_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="leg_l3_joint"
    type="revolute">
    <origin
      xyz="0.0659999798892595 -0.00844115013032576 -0.0600612942702563"
      rpy="0 0 0" />
    <parent
      link="leg_l2_link" />
    <child
      link="leg_l3_link" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="leg_l4_link">
    <inertial>
      <origin
        xyz="0.0313402478257421 0.000706725888048043 -0.150120261712289"
        rpy="0 0 0" />
      <mass
        value="1.17497558131661" />
      <inertia
        ixx="0.00997971716452319"
        ixy="-3.33132726675507E-08"
        ixz="0.000712906953883044"
        iyy="0.0107264961040143"
        iyz="4.64206836963881E-08"
        izz="0.000969157457319042" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_l4_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_l4_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="leg_l4_joint"
    type="revolute">
    <origin
      xyz="-0.0359999999999872 0 -0.32199999999999"
      rpy="0 0 0" />
    <parent
      link="leg_l3_link" />
    <child
      link="leg_l4_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="leg_l5_link">
    <inertial>
      <origin
        xyz="-0.00440858083783857 -3.02581244449041E-07 -0.0140922740058249"
        rpy="0 0 0" />
      <mass
        value="0.0390613963007136" />
      <inertia
        ixx="6.69967001155501E-06"
        ixy="-2.3679048723423E-11"
        ixz="5.57730781248892E-09"
        iyy="9.99510425674673E-06"
        iyz="1.77170764827426E-10"
        izz="8.62664545276797E-06" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_l5_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_l5_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="leg_l5_joint"
    type="revolute">
    <origin
      xyz="0.0394999999999666 0 -0.350000000000004"
      rpy="0 0 0" />
    <parent
      link="leg_l4_link" />
    <child
      link="leg_l5_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="leg_l6_link">
    <inertial>
      <origin
        xyz="0.0410771437364842 5.50301071089443E-06 -0.0664920169559622"
        rpy="0 0 0" />
      <mass
        value="0.824826214328889" />
      <inertia
        ixx="0.000793299777948151"
        ixy="-2.80575081381854E-07"
        ixz="0.00026889436693409"
        iyy="0.0046887884773245"
        iyz="-2.12287185217214E-07"
        izz="0.00486503144882934" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../unsimplified_raw_foot/leg_l6_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shape_000.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shape_001.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shape_002.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shape_003.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shape_004.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shape_005.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shape_006.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shapeback.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shapefront.stl" />
      </geometry>
    </collision>
  </link>
  <joint
    name="leg_l6_joint"
    type="revolute">
    <origin
      xyz="0 0 -0.0170000000000003"
      rpy="0 0 0" />
    <parent
      link="leg_l5_link" />
    <child
      link="leg_l6_link" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="leg_r1_link">
    <inertial>
      <origin
        xyz="-0.00791888180048095 -0.0339203080584578 0.000172948552299879"
        rpy="0 0 0" />
      <mass
        value="0.419695013669055" />
      <inertia
        ixx="0.00133017590151"
        ixy="-6.50091270267743E-05"
        ixz="-7.93073762021797E-05"
        iyy="0.00197776732684508"
        iyz="3.59001022019661E-06"
        izz="0.00140507613043598" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_r1_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_r1_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="leg_r1_joint"
    type="revolute">
    <origin
      xyz="0.0074999999999974 -0.0906813368884098 -0.142967548137531"
      rpy="0.349065850398856 0 0" />
    <parent
      link="waist_yaw_link" />
    <child
      link="leg_r1_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="leg_r2_link">
    <inertial>
      <origin
        xyz="0.0661831172379139 0.00607784760220758 -0.035904464696773"
        rpy="0 0 0" />
      <mass
        value="0.290575848013011" />
      <inertia
        ixx="0.000611999716696905"
        ixy="2.55599157881043E-06"
        ixz="-8.24507302511124E-06"
        iyy="0.000494168819154878"
        iyz="1.50804875116531E-05"
        izz="0.000647130770961394" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_r2_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_r2_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="leg_r2_joint"
    type="revolute">
    <origin
      xyz="-0.0659999999997311 -0.0631176772877871 -0.0251075392296158"
      rpy="-0.34906559022975 0 0" />
    <parent
      link="leg_r1_link" />
    <child
      link="leg_r2_link" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="leg_r3_link">
    <inertial>
      <origin
        xyz="0.00397832598572601 -0.00344230892624537 -0.150160878298536"
        rpy="0 0 0" />
      <mass
        value="1.53376549991826" />
      <inertia
        ixx="0.0109713148123617"
        ixy="1.00513570771074E-05"
        ixz="-0.000218282343881671"
        iyy="0.0138252321237501"
        iyz="2.06856640190975E-05"
        izz="0.00339978292739893" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_r3_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_r3_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="leg_r3_joint"
    type="revolute">
    <origin
      xyz="0.0659999798890882 0.0084411480489144 -0.0600612942701639"
      rpy="0 0 0" />
    <parent
      link="leg_r2_link" />
    <child
      link="leg_r3_link" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="leg_r4_link">
    <inertial>
      <origin
        xyz="0.0313402515501011 0.000706733309919612 -0.150120270688457"
        rpy="0 0 0" />
      <mass
        value="1.17497525674028" />
      <inertia
        ixx="0.00997971549985881"
        ixy="-3.33662405928856E-08"
        ixz="0.000712905970266083"
        iyy="0.0107264937222823"
        iyz="4.66230798862335E-08"
        izz="0.000969157222609313" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_r4_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_r4_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="leg_r4_joint"
    type="revolute">
    <origin
      xyz="-0.0359999999982118 0 -0.322000000000098"
      rpy="0 0 0" />
    <parent
      link="leg_r3_link" />
    <child
      link="leg_r4_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="leg_r5_link">
    <inertial>
      <origin
        xyz="-0.00442058622008357 -3.02581208061481E-07 -0.0140885126656367"
        rpy="0 0 0" />
      <mass
        value="0.0390613963007132" />
      <inertia
        ixx="6.69968091445721E-06"
        ixy="-2.35280860339265E-11"
        ixz="7.2191321038376E-09"
        iyy="9.99510425674681E-06"
        iyz="1.77190875661393E-10"
        izz="8.62663454986572E-06" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_r5_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes_v1_simp/leg_r5_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="leg_r5_joint"
    type="revolute">
    <origin
      xyz="0.0395000000000177 0 -0.350000000000029"
      rpy="0 0 0" />
    <parent
      link="leg_r4_link" />
    <child
      link="leg_r5_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
  <link
    name="leg_r6_link">
    <inertial>
      <origin
        xyz="0.0405771625633287 5.51262344199166E-06 -0.0664920331021337"
        rpy="0 0 0" />
      <mass
        value="0.824826792609758" />
      <inertia
        ixx="0.000793300337619073"
        ixy="-2.80712732499596E-07"
        ixz="0.000268894502560353"
        iyy="0.00468878930816137"
        iyz="-2.12131524975969E-07"
        izz="0.00486503249192508" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../unsimplified_raw_foot/leg_r6_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shape_000.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shape_001.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shape_002.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shape_003.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shape_004.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shape_005.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shape_006.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shapeback.stl" />
      </geometry>
    </collision>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes_v1_simp/modify_feet_shapefront.stl" />
      </geometry>
    </collision>
  </link>
  <joint
    name="leg_r6_joint"
    type="revolute">
    <origin
      xyz="0 0 -0.0169999999999999"
      rpy="0 0 0" />
    <parent
      link="leg_r5_link" />
    <child
      link="leg_r6_link" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-3"
      upper="3"
      effort="1000"
      velocity="100" />
  </joint>
</robot>