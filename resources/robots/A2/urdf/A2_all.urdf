<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>)
     Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
     
<robot name="A2">
  <mujoco>
    <compiler meshdir="../meshes/" balanceinertia="true" discardvisual="false"/>
  </mujoco>

  <link name="base_link">
    <inertial>
      <origin xyz="-0.07245655 -0.00043996 0.16664738" rpy="0 0 0" />
      <mass value="17.71410976" />
      <inertia ixx="0.56785982" iyy="0.52650076" izz="0.17272833" ixy="0.00042996" ixz="0.00807567" iyz="-0.00000077"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>  
        <mesh filename="../meshes/body.STL" />
      </geometry>
      <material name="">
        <color rgba="0.8 0.4 0 1.0" />
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="../meshes/body.STL"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision>
  </link>

  <!-- left leg -->
  <link name="leg_l1_link">
    <inertial>
      <origin xyz="0.00011252 0.02130615 -0.07465143" rpy="0 0 0" />
      <mass value="2.39059365" />
      <inertia ixx="0.00381223" iyy="0.00368166" izz="0.00334373" ixy="-0.00000253" ixz="0.00000433" iyz="0.00019809"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_leg_hip_roll.STL" />
      </geometry>
      <material name="">
        <color rgba="1 0.4 0.4 1" />
      </material>
    </visual>
  </link>
  <joint name="leg_l1_joint" type="revolute">
    <origin xyz="-0.0808 0.105 -0.0652" rpy="0.7854 0 1.5708" />
    <parent link="base_link" />
    <child link="leg_l1_link" />
    <axis xyz="0 0 1" />
    <!-- <limit lower="-0.698" upper="0.698" effort="120" velocity="12" /> -->
    <limit lower="-0.698" upper="0.698" effort="1000" velocity="20" />
  </joint>

  <link name="leg_l2_link">
    <inertial>
      <origin xyz="-0.00012288 0.01576460 0.10828703" rpy="0 0 0" />
      <mass value="3.79911511" />
      <inertia ixx="0.00941292" iyy="0.01442888" izz="0.00698406" ixy="0.00000766" ixz="0.00000238" iyz="-0.00055185"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_leg_hip_yaw.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 0.4 1" />
      </material>
    </visual>
  </link>
  <joint name="leg_l2_joint" type="revolute">
    <origin xyz="0 -0.017 -0.0682" rpy="1.5708 -1.5708 0" />
    <parent link="leg_l1_link" />
    <child link="leg_l2_link" />
    <axis xyz="0 0 1" />
    <!-- <limit lower="-1.57" upper="1.57" effort="120" velocity="12" /> -->
    <limit lower="-1.57" upper="1.57" effort="1000" velocity="20" />
  </joint>

  <link name="leg_l3_link">
    <inertial>
      <origin xyz="-0.05844086 0.00492907 0.02304337" rpy="0 0 0" />
      <mass value="5.30562514" />
      <inertia ixx="0.01185844" iyy="0.07032698" izz="0.07514457" ixy="0.00347724" ixz="-0.00657871" iyz="0.00060915"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_leg_hip_pitch.STL" />
      </geometry>
      <material name="">
        <color rgba="0.4 1 0.4 1" />
      </material>
    </visual>
  </link>
  <joint name="leg_l3_joint" type="revolute">
    <origin xyz="0 -0.020 0.1205" rpy="1.5708 0.7854 0" />
    <parent link="leg_l2_link" />
    <child link="leg_l3_link" />
    <axis xyz="0 0 1" />
    <!-- <limit lower="-1.919862144" upper="0.78539815" effort="140" velocity="12" /> -->
    <limit lower="-1.919862144" upper="0.78539815" effort="1000" velocity="20" />
  </joint>

  <link name="leg_l4_link">
    <inertial>
      <origin xyz="-0.17686628 0.00290708 -0.00002416" rpy="0 0 0" />
      <mass value="2.78469232" />
      <inertia ixx="0.00224858" iyy="0.02247360" izz="0.02359583" ixy="-0.00064522" ixz="0.00034901" iyz="-0.00000111"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_leg_knee.STL" />
      </geometry>
      <material name="">
        <color rgba="0 0.5 1 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="-0.18 0 0"/>
      <geometry>
        <box size="0.28 0.12 0.08"/>
      </geometry>
    </collision>
  </link>
  <joint name="leg_l4_joint" type="revolute">
    <origin xyz="-0.34 0 0" rpy="0 0 0" />
    <parent link="leg_l3_link" />
    <child link="leg_l4_link" />
    <axis xyz="0 0 1" />
    <!-- <limit lower="-0.087266461" upper="2.443460911" effort="140" velocity="12" /> -->
    <limit lower="-1.0" upper="2.443460911" effort="1000" velocity="20" />
  </joint>

  <link name="leg_l5_link">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <mass value="0.08772028" />
      <inertia ixx="0.00001572" iyy="0.00001128" izz="0.00001148" ixy="0.0" ixz="0.0" iyz="0.0"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_leg_toe_pitch.STL" />
      </geometry>
      <material name="">
        <color rgba="0.8 1.0 0.8 1" />
      </material>
    </visual>
  </link>
  <joint name="leg_l5_joint" type="revolute">
    <origin xyz="-0.377 0 0" rpy="0 0 0" />
    <parent link="leg_l4_link" />
    <child link="leg_l5_link" />
    <axis xyz="0 0 1" />
    <!-- <limit lower="-1.047197533" upper="0.523598767" effort="50" velocity="12" /> -->
    <limit lower="-1.047197533" upper="1.0" effort="1000" velocity="20" />
  </joint>

  <link name="leg_l6_link">
    <inertial>
      <origin xyz="-0.04183694 0.00003333 0.02612972" rpy="0 0 0" />
      <mass value="0.90432039" />
      <inertia ixx="0.00532431" iyy="0.00494869" izz="0.00073321" ixy="0.00000010" ixz="0.00024374" iyz="-0.00000302"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_leg_foot.STL" />
      </geometry>
      <material name="">
        <color rgba="1 0.5 1 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin rpy="0.0 0.0 0.0" xyz="-0.05 0.0 0.05"/>
      <geometry>
        <box size="0.02 0.11 0.25"/>
      </geometry>
    </collision>  -->

    <collision>
      <origin rpy="0.0 0.0 0.0" xyz="-0.0 0.0 0.0"/>
      <geometry>
        <mesh filename="../meshes/left_leg_foot.STL" />
      </geometry>
    </collision> 
  </link>
  <joint name="leg_l6_joint" type="revolute">
    <origin xyz="0 0 0" rpy="-1.5707963 0 0" />
    <parent link="leg_l5_link" />
    <child link="leg_l6_link" />
    <axis xyz="0 0 1" />
    <!-- <limit lower="-0.488692182" upper="0.488692182" effort="50" velocity="12" /> -->
    <limit lower="-1.0" upper="1.0" effort="1000" velocity="20" />
  </joint>
  
  <!-- left foot site-->
  <!-- <link name="left_foot_site">
    <visual>
      <origin rpy = "0 0 0" xyz = "0 0 0" />
      <geometry>
        <sphere radius = "0.02" />
      </geometry>
      <material name = "">
        <color rgba = "1 0 0 1" />
        <texture filename = "" />
      </material>
    </visual>
  </link>
  <joint name="left_foot_site_fixed" type="fixed">
    <origin xyz="-0.059 0 0.04" rpy="3.1415926 -1.5707963 0" />
    <parent link="leg_l6_link" />
    <child link="left_foot_site" />
  </joint>

  <joint name = "joint_left_foot_toe_left" type = "fixed">
    <origin rpy = "0 0 0" xyz = "0.08 0.05 0" />
    <parent link = "left_foot_site" />
    <child link = "left_foot_toe_left" />
  </joint>
  <link name = "left_foot_toe_left">
    <visual>
        <origin rpy = "0 0 0" xyz = "0 0 0" />
        <geometry>
          <sphere radius = "0.02" />
        </geometry>
        <material name = "">
          <color rgba = "1 0 0 1" />
          <texture filename = "" />
        </material>
    </visual>
  </link>
  <joint name = "joint_left_foot_toe_right" type = "fixed">
      <origin rpy = "0 0 0" xyz = "0.08 -0.05 0" />
      <parent link = "left_foot_site" />
      <child link = "left_foot_toe_right" />
  </joint>
  <link name = "left_foot_toe_right">
      <visual>
          <origin rpy = "0 0 0" xyz = "0 0 0" />
          <geometry>
            <sphere radius = "0.02" />
          </geometry>
          <material name = "">
            <color rgba = "1 0 0 1" />
            <texture filename = "" />
          </material>
      </visual>
  </link>
  <joint name = "joint_left_foot_heel_left" type = "fixed">
    <origin rpy = "0 0 0" xyz = "-0.11 0.05 0" />
    <parent link = "left_foot_site" />
    <child link = "left_foot_heel_left" />
  </joint>
  <link name = "left_foot_heel_left">
      <visual>
          <origin rpy = "0 0 0" xyz = "0 0 0" />
          <geometry>
            <sphere radius = "0.02" />
          </geometry>
          <material name = "">
            <color rgba = "1 0 0 1" />
            <texture filename = "" />
          </material>
      </visual>
  </link>
  <joint name = "joint_left_foot_heel_right" type = "fixed">
      <origin rpy = "0 0 0" xyz = "-0.11 -0.05 0" />
      <parent link = "left_foot_site" />
      <child link = "left_foot_heel_right" />
  </joint>
  <link name = "left_foot_heel_right">
      <visual>
          <origin rpy = "0 0 0" xyz = "0 0 0" />
          <geometry>
            <sphere radius = "0.02" />
          </geometry>
          <material name = "">
            <color rgba = "1 0 0 1" />
            <texture filename = "" />
          </material>
      </visual>
  </link> -->
<!-- left foot site-->


  <!-- right leg -->
  <link name="leg_r1_link">
    <inertial>
      <origin xyz="-0.00011252 0.02130615 -0.07465143" rpy="0 0 0" />
      <mass value="2.39059365" />
      <inertia ixx="0.00381223" iyy="0.00368166" izz="0.00334373" ixy="0.00000253" ixz="-0.00000433" iyz="0.00019809"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_leg_hip_roll.STL" scale = '-1 1 1' />
      </geometry>
      <material name="">
        <color rgba="1 0.4 0.4 1" />
      </material>
    </visual>
  </link>
  <joint name="leg_r1_joint" type="revolute">
    <origin xyz="-0.0808 -0.105 -0.0652" rpy="0.7854 0 1.5708" />
    <parent link="base_link" />
    <child link="leg_r1_link" />
    <axis xyz="0 0 1" />
    <!-- <limit lower="-0.698" upper="0.698" effort="120" velocity="12" /> -->
    <limit lower="-0.698" upper="0.698" effort="1000" velocity="20" />
  </joint>

  <link name="leg_r2_link">
    <inertial>
      <origin xyz="-0.00012288 -0.01576460 0.10828703" rpy="0 0 0" />
      <mass value="3.79911511" />
      <inertia ixx="0.00941292" iyy="0.01442888" izz="0.00698406" ixy="-0.00000766" ixz="0.00000238" iyz="0.00055185"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_leg_hip_yaw.STL" scale = '1 -1 1' />
      </geometry>
      <material name="">
        <color rgba="1 1 0.4 1" />
      </material>
    </visual>
  </link>
  <joint name="leg_r2_joint" type="revolute">
    <origin xyz="0 -0.017 -0.0682" rpy="1.5708 -1.5708 0" />
    <parent link="leg_r1_link" />
    <child link="leg_r2_link" />
    <axis xyz="0 0 1" />
    <!-- <limit lower="-1.57" upper="1.57" effort="120" velocity="12" /> -->
    <limit lower="-1.57" upper="1.57" effort="1000" velocity="20" />
  </joint>

  <link name="leg_r3_link">
    <inertial>
      <origin xyz="-0.05844086 0.00492907 -0.02304337" rpy="0 0 0" />
      <mass value="5.30562514" />
      <inertia ixx="0.01185844" iyy="0.07032698" izz="0.07514457" ixy="0.00347724" ixz="0.00657871" iyz="-0.00060915"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_leg_hip_pitch.STL" scale = '1 1 -1' />
      </geometry>
      <material name="">
        <color rgba="0.4 1 0.4 1" />
      </material>
    </visual>
  </link>
  <joint name="leg_r3_joint" type="revolute">
    <origin xyz="0 0.020 0.1205" rpy="1.5708 0.7854 0" />
    <parent link="leg_r2_link" />
    <child link="leg_r3_link" />
    <axis xyz="0 0 1" />
    <!-- <limit lower="-1.919862144" upper="0.78539815" effort="140" velocity="12" /> -->
    <limit lower="-1.919862144" upper="0.78539815" effort="1000" velocity="20" />
  </joint>

  <link name="leg_r4_link">
    <inertial>
      <origin xyz="-0.17686628 0.00290708 0.00002416" rpy="0 0 0" />
      <mass value="2.78469232" />
      <inertia ixx="0.00224858" iyy="0.02247360" izz="0.02359583" ixy="-0.00064522" ixz="-0.00034901" iyz="0.00000111"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_leg_knee.STL" scale = '1 1 -1' />
      </geometry>
      <material name="">
        <color rgba="0 0.5 1 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="-0.18 0 0"/>
      <geometry>
        <box size="0.28 0.12 0.08"/>
      </geometry>
    </collision>
  </link>
  <joint name="leg_r4_joint" type="revolute">
    <origin xyz="-0.34 0 0" rpy="0 0 0" />
    <parent link="leg_r3_link" />
    <child link="leg_r4_link" />
    <axis xyz="0 0 1" />
    <!-- <limit lower="-0.087266461" upper="2.443460911" effort="140" velocity="12" /> -->
    <limit lower="-1.0" upper="2.443460911" effort="1000" velocity="20" />
  </joint>

  <link name="leg_r5_link">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <mass value="0.08772028" />
      <inertia ixx="0.00001572" iyy="0.00001128" izz="0.00001148" ixy="0.0" ixz="0.0" iyz="0.0"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_leg_toe_pitch.STL" scale = '1 1 -1'/>
      </geometry>
      <material name="">
        <color rgba="0.8 1.0 0.8 1" />
      </material>
    </visual>
  </link>
  <joint name="leg_r5_joint" type="revolute">
    <origin xyz="-0.377 0 0" rpy="0 0 0" />
    <parent link="leg_r4_link" />
    <child link="leg_r5_link" />
    <axis xyz="0 0 1" />
    <!-- <limit lower="-1.047197533" upper="0.523598767" effort="50" velocity="12" /> -->
    <limit lower="-1.047197533" upper="1.0" effort="1000" velocity="20" />
  </joint>

  <link name="leg_r6_link">
    <inertial>
      <origin xyz="-0.04183694 -0.00003333 0.02612972" rpy="0 0 0" />
      <mass value="0.90432039" />
      <inertia ixx="0.00532431" iyy="0.00494869" izz="0.00073321" ixy="-0.00000010" ixz="0.00024374" iyz="0.00000302"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/right_leg_foot.STL" scale = '1 -1 1' />
      </geometry>
      <material name="">
        <color rgba="1 0.5 1 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin rpy="0.0 0.0 0.0" xyz="-0.05 0.0 0.05"/>
      <geometry>
        <box size="0.02 0.11 0.25"/>
      </geometry>
    </collision>  -->

    <collision>
      <origin rpy="0.0 0.0 0.0" xyz="-0.0 0.0 0.0"/>
      <geometry>
        <mesh filename="../meshes/right_leg_foot.STL" scale = '1 -1 1' />
      </geometry>
    </collision> 
  </link>
  <joint name="leg_r6_joint" type="revolute">
    <origin xyz="0 0 0" rpy="-1.5707963 0 0" />
    <parent link="leg_r5_link" />
    <child link="leg_r6_link" />
    <axis xyz="0 0 1" />
    <!-- <limit lower="-0.488692182" upper="0.488692182" effort="50" velocity="12" /> -->
    <limit lower="-1.0" upper="1.0" effort="1000" velocity="20" />
  </joint>


  <!-- right foot site -->
  <!-- <link name="right_foot_site">
    <visual>
      <origin rpy = "0 0 0" xyz = "0 0 0" />
      <geometry>
        <sphere radius = "0.02" />
      </geometry>
      <material name = "">
        <color rgba = "1 0 0 1" />
        <texture filename = "" />
      </material>
    </visual>
  </link>
  <joint name="right_foot_site_fixed" type="fixed">
    <origin xyz="-0.059 0 0.04" rpy="3.1415926 -1.5707963 0" />
    <parent link="leg_r6_link" />
    <child link="right_foot_site" />
  </joint>

  <joint name = "joint_right_foot_toe_left" type = "fixed">
    <origin rpy = "0 0 0" xyz = "0.08 0.05 0" />
    <parent link = "right_foot_site" />
    <child link = "right_foot_toe_left" />
  </joint>
  <link name = "right_foot_toe_left">
    <visual>
        <origin rpy = "0 0 0" xyz = "0 0 0" />
        <geometry>
          <sphere radius = "0.02" />
        </geometry>
        <material name = "">
          <color rgba = "1 0 0 1" />
          <texture filename = "" />
        </material>
    </visual>
  </link>
  <joint name = "joint_right_foot_toe_right" type = "fixed">
    <origin rpy = "0 0 0" xyz = "0.08 -0.05 0" />
    <parent link = "right_foot_site" />
    <child link = "right_foot_toe_right" />
  </joint>
  <link name = "right_foot_toe_right">
    <visual>
        <origin rpy = "0 0 0" xyz = "0 0 0" />
        <geometry>
          <sphere radius = "0.02" />
        </geometry>
        <material name = "">
          <color rgba = "1 0 0 1" />
          <texture filename = "" />
        </material>
    </visual>
  </link>
  <joint name = "joint_right_foot_heel_left" type = "fixed">
    <origin rpy = "0 0 0" xyz = "-0.11 0.05 0" />
    <parent link = "right_foot_site" />
    <child link = "right_foot_heel_left" />
  </joint>
  <link name = "right_foot_heel_left">
      <visual>
        <origin rpy = "0 0 0" xyz = "0 0 0" />
        <geometry>
          <sphere radius = "0.02" />
        </geometry>
        <material name = "">
          <color rgba = "1 0 0 1" />
          <texture filename = "" />
        </material>
      </visual>
  </link>
  <joint name = "joint_right_foot_heel_right" type = "fixed">
    <origin rpy = "0 0 0" xyz = "-0.11 -0.05 0" />
    <parent link = "right_foot_site" />
    <child link = "right_foot_heel_right" />
  </joint>
  <link name = "right_foot_heel_right">
    <visual>
        <origin rpy = "0 0 0" xyz = "0 0 0" />
        <geometry>
          <sphere radius = "0.02" />
        </geometry>
        <material name = "">
          <color rgba = "1 0 0 1" />
          <texture filename = "" />
        </material>
    </visual>
  </link>   -->
  <!-- right foot site -->


  <!-- left arm -->
  <link name="left_arm_link_base">
  </link>
  <joint name="left_arm_base_joint" type="fixed">
    <origin rpy="1.5707963  0 0" xyz="-0.03779 0 0.3421"/>
    <parent link="base_link"/>
    <child link="left_arm_link_base"/>
  </joint>

  <link name="left_arm_link01">
    <inertial>
      <origin xyz="0.00000249 0.01038065 0.03761745" rpy="0 0 0" />
      <mass value="0.23884197" />
      <inertia ixx="0.00044830" iyy="0.00024895" izz="0.00040350" ixy="0.00000000" ixz="0.00000000" iyz="0.00006606"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_arm_link01.STL" scale="1 1 1" />
      </geometry>
      <material name="">
        <color rgba="1 0.4 0.4 1" />
      </material>
    </visual>
  </link>

  <joint name="uidx13_left_arm_joint1" type="revolute">
    <origin xyz="-0.0 0.0 -0.191 " rpy="0   0  -1.5707963" />
    <parent link="left_arm_link_base" />
    <child link="left_arm_link01" />
    <axis xyz="0 0 1" />
    <limit lower="-2.967" upper="2.967" effort="50" velocity="20" />
  </joint>


  <link name="left_arm_link02">
    <inertial>
      <origin xyz="-0.00003961 -0.05209875 -0.00115698" rpy="0 0 0" />
      <mass value="3.09638867" />
      <inertia ixx="0.01280293" iyy="0.00266575" izz="0.01264891" ixy="-0.00000268" ixz="0.00000182" iyz="0.00017645"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_arm_link02.STL"  scale="1 1 1"/>
      </geometry>
      <material name="">
        <color rgba="1 1 0.4 1" />
      </material>
    </visual>
  </link>
  <joint name="uidx14_left_arm_joint2" type="revolute">
    <origin xyz="0 0 -0.0" rpy="1.5707963 0 0"/>
    <parent link="left_arm_link01" />
    <child link="left_arm_link02" />
    <axis xyz="0 0 1" />
    <limit lower="-0.523598767" upper="1.658062761" effort="50" velocity="20" />
  </joint>

  <link name="left_arm_link03">
    <inertial>
      <origin xyz="0.00000225 0.00035500 0.08376422" rpy="0 0 0" />
      <mass value="1.37452988" />
      <inertia ixx="0.00163610" iyy="0.00181828" izz="0.00109760" ixy="0.00000081" ixz="-0.00000378" iyz="0.00001043"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_arm_link03.STL"  scale="1 1 1"/>
      </geometry>
      <material name="">
        <color rgba="0.4 1 0.4 1" />
      </material>
    </visual>
  </link>
  <joint name="uidx15_left_arm_joint3" type="fixed">
    <origin  rpy="1.5707963 -0 0" xyz="0 -0.154 0" />
    <parent link="left_arm_link02" />
    <child link="left_arm_link03" />
    <axis xyz="0 0 1" />
    <limit lower="-2.967" upper="2.967" effort="50" velocity="20" />
  </joint>


  <link name="left_arm_link04">
    <inertial>
      <origin xyz="-0.00002118 -0.07109905 -0.00481815" rpy="0 0 0" />
      <mass value="0.70457441" />
     <inertia ixx="0.00064706" iyy="0.00037557" izz="0.00056821" ixy="0.00000321" ixz="-0.00000120" iyz="0.00014782"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_arm_link04.STL"  scale="1 1 1"/>
      </geometry>
      <material name="">
        <color rgba="0.4 1 1 1" />
      </material>
    </visual>
  </link>
  <joint name="uidx16_left_arm_joint4" type="revolute">
    <origin xyz="0 0 0.093" rpy="-1.5707963  0  1.5707963" />
    <parent link="left_arm_link03" />
    <child link="left_arm_link04" />
    <axis xyz="0 0 1" />
    <limit lower="-2.094395067" upper="0" effort="50" velocity="20" />
  </joint>

  <link name = "left_arm_link05">
    <inertial>
      <origin xyz="-0.00221884 0.00000000 0.06554799" rpy="0 0 0" />
      <mass value="0.12244729" />
      <inertia ixx="0.00031756" iyy="0.00033014" izz="0.00003000" ixy="0.00000000" ixz="0.00002042" iyz="0.00000000"/>
    </inertial>
    <visual name = "">
      <origin rpy = "0.0 0.0 0.0" xyz = "0.0 0.0 0.0" />
      <geometry>
        <mesh filename = "../meshes/left_arm_link05.STL"  scale="1 1 1"/>
      </geometry>
      <material name = "">
        <color rgba = "0 0.5 1 1.0" />
        <texture filename = "" />
      </material>
    </visual>
  </link>
  <joint name = "uidx17_left_arm_joint5" type = "fixed">
    <origin xyz = "0  -0.103 0.0" rpy = "1.5707963 -1.57 0"  />
    <parent link = "left_arm_link04" />
    <child link = "left_arm_link05" />
    <axis xyz = "0 0 1" />
    <limit lower = "-2.87979" upper = "2.87979" velocity = "0.0" effort = "0"/>
  </joint>


  <link name = "left_arm_link06">
    <inertial>
      <origin xyz="0.00018011 -0.00021701 0.00000000" rpy="0 0 0" />
      <mass value="0.02504627" />
      <inertia ixx="0.00000197" iyy="0.00000173" izz="0.00000071" ixy="0.00000000" ixz="0.00000000" iyz="0.00000000"/>
    </inertial>
    <visual name = "">
      <origin rpy = "0.0 0.0 0.0" xyz = "0.0 0.0 0.0" />
      <geometry>
        <mesh filename="../meshes/left_arm_link06.STL"  scale="1 1 1"/>
      </geometry>
      <material name = "">
        <color rgba = "0.8 1.0 0.8 1" />
        <texture filename = "" />
      </material>
    </visual>
  </link>
   <joint name = "uidx18_left_arm_joint6" type = "fixed">
    <origin xyz="-0.01 0 0.1595" rpy="-1.5707963 1.5707963 0" />
    <parent link = "left_arm_link05" />
    <child link = "left_arm_link06" />
    <axis xyz = "0 0 1" />
    <limit lower = "-0.785398" upper = "0.785398" velocity = "0.0" effort = "0" />
  </joint>

  <link name = "left_arm_link07">
    <inertial>
      <origin xyz="-0.01211757 -0.00009712 0.00886324" rpy="0 0 0" />
      <mass value="0.05050219" />
      <inertia ixx="0.00000828" iyy="0.00001140" izz="0.00001294" ixy="0.00000006" ixz="-0.00000179" iyz="0.00000004"/>
    </inertial>
    <visual name = "">
      <origin rpy = "0.0 0.0 0.0" xyz = "0.0 0.0 0.0" />
      <geometry>
        <mesh filename="../meshes/left_arm_link07.STL"  scale="1 1 1"/>
      </geometry>
      <material name = "">
        <color rgba = "1 0.5 1 1" />
        <texture filename = "" />
      </material>
    </visual>
  </link>
  <joint name = "uidx19_left_arm_joint7" type = "fixed">
    <origin xyz="0 0 0" rpy="1.5707963 0 0" />
    <parent link = "left_arm_link06" />
    <child link = "left_arm_link07" />
    <axis xyz = "0 0 1" />
    <limit lower="-0.5236" upper="0.5236" effort="50" velocity="20" />
  </joint>

  <joint name = "left_arm_joint07_fixed" type = "fixed">
    <origin rpy = "0 0 0" xyz = "0 0 0" />
    <parent link = "left_arm_link07" />
    <child link = "left_arm_end" />
  </joint>
  <link name = "left_arm_end">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <sphere radius = "0.02" />
      </geometry>
      <material name="">
        <color rgba="1 0.5 1 1" />
      </material>
    </visual>
  </link>


  <joint name="left_wrist_motor_A_ball" type="fixed">
    <origin xyz="0.005 0.02245 0.0245" rpy="0 -3.126778944 0" />
    <parent link="left_arm_link05" />
    <child link="left_wrist_motor_A" />
  </joint>
  <link name="left_wrist_motor_A">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_arm_wrist_motor_A.STL" />
      </geometry>
      <material name="">
        <color rgba="1 0.4 0.4 1" />
      </material>
    </visual>
  </link>

  <!-- <joint name="left_wrist_rod_A_joint" type="fixed">
    <origin xyz="0 0 0.135015" rpy="0 0 0" />
    <parent link="left_wrist_motor_A" />
    <child link="left_wrist_rod_A" />
    <axis xyz="0 0 1" />
    <limit lower="-0.01199" upper="0.01204" effort="50" velocity="20" />
  </joint>
  <link name="left_wrist_rod_A">
    <visual>
      <origin xyz="0.00000369429970323472 0.00000000037888964188 -0.00627793227181139" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/WRIST-LA-rod.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 0.4 1" />
      </material>
    </visual>
  </link> -->

  <joint name="left_wrist_motor_B_ball" type="fixed">
    <origin xyz="0.005 -0.02245 0.0245" rpy="0 -3.126778944 0" />
    <parent link="left_arm_link05" />
    <child link="left_wrist_motor_B" />
  </joint>
  <link name="left_wrist_motor_B">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_arm_wrist_motor_A.STL" />
      </geometry>
      <material name="">
        <color rgba="1 0.4 0.4 1" />
      </material>
    </visual>
  </link>

  <!-- <joint name="left_wrist_rod_B_joint" type="fixed">
    <origin xyz="0 0 0.135015" rpy="0 0 0" />
    <parent link="left_wrist_motor_B" />
    <child link="left_wrist_rod_B" />
    <axis xyz="0 0 1" />
    <limit lower="-0.01199" upper="0.01204" effort="50" velocity="20" />
  </joint>
  <link name="left_wrist_rod_B">
    <visual>
      <origin xyz="-0.00000369429544130537 0.00000000014097240042 -0.00627793206999139" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/WRIST-LA-rod.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 0.4 1" />
      </material>
    </visual>
  </link> -->

  <!-- <joint name = "left_arm_joint07_fixed" type = "fixed">
    <origin rpy = "0 0 0" xyz = "0 0 0" />
    <parent link = "left_arm_link07" />
    <child link = "left_arm_end" />
  </joint>
  <link name = "left_arm_end">
    <inertial>
      <mass value = "0.0001" />
      <inertia ixx = "1e-07" ixy = "0.0" ixz = "0.0" iyy = "1e-07" iyz = "0.0" izz = "1e-07" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_hand.STL" />
      </geometry>
      <material name="">
        <color rgba="1 0.5 1 1" />
      </material>
    </visual>
  </link> -->

  <!-- right arm -->
  <link name="right_arm_link_base">
  </link>
  <joint name="right_arm_base_joint" type="fixed">
    <origin rpy="1.5707963  0 0" xyz="-0.03779 0 0.3421"/>
    <parent link="base_link"/>
    <child link="right_arm_link_base"/>
  </joint>

  <link name="right_arm_link01">
    <inertial>
      <origin xyz="0.00000249 0.01038065 -0.03761745" rpy="0 0 0" />
      <mass value="0.23884197" />
      <inertia ixx="0.00044830" iyy="0.00024895" izz="0.00040350" ixy="0.00000000" ixz="-0.00000000" iyz="-0.00006606"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_arm_link01.STL"    scale="1 1 -1" />
      </geometry>
      <material name="">
        <color rgba="1 0.4 0.4 1" />
      </material>
    </visual>
  </link>

  <joint name="uidx20_right_arm_joint1" type="revolute">
    <origin xyz="-0.0 0.0 0.191 " rpy="0   0  -1.5707963" />
    <parent link="right_arm_link_base" />
    <child link="right_arm_link01" />
    <axis xyz="0 0 1" />
    <limit lower="-2.967" upper="2.967" effort="50" velocity="20" />
  </joint>

  <link name="right_arm_link02">
    <inertial>
      <origin xyz="-0.00003961 0.05209875 -0.00115698" rpy="0 0 0" />
      <mass value="3.09638867" />
      <inertia ixx="0.01280293" iyy="0.00266575" izz="0.01264891" ixy="0.00000268" ixz="0.00000182" iyz="-0.00017645"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_arm_link02.STL" scale="-1 -1 1"/>
      </geometry>
      <material name="">
        <color rgba="1 1 0.4 1" />
      </material>
    </visual>
  </link>
  <joint name="uidx21_right_arm_joint2" type="revolute">
    <origin xyz="0 0 0.0" rpy="1.5707963 0 0"/>
    <parent link="right_arm_link01" />
    <child link="right_arm_link02" />
    <axis xyz="0 0 1" />
    <limit lower="-1.658062761" upper="0.523598767" effort="50" velocity="20" />
  </joint>



  <link name="right_arm_link03">
    <inertial>
      <origin xyz="0.00000225 0.00035500 -0.08376422" rpy="0 0 0" />
      <mass value="1.37452988" />
      <inertia ixx="0.00163610" iyy="0.00181828" izz="0.00109760" ixy="0.00000081" ixz="0.00000378" iyz="-0.00001043"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_arm_link03.STL" scale="1 1 -1"/>
      </geometry>
      <material name="">
        <color rgba="0.4 1 0.4 1" />
      </material>
    </visual>
  </link>
  <joint name="uidx22_right_arm_joint3" type="fixed">
    <origin xyz="0 0.154  0" rpy="1.5707963 0 0" />
    <parent link="right_arm_link02" />
    <child link="right_arm_link03" />
    <axis xyz="0 0 1" />
    <limit lower="-2.967" upper="2.967" effort="50" velocity="20" />
  </joint>


  <link name="right_arm_link04">
    <inertial>
      <origin xyz="-0.00002118 0.07109905 -0.00481815" rpy="0 0 0" />
      <mass value="0.70457441" />
     <inertia ixx="0.00064706" iyy="0.00037557" izz="0.00056821" ixy="-0.00000321" ixz="-0.00000120" iyz="-0.00014782"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_arm_link04.STL"  scale="1 -1 1"/>
      </geometry>
      <material name="">
        <color rgba="0.4 1 1 1" />
      </material>
    </visual>
  </link>
  <joint name="uidx23_right_arm_joint4" type="revolute">
    <origin xyz="0 0 -0.093" rpy="-1.5707963  0  1.5707963" />
    <parent link="right_arm_link03" />
    <child link="right_arm_link04" />
    <axis xyz="0 0 1" />
    <limit lower="0" upper="2.094395067" effort="50" velocity="20" />
  </joint>


 
  <link name = "right_arm_link05">
    <inertial>
      <origin xyz="-0.00221884 0.00000000 -0.06554799" rpy="0 0 0" />
      <mass value="0.12244729" />
      <inertia ixx="0.00031756" iyy="0.00033014" izz="0.00003000" ixy="0.00000000" ixz="-0.00002042" iyz="-0.00000000"/>
    </inertial>
    <visual name = "">
      <origin rpy = "0.0 0.0 0.0" xyz = "0.0 0.0 0.0" />
      <geometry>
        <mesh filename = "../meshes/left_arm_link05.STL" scale="1 1 -1"/>
      </geometry>
      <material name = "">
        <color rgba = "0 0.5 1 1" />
        <texture filename = "" />
      </material>
    </visual>
  </link>
  <joint name = "uidx24_right_arm_joint5" type = "fixed">
    <origin xyz = "0  0.103 0.0" rpy = "1.5707963 -1.57 0"  />
    <parent link = "right_arm_link04" />
    <child link = "right_arm_link05" />
    <axis xyz = "0 0 1" />
    <limit lower = "-2.87979" upper = "2.87979" velocity = "0.0" effort = "0.0"/>
  </joint>

  
  <link name = "right_arm_link06">
    <inertial>
      <origin xyz="-0.00018011 -0.00021701 0.00000000" rpy="0 0 0" />
      <mass value="0.02504627" />
      <inertia ixx="0.00000197" iyy="0.00000173" izz="0.00000071" ixy="-0.00000000" ixz="-0.00000000" iyz="0.00000000"/>
    </inertial>
    <visual name = "">
      <origin rpy = "0.0 0.0 0.0" xyz = "0.0 0.0 0.0" />
      <geometry>
        <mesh filename = "../meshes/left_arm_link06.STL" scale="-1 1 1" />
      </geometry>
      <material name = "">
        <color rgba = "0.8 1.0 0.8 1" />
        <texture filename = "" />
      </material>
    </visual>
  </link>
  <joint name = "uidx25_right_arm_joint6" type = "fixed">
    <origin xyz="-0.01 0 -0.1595" rpy="-1.5707963 1.5707963 0" />
    <parent link = "right_arm_link05" />
    <child link = "right_arm_link06" />
    <axis xyz = "0 0 1" />
    <limit lower = "-0.785398" upper = "0.785398" velocity = "0.0" effort = "0.0"/>
  </joint>

  
  <link name = "right_arm_link07">
    <inertial>
      <origin xyz="0.01211757 -0.00009712 0.00886324" rpy="0 0 0" />
      <mass value="0.05050219" />
      <inertia ixx="0.00000828" iyy="0.00001140" izz="0.00001294" ixy="-0.00000006" ixz="0.00000179" iyz="0.00000004"/>
    </inertial>
    <visual name = "">
      <origin rpy = "0.0 0.0 0.0" xyz = "0.0 0.0 0.0" />
      <geometry>
        <mesh filename="../meshes/left_arm_link07.STL" scale="-1 1 1" />
      </geometry>
      <material name = "">
        <color rgba = "1 0.5 1 1" />
        <texture filename = "" />
      </material>
    </visual>
  </link>
  <joint name = "uidx26_right_arm_joint7" type = "fixed">
    <origin xyz="0 0 0" rpy="1.5707963 0 0" />
    <parent link="right_arm_link06" />
    <child link="right_arm_link07" />
    <axis xyz="0 0 1" />
    <limit lower="-0.5236" upper="0.5236" effort="50" velocity="20" />
  </joint>

  <joint name = "right_arm_joint07_fixed" type = "fixed">
    <origin rpy = "0 0 0" xyz = "0 0 0" />
    <parent link = "right_arm_link07" />
    <child link = "right_arm_end" />
  </joint>
  <link name = "right_arm_end">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <sphere radius = "0.02" />
      </geometry>
      <material name="">
        <color rgba="1 0.5 1 1" />
      </material>
    </visual>
  </link>

  <joint name="right_wrist_motor_A_ball" type="fixed">
    <origin xyz="0.005 0.02245 -0.0245" rpy="0 -0.01481371 0" />
    <parent link="right_arm_link05" />
    <child link="right_wrist_motor_A" />
  </joint>
  <link name="right_wrist_motor_A">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_arm_wrist_motor_A.STL" />
      </geometry>
      <material name="">
        <color rgba="1 0.4 0.4 1" />
      </material>
    </visual>
  </link>

  <!-- <joint name="right_wrist_rod_A_joint" type="fixed">
    <origin xyz="0 0 0.135015" rpy="0 0 0" />
    <parent link="right_wrist_motor_A" />
    <child link="right_wrist_rod_A" />
    <axis xyz="0 0 1" />
    <limit lower="-0.01199" upper="0.01204" effort="50" velocity="20" />
  </joint>
  <link name="right_wrist_rod_A">
    <visual>
      <origin xyz="0.00000369429545234862 -0.00000000014095802303 -0.00627793207000482" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/WRIST-LA-rod.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 0.4 1" />
      </material>
    </visual>
  </link> -->

  <joint name="right_wrist_motor_B_ball" type="fixed">
    <origin xyz="0.005 -0.02245 -0.0245" rpy="0 -0.01481371 0" />
    <parent link="right_arm_link05" />
    <child link="right_wrist_motor_B" />
  </joint>
  <link name="right_wrist_motor_B">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/left_arm_wrist_motor_A.STL" />
      </geometry>
      <material name="">
        <color rgba="1 0.4 0.4 1" />
      </material>
    </visual>
  </link>

  <!-- <joint name="right_wrist_rod_B_joint" type="fixed">
    <origin xyz="0 0 0.135015" rpy="0 0 0" />
    <parent link="right_wrist_motor_B" />
    <child link="right_wrist_rod_B" />
    <axis xyz="0 0 1" />
    <limit lower="-0.01199" upper="0.01204" effort="50" velocity="20" />
  </joint>
  <link name="right_wrist_rod_B">
    <visual>
      <origin xyz="-0.00000369429969338756 -0.00000000037887520898 -0.00627793227182449" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/WRIST-LA-rod.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 0.4 1" />
      </material>
    </visual>
  </link> -->

  <link name="head_link01">
    <inertial>
      <origin xyz="-0.00939988257921173 0.00208559651181146 0.0959271251607139" rpy="0 0 0" />
      <mass value="0.997936905754711" />
      <inertia ixx="0.00186667353349004" ixy="6.97491964231661E-07" ixz="-0.000208143145393745" iyy="0.00183203999035382" iyz="6.21573933150457E-06" izz="0.000923073409628197" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/head_link01.STL" />
      </geometry>
      <material name="">
        <color rgba="1 0.4 0.4 1" />
      </material>
    </visual>
  </link>
  <joint name="uidx27_head_joint1" type="fixed">
    <origin xyz="-0.0113 0 0.39304" rpy="0 0 0" />
    <parent link="base_link" />
    <child link="head_link01" />
    <axis xyz="0 0 1" />
    <limit lower="-2.2" upper="2.2" effort="50" velocity="20" />
  </joint>

  <link name="head_link02">
    <inertial>
      <origin xyz="0.0460883659123594 0.0374391617023641 0.00179746002426286" rpy="0 0 0" />
      <mass value="0.483351685161759" />
      <inertia ixx="0.00144980024451702" ixy="-0.000341627033500735" ixz="-5.46745107733584E-06" iyy="0.00135359002574906" iyz="5.5131011593589E-07" izz="0.00158962060674087" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="../meshes/head_link02.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 0.4 1" />
      </material>
    </visual>
  </link>
  <joint name="uidx28_head_joint2" type="fixed">
    <origin xyz="-0.010002 0 0.126" rpy="1.5708 0 0" />
    <parent link="head_link01" />
    <child link="head_link02" />
    <axis xyz="0 0 1" />
    <limit lower="-0.5" upper="0.5" effort="50" velocity="20" />
  </joint>

  <!-- <link name="LeftArm"/>
  <joint name="LeftArmJoint" type="fixed">
    <origin xyz="0 0 0" rpy="0 1.5707963 -1.5707963" />
    <parent link="left_arm_link02" />
    <child link="LeftArm" />
  </joint>
  <link name="RightArm"/>
  <joint name="RightArmJoint" type="fixed">
    <origin xyz="0 0 0" rpy="0 1.5707963 1.5707963" />
    <parent link="right_arm_link02" />
    <child link="RightArm" />
  </joint>

  <link name="LeftForearm"/>
  <joint name="LeftForearmJoint" type="fixed">
    <origin xyz="0 0 0" rpy="-1.5707963 0 3.1415926536" />
    <parent link="left_arm_link04" />
    <child link="LeftForearm" />
  </joint>
  <link name="RightForearm"/>
  <joint name="RightForearmJoint" type="fixed">
    <origin xyz="0 0 0" rpy="1.5707963 0 3.1415926536" />
    <parent link="right_arm_link04" />
    <child link="RightForearm" />
  </joint>

  <link name="LeftHand"/>
  <joint name="LeftHandJoint" type="fixed">
    <origin xyz="0 0 0" rpy="0 1.5707963 3.1415926535" />
    <parent link="left_arm_link07" />
    <child link="LeftHand" />
  </joint>
  <link name="RightHand"/>
  <joint name="RightHandJoint" type="fixed">
    <origin xyz="0 0 0" rpy="0 1.5707963 0" />
    <parent link="right_arm_link07" />
    <child link="RightHand" />
  </joint> -->


<!-- =================================== LeftHand ====================================-->

  <link name="left_hand">
    <inertial>
      <origin xyz="0.0284046972660885 -0.00981060404810299 0.0222000386975785" rpy="0.0 0.0 0.0"/>
      <mass value="0.22621959250221"/>
      <inertia ixx="0.000161057742168925" ixy="-3.93039257205975e-06" ixz="-2.59552833985103e-06" iyy="0.000358621491446785" iyz="4.70540238948447e-06" izz="0.000257940666810454"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/left_paw.stl" scale="1 -1 1"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/left_paw.stl" scale="1 -1 1"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="left_hand_index1">
    <inertial>
      <origin xyz="0.0107729817589723 0.00213781834563882 -1.34009238303423e-07" rpy="0.0 0.0 0.0"/>
      <mass value="0.00330620599604871"/>
      <inertia ixx="1.55837657646135e-07" ixy="-3.78958094877833e-08" ixz="-5.07264353075226e-12" iyy="5.30383928507338e-07" iyz="-1.43523102869642e-12" izz="5.55164105212297e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/left_hand_index1.stl" scale="1 -1 1"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/left_hand_index1.stl" scale="1 -1 1"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="left_hand_index2">
    <inertial>
      <origin xyz="0.019017916514892 0.00563447754295066 1.90796464460963e-08" rpy="0.0 0.0 0.0"/>
      <mass value="0.00453366822308025"/>
      <inertia ixx="8.56969764665792e-08" ixy="-3.65502775787085e-08" ixz="2.02841411241907e-12" iyy="7.58232170757561e-07" iyz="6.07603435089188e-13" izz="7.68830975890361e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/left_hand_index2.stl" scale="1 -1 1"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/left_hand_index2.stl" scale="1 -1 1"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="left_hand_middle1">
    <inertial>
      <origin xyz="0.0107729781873397 0.00213783567195037 -1.14076048385314e-07" rpy="0.0 0.0 0.0"/>
      <mass value="0.00330621565435093"/>
      <inertia ixx="1.55837752420093e-07" ixy="-3.78955767951184e-08" ixz="-4.51740252002264e-12" iyy="5.30384784494186e-07" iyz="-1.08751322448059e-12" izz="5.55164342998361e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/left_hand_middle1.stl" scale="1 -1 1"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/left_hand_middle1.stl" scale="1 -1 1"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="left_hand_middle2">
    <inertial>
      <origin xyz="0.0190179451505274 0.00563447968575673 1.97663586783137e-08" rpy="0.0 0.0 0.0"/>
      <mass value="0.00453367017295165"/>
      <inertia ixx="8.5696963969299e-08" ixy="-3.6550923619766e-08" ixz="1.59786933213099e-12" iyy="7.58233066456215e-07" iyz="6.81108952684927e-13" izz="7.68832070821997e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/left_hand_middle2.stl" scale="1 -1 1"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/left_hand_middle2.stl" scale="1 -1 1"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="left_hand_ring1">
    <inertial>
      <origin xyz="0.0107730930654459 0.00213783260244832 -8.57328914740003e-08" rpy="0.0 0.0 0.0"/>
      <mass value="0.0033061482917439"/>
      <inertia ixx="1.55835970052989e-07" ixy="-3.78956285985318e-08" ixz="-3.98553229256186e-12" iyy="5.30379079243718e-07" iyz="-6.32137439938121e-13" izz="5.5516012983777e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/left_hand_ring1.stl" scale="1 -1 1"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/left_hand_ring1.stl" scale="1 -1 1"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="left_hand_ring2">
    <inertial>
      <origin xyz="0.01901789870244 0.00563447090562313 1.70050325960271e-08" rpy="0.0 0.0 0.0"/>
      <mass value="0.00453366652527048"/>
      <inertia ixx="8.56969335058099e-08" ixy="-3.65501984156752e-08" ixz="1.30339239962579e-12" iyy="7.58232117591424e-07" iyz="7.47249892192207e-13" izz="7.68830949295352e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/left_hand_ring2.stl" scale="1 -1 1"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/left_hand_ring2.stl" scale="1 -1 1"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="left_hand_pinky1">
    <inertial>
      <origin xyz="0.0107729931213964 0.00213782731321216 -7.95186542376647e-08" rpy="0.0 0.0 0.0"/>
      <mass value="0.00330620800050046"/>
      <inertia ixx="1.55837389573248e-07" ixy="-3.78958987950181e-08" ixz="-3.73884747536307e-12" iyy="5.30384597648764e-07" iyz="-3.63224479792676e-13" izz="5.55163794949727e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/left_hand_pinky1.stl" scale="1 -1 1"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/left_hand_pinky1.stl" scale="1 -1 1"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="left_hand_pinky2">
    <inertial>
      <origin xyz="0.0155924853997512 0.00555619186912998 -1.21775195708468e-07" rpy="0.0 0.0 0.0"/>
      <mass value="0.00356886192445218"/>
      <inertia ixx="6.67767175628229e-08" ixy="-2.70385196277176e-08" ixz="-4.60032318294332e-12" iyy="4.33416353432936e-07" iyz="-1.36538016248102e-12" izz="4.39760949009649e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/left_hand_pinky2.stl" scale="1 -1 1"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/left_hand_pinky2.stl" scale="1 -1 1"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="left_hand_thumb0">
    <inertial>
      <origin xyz="-0.000668287872925624 -0.00209320996353089 0.0037633441647693" rpy="0.0 0.0 0.0"/>
      <mass value="0.00487390663164139"/>
      <inertia ixx="1.30408513803687e-07" ixy="-8.92030281859617e-09" ixz="-1.7660325194859e-08" iyy="1.70578986810531e-07" iyz="-1.02749399337787e-09" izz="2.1170907785655e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/left_hand_thumb0_modified.stl" scale="1 1 -1"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.00 0.0 -0.00" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/left_hand_thumb0_modified.stl" scale="1 1 -1"/>
      </geometry>
      <origin xyz="0.00 0.0 -0.00" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="left_hand_thumb1">
    <inertial>
      <origin xyz="0.0261732090129406 0.00353701767650547 -1.92601341111875e-07" rpy="0.0 0.0 0.0"/>
      <mass value="0.00652729219675812"/>
      <inertia ixx="8.93817123585481e-07" ixy="6.28368840765271e-08" ixz="-2.00458419649248e-11" iyy="2.37370603240555e-06" iyz="-1.67466441559282e-12" izz="2.75021956933668e-06"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/left_hand_thumb1.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/left_hand_thumb1.stl"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="left_hand_thumb2">
    <inertial>
      <origin xyz="-0.007316119869056 -0.00552223433089214 2.14610152510231e-07" rpy="0.0 0.0 0.0"/>
      <mass value="0.0022695308360543"/>
      <inertia ixx="1.7281059843115e-07" ixy="4.52282139518593e-08" ixz="-2.88816078436063e-12" iyy="2.78046552011382e-07" iyz="7.05843881266438e-13" izz="2.99630657437474e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/left_hand_thumb2.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/left_hand_thumb2.stl"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="left_hand_thumb3">
    <inertial>
      <origin xyz="-3.3937612879259e-06 0.0106230303829815 0.000584861433263272" rpy="0.0 0.0 0.0"/>
      <mass value="0.00330869751403717"/>
      <inertia ixx="1.96169236089202e-07" ixy="-1.43223823727531e-10" ixz="5.64113109591019e-11" iyy="8.67914197835909e-08" iyz="2.46315104630635e-08" izz="1.85481756939709e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/left_hand_thumb3.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/left_hand_thumb3.stl"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <joint name="left_hand_fixed" type="fixed">
    <parent link="left_arm_end"/>
    <child link="left_hand"/>
    <origin xyz="-0.025 0.02 0.01" rpy="1.5707963 3.1415926535 0"/>
  </joint>
  <joint name="left_hand_index_joint1" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.6" upper="0.04"/>
    <parent link="left_hand"/>
    <child link="left_hand_index1"/>
    <axis xyz="0. 0. -1."/>
    <origin xyz="0.08225 0.0077547 -0.0040172" rpy="5.2178e-10 -6.245e-17 0.14966"/>
  </joint>
  <joint name="left_hand_index_joint2" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.7" upper="0.16"/>
    <parent link="left_hand_index1"/>
    <child link="left_hand_index2"/>
    <axis xyz="0. 0. -1."/>
    <origin xyz="0.032132 0.0 0.0" rpy="-5.086e-10 -1.1653e-10 0.22524"/>
  </joint>
  <joint name="left_hand_middle_joint1" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.6" upper="0.04"/>
    <parent link="left_hand"/>
    <child link="left_hand_middle1"/>
    <axis xyz="0. 0. -1."/>
    <origin xyz="0.081939 0.0077547 0.015305" rpy="0.005615800000000001 -0.034452 0.16161"/>
  </joint>
  <joint name="left_hand_middle_joint2" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.7" upper="0.16"/>
    <parent link="left_hand_middle1"/>
    <child link="left_hand_middle2"/>
    <axis xyz="0. 0. -1."/>
    <origin xyz="0.032132 0.0 0.0" rpy="-5.0853e-10 -1.1686e-10 0.22588"/>
  </joint>
  <joint name="left_hand_ring_joint1" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.6" upper="0.04"/>
    <parent link="left_hand"/>
    <child link="left_hand_ring1"/>
    <axis xyz="0. 0. -1."/>
    <origin xyz="0.080765 0.0077547 0.034505" rpy="0.010408 -0.086645 0.11971"/>
  </joint>
  <joint name="left_hand_ring_joint2" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.7" upper="0.16"/>
    <parent link="left_hand_ring1"/>
    <child link="left_hand_ring2"/>
    <axis xyz="0. 0. -1."/>
    <origin xyz="0.032132 0.0 0.0" rpy="-5.0314e-10 -1.3824e-10 0.26815"/>
  </joint>
  <joint name="left_hand_pinky_joint1" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.6" upper="0.04"/>
    <parent link="left_hand"/>
    <child link="left_hand_pinky1"/>
    <axis xyz="0. 0. -1."/>
    <origin xyz="0.078587 0.0077547 0.053617" rpy="0.022524 -0.13781 0.16255"/>
  </joint>
  <joint name="left_hand_pinky_joint2" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.7" upper="0.16"/>
    <parent link="left_hand_pinky1"/>
    <child link="left_hand_pinky2"/>
    <axis xyz="0. 0. -1."/>
    <origin xyz="0.032132 0.0 0.0" rpy="-5.0686e-10 -1.2389e-10 0.23972000000000002"/>
  </joint>
  <joint name="left_hand_thumb_joint0" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.2" upper="0.4"/>
    <parent link="left_hand"/>
    <child link="left_hand_thumb0"/>
    <axis xyz="0. -1. 0."/>
    <origin xyz="0.00870156 0.02523165 -0.00066618" rpy="3.049235 1.1827 1.4853"/>
  </joint>
  <joint name="left_hand_thumb_joint1" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-0.28" upper="0.4"/>
    <parent link="left_hand_thumb0"/>
    <child link="left_hand_thumb1"/>
    <axis xyz="0.  0. -1."/>
    <origin xyz="0.0115  -0.0035  -0.00025" rpy="-3.1415853071795867 -5.2736e-16 0.96726"/>
  </joint>
  <joint name="left_hand_thumb_joint2" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-0.24" upper="0.52"/>
    <parent link="left_hand_thumb1"/>
    <child link="left_hand_thumb2"/>
    <axis xyz="0. 0. 1."/>
    <origin xyz="0.056001 0.0 0.0" rpy="0.0 -1.3878e-16 2.9661"/>
  </joint>
  <joint name="left_hand_thumb_joint3" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-0.8" upper="-0.2"/>
    <parent link="left_hand_thumb2"/>
    <child link="left_hand_thumb3"/>
    <axis xyz="1. 0. 0."/>
    <origin xyz="-0.022653 0.0 0.0" rpy="-1.412492653589793 -1.5707926535755432 -3.141592653589793"/>
  </joint>

<!-- =================================== RightHand ================================= -->
<link name="right_hand">
    <inertial>
      <origin xyz="0.0284046972660885 -0.00981060404810299 0.0222000386975785" rpy="0.0 0.0 0.0"/>
      <mass value="0.22621959250221"/>
      <inertia ixx="0.000161057742168925" ixy="-3.93039257205975e-06" ixz="-2.59552833985103e-06" iyy="0.000358621491446785" iyz="4.70540238948447e-06" izz="0.000257940666810454"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/right_paw.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/right_paw.stl"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="right_hand_index1">
    <inertial>
      <origin xyz="0.0107729817589723 0.00213781834563882 -1.34009238303423e-07" rpy="0.0 0.0 0.0"/>
      <mass value="0.00330620599604871"/>
      <inertia ixx="1.55837657646135e-07" ixy="-3.78958094877833e-08" ixz="-5.07264353075226e-12" iyy="5.30383928507338e-07" iyz="-1.43523102869642e-12" izz="5.55164105212297e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/right_hand_index1.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/right_hand_index1.stl"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="right_hand_index2">
    <inertial>
      <origin xyz="0.019017916514892 0.00563447754295066 1.90796464460963e-08" rpy="0.0 0.0 0.0"/>
      <mass value="0.00453366822308025"/>
      <inertia ixx="8.56969764665792e-08" ixy="-3.65502775787085e-08" ixz="2.02841411241907e-12" iyy="7.58232170757561e-07" iyz="6.07603435089188e-13" izz="7.68830975890361e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/right_hand_index2.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/right_hand_index2.stl"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="right_hand_middle1">
    <inertial>
      <origin xyz="0.0107729781873397 0.00213783567195037 -1.14076048385314e-07" rpy="0.0 0.0 0.0"/>
      <mass value="0.00330621565435093"/>
      <inertia ixx="1.55837752420093e-07" ixy="-3.78955767951184e-08" ixz="-4.51740252002264e-12" iyy="5.30384784494186e-07" iyz="-1.08751322448059e-12" izz="5.55164342998361e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/right_hand_middle1.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/right_hand_middle1.stl"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="right_hand_middle2">
    <inertial>
      <origin xyz="0.0190179451505274 0.00563447968575673 1.97663586783137e-08" rpy="0.0 0.0 0.0"/>
      <mass value="0.00453367017295165"/>
      <inertia ixx="8.5696963969299e-08" ixy="-3.6550923619766e-08" ixz="1.59786933213099e-12" iyy="7.58233066456215e-07" iyz="6.81108952684927e-13" izz="7.68832070821997e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/right_hand_middle2.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/right_hand_middle2.stl"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="right_hand_ring1">
    <inertial>
      <origin xyz="0.0107730930654459 0.00213783260244832 -8.57328914740003e-08" rpy="0.0 0.0 0.0"/>
      <mass value="0.0033061482917439"/>
      <inertia ixx="1.55835970052989e-07" ixy="-3.78956285985318e-08" ixz="-3.98553229256186e-12" iyy="5.30379079243718e-07" iyz="-6.32137439938121e-13" izz="5.5516012983777e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/right_hand_ring1.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/right_hand_ring1.stl"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="right_hand_ring2">
    <inertial>
      <origin xyz="0.01901789870244 0.00563447090562313 1.70050325960271e-08" rpy="0.0 0.0 0.0"/>
      <mass value="0.00453366652527048"/>
      <inertia ixx="8.56969335058099e-08" ixy="-3.65501984156752e-08" ixz="1.30339239962579e-12" iyy="7.58232117591424e-07" iyz="7.47249892192207e-13" izz="7.68830949295352e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/right_hand_ring2.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/right_hand_ring2.stl"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="right_hand_pinky1">
    <inertial>
      <origin xyz="0.0107729931213964 0.00213782731321216 -7.95186542376647e-08" rpy="0.0 0.0 0.0"/>
      <mass value="0.00330620800050046"/>
      <inertia ixx="1.55837389573248e-07" ixy="-3.78958987950181e-08" ixz="-3.73884747536307e-12" iyy="5.30384597648764e-07" iyz="-3.63224479792676e-13" izz="5.55163794949727e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/right_hand_pinky1.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/right_hand_pinky1.stl"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="right_hand_pinky2">
    <inertial>
      <origin xyz="0.0155924853997512 0.00555619186912998 -1.21775195708468e-07" rpy="0.0 0.0 0.0"/>
      <mass value="0.00356886192445218"/>
      <inertia ixx="6.67767175628229e-08" ixy="-2.70385196277176e-08" ixz="-4.60032318294332e-12" iyy="4.33416353432936e-07" iyz="-1.36538016248102e-12" izz="4.39760949009649e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/right_hand_pinky2.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/right_hand_pinky2.stl"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="right_hand_thumb0">
    <inertial>
      <origin xyz="-0.000668287872925624 -0.00209320996353089 0.0037633441647693" rpy="0.0 0.0 0.0"/>
      <mass value="0.00487390663164139"/>
      <inertia ixx="1.30408513803687e-07" ixy="-8.92030281859617e-09" ixz="-1.7660325194859e-08" iyy="1.70578986810531e-07" iyz="-1.02749399337787e-09" izz="2.1170907785655e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/right_hand_thumb0_modified.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.00 0.0 -0.00" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/right_hand_thumb0_modified.stl"/>
      </geometry>
      <origin xyz="0.00 0.0 -0.00" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="right_hand_thumb1">
    <inertial>
      <origin xyz="0.0261732090129406 0.00353701767650547 -1.92601341111875e-07" rpy="0.0 0.0 0.0"/>
      <mass value="0.00652729219675812"/>
      <inertia ixx="8.93817123585481e-07" ixy="6.28368840765271e-08" ixz="-2.00458419649248e-11" iyy="2.37370603240555e-06" iyz="-1.67466441559282e-12" izz="2.75021956933668e-06"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/right_hand_thumb1.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/right_hand_thumb1.stl"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="right_hand_thumb2">
    <inertial>
      <origin xyz="-0.007316119869056 -0.00552223433089214 2.14610152510231e-07" rpy="0.0 0.0 0.0"/>
      <mass value="0.0022695308360543"/>
      <inertia ixx="1.7281059843115e-07" ixy="4.52282139518593e-08" ixz="-2.88816078436063e-12" iyy="2.78046552011382e-07" iyz="7.05843881266438e-13" izz="2.99630657437474e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/right_hand_thumb2.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/right_hand_thumb2.stl"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <link name="right_hand_thumb3">
    <inertial>
      <origin xyz="-3.3937612879259e-06 0.0106230303829815 0.000584861433263272" rpy="0.0 0.0 0.0"/>
      <mass value="0.00330869751403717"/>
      <inertia ixx="1.96169236089202e-07" ixy="-1.43223823727531e-10" ixz="5.64113109591019e-11" iyy="8.67914197835909e-08" iyz="2.46315104630635e-08" izz="1.85481756939709e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="../meshes/right_hand_thumb3.stl"/>
      </geometry>
      <material name=""/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <mesh filename="../meshes/right_hand_thumb3.stl"/>
      </geometry>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </collision> -->
  </link>
  <joint name="right_hand_fixed" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.6" upper="0.04"/>
    <parent link="right_arm_end"/>
    <child link="right_hand"/>
    <axis xyz="0. 0. 1."/>
    <origin xyz="0.025 0.02 0.01" rpy="1.5707963 0 0"/>
  </joint>
  <joint name="right_hand_index_joint1" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.6" upper="0.04"/>
    <parent link="right_hand"/>
    <child link="right_hand_index1"/>
    <axis xyz="0. 0. 1."/>
    <origin xyz="0.08225 -0.0077547 -0.0040172" rpy="5.2178e-10 -6.245e-17 -0.14966"/>
  </joint>
  <joint name="right_hand_index_joint2" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.7" upper="0.16"/>
    <parent link="right_hand_index1"/>
    <child link="right_hand_index2"/>
    <axis xyz="0. 0. 1."/>
    <origin xyz="0.032132 0.0 0.0" rpy="-5.086e-10 -1.1653e-10 -0.22524"/>
  </joint>
  <joint name="right_hand_middle_joint1" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.6" upper="0.04"/>
    <parent link="right_hand"/>
    <child link="right_hand_middle1"/>
    <axis xyz="0. 0. 1."/>
    <origin xyz="0.081939 -0.0077547 0.015305" rpy="0.005615800000000001 -0.034452 -0.16161"/>
  </joint>
  <joint name="right_hand_middle_joint2" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.7" upper="0.16"/>
    <parent link="right_hand_middle1"/>
    <child link="right_hand_middle2"/>
    <axis xyz="0. 0. 1."/>
    <origin xyz="0.032132 0.0 0.0" rpy="-5.0853e-10 -1.1686e-10 -0.22588"/>
  </joint>
  <joint name="right_hand_ring_joint1" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.6" upper="0.04"/>
    <parent link="right_hand"/>
    <child link="right_hand_ring1"/>
    <axis xyz="0. 0. 1."/>
    <origin xyz="0.080765 -0.0077547 0.034505" rpy="0.010408 -0.086645 -0.11971"/>
  </joint>
  <joint name="right_hand_ring_joint2" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.7" upper="0.16"/>
    <parent link="right_hand_ring1"/>
    <child link="right_hand_ring2"/>
    <axis xyz="0. 0. 1."/>
    <origin xyz="0.032132 0.0 0.0" rpy="-5.0314e-10 -1.3824e-10 -0.26815"/>
  </joint>
  <joint name="right_hand_pinky_joint1" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.6" upper="0.04"/>
    <parent link="right_hand"/>
    <child link="right_hand_pinky1"/>
    <axis xyz="0. 0. 1."/>
    <origin xyz="0.078587 -0.0077547 0.053617" rpy="0.022524 -0.13781 -0.16255"/>
  </joint>
  <joint name="right_hand_pinky_joint2" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.7" upper="0.16"/>
    <parent link="right_hand_pinky1"/>
    <child link="right_hand_pinky2"/>
    <axis xyz="0. 0. 1."/>
    <origin xyz="0.032132 0.0 0.0" rpy="-5.0686e-10 -1.2389e-10 -0.23972000000000002"/>
  </joint>
  <joint name="right_hand_thumb_joint0" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-1.2" upper="0.4"/>
    <parent link="right_hand"/>
    <child link="right_hand_thumb0"/>
    <axis xyz="0. 1. 0."/>
    <origin xyz="0.00870156 -0.02523165 -0.00066618" rpy="0.09236500000000002 1.1827 -1.4853"/>
  </joint>
  <joint name="right_hand_thumb_joint1" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-0.28" upper="0.4"/>
    <parent link="right_hand_thumb0"/>
    <child link="right_hand_thumb1"/>
    <axis xyz="0.  0. -1."/>
    <origin xyz="0.0115  -0.0035  -0.00025" rpy="-3.1415853071795867 -5.2736e-16 0.96726"/>
  </joint>
  <joint name="right_hand_thumb_joint2" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-0.24" upper="0.52"/>
    <parent link="right_hand_thumb1"/>
    <child link="right_hand_thumb2"/>
    <axis xyz="0. 0. 1."/>
    <origin xyz="0.056001 0.0 0.0" rpy="0.0 -1.3878e-16 2.9661"/>
  </joint>
  <joint name="right_hand_thumb_joint3" type="fixed">
    <limit effort="1.0" velocity="1.0" lower="-0.8" upper="-0.2"/>
    <parent link="right_hand_thumb2"/>
    <child link="right_hand_thumb3"/>
    <axis xyz="1. 0. 0."/>
    <origin xyz="-0.022653 0.0 0.0" rpy="-1.412492653589793 -1.5707926535755432 -3.141592653589793"/>
  </joint>

</robot>
