<?xml version="1.0" encoding="utf-8"?>
<!--
This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>)
Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
For more information, please see http://wiki.ros.org/sw_urdf_exporter
-->
<robot name="gaoqing">
  <mujoco>
    <compiler meshdir="resources/robots/gaoqing/meshes" balanceinertia="true" discardvisual="false" fusestatic="false"/>
  </mujoco>
  <!-- ********************************************************************** -->
  <!-- ************************ imu link and joint ************************** -->
  <!-- ********************************************************************** -->

	<link name="base_link">
		<inertial>
			<origin xyz="0.0096 0.0005 0.1157" rpy="0 0 0" />
			<mass value="2.503" />
			<inertia ixx="0.0584" ixy="-1.0E-08" ixz="-0.0035" iyy="0.0536" iyz="-0.0002" izz="0.0099" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/base_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="0 0 0.165" rpy="0 0 0" />
			<geometry>
				<box size="0.12 0.14 0.2" />
			</geometry>
		</collision>
	</link>

  <!-- ************************************************************ -->
  <!-- ************************ right leg ************************** -->
  <!-- ************************************************************ -->
  
  <!-- ************* part 1 *************** -->

	<link name="leg_r1_link">
		<inertial>
			<origin xyz="-0.0594 0 -0.0436" rpy="0 0 0" />
			<mass value="0.3593" />
			<inertia ixx="0.0009" ixy="-1.0E-08" ixz="-0.001" iyy="0.0022" iyz="-0" izz="0.0015" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/r_hip_yaw_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="0 0 0.03" rpy="0 0 0" />
			<geometry>
				 <cylinder length="0.05" radius="0.025"/>
			</geometry>
		</collision>
	</link><!-- 1 -->
	<joint name="leg_r1_joint" type="revolute">
		<origin xyz="0 -0.075 0" rpy="0 0 0" />
		<parent link="base_link" />
		<child link="leg_r1_link" />
		<axis xyz="0 0 1" />
		<limit lower="-3.14" upper="3.14" effort="21" velocity="21" />
	    <dynamics damping="0.00" friction="0.02" />
	</joint> <!-- 1 -->
	<link name="leg_r2_link">
		<inertial>
			<origin xyz="0.0366 0.0002 0.0004" rpy="0 0 0" />
			<mass value="0.7306" />
			<inertia ixx="0.0014" ixy="-1.0E-08" ixz="-1.0E-08" iyy="0.0003" iyz="-1.0E-08" izz="0.0014" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/r_hip_roll_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="0.038 0 0.00" rpy="1.57 0 0" />
			<geometry>
				 <cylinder length="0.135" radius="0.025"/>
			</geometry>
		</collision>
	</link><!-- 2 -->
	<joint name="leg_r2_joint" type="revolute">
		<origin xyz="-0.037 0 -0.049" rpy="0 0 0" />
		<parent link="leg_r1_link" />
		<child link="leg_r2_link" />
		<axis xyz="1 0 0" />
		<limit lower="-3.14" upper="3.14" effort="21" velocity="21" />
		<dynamics damping="0.00" friction="0.02" />
	</joint> <!-- 2 -->
	<link name="leg_r3_link">
		<inertial>
			<origin xyz="0.0043 0.0024 -0.0565" rpy="0 0 0" />
			<mass value="0.10988" />
			<inertia ixx="0.0006751" ixy="-4E-07" ixz="-4.96E-05" iyy="0.0006779" iyz="-3E-07" izz="3.66E-05" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/r_thigh_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="-0.066 0 0.00" rpy="0 1.57 0" />
			<geometry>
				 <cylinder length="0.05" radius="0.025"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0.012 0 -0.08" rpy="0 0 0" />
			<geometry>
				 <box size="0.02 0.024 0.08"/>
			</geometry>
		</collision>
	</link><!-- 3 -->
	<joint name="leg_r3_joint" type="revolute">
		<origin xyz="0.039 0 0" rpy="0 0 0" />
		<parent link="leg_r2_link" />
		<child link="leg_r3_link" />
		<axis xyz="0 1 0" />
		<limit lower="-3.14" upper="3.14" effort="21" velocity="21" />
		<dynamics damping="0.00" friction="0.02" />
	</joint> <!-- 3 -->
	<link name="leg_r4_link">
		<inertial>
			<origin xyz="0.0040818 -0.0007633 -0.0728549" rpy="0 0 0" />
			<mass value="0.5043936" />
			<inertia ixx="0.0034667" ixy="-1.4E-06" ixz="-0.0001407" iyy="0.0035012" iyz="-2.43E-05" izz="0.0001659" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/r_calf_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="0 0 -0.07" rpy="0 0 0" />
			<geometry>
				 <box size="0.044 0.04 0.1"/>
			</geometry>
		</collision>
	</link><!-- 4 -->
	<joint name="leg_r4_joint" type="revolute">
		<origin xyz="0.011329 0 -0.14915" rpy="0 0 0" />
		<parent link="leg_r3_link" />
		<child link="leg_r4_link" />
		<axis xyz="0 1 0" />
		<limit lower="-3.14" upper="3.14" effort="21" velocity="21" />
		<dynamics damping="0.00" friction="0.02" />
	</joint> <!-- 4 -->
	<link name="leg_r5_link">
		<inertial>
			<origin xyz="0 -0.00081942 0" rpy="0 0 0" />
			<mass value="0.0088647" />
			<inertia ixx="2E-07" ixy="-1.0E-08" ixz="-1.0E-08" iyy="1E-06" iyz="-1.0E-08" izz="1E-06" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/r_ankle_pitch_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.89804 0.91765 0.92941 1" />
			</material>
		</visual>
	</link><!-- 5 -->
	<joint name="leg_r5_joint" type="revolute">
		<origin xyz="0 0 -0.16" rpy="0 0 0" />
		<parent link="leg_r4_link" />
		<child link="leg_r5_link" />
		<axis xyz="0 1 0" />
		<limit lower="-3.14" upper="3.14" effort="21" velocity="21" />
		<dynamics damping="0.00" friction="0.02" />
	</joint> <!-- 5 -->
	<link name="leg_r6_link">
		<inertial>
			<origin xyz="0.0244038 -1.5E-06 -0.0187171" rpy="0 0 0" />
			<mass value="0.1803263" />
			<inertia ixx="0.0001439" ixy="-1.0E-08" ixz="-0.0001053" iyy="0.0005162" iyz="-1.0E-08" izz="0.0005037" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/l_ankle_roll_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
    <collision>
      <origin xyz="0.032 0.0  -0.03" rpy="0. 0. 0.0" />
      <geometry>
        <box size="0.141 0.04 0.01" />
		</geometry>
	  </collision>
	</link><!-- 6 -->
	<joint name="leg_r6_joint" type="revolute">
		<origin xyz="0 -0.00081906 0" rpy="0 0 0" />
		<parent link="leg_r5_link" />
		<child link="leg_r6_link" />
		<axis xyz="1 0 0" />
		<limit lower="-3.14" upper="3.14" effort="21" velocity="21" />
		<dynamics damping="0.00" friction="0.02" />
	</joint> <!-- 6 -->
	<joint name="R_foot_fixed" type="fixed" dont_collapse="true">
		<origin rpy="0 0 0" xyz="0.05 -0.01 -0.02"/>
		<parent link="leg_r6_link"/>
		<child link="R_FOOT"/>
	</joint>  <!-- 7 -->
	<link name="R_FOOT">
		<visual>
			<origin rpy="0 0 0" xyz="0 0 0"/>
			<geometry>
			<sphere radius="0.027499999999999997"/>
			</geometry>
			<material name="orange"/>
		</visual>
		<collision>
			<origin rpy="0 0 0" xyz="0 0 0"/>
			<geometry>
			<sphere radius="0.0375"/>
			</geometry>
		</collision>
		<inertial>
			<mass value="0.06"/>
			<inertia ixx="3.375e-05" ixy="0.0" ixz="0.0" iyy="3.375e-05" iyz="0.0" izz="3.375e-05"/>
		</inertial>
	</link><!-- 7 -->

	<joint name="R_foot_fixed_2" type="fixed" dont_collapse="true">
		<origin rpy="0 0 0" xyz="0 0.02 0"/>
		<parent link="R_FOOT"/>
		<child link="R_FOOT_2"/>
	</joint> <!-- 8 -->
	<link name="R_FOOT_2">
		<visual>
			<origin rpy="0 0 0" xyz="0 0 0"/>
			<geometry>
			<sphere radius="0.027499999999999997"/>
			</geometry>
			<material name="orange"/>
		</visual>
		<collision>
			<origin rpy="0 0 0" xyz="0 0 0"/>
			<geometry>
			<sphere radius="0.0375"/>
			</geometry>
		</collision>
		<inertial>
			<mass value="0.06"/>
			<inertia ixx="3.375e-05" ixy="0.0" ixz="0.0" iyy="3.375e-05" iyz="0.0" izz="3.375e-05"/>
		</inertial>
	</link><!-- 8 -->

  <!-- ************************************************************ -->
  <!-- ************************ left leg ************************** -->
  <!-- ************************************************************ -->



	<link name="leg_l1_link">
		<inertial>
			<origin xyz="-0.0593709 0 -0.0436487" rpy="0 0 0" />
			<mass value="0.3593216" />
			<inertia ixx="0.0008659" ixy="-1.0E-8" ixz="-0.0010182" iyy="0.0022471" iyz="-1.0E-8" izz="0.0014877" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/l_hip_yaw_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="0 0 0.03" rpy="0 0 0" />
			<geometry>
				 <cylinder length="0.05" radius="0.025"/>
			</geometry>
		</collision>
	</link><!-- 9 -->
	<joint name="leg_l1_joint" type="revolute">
		<origin xyz="0 0.075 0" rpy="0 0 0" />
		<parent link="base_link" />
		<child link="leg_l1_link" />
		<axis xyz="0 0 1" />
		<limit lower="-3.14" upper="3.14" effort="21" velocity="21" />
		<dynamics damping="0.00" friction="0.02" />
	</joint> <!-- 9 -->
	<link name="leg_l2_link">
		<inertial>
			<origin xyz="0.0363921 0.0008346 0.0004071" rpy="0 0 0" />
			<mass value="0.73062" />
			<inertia ixx="0.0014025" ixy="-2.05E-05" ixz="-5.7E-06" iyy="0.0012475" iyz="-2E-07" izz="0.0024131" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/l_hip_roll_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="0.038 0 0.00" rpy="1.57 0 0" />
			<geometry>
				 <cylinder length="0.135" radius="0.025"/>
			</geometry>
		</collision>
	</link><!-- 10 -->
	<joint name="leg_l2_joint" type="revolute">
		<origin xyz="-0.037 0 -0.049" rpy="0 0 0" />
		<parent link="leg_l1_link" />
		<child link="leg_l2_link" />
		<axis xyz="1 0 0" />
		<limit lower="-3.14" upper="3.14" effort="21" velocity="21" />
		<dynamics damping="0.00" friction="0.02" />
	</joint> <!-- 10 -->
	<link name="leg_l3_link">
		<inertial>
			<origin xyz="0.0045462 -0.0023864 -0.0566449" rpy="0 0 0" />
			<mass value="0.1098811" />
			<inertia ixx="0.0006773" ixy="-1.0E-8" ixz="-5.17E-05" iyy="0.0006804" iyz="-1E-07" izz="3.69E-05" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/l_thigh_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="-0.066 0 0.00" rpy="0 1.57 0" />
			<geometry>
				 <cylinder length="0.05" radius="0.025"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0.012 0 -0.08" rpy="0 0 0" />
			<geometry>
				 <box size="0.02 0.024 0.08"/>
			</geometry>
		</collision>
	</link><!-- 11 -->
	<joint name="leg_l3_joint" type="revolute">
		<origin xyz="0.039 0 0" rpy="0 0 0" />
		<parent link="leg_l2_link" />
		<child link="leg_l3_link" />
		<axis xyz="0 1 0" />
		<limit lower="-3.14" upper="3.14" effort="21" velocity="21" />
		<dynamics damping="0.00" friction="0.02" />
	</joint> <!-- 11 -->
	<link name="leg_l4_link">
		<inertial>
			<origin xyz="0.0040818 1.4E-06 -0.0728549" rpy="0 0 0" />
			<mass value="0.5043936" />
			<inertia ixx="0.0034664" ixy="-1.0E-8" ixz="-0.0001407" iyy="0.0035012" iyz="-5.6E-06" izz="0.0001656" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/l_calf_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="0 0 -0.07" rpy="0 0 0" />
			<geometry>
				 <box size="0.044 0.04 0.1"/>
			</geometry>
		</collision>
	</link><!-- 12 -->
	<joint name="leg_l4_joint" type="revolute">
		<origin xyz="0.012 0 -0.14952" rpy="0 0 0" />
		<parent link="leg_l3_link" />
		<child link="leg_l4_link" />
		<axis xyz="0 1 0" />
		<limit lower="-3.14" upper="3.14" effort="21" velocity="21" />
		<dynamics damping="0.00" friction="0.02" />
	</joint> <!-- 12 -->
	<link name="leg_l5_link">
		<inertial>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<mass value="0.0088647" />
			<inertia ixx="2E-07" ixy="-1.0E-8" ixz="-1.0E-8" iyy="1E-06" iyz="-1.0E-8" izz="1E-06" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/l_ankle_pitch_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.89804 0.91765 0.92941 1" />
			</material>
		</visual>
	</link><!-- 13 -->
	<joint name="leg_l5_joint" type="revolute">
		<origin xyz="0 0 -0.16" rpy="0 0 0" />
		<parent link="leg_l4_link" />
		<child link="leg_l5_link" />
		<axis xyz="0 1 0" />
		<limit lower="-3.14" upper="3.14" effort="21" velocity="21" />
		<dynamics damping="0.00" friction="0.02" />
	</joint> <!-- 13 -->
	<link name="leg_l6_link">
		<inertial>
			<origin xyz="0.0237112 0 -0.0185693" rpy="0 0 0" />
			<mass value="0.1749744" />
			<inertia ixx="0.0001392" ixy="-1.0E-8" ixz="-9.94E-05" iyy="0.0004925" iyz="-1.0E-8" izz="0.0004813" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/l_ankle_roll_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
    <collision>
      <origin xyz="0.032 0.0  -0.03" rpy="0. 0. 0.0" />
      <geometry>
        <box size="0.141 0.04 0.01" />
      </geometry>
    </collision>
	</link><!-- 14 -->
	<joint name="leg_l6_joint" type="revolute">
		<origin xyz="0 0 0" rpy="0 0 0" />
		<parent link="leg_l5_link" />
		<child link="leg_l6_link" />
		<axis xyz="1 0 0" />
		<limit lower="-3.14" upper="3.14" effort="21" velocity="21" />
		<dynamics damping="0.00" friction="0.02" />
	</joint> <!-- 14 -->

	<joint name="L_foot_fixed" type="fixed" dont_collapse="true">
		<origin rpy="0 0 0" xyz="0.05 -0.01 -0.02"/>
		<parent link="leg_l6_link"/>
		<child link="L_FOOT"/>
	  </joint> <!-- 15 -->
	<link name="L_FOOT">
	<visual>
		<origin rpy="0 0 0" xyz="0 0 0"/>
		<geometry>
		<sphere radius="0.027499999999999997"/>
		</geometry>
		<material name="orange"/>
	</visual>
	<collision>
		<origin rpy="0 0 0" xyz="0 0 0"/>
		<geometry>
		<sphere radius="0.0375"/>
		</geometry>
	</collision>
	<inertial>
		<mass value="0.06"/>
		<inertia ixx="3.375e-05" ixy="0.0" ixz="0.0" iyy="3.375e-05" iyz="0.0" izz="3.375e-05"/>
	</inertial>
	</link><!-- 15 -->
	
	<joint name="L_foot_fixed_2" type="fixed" dont_collapse="true">
		<origin rpy="0 0 0" xyz="0 0.02 0"/>
		<parent link="L_FOOT"/>
		<child link="L_FOOT_2"/>
	</joint> <!-- 16 -->
	<link name="L_FOOT_2">
	<visual>
		<origin rpy="0 0 0" xyz="0 0 0"/>
		<geometry>
		<sphere radius="0.027499999999999997"/>
		</geometry>
		<material name="orange"/>
	</visual>
	<collision>
		<origin rpy="0 0 0" xyz="0 0 0"/>
		<geometry>
		<sphere radius="0.0375"/>
		</geometry>
	</collision>
	<inertial>
		<mass value="0.06"/>
		<inertia ixx="3.375e-05" ixy="0.0" ixz="0.0" iyy="3.375e-05" iyz="0.0" izz="3.375e-05"/>
	</inertial>
	</link><!-- 16 -->

	  

	<link name="r_shoulder_pitch_link">
		<inertial>
			<origin xyz="0.0020923 -0.0251964 0" rpy="0 0 0" />
			<mass value="0.2129671" />
			<inertia ixx="0.000206" ixy="-8.3E-06" ixz="0" iyy="5.86E-05" iyz="0" izz="0.0002099" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/r_shoulder_pitch_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="0 -0.028574 0.00" rpy="0 1.57 0" />
			<geometry>
				 <cylinder length="0.04" radius="0.022"/>
			</geometry>
		</collision>
	</link><!-- 17 -->
	<joint name="r_shoulder_pitch_joint" type="revolute">
		<origin xyz="-0.00238 -0.0855 0.2045" rpy="0 0 0" />
		<parent link="base_link" />
		<child link="r_shoulder_pitch_link" />
		<axis xyz="0 1 0" />
		<limit lower="-3.14" upper="3.14" effort="21" velocity="21" />
	</joint> <!-- 17 -->
	<link name="r_shoulder_roll_link">
		<inertial>
			<origin xyz="-0.0154214 0 -0.0421688" rpy="0 0 0" />
			<mass value="0.2549246" />
			<inertia ixx="0.0005765" ixy="0" ixz="0.0002024" iyy="0.0006886" iyz="0" izz="0.0001687" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/r_shoulder_roll_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="-0.024 0 -0.05" rpy="0 0 0" />
			<geometry>
				 <cylinder length="0.04" radius="0.022"/>
			</geometry>
		</collision>
	</link><!-- 18 -->
	<joint name="r_shoulder_roll_joint" type="revolute">
		<origin xyz="0.024 -0.028574 0" rpy="0 0 0" />
		<parent link="r_shoulder_pitch_link" />
		<child link="r_shoulder_roll_link" />
		<axis xyz="1 0 0" />
		<limit lower="-2.27" upper="0" effort="21" velocity="21" />
	</joint> <!-- 18 -->
	<link name="r_shoulder_yaw_link">
		<inertial>
			<origin xyz="0 -0.0014711 -0.0825374" rpy="0 0 0" />
			<mass value="0.2397004" />
			<inertia ixx="0.0018366" ixy="0" ixz="0" iyy="0.0018331" iyz="3.06E-05" izz="5.73E-05" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/r_shoulder_yaw_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="-0 0 -0.035" rpy="0 0 0" />
			<geometry>
				 <cylinder length="0.05" radius="0.01"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="-0 0 -0.094" rpy="1.57 0 0" />
			<geometry>
				 <cylinder length="0.034" radius="0.022"/>
			</geometry>
		</collision>
	</link><!-- 19 -->
	<joint name="r_shoulder_yaw_joint" type="revolute">
		<origin xyz="-0.024 0 -0.071" rpy="0 0 0" />
		<parent link="r_shoulder_roll_link" />
		<child link="r_shoulder_yaw_link" />
		<axis xyz="0 0 1" />
		<limit lower="-1.57" upper="2" effort="21" velocity="21" />
	</joint> <!-- 19 -->
	<link name="r_elbow_link">
		<inertial>
			<origin xyz="0.0294356 0.0093745 0" rpy="0 0 0" />
			<mass value="0.0490686" />
			<inertia ixx="1.46E-05" ixy="3.39E-05" ixz="0" iyy="0.0001645" iyz="0" izz="0.0001744" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/r_elbow_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="0.1 0.024 0" rpy="0 1.57 0" />
			<geometry>
				 <cylinder length="0.14" radius="0.008"/>
			</geometry>
		</collision>
	</link><!-- 20 -->
	<joint name="r_elbow_joint" type="revolute">
		<origin xyz="0.010624 -0.024 -0.094" rpy="0 0 0" />
		<parent link="r_shoulder_yaw_link" />
		<child link="r_elbow_link" />
		<axis xyz="0 1 0" />
		<limit lower="-0.9" upper="1.6" effort="21" velocity="21" />
	</joint> <!-- 20 -->

	<joint name="R_hand_fixed" type="fixed" dont_collapse="true">
		<origin rpy="0 0 0" xyz="0.17 0.03 0"/>
		<parent link="r_elbow_link"/>
		<child link="R_HAND"/>
	</joint> <!-- 21 -->
	<link name="R_HAND">
		<visual>
			<origin rpy="0 0 0" xyz="0 0 0"/>
			<geometry>
			<sphere radius="0.027499999999999997"/>
			</geometry>
			<material name="orange"/>
		</visual>
		<collision>
			<origin rpy="0 0 0" xyz="0 0 0"/>
			<geometry>
			<sphere radius="0.0375"/>
			</geometry>
		</collision>
		<inertial>
			<mass value="0.06"/>
			<inertia ixx="3.375e-05" ixy="0.0" ixz="0.0" iyy="3.375e-05" iyz="0.0" izz="3.375e-05"/>
		</inertial>
	</link><!-- 21 -->


	<link name="l_shoulder_pitch_link">
		<inertial>
			<origin xyz="0.001691 0.0289883 0 " rpy="0 0 0" />
			<mass value="0.2085303" />
			<inertia ixx="0.0002429" ixy="0" ixz="-8.8E-06" iyy="0.0002465" iyz="0" izz="5.51E-05" />
		</inertial>
		<visual>
			<origin xyz="0 -0.003 0" rpy="1.57 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/l_shoulder_pitch_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="0 0.028574 0" rpy="0 1.57 0" />
			<geometry>
				 <cylinder length="0.04" radius="0.022"/>
			</geometry>
		</collision>
	</link><!-- 22 -->
	<joint name="l_shoulder_pitch_joint" type="revolute">
		<origin xyz="-0.00237999999999805 0.0854999999999995 0.204500000000003" rpy="0 0 0" />
		<parent link="base_link" />
		<child link="l_shoulder_pitch_link" />
		<axis xyz="0 1 0" />
		<limit lower="-3.14" upper="3.14" effort="21" velocity="21" />
	</joint> <!-- 22 -->
	<link name="l_shoulder_roll_link">
		<inertial>
			<origin xyz="-0.0154214 0 -0.0421688" rpy="0 0 0" />
			<mass value="0.2549246" />
			<inertia ixx="0.0005765" ixy="0" ixz="0.0002024" iyy="0.0006886" iyz="0" izz="0.0001687" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/l_shoulder_roll_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="-0.024 0 -0.05" rpy="0 0 0" />
			<geometry>
				 <cylinder length="0.04" radius="0.022"/>
			</geometry>
		</collision>
	</link><!-- 23 -->
	<joint name="l_shoulder_roll_joint" type="revolute">
		<origin xyz="0.024 0.028574 -0.0" rpy="0 0 0" />
		<parent link="l_shoulder_pitch_link" />
		<child link="l_shoulder_roll_link" />
		<axis xyz="1 0 0" />
		<limit lower="0" upper="2.27" effort="21" velocity="21" />
	</joint> <!-- 23 -->
	<link name="l_shoulder_yaw_link">
		<inertial>
			<origin xyz="0 0.0014711 -0.08002" rpy="0 0 0" />
			<mass value="0.2397004" />
			<inertia ixx="0.0017385" ixy="0" ixz="0" iyy="0.001735" iyz="-2.98E-05" izz="5.73E-05" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/l_shoulder_yaw_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="-0 0 -0.035" rpy="0 0 0" />
			<geometry>
				 <cylinder length="0.05" radius="0.01"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="-0 0 -0.094" rpy="1.57 0 0" />
			<geometry>
				 <cylinder length="0.034" radius="0.022"/>
			</geometry>
		</collision>
	</link><!-- 24 -->
	<joint name="l_shoulder_yaw_joint" type="revolute">
		<origin xyz="-0.024 0 -0.071" rpy="0 0 0" />
		<parent link="l_shoulder_roll_link" />
		<child link="l_shoulder_yaw_link" />
		<axis xyz="0 0 1" />
		<limit lower="-2" upper="1.57" effort="21" velocity="21" />
	</joint> <!-- 24 -->
	<link name="l_elbow_link">
		<inertial>
			<origin xyz="0.0400599 -0.0093745 0" rpy="0 0 0" />
			<mass value="0.0490686" />
			<inertia ixx="1.46E-05" ixy="-3.88E-05" ixz="0" iyy="0.0002007" iyz="0" izz="0.0002106" />
		</inertial>
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<geometry>
				<mesh filename="resources/robots/gaoqing/meshes/gaoqing/l_elbow_link.STL" />
			</geometry>
			<material name="">
				<color rgba="0.75294 0.75294 0.75294 1" />
			</material>
		</visual>
		<collision>
			<origin xyz="0.1 -0.024 0" rpy="0 1.57 0" />
			<geometry>
				 <cylinder length="0.14" radius="0.008"/>
			</geometry>
		</collision>
	</link><!-- 25 -->
	<joint name="l_elbow_joint" type="revolute">
		<origin xyz="0 0.024 -0.091483" rpy="0 0 0" />
		<parent link="l_shoulder_yaw_link" />
		<child link="l_elbow_link" />
		<axis xyz="0 1 0" />
		<limit lower="-0.9" upper="1.6" effort="21" velocity="21" />
	</joint> <!-- 25 -->

	<joint name="L_hand_fixed" type="fixed" dont_collapse="true">
		<origin rpy="0 0 0" xyz="0.17 -0.03 0"/>
		<parent link="l_elbow_link"/>
		<child link="L_HAND"/>
	  </joint> <!-- 26 -->
	  <link name="L_HAND">
		<visual>
		  <origin rpy="0 0 0" xyz="0 0 0"/>
		  <geometry>
			<sphere radius="0.027499999999999997"/>
		  </geometry>
		  <material name="orange"/>
		</visual>
		<collision>
		  <origin rpy="0 0 0" xyz="0 0 0"/>
		  <geometry>
			<sphere radius="0.0375"/>
		  </geometry>
		</collision>
		<inertial>
		  <mass value="0.06"/>
		  <inertia ixx="3.375e-05" ixy="0.0" ixz="0.0" iyy="3.375e-05" iyz="0.0" izz="3.375e-05"/>
		</inertial>
	  </link><!-- 26 -->

	<link name="dummy_link">
		<inertial>
			<mass value="0.0" />
			<inertia ixx="0.0" ixy="0.0" ixz="0.0" iyy="0.0" iyz="0.0" izz="0.0" />
		</inertial>
	</link><!-- 27 -->
	
	 <!--Create a fixed joint that connects the base_link to the dummy_link -->
	<joint name="base_to_dummy_joint" type="fixed">
		<parent link="base_link"/>
		<child link="dummy_link"/>
		<origin xyz="0 0 0" rpy="0 0 0"/>
	</joint> <!-- 27 -->


	<!-- Joint 1 and Torso Link -->
	<joint name="joint_1" type="fixed">
		<parent link="base_link"/>
		<child link="torso"/>
		<origin xyz="0 0 0" rpy="0 0 0"/>
	</joint><!-- 28 -->

	<link name="torso">
		<inertial>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<mass value="0" />
			<inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0" />
		</inertial>
	</link><!-- 28 -->

	<!-- Joint 2 and Neck Link -->
	<joint name="joint_2" type="fixed">
		<parent link="torso"/>
		<child link="neck"/>
		<origin xyz="0 0 0" rpy="0 0 0"/>
	</joint><!-- 29 -->

	<link name="neck">
		<inertial>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<mass value="0" />
			<inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0" />
		</inertial>
	</link><!-- 29 -->

	<!-- Joint 3 and Head Link -->
	<joint name="joint_3" type="fixed">
		<parent link="neck"/>
		<child link="head"/>
		<origin xyz="0 0 0" rpy="0 0 0"/>
	</joint><!-- 30 -->

	<link name="head">
		<inertial>
			<origin xyz="0 0 0" rpy="0 0 0" />
			<mass value="0" />
			<inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0" />
		</inertial>
	</link><!-- 30 -->


</robot>
