<?xml version='1.0' encoding='utf-8'?>

<robot name="AgiBot-X2">
    <mujoco>
        <compiler meshdir="meshes" discardvisual="false"/>
    </mujoco>


    <!-- Base Link -->

    <!-- [CAUTION] uncomment when convert to mujoco -->
    <!-- <link name="world"></link>
    <joint name="floating_base_joint" type="floating">
      <parent link="world"/>
      <child link="base_link"/>
    </joint> -->


    <link name="base_link">
        <inertial>
            <origin xyz="-0.00064 0.00016 -0.11191" rpy="0 0 0"/>
            <mass value="4.60817"/>
            <inertia ixx="0.02285" iyy="0.01982" izz="0.00853" ixy="-0.00000" iyz="-0.00004" ixz="-0.00022"/>
        </inertial>

        <collision>
            <origin xyz="0 0 -0.14" rpy="0 0 0" />
            <geometry>
                <box size="0.1 0.16 0.12" />
            </geometry>
        </collision> 

        <visual name="pelvis_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/pelvis.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="metal">
                <color rgba="0.7 0.7 0.7 1"/>
            </material>
        </visual>
    </link>

    <link name="pelvis_contour_link">
        <inertial>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <mass value="0.001"/>
            <inertia ixx="1e-7" ixy="0" ixz="0" iyy="1e-7" iyz="0" izz="1e-7"/>
        </inertial>

        <!-- <collision name="pelvis_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/pelvis_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="pelvis_pelvis-balck_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/pelvis_pelvis-balck.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="black">
                <color rgba="0.1 0.1 0.1 1"/>
            </material>
        </visual>
    </link>

    <joint name="pelvis_contour_joint" type="fixed">
        <parent link="base_link"/>
        <child link="pelvis_contour_link"/>
    </joint>


    <!-- Legs -->
    <link name="leg_l1_link">
        <inertial>
            <origin xyz="0.00602 0.04824 -0.00091" rpy="0 0 0"/>
            <mass value="1.27834"/>
            <inertia ixx="0.00164" iyy="0.00114" izz="0.00125" ixy="-0.00001" iyz="0.00001" ixz="-0.00002"/>
        </inertial>

        <visual name="left_hip_pitch_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_hip_pitch.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>

        <visual name="left_hip_pitch_left_hip_pitch-black_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_hip_pitch_left_hip_pitch-black.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="black">
                <color rgba="0.1 0.1 0.1 1"/>
            </material>
        </visual>
    </link>

    <joint name="leg_l1_joint" type="revolute">
        <origin xyz="0 0.08950 -0.13693" rpy="0 0 0"/>
        <parent link="base_link"/>
        <child link="leg_l1_link"/>
        <axis xyz="0 1 0"/>
        <limit lower="-2.6180" upper="2.6180" effort="110" velocity="11"/>
    </joint>


    <link name="leg_l2_link">
        <inertial>
            <origin xyz="0.00052 0.00017 -0.08251" rpy="0 0 0"/>
            <mass value="1.63368"/>
            <inertia ixx="0.00364" iyy="0.00427" izz="0.00266" ixy="0" iyz="-0.00007" ixz="-0.00001"/>
        </inertial>

        <!-- <collision name="left_hip_roll_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_hip_roll_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="left_hip_roll_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_hip_roll.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>

        <visual name="left_hip_roll_left_hip_roll-black_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_hip_roll_left_hip_roll-black.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="black">
                <color rgba="0.1 0.1 0.1 1"/>
            </material>
        </visual>
    </link>

    <joint name="leg_l2_joint" type="revolute">
        <origin xyz="0 0.05000 0" rpy="0 0 0"/>
        <parent link="leg_l1_link"/>
        <child link="leg_l2_link"/>
        <axis xyz="1 0 0"/>
        <limit lower="-0.1745" upper="2.9671" effort="110" velocity="11"/>
    </joint>


    <link name="leg_l3_link">
        <inertial>
            <origin xyz="-0.00276 0.00515 -0.14201" rpy="0 0 0"/>
            <mass value="1.46263"/>
            <inertia ixx="0.00368" iyy="0.00415" izz="0.00144" ixy="0.00001" iyz="0.00014" ixz="-0.00015"/>
        </inertial>

        <!-- <collision name="left_hip_yaw_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_hip_yaw_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="left_hip_yaw_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_hip_yaw.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>

        <visual name="left_hip_yaw_left_hip_yaw-metal_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_hip_yaw_left_hip_yaw-metal.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="metal">
                <color rgba="0.7 0.7 0.7 1"/>
            </material>
        </visual>
    </link>

    <joint name="leg_l3_joint" type="revolute">
        <origin xyz="0 0 -0.13420" rpy="0 0 0"/>
        <parent link="leg_l2_link"/>
        <child link="leg_l3_link"/>
        <axis xyz="0 0 1"/>
        <limit lower="-1.7453" upper="3.4906" effort="110" velocity="11"/>
    </joint>


    <link name="leg_l4_link">
        <inertial>
            <origin xyz="-0.00612 0.00425 -0.14176" rpy="0 0 0"/>
            <mass value="1.42486"/>
            <inertia ixx="0.00717" iyy="0.00763" izz="0.00195" ixy="0.00003" iyz="-0.00021" ixz="-0.00026"/>
        </inertial>

        <!-- <collision name="left_knee_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_knee_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="left_knee_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_knee.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>

        <visual name="left_knee_left_knee-metalic_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_knee_left_knee-metalic.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="metal">
                <color rgba="0.9 0.9 0.9 1"/>
            </material>
        </visual>
    </link>

    <joint name="leg_l4_joint" type="revolute">
        <origin xyz="-0.00500 0 -0.16050" rpy="0 0 0"/>
        <parent link="leg_l3_link"/>
        <child link="leg_l4_link"/>
        <axis xyz="0 1 0"/>
        <limit lower="0" upper="2.1817" effort="110" velocity="11"/>
    </joint>


    <link name="leg_l5_link">
        <inertial>
            <origin xyz="-0.00395 0 0" rpy="0 0 0"/>
            <mass value="0.04275"/>
            <inertia ixx="0.00001" iyy="0.00001" izz="0.00001" ixy="0" iyz="0" ixz="0"/>
        </inertial>

        <visual name="left_ankle_pitch_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_ankle_pitch.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="metal">
                <color rgba="0.7 0.7 0.7 1"/>
            </material>
        </visual>
    </link>

    <joint name="leg_l5_joint" type="revolute">
        <origin xyz="-0.02000 0 -0.28000" rpy="0 0 0"/>
        <parent link="leg_l4_link"/>
        <child link="leg_l5_link"/>
        <axis xyz="0 1 0"/>
        <limit lower="-0.8727" upper="0.4887" effort="40" velocity="7.5"/>
    </joint>


    <link name="leg_l6_link">
        <inertial>
            <origin xyz="-0.00473 -0.00012 -0.03049" rpy="0 0 0"/>
            <mass value="1.03041"/>
            <inertia ixx="0.00145" iyy="0.00461" izz="0.00439" ixy="0" iyz="0.00001" ixz="0.00093"/>
        </inertial>

        <!-- <collision>
            <origin xyz="0.125 -0.05 -0.068" rpy="0 0 0"/>
            <geometry>
                <sphere radius="0.005"/>
            </geometry>
        </collision>
        <collision>
            <origin xyz="0.125 0.05 -0.068" rpy="0 0 0"/>
            <geometry>
                <sphere radius="0.005"/>
            </geometry>
        </collision>
        <collision>
            <origin xyz="-0.0638 -0.05 -0.068" rpy="0 0 0"/>
            <geometry>
                <sphere radius="0.005"/>
            </geometry>
        </collision>
        <collision>
            <origin xyz="-0.0638 0.05 -0.068" rpy="0 0 0"/>
            <geometry>
                <sphere radius="0.005"/>
            </geometry>
        </collision> -->

        <collision>
        <origin xyz="0.03 0.0 -0.052" rpy="0 0 0" />
        <geometry>
            <box size="0.18 0.096 0.032" />
        </geometry>
        </collision> 

        <visual name="left_ankle_roll_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_ankle_roll.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>

        <visual name="left_ankle_roll_left_ankle_roll-balck_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_ankle_roll_left_ankle_roll-balck.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="black">
                <color rgba="0.1 0.1 0.1 1"/>
            </material>
        </visual>

        <visual name="left_ankle_roll_left_ankle_roll-yellow_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_ankle_roll_left_ankle_roll-yellow.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="metal">
                <color rgba="1 0.82 0.31 1"/>
            </material>
        </visual>
    </link>

    <joint name="leg_l6_joint" type="revolute">
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <parent link="leg_l5_link"/>
        <child link="leg_l6_link"/>
        <axis xyz="1 0 0"/>
        <limit lower="-0.4887" upper="0.4887" effort="20" velocity="15"/>
    </joint>


    <link name="leg_r1_link">
        <inertial>
            <origin xyz="0.00607 -0.04815 -0.00128" rpy="0 0 0"/>
            <mass value="1.26879"/>
            <inertia ixx="0.00161" iyy="0.00112" izz="0.00124" ixy="0.00001" iyz="-0.00001" ixz="-0.00002"/>
        </inertial>

        <visual name="right_hip_pitch_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_hip_pitch.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>

        <visual name="right_hip_pitch_right_hip_pitch-black_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_hip_pitch_right_hip_pitch-black.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="black">
                <color rgba="0.1 0.1 0.1 1"/>
            </material>
        </visual>
    </link>

    <joint name="leg_r1_joint" type="revolute">
        <origin xyz="0 -0.08950 -0.13693" rpy="0 0 0"/>
        <parent link="base_link"/>
        <child link="leg_r1_link"/>
        <axis xyz="0 1 0"/>
        <limit lower="-2.6180" upper="2.6180" effort="110" velocity="11"/>
    </joint>


    <link name="leg_r2_link">
        <inertial>
            <origin xyz="0.00052 -0.00017 -0.08251" rpy="0 0 0"/>
            <mass value="1.63368"/>
            <inertia ixx="0.00364" iyy="0.00427" izz="0.00266" ixy="0" iyz="0.00001" ixz="-0.00007"/>
        </inertial>

        <!-- <collision name="right_hip_roll_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_hip_roll_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="right_hip_roll_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_hip_roll.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>

        <visual name="right_hip_roll_right_hip_roll-black_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_hip_roll_right_hip_roll-black.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="black">
                <color rgba="0.1 0.1 0.1 1"/>
            </material>
        </visual>
    </link>

    <joint name="leg_r2_joint" type="revolute">
        <origin xyz="0 -0.05000 0" rpy="0 0 0"/>
        <parent link="leg_r1_link"/>
        <child link="leg_r2_link"/>
        <axis xyz="1 0 0"/>
        <limit lower="-2.9671" upper="0.1745" effort="110" velocity="11"/>
    </joint>


    <link name="leg_r3_link">
        <inertial>
            <origin xyz="-0.00276 -0.00515 -0.14201" rpy="0 0 0"/>
            <mass value="1.46263"/>
            <inertia ixx="0.00368" iyy="0.00415" izz="0.00144" ixy="-0.00001" iyz="-0.00014" ixz="-0.00015"/>
        </inertial>

        <!-- <collision name="right_hip_yaw_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_hip_yaw_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="right_hip_yaw_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_hip_yaw.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>

        <visual name="right_hip_yaw_right_hip_yaw-metal_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_hip_yaw_right_hip_yaw-metal.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="metal">
                <color rgba="0.7 0.7 0.7 1"/>
            </material>
        </visual>
    </link>

    <joint name="leg_r3_joint" type="revolute">
        <origin xyz="0 0 -0.13420" rpy="0 0 0"/>
        <parent link="leg_r2_link"/>
        <child link="leg_r3_link"/>
        <axis xyz="0 0 1"/>
        <limit lower="-3.4906" upper="1.7453" effort="110" velocity="11"/>
    </joint>


    <link name="leg_r4_link">
        <inertial>
            <origin xyz="-0.00605 -0.00425 -0.14176" rpy="0 0 0"/>
            <mass value="1.42485"/>
            <inertia ixx="0.00717" iyy="0.00763" izz="0.00194" ixy="-0.00003" iyz="0.00021" ixz="-0.00026"/>
        </inertial>

        <!-- <collision name="right_knee_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_knee_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="right_knee_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_knee.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>

        <visual name="right_knee_right_knee-metalic_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_knee_right_knee-metalic.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="metal">
                <color rgba="0.9 0.9 0.9 1"/>
            </material>
        </visual>
    </link>

    <joint name="leg_r4_joint" type="revolute">
        <origin xyz="-0.00500 0 -0.16050" rpy="0 0 0"/>
        <parent link="leg_r3_link"/>
        <child link="leg_r4_link"/>
        <axis xyz="0 1 0"/>
        <limit lower="0" upper="2.1817" effort="110" velocity="11"/>
    </joint>


    <link name="leg_r5_link">
        <inertial>
            <origin xyz="-0.00395 0 0" rpy="0 0 0"/>
            <mass value="0.04275"/>
            <inertia ixx="0.00001" iyy="0.00001" izz="0.00001" ixy="0" iyz="0" ixz="0"/>
        </inertial>

        <visual name="right_ankle_pitch_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_ankle_pitch.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="metal">
                <color rgba="0.9 0.9 0.9 1"/>
            </material>
        </visual>
    </link>

    <joint name="leg_r5_joint" type="revolute">
        <origin xyz="-0.02000 0 -0.28000" rpy="0 0 0"/>
        <parent link="leg_r4_link"/>
        <child link="leg_r5_link"/>
        <axis xyz="0 1 0"/>
        <limit lower="-0.8727" upper="0.4887" effort="40" velocity="7.5"/>
    </joint>


    <link name="leg_r6_link">
        <inertial>
            <origin xyz="-0.00473 0.00012 -0.03049" rpy="0 0 0"/>
            <mass value="1.03041"/>
            <inertia ixx="0.00145" iyy="0.00461" izz="0.00439" ixy="0" iyz="-0.00001" ixz="0.00093"/>
        </inertial>

        <!-- <collision>
            <origin xyz="0.125 -0.05 -0.068" rpy="0 0 0"/>
            <geometry>
                <sphere radius="0.005"/>
            </geometry>
        </collision>
        <collision>
            <origin xyz="0.125 0.05 -0.068" rpy="0 0 0"/>
            <geometry>
                <sphere radius="0.005"/>
            </geometry>
        </collision>
        <collision>
            <origin xyz="-0.0638 -0.05 -0.068" rpy="0 0 0"/>
            <geometry>
                <sphere radius="0.005"/>
            </geometry>
        </collision>
        <collision>
            <origin xyz="-0.0638 0.05 -0.068" rpy="0 0 0"/>
            <geometry>
                <sphere radius="0.005"/>
            </geometry>
        </collision> -->

        <collision>
        <origin xyz="0.03 0.0 -0.052" rpy="0 0 0" />
        <geometry>
            <box size="0.18 0.096 0.032" />
        </geometry>
        </collision> 

        <visual name="right_ankle_roll_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_ankle_roll.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>

        <visual name="right_ankle_roll_right_ankle_roll-balck_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_ankle_roll_right_ankle_roll-balck.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="black">
                <color rgba="0.1 0.1 0.1 1"/>
            </material>
        </visual>

        <visual name="right_ankle_roll_right_ankle_roll-yellow_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_ankle_roll_right_ankle_roll-yellow.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="metal">
                <color rgba="1 0.82 0.31 1"/>
            </material>
        </visual>
    </link>

    <joint name="leg_r6_joint" type="revolute">
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <parent link="leg_r5_link"/>
        <child link="leg_r6_link"/>
        <axis xyz="1 0 0"/>
        <limit lower="-0.4887" upper="0.4887" effort="20" velocity="15"/>
    </joint>


    <!-- Torso -->
    <link name="waist_link">
        <inertial>
            <origin xyz="-0.00821 -0.00028 0.19986" rpy="0 0 0"/>
            <mass value="8.02725"/>
            <inertia ixx="0.06648" iyy="0.06010" izz="0.04703" ixy="0.00004" iyz="-0.00017" ixz="0.00281"/>
        </inertial>

        <!-- <collision name="torso_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/torso_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="torso_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/torso.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>

        <visual name="torso_torso-balck_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/torso_torso-balck.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="black">
                <color rgba="0.1 0.1 0.1 1"/>
            </material>
        </visual>

        <visual name="torso_torso-red_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/torso_torso-red.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="red">
                <color rgba="0.9 0.0 0.0 1"/>
            </material>
        </visual>
    </link>

    <joint name="idx21_waist_joint" type="revolute">
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <parent link="base_link"/>
        <child link="waist_link"/>
        <axis xyz="0 0 1"/>
        <limit lower="-3.1416" upper="3.1416" effort="110" velocity="11"/>
    </joint>


    <link name="head_link">
        <inertial>
            <origin xyz="-0.00388 0.00024 0.04732" rpy="0 0 0"/>
            <mass value="0.46472"/>
            <inertia ixx="0.00140" iyy="0.00167" izz="0.00129" ixy="0" iyz="-0.00001" ixz="0.00007"/>
        </inertial>

        <!-- <collision name="head_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/head_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="head_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/head.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>

        <visual name="head_head-black_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/head_head-black.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="black">
                <color rgba="0.1 0.1 0.1 1"/>
            </material>
        </visual>

        <visual name="head_head-transparent_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/head_head-transparent.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="black">
                <color rgba="0.1 0.1 0.1 0.9"/>
            </material>
        </visual>
    </link>

    <joint name="head_joint" type="fixed">
        <origin xyz="0.00103 0 0.34100" rpy="0 0 0"/>
        <parent link="waist_link"/>
        <child link="head_link"/>
        <axis xyz="0 0 1"/>
        <limit lower="-1.5708" upper="1.5708" effort="4" velocity="7"/>
    </joint>


    <!-- Arms -->
    <link name="left_arm_link01">
        <inertial>
            <origin xyz="0.00593 0.07913 0" rpy="0 0 0"/>
            <mass value="0.79706"/>
            <inertia ixx="0.00123" iyy="0.00051" izz="0.00117" ixy="-0.00008" iyz="0" ixz="0"/>
        </inertial>

        <visual name="left_shoulder_pitch_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_shoulder_pitch.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>
    </link>

    <joint name="idx13_left_arm_joint1" type="revolute">
        <origin xyz="0 0.09731 0.23319" rpy="0.20944 0 0"/>
        <parent link="waist_link"/>
        <child link="left_arm_link01"/>
        <axis xyz="0 1 0"/>
        <limit lower="-2.618" upper="2.618" effort="110" velocity="11"/>
    </joint>


    <link name="left_arm_link02">
        <inertial>
            <origin xyz="-0.00043 0 -0.05768" rpy="0 0 0"/>
            <mass value="0.66035"/>
            <inertia ixx="0.00127" iyy="0.00140" izz="0.00061" ixy="0" iyz="0" ixz="0.00002"/>
        </inertial>

        <!-- <collision name="left_shoulder_roll_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_shoulder_roll_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="left_shoulder_roll_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_shoulder_roll.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>
    </link>

    <joint name="idx14_left_arm_joint2" type="revolute">
        <origin xyz="0 0.09500 0" rpy="-0.003 0 0"/>
        <parent link="left_arm_link01"/>
        <child link="left_arm_link02"/>
        <axis xyz="1 0 0"/>
        <limit lower="-0.1222" upper="3.6826" effort="30" velocity="21"/>
    </joint>


    <link name="left_arm_link03">
        <inertial>
            <origin xyz="0.00361 -0.00159 -0.07060" rpy="0 0 0"/>
            <mass value="0.62086"/>
            <inertia ixx="0.00113" iyy="0.00121" izz="0.00029" ixy="0.00001" iyz="-0.00004" ixz="0.00012"/>
        </inertial>

        <!-- <collision name="left_shoulder_yaw_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_shoulder_yaw_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="left_shoulder_yaw_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_shoulder_yaw.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>

        <visual name="left_shoulder_yaw_left_shoulder_yaw-metal_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_shoulder_yaw_left_shoulder_yaw-metal.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="metal">
                <color rgba="1 0.82 0.31 1"/>
            </material>
        </visual>
    </link>

    <joint name="idx15_left_arm_joint3" type="revolute">
        <origin xyz="0 0 -0.10737" rpy="0 0 0"/>
        <parent link="left_arm_link02"/>
        <child link="left_arm_link03"/>
        <axis xyz="0 0 1"/>
        <limit lower="-0.9598" upper="2.7048" effort="20" velocity="15"/>
    </joint>


    <link name="left_arm_link04">
        <inertial>
            <origin xyz="0.06889 -0.00152 -0.01384" rpy="0 0 0"/>
            <mass value="0.63231"/>
            <inertia ixx="0.00047" iyy="0.00080" izz="0.00078" ixy="-0.00006" iyz="0.00001" ixz="0.00002"/>
        </inertial>

        <!-- <collision name="left_elbow_pitch_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_elbow_pitch_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="left_elbow_pitch_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_elbow_pitch.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>
    </link>

    <joint name="idx16_left_arm_joint4" type="revolute">
        <origin xyz="0.01302 -0.00004 -0.11636" rpy="0 1.141 0"/>
        <parent link="left_arm_link03"/>
        <child link="left_arm_link04"/>
        <axis xyz="0 1 0"/>
        <limit lower="-0.8376" upper="1.5708" effort="40" velocity="7.5"/>
    </joint>


    <link name="left_elbow_roll_link">
        <inertial>
            <origin xyz="0.04335 0.00040 0.00001" rpy="0 0 0"/>
            <mass value="0.35334"/>
            <inertia ixx="0.00035" iyy="0.00037" izz="0.00036" ixy="0" iyz="0" ixz="0"/>
        </inertial>

        <!-- <collision name="left_elbow_roll_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_elbow_roll_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="left_elbow_roll_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_elbow_roll.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>
    </link>

    <joint name="left_elbow_roll_joint" type="fixed">
        <origin xyz="0.10617 0.00004 -0.01302" rpy="0 0 0"/>
        <parent link="left_arm_link04"/>
        <child link="left_elbow_roll_link"/>
        <axis xyz="1 0 0"/>
        <limit lower="-2.2689" upper="2.2689" effort="20" velocity="15"/>
    </joint>


    <link name="left_wrist_yaw_link">
        <inertial>
            <origin xyz="-0.00535 0 0" rpy="0 0 0"/>
            <mass value="0.17995"/>
            <inertia ixx="0.00006" iyy="0.00008" izz="0.00010" ixy="0" iyz="0" ixz="0"/>
        </inertial>

        <visual name="left_wrist_yaw_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_wrist_yaw.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="metal">
                <color rgba="1 0.82 0.31 1"/>
            </material>
        </visual>
    </link>

    <joint name="left_wrist_yaw_joint" type="fixed">
        <origin xyz="0.09770 0 0" rpy="0 0 0"/>
        <parent link="left_elbow_roll_link"/>
        <child link="left_wrist_yaw_link"/>
        <axis xyz="0 0 1"/>
        <limit lower="-0.7854" upper="0.7854" effort="4" velocity="7"/>
    </joint>


    <link name="left_wrist_pitch_link">
        <inertial>
            <origin xyz="0.04343 0 0" rpy="0 0 0"/>
            <mass value="0.09865"/>
            <inertia ixx="0.00010" iyy="0.00007" izz="0.00010" ixy="0" iyz="0" ixz="0"/>
        </inertial>

        <!-- <collision name="left_wrist_pitch_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_wrist_pitch_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="left_wrist_pitch_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/left_wrist_pitch.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="metal">
                <color rgba="1 0.82 0.31 1"/>
            </material>
        </visual>
    </link>

    <joint name="left_wrist_pitch_joint" type="fixed">
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <parent link="left_wrist_yaw_link"/>
        <child link="left_wrist_pitch_link"/>
        <axis xyz="0 1 0"/>
        <limit lower="-0.7854" upper="0.7854" effort="4" velocity="7"/>
    </joint>


    <link name="right_arm_link01">
        <inertial>
            <origin xyz="0.00593 -0.07914 0" rpy="0 0 0"/>
            <mass value="0.79705"/>
            <inertia ixx="0.00123" iyy="0.00051" izz="0.00117" ixy="0.00008" iyz="0" ixz="0"/>
        </inertial>

        <visual name="right_shoulder_pitch_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_shoulder_pitch.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </visual>
    </link>

    <joint name="idx17_right_arm_joint1" type="revolute">
        <origin xyz="0 -0.09730 0.23319" rpy="-0.20944 0 0"/>
        <parent link="waist_link"/>
        <child link="right_arm_link01"/>
        <axis xyz="0 1 0"/>
        <limit lower="-2.618" upper="2.618" effort="110" velocity="11"/>
    </joint>


    <link name="right_arm_link02">
        <inertial>
            <origin xyz="-0.00043 0 -0.05768" rpy="0 0 0"/>
            <mass value="0.66035"/>
            <inertia ixx="0.00127" iyy="0.00140" izz="0.00061" ixy="0" iyz="0" ixz="0.00002"/>
        </inertial>

        <!-- <collision name="right_shoulder_roll_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_shoulder_roll_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="right_shoulder_roll_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_shoulder_roll.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>
    </link>

    <joint name="idx18_right_arm_joint2" type="revolute">
        <origin xyz="0 -0.09500 0" rpy="0.003 0 0"/>
        <parent link="right_arm_link01"/>
        <child link="right_arm_link02"/>
        <axis xyz="1 0 0"/>
        <limit lower="-3.6826" upper="0.1222" effort="30" velocity="21"/>
    </joint>


    <link name="right_arm_link03">
        <inertial>
            <origin xyz="0.00361 0.00159 -0.07060" rpy="0 0 0"/>
            <mass value="0.62086"/>
            <inertia ixx="0.00113" iyy="0.00121" izz="0.00029" ixy="-0.00001" iyz="0.00004" ixz="0.00012"/>
        </inertial>

        <!-- <collision name="right_shoulder_yaw_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_shoulder_yaw_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="right_shoulder_yaw_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_shoulder_yaw.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>

        <visual name="right_shoulder_yaw_right_shoulder_yaw-metal_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_shoulder_yaw_right_shoulder_yaw-metal.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="metal">
                <color rgba="1 0.82 0.31 1"/>
            </material>
        </visual>
    </link>

    <joint name="idx19_right_arm_joint3" type="revolute">
        <origin xyz="0 0 -0.10737" rpy="0 0 0"/>
        <parent link="right_arm_link02"/>
        <child link="right_arm_link03"/>
        <axis xyz="0 0 1"/>
        <limit lower="-2.7048" upper="0.9598" effort="20" velocity="15"/>
    </joint>


    <link name="right_elbow_pitch_link">
        <inertial>
            <origin xyz="0.06889 0.00152 -0.01384" rpy="0 0 0"/>
            <mass value="0.63230"/>
            <inertia ixx="0.00047" iyy="0.00080" izz="0.00078" ixy="0.00006" iyz="-0.00001" ixz="0.00002"/>
        </inertial>

        <!-- <collision name="right_elbow_pitch_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_elbow_pitch_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="right_elbow_pitch_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_elbow_pitch.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>
    </link>

    <joint name="idx20_right_arm_joint4" type="revolute">
        <origin xyz="0.01302 0.00004 -0.11636" rpy="0 1.141 0"/>
        <parent link="right_arm_link03"/>
        <child link="right_elbow_pitch_link"/>
        <axis xyz="0 1 0"/>
        <limit lower="-0.8376" upper="1.5708" effort="40" velocity="7.5"/>
    </joint>


    <link name="right_arm_link04">
        <inertial>
            <origin xyz="0.04335 -0.00040 0.00001" rpy="0 0 0"/>
            <mass value="0.35334"/>
            <inertia ixx="0.00035" iyy="0.00037" izz="0.00036" ixy="0" iyz="0" ixz="0"/>
        </inertial>

        <!-- <collision name="right_elbow_roll_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_elbow_roll_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="right_elbow_roll_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_elbow_roll.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="white">
                <color rgba="1 1 1 1"/>
            </material>
        </visual>
    </link>

    <joint name="right_elbow_roll_joint" type="fixed">
        <origin xyz="0.10617 -0.00004 -0.01302" rpy="0 0 0"/>
        <parent link="right_elbow_pitch_link"/>
        <child link="right_arm_link04"/>
        <axis xyz="1 0 0"/>
        <limit lower="-2.2689" upper="2.2689" effort="20" velocity="15"/>
    </joint>


    <link name="right_wrist_yaw_link">
        <inertial>
            <origin xyz="-0.00535 0 0" rpy="0 0 0"/>
            <mass value="0.17995"/>
            <inertia ixx="0.00006" iyy="0.00008" izz="0.00010" ixy="0" iyz="0" ixz="0"/>
        </inertial>

        <visual name="right_wrist_yaw_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_wrist_yaw.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="metal">
                <color rgba="1 0.82 0.31 1"/>
            </material>
        </visual>
    </link>

    <joint name="rright_wrist_yaw_joint" type="fixed">
        <origin xyz="0.09770 0 0" rpy="0 0 0"/>
        <parent link="right_arm_link04"/>
        <child link="right_wrist_yaw_link"/>
        <axis xyz="0 0 1"/>
        <limit lower="-0.7854" upper="0.7854" effort="4" velocity="7"/>
    </joint>


    <link name="right_wrist_pitch_link">
        <inertial>
            <origin xyz="0.04343 0 0" rpy="0 0 0"/>
            <mass value="0.09865"/>
            <inertia ixx="0.00010" iyy="0.00007" izz="0.00010" ixy="0" iyz="0" ixz="0"/>
        </inertial>

        <!-- <collision name="right_wrist_pitch_collider">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_wrist_pitch_collider.stl" scale="0.001 0.001 0.001"/>
            </geometry>
        </collision> -->

        <visual name="right_wrist_pitch_geom">
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/right_wrist_pitch.stl" scale="0.001 0.001 0.001"/>
            </geometry>
            <material name="metal">
                <color rgba="1 0.82 0.31 1"/>
            </material>
        </visual>
    </link>

    <joint name="right_wrist_pitch_joint" type="fixed">
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <parent link="right_wrist_yaw_link"/>
        <child link="right_wrist_pitch_link"/>
        <axis xyz="0 1 0"/>
        <limit lower="-0.7854" upper="0.7854" effort="4" velocity="7"/>
    </joint>


    <!-- IMU -->
    <link name="imu_in_torso"></link>
    <joint name="imu_in_torso_joint" type="fixed">
        <origin xyz="0.0618 0 0.1667" rpy="0 0 0"/>
        <parent link="waist_link"/>
        <child link="imu_in_torso"/>
    </joint>

    <link name="imu_in_pelvis"></link>
    <joint name="imu_in_pelvis_joint" type="fixed">
        <origin xyz="0 0 -0.1" rpy="0 0 0"/>
        <parent link="base_link"/>
        <child link="imu_in_pelvis"/>
    </joint>
</robot>