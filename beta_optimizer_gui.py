import sys  
import os  


# 导入PyQt5 GUI相关模块
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QSlider, QPushButton, QGroupBox,
                            QFileDialog, QLineEdit, QDoubleSpinBox, QTabWidget)
# QApplication: 应用程序类，管理GUI程序的控制流和主要设置
# QMainWindow: 主窗口类，提供主应用程序窗口
# QWidget: 所有用户界面对象的基类
# QVBoxLayout: 垂直布局管理器
# QHBoxLayout: 水平布局管理器
# QLabel: 文本或图像显示标签
# QSlider: 滑块控件
# QPushButton: 按钮控件
# QGroupBox: 分组框控件
# QFileDialog: 文件对话框
# QLineEdit: 单行文本输入框
# QDoubleSpinBox: 浮点数输入框
# QTabWidget: 标签页控件

# 导入PyQt5核心模块
from PyQt5.QtCore import Qt, QTimer
# Qt: 包含Qt的核心非GUI功能
# QTimer: 定时器类，用于定时触发事件

# 导入PyQt5图表模块
from PyQt5.QtChart import QChart, QChartView, QLineSeries, QValueAxis
# QChart: 图表基类
# QChartView: 图表视图类
# QLineSeries: 线图数据系列
# QValueAxis: 数值坐标轴

# 导入PyQt5绘图模块
from PyQt5.QtGui import QPainter, QColor, QPen
# QPainter: 用于绘制图形和颜色
# QPen: 用于设置线条样式

# 导入数值计算模块
import numpy as np  # 用于数值计算

# 导入自定义模块
from humanoid.utils import humanoid_batch_register  # 导入人形机器人环境注册模块
import torch  # 导入PyTorch深度学习框架
import argparse  # 用于解析命令行参数
import json  # 用于处理JSON数据
import time  # 用于时间相关操作
import queue  # 用于线程间通信的队列

# 导入自定义的SMPL模型相关模块
from utils.smpl_beta_core import SMPLBetaOptimizer, SMPLVisualizer, OptimizationParams
# SMPLBetaOptimizer: SMPL模型的beta参数优化器
# SMPLVisualizer: SMPL模型可视化器
# OptimizationParams: 优化参数类

class BetaOptimizerGUI(QMainWindow):
    """SMPL Beta参数优化器的GUI类"""
    
    def __init__(self, default_config_path="configs/default_beta_config.json", 
                 save_result="shape_scale_bias.json", 
                 save_config_path="configs/default_beta_config.json",
                 device="cpu", task_name="cdroid_retarget", args=None):
        """初始化函数
        
        Args:
            default_config_path (str): 默认配置文件的路径，默认为 "configs/default_beta_config.json"
            save_result (str): 优化结果保存的文件名称，默认为 "shape_scale_bias.json"
            save_config_path (str): 配置保存的完整文件路径，默认为 "configs/default_beta_config.json"
            device (str): 运行设备，可选 "cpu" 或 "cuda"，默认为 "cpu"
            task_name (str): 任务名称，可选值包括：
                - A2_retarget
                - T1_retarget
                - dobot_retarget
                - unitreeG1_retarget
                - x2_retarget
                - cdroid_retarget
                默认为 "cdroid_retarget"
            args: 命令行参数对象
        """
        super().__init__()  # 调用父类初始化
        self.setWindowTitle("SMPL Beta 优化器")  # 设置窗口标题
        self.setGeometry(100, 100, 1600, 1000)  # 设置窗口位置和大小
        
        # 保存命令行参数
        self.args = args
        
        # 设置默认配置文件路径
        self.default_config_path = default_config_path
        
        # 设置优化结果保存路径
        task_prefix = task_name.split('_')[0]
        # 检查文件夹是否存在
        if not os.path.exists(os.path.join("data", "calc_beta", task_prefix)):
            os.makedirs(os.path.join("data", "calc_beta", task_prefix))
        # 使用args中的save_result如果存在，否则使用默认值
        result_filename = args.save_result if args else save_result
        self.save_result = str(os.path.join("data", "calc_beta", task_prefix, result_filename))
        
        # 设置配置保存路径
        self.save_config_path = str(args.save_config if args else save_config_path)
        
        # 设置任务名称
        self.task_name = task_name
        
        # 初始化优化参数
        self.params = OptimizationParams()
        # print(f"初始化后的学习率: {self.params.learning_rate}")  # 添加调试信息
        
        # 初始化优化变量
        self.device = torch.device(device)  # 根据参数设置设备
        print(f"使用设备: {self.device}")  # 打印设备信息
        # 初始化形状参数，10维向量，全为0
        self.shape_new = torch.zeros([1, 10], device=self.device)
        # 初始化缩放参数，默认值为0.656856
        self.scale = torch.ones([1], device=self.device) * 0.656856
        
        # 尝试加载默认配置
        self.load_default_config()
        # print(f"加载配置后的学习率: {self.params.learning_rate}")  # 添加调试信息

        # 将张量转换为可优化的参数
        self.shape_new = torch.nn.Parameter(self.shape_new.data.clone())
        self.scale = torch.nn.Parameter(self.scale.data.clone())
        
        # 初始化优化器和可视化器
        self.init_optimizer()
        
        # 初始化Adam优化器
        self.update_optimizer()
        
        # 初始化损失数据存储
        self.loss_data = []
        self.max_data_points = 100  # 最多显示100个数据点
        
        # 初始化关节距离差值数据
        self.joint_distance_data = {}
        
        # 创建图表
        self.init_charts()
        
        # 创建用户界面
        self.init_ui()
        
        # 创建优化定时器
        self.optimization_timer = QTimer()
        self.optimization_timer.timeout.connect(self.optimization_step)
        self.is_optimizing = False
        
        # 初始化迭代计数器
        self.current_iteration = 0

        # 更新初始可视化
        self.update_initial_visualization()

    def init_optimizer(self):
        """初始化优化器和可视化器"""
        # 创建默认参数对象
        args = argparse.Namespace()
        args.task = self.task_name  # 使用类属性中的任务名称
        args.no_show = False  # 设置是否显示可视化
        args.iterations = 1500  # 设置最大迭代次数
        
        try:
            # 注册任务到人形机器人环境
            humanoid_batch_register.register_task(args.task)
            # 获取任务配置
            motion_lib_cfg = humanoid_batch_register.get_config(args.task)
        except Exception as e:
            error_msg = f"任务 '{args.task}' 注册失败: {str(e)}"
            print(error_msg)
            self.status_label.setText(error_msg)
            raise ValueError(error_msg)
        
        # 创建SMPL beta参数优化器
        self.core_optimizer = SMPLBetaOptimizer(motion_lib_cfg)
        
        # 创建可视化器并初始化MuJoCo查看器
        self.visualizer = SMPLVisualizer(self.core_optimizer)
        self.visualizer.init_mujoco_viewer()
        
        # 启动Open3D可视化线程
        self.visualizer.start_visualization_thread()
        
        # 获取T1关节位置 - 计算一次，重复使用
        self.T1_positions = self.core_optimizer.get_T1_positions()

    def init_ui(self):
        """初始化用户界面"""
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        # 创建主布局
        layout = QVBoxLayout(central_widget)

        # 创建滑块组
        slider_group = QGroupBox("优化参数")
        slider_layout = QVBoxLayout()
        
        # 创建关节损失权重滑块
        self.joint_loss_slider, self.joint_loss_slider_widget, self.joint_loss_spinbox = self.create_slider_with_label(
            "关节损失权重", 0, 200, 
            int(self.params.joint_loss_weight * 100),
            lambda x: self.update_param('joint_loss_weight', x/100))
        slider_layout.addLayout(self.joint_loss_slider)
        
        # 创建Beta正则化权重滑块
        self.beta_reg_slider, self.beta_reg_slider_widget, self.beta_reg_spinbox = self.create_slider_with_label(
            "Beta正则化权重", 0, 1000, 
            int(self.params.beta_reg_weight * 10000),
            lambda x: self.update_param('beta_reg_weight', x/10000))
        slider_layout.addLayout(self.beta_reg_slider)
        
        # 创建Scale正则化权重滑块
        self.scale_reg_slider, self.scale_reg_slider_widget, self.scale_reg_spinbox = self.create_slider_with_label(
            "Scale正则化权重", 0, 1000, 
            int(self.params.scale_reg_weight * 10000),
            lambda x: self.update_param('scale_reg_weight', x/10000))
        slider_layout.addLayout(self.scale_reg_slider)
        
        # 创建对称性损失权重滑块
        symmetry_loss_val = int(self.params.symmetry_loss_weight * 100)  # 确保使用params中的值
        self.symmetry_loss_slider, self.symmetry_loss_slider_widget, self.symmetry_loss_spinbox = self.create_slider_with_label(
            "对称性损失权重", 0, 100, 
            symmetry_loss_val,
            lambda x: self.update_param('symmetry_loss_weight', x/100))
        slider_layout.addLayout(self.symmetry_loss_slider)

        # 创建关节角度损失权重滑块
        self.joint_angle_slider, self.joint_angle_slider_widget, self.joint_angle_spinbox = self.create_slider_with_label(
            "关节角度损失权重", 0, 100, 
            int(self.params.joint_angle_weight * 1000),
            lambda x: self.update_param('joint_angle_weight', x/1000))
        slider_layout.addLayout(self.joint_angle_slider)

        # 创建骨骼长度损失权重滑块
        self.bone_length_slider, self.bone_length_slider_widget, self.bone_length_spinbox = self.create_slider_with_label(
            "骨骼长度损失权重", 0, 100, 
            int(self.params.bone_length_weight * 1000),
            lambda x: self.update_param('bone_length_weight', x/1000))
        slider_layout.addLayout(self.bone_length_slider)

        # 创建末端位置损失权重滑块
        self.endpoint_loss_slider, self.endpoint_loss_slider_widget, self.endpoint_loss_spinbox = self.create_slider_with_label(
            "末端位置损失权重", 0, 100, 
            int(self.params.endpoint_loss_weight * 100),
            lambda x: self.update_param('endpoint_loss_weight', x/100))
        slider_layout.addLayout(self.endpoint_loss_slider)
        
        # 创建学习率滑块
        self.lr_slider, self.lr_slider_widget, self.lr_spinbox = self.create_slider_with_label(
            "学习率", 1, 10000,
            int(self.params.learning_rate * 10000),
            lambda x: self.update_param('learning_rate', x/10000))
        slider_layout.addLayout(self.lr_slider)
        
        # 设置滑块组布局
        slider_group.setLayout(slider_layout)
        layout.addWidget(slider_group)

        # 添加图表标签页控件
        layout.addWidget(self.tab_widget) 

        # 创建控制按钮布局
        button_layout = QHBoxLayout()
        
        # 创建开始优化按钮
        self.start_button = QPushButton("开始优化")
        self.start_button.clicked.connect(self.toggle_optimization)
        button_layout.addWidget(self.start_button)
        
        # 创建重置按钮
        self.reset_button = QPushButton("重置")
        self.reset_button.clicked.connect(self.reset_optimization)
        button_layout.addWidget(self.reset_button)
        
        # 创建保存配置按钮
        self.save_button = QPushButton("保存配置")
        self.save_button.clicked.connect(lambda: self.save_config())
        button_layout.addWidget(self.save_button)
        
        # 创建加载配置按钮
        self.load_button = QPushButton("加载配置")
        self.load_button.clicked.connect(self.load_config)
        button_layout.addWidget(self.load_button)
        
        # 创建保存结果按钮
        self.save_result_button = QPushButton("保存优化结果")
        self.save_result_button.clicked.connect(self.save_optimization_result)
        button_layout.addWidget(self.save_result_button)
        
        # 添加按钮布局到主布局
        layout.addLayout(button_layout)

        # 创建状态显示标签
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)

        # 更新滑块值以匹配当前参数
        self.update_sliders_from_params()

    def create_slider_with_label(self, name, min_val, max_val, default_val, callback):
        """创建带标签和数值输入框的滑块
        
        Args:
            name (str): 滑块名称
            min_val (int): 最小值
            max_val (int): 最大值
            default_val (int): 默认值
            callback (function): 值改变时的回调函数
            
        Returns:
            tuple: (布局, 滑块控件, 数值输入框)
        """
        # 创建水平布局
        layout = QHBoxLayout()
        
        # 创建标签
        label = QLabel(name + ":")
        layout.addWidget(label)
        
        # 从配置文件获取范围设置
        weight_ranges = self.config.get('weight_ranges', {})
        param_range = weight_ranges.get(self.get_param_name(name), {})
        
        # 设置默认值
        min_val = param_range.get('min', min_val)
        max_val = param_range.get('max', max_val)
        step = param_range.get('step', 0.01)
        decimals = param_range.get('decimals', 2)
        
        # 创建数值输入框
        spinbox = QDoubleSpinBox()
        spinbox.setDecimals(decimals)  # 设置小数位数
        spinbox.setRange(min_val, max_val)  # 设置数值范围
        spinbox.setSingleStep(step)  # 设置步长
        # 设置默认值，根据参数类型调整缩放
        spinbox.setValue(default_val/100 if name not in ["Beta正则化权重", "Scale正则化权重", "学习率"] else default_val/10000)
        
        # 设置数值输入框的更新行为：只在编辑完成时更新
        spinbox.setKeyboardTracking(False)
        spinbox.editingFinished.connect(
            lambda: self.on_spinbox_change(spinbox.value(), slider, name, callback)
        )
        layout.addWidget(spinbox)
        
        # 创建滑块
        slider = QSlider(Qt.Horizontal)
        # 设置滑块范围，根据参数类型调整缩放
        scale_factor = 10000 if name in ["Beta正则化权重", "Scale正则化权重", "学习率"] else 100
        slider.setMinimum(int(min_val * scale_factor))
        slider.setMaximum(int(max_val * scale_factor))
        slider.setValue(default_val)
        
        # 连接滑块值改变信号
        slider.valueChanged.connect(
            lambda x: self.on_slider_change(x, spinbox, name, callback)
        )
        
        layout.addWidget(slider)
        
        # 设置布局中各部分的拉伸比例
        layout.setStretch(0, 1)  # 标签
        layout.setStretch(1, 1)  # 输入框
        layout.setStretch(2, 4)  # 滑块
        
        return layout, slider, spinbox

    def get_param_name(self, display_name):
        """将显示名称转换为参数名称
        
        Args:
            display_name (str): 显示名称
            
        Returns:
            str: 对应的参数名称
        """
        # 显示名称到参数名称的映射字典
        name_map = {
            "关节损失权重": "joint_loss_weight",
            "Beta正则化权重": "beta_reg_weight", 
            "Scale正则化权重": "scale_reg_weight",
            "对称性损失权重": "symmetry_loss_weight",
            "关节角度损失权重": "joint_angle_weight",
            "骨骼长度损失权重": "bone_length_weight",
            "学习率": "learning_rate"
        }
        return name_map.get(display_name)

    def on_slider_change(self, value, spinbox, name, callback):
        """滑块值改变时的处理函数
        
        Args:
            value (int): 新的滑块值
            spinbox (QDoubleSpinBox): 数值输入框控件
            name (str): 参数名称
            callback (function): 回调函数
        """
        # 更新数值输入框，但不触发它的valueChanged信号
        spinbox.blockSignals(True)
        # 根据参数类型调整值的缩放
        if name in ["Beta正则化权重", "Scale正则化权重", "学习率"]:
            spinbox.setValue(value/10000)
        else:
            spinbox.setValue(value/100)
        spinbox.blockSignals(False)
        
        # 调用回调函数更新参数
        callback(value)

    def on_spinbox_change(self, value, slider, name, callback):
        """数值输入框值改变时的处理函数
        
        Args:
            value (float): 新的输入框值
            slider (QSlider): 滑块控件
            name (str): 参数名称
            callback (function): 回调函数
        """
        # 更新滑块位置，但不触发它的valueChanged信号
        slider.blockSignals(True)
        # 根据参数类型调整值的缩放
        if name in ["Beta正则化权重", "Scale正则化权重", "学习率"]:
            slider_value = int(value * 10000)
            slider.setValue(slider_value)
            callback(slider_value)
        else:
            slider_value = int(value * 100)
            slider.setValue(slider_value)
            callback(slider_value)
        slider.blockSignals(False)

    def update_param(self, param_name, value):
        """更新参数值
        
        Args:
            param_name (str): 参数名称
            value (float): 新的参数值
        """
        # 设置参数值
        setattr(self.params, param_name, value)
        # 如果正在优化，更新优化器
        if self.is_optimizing:
            self.update_optimizer()

    def update_optimizer(self):
        """更新优化器
        
        创建或更新Adam优化器，用于优化shape_new、scale和根节点位置偏移
        """
        self.optimizer_torch = torch.optim.Adam([
            self.shape_new,  # 形状参数
            self.scale,      # 缩放参数
            self.core_optimizer.smpl_root_trans  # 根节点位置偏移
        ], lr=self.params.learning_rate)  # 使用当前学习率

    def init_charts(self):
        """初始化所有图表
        
        创建损失曲线图表和关节距离差值图表，并设置它们的布局和样式
        """
        # 创建标签页控件
        self.tab_widget = QTabWidget()
        
        # 创建损失图表页
        loss_tab = QWidget()
        loss_layout = QVBoxLayout(loss_tab)
        
        # 创建损失图表
        self.chart = QChart()
        self.chart.setTitle("优化损失曲线")
        
        # 创建数据系列
        self.loss_series = QLineSeries()
        self.loss_series.setName("总损失")
        self.joint_loss_series = QLineSeries()
        self.joint_loss_series.setName("关节损失")
        self.beta_loss_series = QLineSeries()
        self.beta_loss_series.setName("Beta损失")
        self.scale_loss_series = QLineSeries()
        self.scale_loss_series.setName("Scale损失")
        self.symmetry_loss_series = QLineSeries()
        self.symmetry_loss_series.setName("对称损失")
        
        # 添加系列到图表
        self.chart.addSeries(self.loss_series)
        self.chart.addSeries(self.joint_loss_series)
        self.chart.addSeries(self.beta_loss_series)
        self.chart.addSeries(self.scale_loss_series)
        self.chart.addSeries(self.symmetry_loss_series)
        
        # 创建坐标轴
        self.axis_x = QValueAxis()
        self.axis_x.setTitleText("迭代次数")
        self.axis_y = QValueAxis()
        self.axis_y.setTitleText("损失值")
        
        # 添加坐标轴到图表
        self.chart.addAxis(self.axis_x, Qt.AlignBottom)
        self.chart.addAxis(self.axis_y, Qt.AlignLeft)
        
        # 将系列附加到坐标轴
        self.loss_series.attachAxis(self.axis_x)
        self.loss_series.attachAxis(self.axis_y)
        self.joint_loss_series.attachAxis(self.axis_x)
        self.joint_loss_series.attachAxis(self.axis_y)
        self.beta_loss_series.attachAxis(self.axis_x)
        self.beta_loss_series.attachAxis(self.axis_y)
        self.scale_loss_series.attachAxis(self.axis_x)
        self.scale_loss_series.attachAxis(self.axis_y)
        self.symmetry_loss_series.attachAxis(self.axis_x)
        self.symmetry_loss_series.attachAxis(self.axis_y)
        
        # 创建图表视图
        self.chart_view = QChartView(self.chart)
        self.chart_view.setRenderHint(QPainter.Antialiasing)  # 启用抗锯齿
        
        # 设置初始范围
        self.axis_x.setRange(0, 1000)
        self.axis_y.setRange(0, 1)
        
        # 设置网格
        self.axis_x.setGridLineVisible(True)
        self.axis_y.setGridLineVisible(True)
        
        # 设置刻度数量
        self.axis_x.setTickCount(10)
        self.axis_y.setTickCount(10)
        
        # 添加图表视图到布局
        loss_layout.addWidget(self.chart_view)
        
        # 创建关节距离差值图表页
        joint_distance_tab = QWidget()
        joint_distance_layout = QVBoxLayout(joint_distance_tab)
        
        # 创建关节距离差值图表
        self.joint_distance_chart = QChart()
        self.joint_distance_chart.setTitle("关节距离差值")
        
        # 创建关节距离差值图表视图
        self.joint_distance_chart_view = QChartView(self.joint_distance_chart)
        self.joint_distance_chart_view.setRenderHint(QPainter.Antialiasing)
        
        # 创建坐标轴
        self.joint_distance_axis_x = QValueAxis()
        self.joint_distance_axis_x.setTitleText("迭代次数")
        self.joint_distance_axis_y = QValueAxis()
        self.joint_distance_axis_y.setTitleText("距离差值 (米)")
        
        # 添加坐标轴到图表
        self.joint_distance_chart.addAxis(self.joint_distance_axis_x, Qt.AlignBottom)
        self.joint_distance_chart.addAxis(self.joint_distance_axis_y, Qt.AlignLeft)
        
        # 设置初始范围
        self.joint_distance_axis_x.setRange(0, 1000)
        self.joint_distance_axis_y.setRange(0, 0.1)
        
        # 设置网格
        self.joint_distance_axis_x.setGridLineVisible(True)
        self.joint_distance_axis_y.setGridLineVisible(True)
        
        # 设置刻度数量
        self.joint_distance_axis_x.setTickCount(10)
        self.joint_distance_axis_y.setTickCount(10)
        
        # 添加图表视图到布局
        joint_distance_layout.addWidget(self.joint_distance_chart_view)
        
        # 初始化关节距离系列字典
        self.joint_distance_series = {}
        
        # 添加标签页到标签页控件
        self.tab_widget.addTab(loss_tab, "损失曲线")
        self.tab_widget.addTab(joint_distance_tab, "关节距离差值")
        
        # 初始化数据存储
        self.min_loss = float('inf')
        self.max_loss = float('-inf')

    def update_chart(self, iteration, total_loss, joint_loss, beta_loss, scale_loss, symmetry_loss):
        """更新损失图表"""
        # 添加新的数据点
        self.loss_series.append(iteration, total_loss)
        self.joint_loss_series.append(iteration, joint_loss)
        self.beta_loss_series.append(iteration, beta_loss)
        self.scale_loss_series.append(iteration, scale_loss)
        self.symmetry_loss_series.append(iteration, symmetry_loss)
        
        # 动态调整X轴范围
        x_min = max(0, iteration - 500)  # 显示最近500个点
        x_max = max(1000, iteration + 100)  # 至少显示1000个点，并留有余量
        
        # 计算当前视野内的最大最小值
        y_min = float('inf')
        y_max = float('-inf')
        
        # 遍历所有系列的可见数据点
        for series in [self.loss_series, self.joint_loss_series, 
                      self.beta_loss_series, self.scale_loss_series,
                      self.symmetry_loss_series]:
            points = series.pointsVector()
            for point in points:
                if x_min <= point.x() <= x_max:  # 只考虑视野内的点
                    y_min = min(y_min, point.y())
                    y_max = max(y_max, point.y())
        
        # 如果没有数据点，使用默认范围
        if y_min == float('inf'):
            y_min = 0
            y_max = 1
        
        # 添加边距
        y_margin = (y_max - y_min) * 0.1  # 10%的边距
        y_min = max(0, y_min - y_margin)  # 下限不小于0
        y_max = y_max + y_margin
        
        # 设置新的范围
        self.axis_x.setRange(x_min, x_max)
        self.axis_y.setRange(y_min, y_max)
        
        # 每500个点清理一次旧数据以提高性能
        if iteration % 500 == 0:
            points_to_keep = 500
            if self.loss_series.count() > points_to_keep:
                for _ in range(self.loss_series.count() - points_to_keep):
                    self.loss_series.remove(0)
                    self.joint_loss_series.remove(0)
                    self.beta_loss_series.remove(0)
                    self.scale_loss_series.remove(0)
                    self.symmetry_loss_series.remove(0)

    def update_joint_distance_chart(self, iteration, T1_positions, smpl_positions):
        """更新关节距离差值图表"""
        try:
            # 计算每个关节点的距离差值 - 修复维度计算
            # T1_positions 和 smpl_positions 的形状可能是 [1, 1, num_joints, 3] 或 [1, num_joints, 3]
            # 我们需要计算每个关节的欧氏距离，并确保结果是一维的
            diff_sq = (T1_positions - smpl_positions) ** 2
            sum_sq = torch.sum(diff_sq, dim=-1)
            distances = torch.sqrt(sum_sq).squeeze() # 使用 squeeze() 移除多余维度

            # 检查 distances 是否为 1D 张量
            if distances.dim() != 1:
                print(f"警告: 计算出的距离张量维度不为 1 (形状: {distances.shape})，跳过图表更新。")
                print(f"T1_positions shape: {T1_positions.shape}")
                print(f"smpl_positions shape: {smpl_positions.shape}")
                return

            # 获取关节数量
            num_joints = distances.shape[0]
            
            # 确保每个关节都有对应的数据系列
            for i in range(num_joints):
                joint_name = f"关节 {i+1}"
                if joint_name not in self.joint_distance_series:
                    # 创建新的数据系列
                    series = QLineSeries()
                    series.setName(joint_name)
                    pen = series.pen()
                    pen.setWidth(2)  # 设置线条宽度为3
                    series.setPen(pen)
                    
                    # 设置关节颜色 - 使用相同颜色但用实线和虚线区分左右
                    if i == 0:  # 左膝
                        series.setColor(QColor(255, 165, 0))  # 橙色
                        series.setPen(QPen(QColor(255, 165, 0), 2, Qt.SolidLine))  # 实线
                    elif i == 3:  # 右膝
                        series.setColor(QColor(255, 165, 0))  # 橙色
                        series.setPen(QPen(QColor(255, 165, 0), 2, Qt.DashLine))  # 虚线
                    elif i == 1:  # 左踝
                        series.setColor(QColor(128, 0, 128))  # 紫色
                        series.setPen(QPen(QColor(128, 0, 128), 2, Qt.SolidLine))  # 实线
                    elif i == 4:  # 右踝
                        series.setColor(QColor(128, 0, 128))  # 紫色
                        series.setPen(QPen(QColor(128, 0, 128), 2, Qt.DashLine))  # 虚线
                    elif i == 2:  # 左脚趾
                        series.setColor(QColor(128, 0, 255))  # 蓝紫色
                        series.setPen(QPen(QColor(128, 0, 255), 2, Qt.SolidLine))  # 实线
                    elif i == 5:  # 右脚趾
                        series.setColor(QColor(128, 0, 255))  # 蓝紫色
                        series.setPen(QPen(QColor(128, 0, 255), 2, Qt.DashLine))  # 虚线
                    elif i == 6:  # 左肩
                        series.setColor(QColor(0, 0, 255))  # 蓝色
                        series.setPen(QPen(QColor(0, 0, 255), 2, Qt.SolidLine))  # 实线
                    elif i == 9:  # 右肩
                        series.setColor(QColor(0, 0, 255))  # 蓝色
                        series.setPen(QPen(QColor(0, 0, 255), 2, Qt.DashLine))  # 虚线
                    elif i == 7:  # 左肘
                        series.setColor(QColor(0, 128, 0))  # 绿色
                        series.setPen(QPen(QColor(0, 128, 0), 2, Qt.SolidLine))  # 实线
                    elif i == 10:  # 右肘
                        series.setColor(QColor(0, 128, 0))  # 绿色
                        series.setPen(QPen(QColor(0, 128, 0), 2, Qt.DashLine))  # 虚线
                    elif i == 8:  # 左腕
                        series.setColor(QColor(0, 200, 200))  # 青色
                        series.setPen(QPen(QColor(0, 200, 200), 2, Qt.SolidLine))  # 实线
                    elif i == 11:  # 右腕
                        series.setColor(QColor(0, 200, 200))  # 青色
                        series.setPen(QPen(QColor(0, 200, 200), 2, Qt.DashLine))  # 虚线
                    
                    # 添加系列到图表
                    self.joint_distance_chart.addSeries(series)
                    
                    # 将系列附加到坐标轴
                    series.attachAxis(self.joint_distance_axis_x)
                    series.attachAxis(self.joint_distance_axis_y)
                    
                    # 存储系列引用
                    self.joint_distance_series[joint_name] = series
                
                # 添加新的数据点
                # 确保 distances[i] 是标量张量
                distance_value = distances[i]
                if distance_value.numel() == 1:
                     self.joint_distance_series[joint_name].append(iteration, distance_value.item())
                else:
                     print(f"警告: distances[{i}] 不是标量 (值: {distance_value})，跳过添加点。")

            # 动态调整X轴范围
            x_min = max(0, iteration - 500)  # 显示最近500个点
            x_max = max(1000, iteration + 100)  # 至少显示1000个点，并留有余量
            
            # 计算当前视野内的最大最小值
            y_min = float('inf')
            y_max = float('-inf')
            
            # 遍历所有系列的可见数据点
            for series in self.joint_distance_series.values():
                points = series.pointsVector()
                for point in points:
                    if x_min <= point.x() <= x_max:  # 只考虑视野内的点
                        y_min = min(y_min, point.y())
                        y_max = max(y_max, point.y())
            
            # 如果没有数据点，使用默认范围
            if y_min == float('inf'):
                y_min = 0
                y_max = 0.1
            
            # 添加边距
            y_margin = (y_max - y_min) * 0.1  # 10%的边距
            y_min = max(0, y_min - y_margin)  # 下限不小于0
            y_max = y_max + y_margin
            
            # 设置新的范围
            self.joint_distance_axis_x.setRange(x_min, x_max)
            self.joint_distance_axis_y.setRange(y_min, y_max)
            
            # 每500个点清理一次旧数据以提高性能
            if iteration % 500 == 0:
                points_to_keep = 500
                for series in self.joint_distance_series.values():
                    if series.count() > points_to_keep:
                        for _ in range(series.count() - points_to_keep):
                            series.remove(0)
        
        except Exception as e:
            print(f"更新关节距离图表时出错: {e}")
            import traceback
            traceback.print_exc()

    def toggle_optimization(self):
        """切换优化状态"""
        if self.is_optimizing:
            self.stop_optimization()
        else:
            self.start_optimization()

    def start_optimization(self):
        """开始优化"""
        self.is_optimizing = True
        self.start_button.setText("停止优化")
        self.optimization_timer.start(10)  # 每10毫秒触发一次

    def stop_optimization(self):
        """停止优化"""
        self.is_optimizing = False
        self.start_button.setText("开始优化")
        self.optimization_timer.stop()

    def reset_optimization(self):
        """重置优化"""
        self.stop_optimization()
        self.current_iteration = 0
        
        # 重置优化变量
        with torch.no_grad(): # 确保在非计算图模式下操作
            # 直接修改 Parameter 的 data
            self.shape_new.data.zero_()
            self.scale.data.fill_(0.656856)
        
        # 清空损失数据
        self.loss_data = []
        
        # 清空图表
        self.loss_series.clear()
        self.joint_loss_series.clear()
        self.beta_loss_series.clear()
        self.scale_loss_series.clear()
        self.symmetry_loss_series.clear()
        
        # 清空关节距离差值图表
        for series in self.joint_distance_series.values():
            series.clear()
        # 重置图表坐标轴范围 (可选，但推荐)
        self.axis_x.setRange(0, 1000)
        self.axis_y.setRange(0, 1)
        self.joint_distance_axis_x.setRange(0, 1000)
        self.joint_distance_axis_y.setRange(0, 0.1)

        # --- 更新可视化 ---
        print("正在更新重置后的可视化...")
        try:
            with torch.no_grad(): # 使用 no_grad 避免梯度计算
                # 获取重置后的 SMPL 状态
                Smpl_verts_reset, Smpl_joints_reset, _ = self.core_optimizer.get_smpl_joints(
                    self.shape_new, self.scale # 使用已重置的参数
                )

                # 更新 MuJoCo 查看器
                if self.visualizer and self.visualizer.viewer and self.visualizer.viewer.is_running():
                    print("更新重置后的 MuJoCo 视图...")
                    self.visualizer.update_mujoco_viewer(
                        Smpl_joints_reset,
                        self.T1_positions, # 使用初始 T1 位置
                        self.shape_new,
                        self.scale,
                        iteration=0 # 重置迭代次数为 0
                    )
                else:
                    print("MuJoCo 查看器未准备好进行重置更新。")

                # 更新 Open3D 查看器 (放入队列)
                if self.visualizer and self.visualizer.vis_thread and self.visualizer.vis_thread.is_alive():
                     if self.visualizer.o3d_vis_ready.is_set():
                        print("更新重置后的 Open3D 视图...")
                        # 清空队列
                        while not self.visualizer.mesh_queue.empty():
                            try:
                                self.visualizer.mesh_queue.get_nowait()
                            except queue.Empty:
                                break
                        # 放入重置后的顶点数据
                        smpl_verts_np_reset = Smpl_verts_reset[0].cpu().numpy()
                        self.visualizer.mesh_queue.put_nowait(smpl_verts_np_reset)
                     else:
                         print("Open3D 查看器未就绪，无法进行重置更新。")
                else:
                    print("Open3D 可视化线程未运行，无法进行重置更新。")


        except Exception as e:
            print(f"重置可视化时出错: {e}")
            import traceback
            traceback.print_exc()
        # --- 可视化更新结束 ---
        print("重置可视化更新尝试完成。")

        self.status_label.setText("已重置优化")

        # 重置后，确保滑块也更新
        self.update_sliders_from_params()

    def optimization_step(self):
        """执行一步优化"""
        try:
            # 获取SMPL关节位置和顶点
            Smpl_verts, Smpl_joints, smpl_positions = self.core_optimizer.get_smpl_joints(self.shape_new, self.scale)
            
            # 计算损失
            loss_dict = self.core_optimizer.compute_losses(
                self.T1_positions, 
                smpl_positions, 
                self.shape_new, 
                self.scale, 
                self.params
            )
            
            # 计算对称性损失
            symmetry_loss = self.core_optimizer.compute_symmetry_loss(Smpl_joints, self.params)
            
            # 计算末端位置损失
            try:
                # 获取SMPL关节名称到索引的映射
                smpl_joint_names = self.core_optimizer.smpl_joint_pick
                smpl_joint_indices = self.core_optimizer.smpl_joint_pick_idx
                
                # 找到末端关节在smpl_joint_pick中的索引
                endpoint_names = ['L_Wrist', 'R_Wrist', 'L_Ankle', 'R_Ankle']
                endpoint_indices = []
                for name in endpoint_names:
                    if name in smpl_joint_names:
                        idx = smpl_joint_names.index(name)
                        # 使用smpl_joint_pick中的索引，而不是原始SMPL模型索引
                        endpoint_indices.append(idx)
                
                if len(endpoint_indices) > 0:
                    # 打印调试信息
                    # print(f"T1_positions shape: {self.T1_positions.shape}")
                    # print(f"smpl_positions shape: {smpl_positions.shape}")
                    # print(f"使用的末端关节索引: {endpoint_indices}")
                    # print(f"对应的关节名称: {[smpl_joint_names[i] for i in endpoint_indices]}")
                    
                    endpoint_loss = torch.mean((self.T1_positions[0, 0, endpoint_indices] - smpl_positions[endpoint_indices]) ** 2)
                else:
                    print("警告：未找到任何末端关节")
                    endpoint_loss = torch.tensor(0.0, device=self.device)
            except Exception as e:
                print(f"计算末端位置损失时出错: {e}")
                endpoint_loss = torch.tensor(0.0, device=self.device)  # 如果出错，使用零损失
            
            # 计算总损失
            total_loss = (loss_dict['joint_loss'] + 
                         loss_dict['beta_reg_loss'] + 
                         loss_dict['scale_reg_loss'] + 
                         loss_dict['joint_angle_loss'] +
                         loss_dict['bone_length_loss'] +
                         symmetry_loss +
                         self.params.endpoint_loss_weight * endpoint_loss)  # 添加末端位置损失
            
            # 优化步骤
            self.optimizer_torch.zero_grad()
            total_loss.backward()
            self.optimizer_torch.step()
            
            # 更新状态显示
            self.status_label.setText(
                f"迭代: {self.current_iteration} | 损失: {total_loss.item():.6f} "
                f"(关节: {loss_dict['joint_loss'].item():.6f}, "
                f"Beta: {loss_dict['beta_reg_loss'].item():.6f}, "
                f"Scale: {loss_dict['scale_reg_loss'].item():.6f}, "
                f"角度: {loss_dict['joint_angle_loss'].item():.6f}, "
                f"骨骼: {loss_dict['bone_length_loss'].item():.6f}, "
                f"对称: {symmetry_loss.item():.6f}, "
                f"末端: {endpoint_loss.item():.6f})"
            )
            
            # 更新MuJoCo和Open3D可视化 (调整更新频率)
            if self.current_iteration % 5 == 0: # 每 5 次迭代更新一次 MuJoCo 和 Open3D
                # 更新 MuJoCo
                self.visualizer.update_mujoco_viewer(Smpl_joints, self.T1_positions, self.shape_new, self.scale, self.current_iteration)

                # 更新 Open3D (将顶点数据放入队列)
                try:
                    # 确保队列是空的或者只有一个元素，避免阻塞和旧数据堆积
                    while not self.visualizer.mesh_queue.empty():
                        try:
                            self.visualizer.mesh_queue.get_nowait()
                        except queue.Empty:
                            break # 队列已空
                    
                    # 放入新的顶点数据 (确保是 numpy 数组)
                    smpl_verts_np = Smpl_verts[0].detach().cpu().numpy()
                    self.visualizer.mesh_queue.put_nowait(smpl_verts_np)

                except queue.Full:
                    # 如果队列满了（理论上不应该发生，因为我们先清空了），忽略这次更新
                    # print("警告：Open3D 网格队列已满，跳过本次更新。")
                    pass
                except Exception as e:
                    print(f"放入网格数据到队列时出错: {e}")

            # 更新损失图表
            self.update_chart(
                self.current_iteration,
                total_loss.item(),
                loss_dict['joint_loss'].item(),
                loss_dict['beta_reg_loss'].item(),
                loss_dict['scale_reg_loss'].item(),
                symmetry_loss.item()
            )
            
            # 更新关节距离差值图表
            self.update_joint_distance_chart(
                self.current_iteration,
                self.T1_positions,
                smpl_positions # 注意这里传递的是选定的关节位置
            )
            
            # 保存损失数据
            self.loss_data.append(total_loss.item())
            
            # 更新迭代计数
            self.current_iteration += 1
            
        except Exception as e:
            print(f"优化步骤出错: {e}")
            import traceback
            traceback.print_exc()
            self.stop_optimization()

    def load_default_config(self):
        """加载默认配置，如果不存在则创建"""
        try:
            if os.path.exists(self.default_config_path):
                self.load_config(self.default_config_path)
            else:
                # 创建默认配置
                default_config = {
                    'joint_loss_weight': 1.0,
                    'beta_reg_weight': 0.001,
                    'scale_reg_weight': 0.001,
                    'symmetry_loss_weight': 0.7,
                    'joint_angle_weight': 0.02,
                    'bone_length_weight': 0.02,
                    'endpoint_loss_weight': 0.5,
                    'learning_rate': 0.005,
                    'shape': [0.0] * 10,
                    'scale': 0.656856,
                    'weight_ranges': {
                        "joint_loss_weight": {
                            "min": 0.0,
                            "max": 2.0,
                            "step": 0.01,
                            "decimals": 2
                        },
                        "beta_reg_weight": {
                            "min": 0.0,
                            "max": 0.1,
                            "step": 0.0001,
                            "decimals": 4
                        },
                        "scale_reg_weight": {
                            "min": 0.0,
                            "max": 0.1,
                            "step": 0.0001,
                            "decimals": 4
                        },
                        "symmetry_loss_weight": {
                            "min": 0.0,
                            "max": 1.0,
                            "step": 0.01,
                            "decimals": 2
                        },
                        "joint_angle_weight": {
                            "min": 0.0,
                            "max": 1.0,
                            "step": 0.01,
                            "decimals": 2
                        },
                        "bone_length_weight": {
                            "min": 0.0,
                            "max": 1.0,
                            "step": 0.01,
                            "decimals": 2
                        },
                        "endpoint_loss_weight": {
                            "min": 0.0,
                            "max": 1.0,
                            "step": 0.01,
                            "decimals": 2
                        },
                        "learning_rate": {
                            "min": 0.0001,
                            "max": 0.01,
                            "step": 0.0001,
                            "decimals": 4
                        }
                    }
                }
                
                # 确保配置目录存在
                os.makedirs(os.path.dirname(self.default_config_path), exist_ok=True)
                
                # 保存默认配置
                with open(self.default_config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=4, ensure_ascii=False)
                
                print(f"已创建默认配置文件: {self.default_config_path}")
        except Exception as e:
            print(f"处理默认配置时出错: {e}")

    def save_config(self):
        """保存当前配置到配置文件"""
        try:
            # 使用core_optimizer的save_config方法，使用args中的save_config路径
            save_path = self.args.save_config if self.args else self.save_config_path
            saved_path = self.core_optimizer.save_config(save_path)
            
            self.status_label.setText(f"配置已保存到: {saved_path}")
            print(f"配置已保存到: {saved_path}")
        except Exception as e:
            error_msg = f"保存配置失败: {str(e)}"
            self.status_label.setText(error_msg)
            print(error_msg)
            import traceback
            traceback.print_exc()

    def load_config(self, file_path=None):
        """加载配置"""
        original_file_path = file_path # 保存原始路径用于判断是否是默认加载
        try:
            if file_path is None:
                file_path, _ = QFileDialog.getOpenFileName(
                    self,
                    "加载配置",
                    "configs/",
                    "JSON Files (*.json)"
                )
            
            if file_path and os.path.exists(file_path):
                # 加载配置
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 保存完整配置以供后续使用
                self.config = config
                
                # 更新参数 (OptimizationParams)
                self.params.joint_loss_weight = config.get('joint_loss_weight', self.params.joint_loss_weight)
                self.params.beta_reg_weight = config.get('beta_reg_weight', self.params.beta_reg_weight)
                self.params.scale_reg_weight = config.get('scale_reg_weight', self.params.scale_reg_weight)
                self.params.symmetry_loss_weight = config.get('symmetry_loss_weight', 0.7)  # 使用0.7作为默认值
                self.params.joint_angle_weight = config.get('joint_angle_weight', self.params.joint_angle_weight)
                self.params.bone_length_weight = config.get('bone_length_weight', self.params.bone_length_weight)
                self.params.endpoint_loss_weight = config.get('endpoint_loss_weight', self.params.endpoint_loss_weight)
                self.params.learning_rate = config.get('learning_rate', self.params.learning_rate)

                # 更新优化变量 (shape_new 和 scale 的 data)
                shape_data = config.get('shape', [0.0] * 10)
                scale_data = config.get('scale', 0.656856)
                # 确保形状数据长度正确
                if len(shape_data) != 10:
                    print(f"警告: 配置文件中的 shape 长度 ({len(shape_data)}) 不为10，将使用默认值。")
                    shape_data = [0.0] * 10

                # --- 重要：更新 Parameter 的 data ---
                # 检查 Parameter 是否已创建 (在 __init__ 中)
                if hasattr(self, 'shape_new') and isinstance(self.shape_new, torch.nn.Parameter):
                     with torch.no_grad():
                         self.shape_new.data = torch.tensor([shape_data], device=self.device, dtype=self.shape_new.dtype) # 注意加一层[]
                         self.scale.data.fill_(scale_data)
                else:
                     # 如果 Parameter 还没创建（理论上不应该发生），直接赋值 Tensor
                     self.shape_new = torch.tensor([shape_data], device=self.device, dtype=torch.float32)
                     self.scale = torch.tensor([scale_data], device=self.device, dtype=torch.float32)
                # --- 更新结束 ---

                # 如果GUI已经初始化，更新滑块
                if hasattr(self, 'joint_loss_slider_widget'): # 检查UI元素是否存在
                    self.update_sliders_from_params()

                # 如果优化器已创建，更新它 (主要是学习率)
                if hasattr(self, 'optimizer_torch'):
                    self.update_optimizer()
                
                # 触发一次可视化更新，以反映加载的 shape 和 scale
                # 确保在 init_optimizer 之后调用
                if hasattr(self, 'core_optimizer') and hasattr(self, 'visualizer'):
                     self.update_initial_visualization() # 复用这个方法来更新视图

                if original_file_path != self.default_config_path:  # 不是加载默认配置时才显示消息
                    self.status_label.setText(f"配置已加载: {os.path.basename(file_path)}") # 显示文件名
                    
                print(f"配置已从 {file_path} 加载。")
                return True
            elif file_path: # 文件路径有效但文件不存在
                 print(f"警告: 配置文件不存在: {file_path}")
                 return False
            else: # 用户取消了文件对话框
                 print("加载配置已取消。")
                 return False
        except Exception as e:
            error_msg = f"加载配置失败: {str(e)}"
            if original_file_path != self.default_config_path:  # 不是加载默认配置时才显示错误
                self.status_label.setText(error_msg)
            print(error_msg)
            import traceback
            traceback.print_exc()
            return False

    def update_sliders_from_params(self):
        """根据当前参数更新所有滑块和数值框的值"""
        if not hasattr(self, 'joint_loss_slider'): # 检查UI是否已初始化
            return

        # --- 更新关节损失 ---
        joint_loss_val = int(self.params.joint_loss_weight * 100)
        self.joint_loss_slider_widget.setValue(joint_loss_val)
        self.joint_loss_spinbox.setValue(self.params.joint_loss_weight)

        # --- 更新Beta正则化 ---
        beta_reg_val = int(self.params.beta_reg_weight * 10000)
        self.beta_reg_slider_widget.setValue(beta_reg_val)
        self.beta_reg_spinbox.setValue(self.params.beta_reg_weight)

        # --- 更新Scale正则化 ---
        scale_reg_val = int(self.params.scale_reg_weight * 10000)
        self.scale_reg_slider_widget.setValue(scale_reg_val)
        self.scale_reg_spinbox.setValue(self.params.scale_reg_weight)

        # --- 更新对称性损失 ---
        symmetry_loss_val = int(self.params.symmetry_loss_weight * 100)
        self.symmetry_loss_slider_widget.setValue(symmetry_loss_val)
        self.symmetry_loss_spinbox.setValue(self.params.symmetry_loss_weight)

        # --- 更新关节角度损失 ---
        joint_angle_val = int(self.params.joint_angle_weight * 1000)
        self.joint_angle_slider_widget.setValue(joint_angle_val)
        self.joint_angle_spinbox.setValue(self.params.joint_angle_weight)

        # --- 更新骨骼长度损失 ---
        bone_length_val = int(self.params.bone_length_weight * 1000)
        self.bone_length_slider_widget.setValue(bone_length_val)
        self.bone_length_spinbox.setValue(self.params.bone_length_weight)

        # --- 更新末端位置损失 ---
        endpoint_loss_val = int(self.params.endpoint_loss_weight * 100)
        self.endpoint_loss_slider_widget.setValue(endpoint_loss_val)
        self.endpoint_loss_spinbox.setValue(self.params.endpoint_loss_weight)

        # --- 更新学习率 ---
        lr_val = int(self.params.learning_rate * 10000)
        self.lr_slider_widget.setValue(lr_val)
        self.lr_spinbox.setValue(self.params.learning_rate)

    def save_optimization_result(self, save_path=None):
        """保存优化结果
        Args:
            save_path: 可选的保存路径，如果不指定则使用args.save_result
        """
        try:
            # 获取最新的 SMPL 状态
            with torch.no_grad():
                Smpl_verts, Smpl_joints, smpl_positions = self.core_optimizer.get_smpl_joints(
                    self.shape_new, self.scale
                )
            
            # 分离梯度后再保存结果
            with torch.no_grad():
                shape_new_detached = self.shape_new.detach()
                scale_detached = self.scale.detach()
                T1_positions_detached = self.T1_positions.detach()
                smpl_positions_detached = smpl_positions.detach()
            
            # 使用args.save_result作为保存路径
            save_path = self.args.save_result
            
            # 保存结果
            saved_path = self.core_optimizer.save_optimization_result(
                shape_new=shape_new_detached,
                scale=scale_detached,
                T1_positions=T1_positions_detached,
                smpl_positions=smpl_positions_detached,
                task_name=self.task_name,
                loss=self.loss_data[-1] if self.loss_data else None,
                save_path=save_path
            )
            
            self.status_label.setText(f"优化结果已保存到: {saved_path}")
            print(f"优化结果已保存到: {saved_path}")
            
        except Exception as e:
            error_msg = f"保存优化结果失败: {str(e)}"
            self.status_label.setText(error_msg)
            print(error_msg)
            import traceback
            traceback.print_exc()

    def closeEvent(self, event):
        """关闭窗口时的处理函数"""
        print("接收到关闭事件...")
        # 停止优化
        self.stop_optimization()
        
        # 停止可视化
        if hasattr(self, 'visualizer'):
             print("正在调用 visualizer.stop_visualization()...")
             self.visualizer.stop_visualization()
             print("visualizer.stop_visualization() 调用完成。")
        
        # 接受关闭事件
        print("接受关闭事件。")
        event.accept()

    def update_initial_visualization(self):
        """在 GUI 启动时更新可视化以显示初始状态"""
        print("正在更新初始可视化...")
        try:
            with torch.no_grad(): # 使用 no_grad 避免梯度计算
                # 获取初始的 SMPL 状态 (使用当前 self.shape_new 和 self.scale)
                Smpl_verts_init, Smpl_joints_init, _ = self.core_optimizer.get_smpl_joints(
                    self.shape_new, self.scale
                )

                # 更新 MuJoCo 查看器
                if self.visualizer and self.visualizer.viewer and self.visualizer.viewer.is_running():
                    print("更新初始 MuJoCo 视图...")
                    self.visualizer.update_mujoco_viewer(
                        Smpl_joints_init,
                        self.T1_positions, # 使用已计算的 T1 位置
                        self.shape_new,
                        self.scale,
                        iteration=0 # 初始迭代次数为 0
                    )
                else:
                    print("MuJoCo 查看器未准备好进行初始更新。")


                # 更新 Open3D 查看器 (放入队列)
                # 需要等待 Open3D 线程准备好
                if self.visualizer and self.visualizer.vis_thread and self.visualizer.vis_thread.is_alive():
                    if self.visualizer.o3d_vis_ready.is_set(): # 检查 Open3D 是否已就绪
                        print("更新初始 Open3D 视图...")
                        # 清空队列
                        while not self.visualizer.mesh_queue.empty():
                            try:
                                self.visualizer.mesh_queue.get_nowait()
                            except queue.Empty:
                                break
                        # 放入初始顶点数据
                        smpl_verts_np_init = Smpl_verts_init[0].cpu().numpy()
                        self.visualizer.mesh_queue.put_nowait(smpl_verts_np_init)
                    else:
                        print("Open3D 查看器未就绪，稍后尝试更新...")
                        # 可以考虑使用 QTimer 延迟更新，或者让用户手动触发
                        # 这里暂时只打印信息
                else:
                     print("Open3D 可视化线程未运行，无法进行初始更新。")


        except Exception as e:
            print(f"更新初始可视化时出错: {e}")
            import traceback
            traceback.print_exc()
        print("初始可视化更新尝试完成。")

def main():
    """主函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='SMPL Beta 优化器 GUI')
    parser.add_argument('--config', type=str, default="configs/default_beta_config.json",
                      help='默认配置文件的路径 (默认: configs/default_beta_config.json)')
    parser.add_argument('--save-result', type=str, default="shape_scale_bias.json",
                      help='优化结果保存的文件名称 (默认: shape_scale_bias.json)')
    parser.add_argument('--save-config', type=str, default="configs/default_beta_config.json",
                      help='配置保存的完整文件路径 (默认: configs/default_beta_config.json)')
    parser.add_argument('--device', type=str, default="cpu", choices=["cpu", "cuda"],
                      help='运行设备，可选 "cpu" 或 "cuda" (默认: cpu)')
    parser.add_argument('--task', type=str, default="unitreeG1_retarget",
                      choices=["A2_retarget", "T1_retarget", "dobot_retarget", 
                              "unitreeG1_retarget", "x2_retarget", "cdroid_retarget"],
                      help='任务名称，可选值包括：A2_retarget, T1_retarget, dobot_retarget, '
                           'unitreeG1_retarget, x2_retarget, cdroid_retarget (默认: cdroid_retarget)')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 检查CUDA是否可用
    if args.device == "cuda" and not torch.cuda.is_available():
        print("警告: 请求使用CUDA但CUDA不可用，将使用CPU")
        args.device = "cpu"
    
    # 创建应用程序实例
    app = QApplication(sys.argv)
    
    try:
        # 创建主窗口，传入所有参数
        window = BetaOptimizerGUI(
            default_config_path=args.config,
            save_result=args.save_result,
            save_config_path=args.save_config,
            device=args.device,
            task_name=args.task,
            args=args  # 传入args对象
        )
        window.show()
        
        # 运行应用程序
        sys.exit(app.exec_())
    except ValueError as e:
        print(f"错误: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main() 