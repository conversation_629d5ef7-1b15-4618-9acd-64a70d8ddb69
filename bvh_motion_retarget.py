#!/usr/bin/env python3
"""
基于BVH的动作重定向脚本

这个脚本直接使用BVH动作数据进行重定向，不依赖SMPL模型。
使用bone_optimizer_gui.py拟合的骨骼参数进行动作重定向。
参考grad_rotation_fit_ik.py的实现思路。

"""

import argparse
import os
import sys
import json
import numpy as np
import torch
import time
from datetime import datetime
from tqdm import tqdm
import matplotlib.pyplot as plt
from scipy.spatial.transform import Rotation as sRot
import joblib

# 导入自定义模块
from humanoid.utils import humanoid_batch_register
from humanoid.utils.torch_humanoid_batch import Humanoid_Batch
from humanoid.retarget_motion.base.motion_lib_cfg import MotionLibCfg
from humanoid.utils.math_tool import butter_lowpass_filter_torch, gaussian_filter_1d_batch
from humanoid.utils.rotation_conversions import axis_angle_to_matrix
from utils.bvh_utils import BVHMotion
from utils.mujoco_viewer import MujocoViwer


def light_visualization(position, pos2=None):
    from matplotlib import pyplot as plt
    from mpl_toolkits.mplot3d import Axes3D
    figure = plt.figure(figsize=(5, 5))
    ax = figure.add_subplot(111, projection='3d')
    ax.view_init(elev=0, azim=-180, roll=-90)
    plt.ion()
    plt.show(block=False)
    current_frame: int = 0
    # while True:

    for i, positions in tqdm(enumerate(position)):
        ax.clear()
        ax.set_xlabel('x')
        ax.set_ylabel('y')
        ax.set_zlabel('z')
        ax.set_aspect('equal')
        # ax.set_ylim(30, 35)
        # positions = self.positions_for_debug[current_frame % self.positions_for_debug.shape[0]]
        x = [p[0] for p in positions]
        y = [p[1] for p in positions]
        z = [p[2] for p in positions]
        if pos2 is not None:
            x_2 = [p[0] for p in pos2]
            y_2 = [p[1] for p in pos2]
            z_2 = [p[2] for p in pos2]
            x += x_2
            y += y_2
            z += z_2
        ax.scatter(x, y, z, c='b', s=100, alpha=.9)
        current_frame += 1
        # time.sleep(1/20)
        plt.pause(0.01)

    raise


class BVHMotionRetargeter:
    """BVH动作重定向器"""
    
    def __init__(self, args):
        """初始化重定向器
        
        Args:
            args: 命令行参数
        """
        self.args = args
        self.device = torch.device(args.device)
        
        # 设置参数
        self.setPrama()
        
        # 加载骨骼拟合结果配置
        print(f"加载骨骼拟合配置: {self.config_file}")
        self.skeleton_params = self.load_skeleton_params(self.config_file)  # 解析json，返回值包含优化后的offset，机器人t_pose dof，关节映射，机器人 keypoint关节索引，bvh keypoint关节索引等

        # 注册机器人和初始化
        self.registryRobot()
        self.initRobotBody()

        # 加载优化后的BVH动作数据
        print(f"加载优化后的BVH文件: {self.bvh_file}")
        self.bvh_motion = BVHMotion(self.bvh_file)  # BVHMotion会直接读取offset，通常是厘米单位,得到的self.bvh_motion包含相对于父的旋转（欧拉角（度））和位置等其他信息
        self.bvh_motion.joint_position = self.bvh_motion.joint_position * 0.01
        self.bvh_motion.motion_data_list

        # 初始化BVH播放器 - 在此之前先设置正确的机器人关节索引
        if not args.no_vis:
            # 从配置文件中获取机器人关节索引，确保在播放器初始化时就有正确的索引
            robot_joint_indices = self.skeleton_params['joint_correspondence'].get('robot_joint_indices', [])
            if robot_joint_indices:
                self.skeleton_params['robot_joint_indices'] = robot_joint_indices
                print(f"✅ 预设机器人关节索引到skeleton_params: {robot_joint_indices}")
            else:
                print("⚠️ 配置文件中没有robot_joint_indices，将使用原始计算")

            self.mujoco_viewer = MujocoViwer(self.motion_lib_cfg)

        # 初始化脚趾索引用于接触检测
        try:
            self.left_toe_idx = self.motion_lib_cfg.joint_names.index(self.motion_lib_cfg.left_toe_name)
            self.right_toe_idx = self.motion_lib_cfg.joint_names.index(self.motion_lib_cfg.right_toe_name)
            print(f"✅ 脚趾索引初始化成功: Left={self.left_toe_idx} ('{self.motion_lib_cfg.left_toe_name}'), Right={self.right_toe_idx} ('{self.motion_lib_cfg.right_toe_name}')")
        except ValueError as e:
            print(f"⚠️ 脚趾索引初始化失败: {e}")
            self.left_toe_idx = -1
            self.right_toe_idx = -1

        # 损失函数
        self.mse_loss = torch.nn.MSELoss()

        # 初始化关节权重
        self.init_joint_weights()

        # 设置接触序列文件路径
        if hasattr(args, 'contact_file') and args.contact_file:
            self.contact_file_path = args.contact_file
            print(f"✅ 使用指定的接触序列文件: {self.contact_file_path}")
        else:
            print(f"⚠️ 未指定接触序列文件")

        print("BVH动作重定向器初始化完成")

    def setPrama(self):
        """设置可调参数"""
        self.bvh_file = self.args.bvh
        self.config_file = self.args.config
        self.device = self.args.device
        self.kernel_size = self.args.kernel_size
        self.sigma = self.args.sigma
        self.iterations = self.args.iterations
        self.learning_rate = self.args.learning_rate

        # 损失权重
        self.keypoint_loss_weight = 1.0
        self.smoothness_loss_weight = 0.0
        self.acceleration_loss_weight = 0.0
        self.foot_slip_loss_weight = 0.0
        self.foot_rotation_loss_weight = 0.0
        self.root_trans_loss_weight = 0.0
        self.root_rot_loss_weight = 0.0
        self.contact_foot_loss_weight = 0.0
        self.foot_height_loss_weight = 0.0



    def load_skeleton_params(self, config_path):
        """加载骨骼拟合参数 - 从bone_optimizer_gui的输出中读取"""
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")

        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 验证配置文件格式
        if not self.validate_config_file(config_path):
            raise ValueError(f"无效的配置文件格式: {config_path}")

        # 提取task_name
        self.task_name = config['metadata']['task_name']

        # 提取关节对应关系
        joint_correspondence = config['joint_correspondence']

        # 提取BVH关节信息
        bvh_info = config.get('bvh_info', {})
        bvh_joint_names = bvh_info.get('joint_names', [])
        bvh_selected_joints = bvh_info.get('selected_joints', [])

        # 提取机器人关节信息
        robot_info = config.get('robot_info', {})
        robot_joint_names = robot_info.get('joint_names', [])
        robot_selected_joints = robot_info.get('selected_joints', [])

        robot_joint_indices = joint_correspondence['robot_joint_indices']
        bvh_joint_indices = joint_correspondence['bvh_joint_indices']

        # 构建完整的参数结构
        params = {
            'metadata': {
                'task_name': self.task_name,
                'source_file': config_path,
                'optimization_version': config['metadata'].get('optimization_version', 'BVH_Direct_v1.0')
            },
            'joint_correspondence': {
                'robot_to_bvh': joint_correspondence.get('robot_to_bvh', {}),
                'bvh_to_robot': joint_correspondence.get('bvh_to_robot', {}),
                'robot_joint_indices': robot_joint_indices,  # selected_joints
                'bvh_joint_indices': bvh_joint_indices
            },
            'skeleton_params': config.get('skeleton_params', {}),  # 优化后的offset
            't_pose_config': config.get('t_pose_config', {}),  # 机器人t_pose dof
            'coordinate_transform': config.get('coordinate_transform', {}),  # 坐标系变换信息
            'bvh_info': {
                'joint_names': bvh_joint_names,
                'selected_joints': bvh_selected_joints
            },
            'robot_info': {
                'joint_names': robot_joint_names,
                'selected_joints': robot_selected_joints
            }
        }

        print("骨骼参数加载成功:")
        print(f"  - 任务: {self.task_name}")
        print(f"  - 关节映射: {len(joint_correspondence.get('robot_to_bvh', {}))} 对")


        skeleton_params = params['skeleton_params']
        if 'scale_factor' in skeleton_params:
            print(f"  - 缩放因子: {skeleton_params['scale_factor']:.4f}")

        # 显示坐标变换信息
        coord_transform = params.get('coordinate_transform', {})
        if 'transform_matrix' in coord_transform:
            print(f"  - 坐标变换矩阵: {coord_transform['transform_matrix']}")
            if 'bvh_to_robot_description' in coord_transform:
                print(f"  - 变换描述: {coord_transform['bvh_to_robot_description']}")

        return params


    def validate_config_file(self, config_path):
        """验证配置文件是否是有效的bone_optimizer_gui输出

        Args:
            config_path: 配置文件路径

        Returns:
            bool: 是否是有效的配置文件
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 检查必需的字段
            required_fields = [
                'metadata',
                'joint_correspondence',
                'skeleton_params',
                't_pose_config'
            ]

            for field in required_fields:
                if field not in config:
                    return False

            # 检查metadata中是否有task_name
            if 'task_name' not in config['metadata']:
                return False

            # 检查是否是bone_optimizer_gui的输出
            if 'optimization_version' in config['metadata']:
                version = config['metadata']['optimization_version']
                if 'BVH' in version or 'Direct' in version:
                    return True

            return True

        except (json.JSONDecodeError, KeyError, Exception):
            return False

    def registryRobot(self):
        """注册机器人信息 - 使用从配置中提取的task_name"""
        # 注册机器人任务
        humanoid_batch_register.register_task(self.task_name)
        self.motion_lib_cfg: MotionLibCfg = humanoid_batch_register.get_config(self.task_name)
        self.motion_lib_cfg.device = self.device

        # 直接从motion_lib_cfg中读取optimize_root配置
        self.optimize_root = getattr(self.motion_lib_cfg, 'optimize_root', False)
        print(f"optimize_root: {self.optimize_root}")

        # 初始化前向运动学
        self.Humanoid_fk = Humanoid_Batch(
            self.motion_lib_cfg.mjcf_file,
            self.motion_lib_cfg.extend_node_dict,
            device=self.device
        )

    def initRobotBody(self):
        """初始化机器人关节信息"""
        # 获取关节信息
        self.robot_joint_names = self.motion_lib_cfg.joint_names
        self.robot_pos_names = self.motion_lib_cfg.pos_names
        self.limb_names = self.motion_lib_cfg.limb_names
        self.robot_joint_pick = self.motion_lib_cfg.joint_pick
        self.robot_joint_pick_bias = self.motion_lib_cfg.joint_pick_bias

        # BVH关节信息 - 从配置文件中获取
        self.bvh_joint_pick = self.skeleton_params['bvh_info']['selected_joints']

        # 如果配置中没有BVH关节索引，尝试根据关节名称生成
        if 'bvh_joint_indices' in self.skeleton_params['joint_correspondence']:
            self.bvh_joint_pick_idx = self.skeleton_params['joint_correspondence']['bvh_joint_indices']
        else:
            print("警告: 配置中没有BVH关节索引，将在运行时动态生成")
            self.bvh_joint_pick_idx = []

        # 创建关节链条
        self.limb_index = {}
        for key in self.limb_names:
            limb_name = self.limb_names[key]
            self.limb_index[key] = [self.robot_pos_names.index(i) for i in limb_name if i in self.robot_pos_names]

        # 获取关节名称对应的索引 - 统一使用robot_joint_pick_idx
        self.robot_joint_pick_idx = [self.robot_joint_names.index(j) for j in self.robot_joint_pick]

        print(f"机器人模型初始化完成: {len(self.robot_joint_names)} 个关节")
        print(f"机器人关键关节数: {len(self.robot_joint_pick)}")
        print(f"BVH关键关节数: {len(self.bvh_joint_pick)}")

    def init_joint_weights(self):
        """初始化关节权重"""
        # 创建关节权重张量 - 基于实际的机器人关节数量
        self.joint_weights = torch.ones(len(self.robot_joint_pick), device=self.device)

        # 如果配置中有关节权重设置，应用它们
        if hasattr(self.motion_lib_cfg, 'joint_weights'):
            for i, joint_name in enumerate(self.robot_joint_pick):
                if joint_name in self.motion_lib_cfg.joint_weights:
                    self.joint_weights[i] = self.motion_lib_cfg.joint_weights[joint_name]

        # 扩展权重到3D - 这里先用原始数量，后面会根据有效关节调整
        self.joint_weights = self.joint_weights.unsqueeze(0).unsqueeze(-1).expand(1, -1, 3)

    def update_joint_weights_for_valid_joints(self, num_valid_joints):
        """根据有效关节数量更新关节权重"""
        if num_valid_joints != len(self.robot_joint_pick):
            print(f"调整关节权重: {len(self.robot_joint_pick)} -> {num_valid_joints}")
            # 只保留有效关节的权重
            if num_valid_joints <= len(self.robot_joint_pick):
                self.joint_weights = self.joint_weights[:, :num_valid_joints, :]
            else:
                # 如果有效关节更多，扩展权重
                additional_weights = torch.ones(1, num_valid_joints - len(self.robot_joint_pick), 3, device=self.device)
                self.joint_weights = torch.cat([self.joint_weights, additional_weights], dim=1)

    def apply_skeleton_transform(self, bvh_positions):
        """
        应用坐标系变换 - 直接从JSON配置文件中获取4x4变换矩阵
        """
        print("应用坐标系转换...")

        # 从配置文件中获取变换信息
        coord_transform = self.skeleton_params.get('coordinate_transform', {})

        if 'transform_matrix' in coord_transform:
            # 直接使用配置文件中的4x4变换矩阵
            transform_matrix = coord_transform['transform_matrix']
            transform_4x4 = torch.tensor(transform_matrix, device=self.device, dtype=torch.float32)

            print(f"使用配置文件中的4x4变换矩阵")
            # 应用齐次变换
            transformed_positions = self._apply_homogeneous_transform(bvh_positions, transform_4x4)
        else:
            print("配置文件中没有变换矩阵，使用默认变换")
            transformed_positions = self._apply_default_transform(bvh_positions)

        # print(f"变换后位置范围: X[{transformed_positions[:,:,0].min():.3f}, {transformed_positions[:,:,0].max():.3f}], "
        #       f"Y[{transformed_positions[:,:,1].min():.3f}, {transformed_positions[:,:,1].max():.3f}], "
        #       f"Z[{transformed_positions[:,:,2].min():.3f}, {transformed_positions[:,:,2].max():.3f}]")

        return transformed_positions



    def _apply_homogeneous_transform(self, positions, transform_matrix):
        """应用齐次坐标变换"""
        original_shape = positions.shape

        # 将位置转换为齐次坐标 [x, y, z, 1]
        positions_flat = positions.reshape(-1, 3)
        ones = torch.ones(positions_flat.shape[0], 1, device=self.device, dtype=torch.float32)
        positions_homogeneous = torch.cat([positions_flat, ones], dim=1)  # [N, 4]

        # 应用变换: new_pos = transform_matrix @ old_pos.T
        transformed_homogeneous = (transform_matrix @ positions_homogeneous.T).T[..., :3]# [4, N]
        transformed_positions = transformed_homogeneous.reshape(original_shape)

        return transformed_positions

    def _apply_default_transform(self, bvh_positions):
        """应用默认的坐标系转换"""
        print("使用默认坐标系转换: BVH(x=左右,y=上下,z=前后) -> Robot(x=前后,y=左右,z=上下)")
        transformed_positions = torch.zeros_like(bvh_positions)
        transformed_positions[..., 0] = bvh_positions[..., 2]  # x_robot = z_bvh (前后)
        transformed_positions[..., 1] = bvh_positions[..., 0]  # y_robot = x_bvh (左右)
        transformed_positions[..., 2] = bvh_positions[..., 1]  # z_robot = y_bvh (上下)
        return transformed_positions



    def apply_rotation_coordinate_transform(self, bvh_rotations):
        """
        简单的旋转坐标系转换 - 直接从JSON配置文件中获取变换矩阵

        Args:
            bvh_rotations: [num_frames, 3] 轴角表示的旋转

        Returns:
            transformed_rotations: [num_frames, 3] 转换后的旋转
        """
        from scipy.spatial.transform import Rotation as R
        import numpy as np

        coord_cfg = self.skeleton_params.get('coordinate_transform', {})
        if 'transform_matrix' not in coord_cfg:
            # 未配置时不变
            return bvh_rotations

        T = np.asarray(coord_cfg['transform_matrix'], dtype=np.float32)[:3, :3]  # (3,3)
        T_inv = T.T    # 因为 T 是纯正交旋转，T⁻¹ = Tᵀ

        # ── 2. 轴角 → 旋转矩阵
        rot_mat = sRot.from_rotvec(
            bvh_rotations.detach().cpu().numpy()     # (T,3)
        ).as_matrix()                                 # (T,3,3)

        # ── 3. 坐标系变换  R' = T · R · Tᵀ
        #    等价于把 R 表示的姿态从 "参考系" 改写到 "机器人系"
        rot_mat_tr = np.einsum('ij,njk,kl->nil', T, rot_mat, T_inv)  # (T,3,3)

        # ── 4. 再转回轴角
        rotvec_tr = sRot.from_matrix(rot_mat_tr).as_rotvec()         # (T,3) rad

        return torch.tensor(
            rotvec_tr, device=bvh_rotations.device, dtype=bvh_rotations.dtype
        )



    def caluBVH2Robot(self):
        """
        计算BVH到机器人的映射

        Returns:
            dof_pos: 初始化的关节角度（T-pose）
            bvh_target_positions: BVH目标关节位置（已处理）
            root_pos: 根节点位置（已处理）
            root_rot: 根节点旋转（已处理）
        """
        # 获取BVH动作数据
        self.num_frames = self.bvh_motion.joint_position.shape[0]
        self.fps = self.bvh_motion.mocap_framerate

        print(f"BVH动作数据:")
        print(f"  - 帧数: {self.num_frames}")
        print(f"  - 帧率: {self.fps:.1f} fps")
        print(f"  - 时长: {self.num_frames / self.fps:.2f} 秒")
        print(f"  - 关节数: {len(self.bvh_motion.joint_name)}")

        # 获取关节对应关系
        joint_correspondence = self.skeleton_params.get('joint_correspondence', {})
        if 'bvh_joint_indices' in joint_correspondence and 'robot_joint_indices' in joint_correspondence:
            self.valid_bvh_joint_indices = joint_correspondence['bvh_joint_indices']
            self.valid_robot_joint_indices = joint_correspondence['robot_joint_indices']
            print(f"✅ 加载关节对应关系: {len(self.valid_bvh_joint_indices)} 对")
        else:
            raise ValueError("配置文件中缺少关节索引信息！")

        # 直接从配置文件获取参数
        skeleton_params = self.skeleton_params.get('skeleton_params', {})
        scale_factor = skeleton_params.get('scale_factor', 1.0)
        bvh_root_trans = skeleton_params.get('bvh_root_trans', [0.0, 0.0])

        print(f"✅ 从配置文件加载参数:")
        print(f"  - 缩放因子: {scale_factor}")
        print(f"  - 根节点变换: {bvh_root_trans}")


        # 对于动态运动，我们需要为每一帧计算FK，然后应用相同的坐标转换
        print("计算所有帧的BVH前向运动学...")
        self.bvh_motion.batch_forward_kinematics()

        bvh_joint_positions = self.bvh_motion.joint_translation

        bvh_joint_positions = torch.tensor(bvh_joint_positions, device=self.device, dtype=torch.float32)

        # 直接应用坐标系转换（与bone_optimizer_gui.py保持一致）
        print("应用坐标系转换...")

        # 坐标系转换：BVH坐标系 -> 机器人坐标系
        # BVH: z向前，y向上，x向左 -> 机器人: z向上，y向前，x向右
        bvh_positions_converted = torch.zeros_like(bvh_joint_positions)
        bvh_positions_converted[..., 0] = bvh_joint_positions[..., 2]   # x_robot = z_bvh
        bvh_positions_converted[..., 1] = bvh_joint_positions[..., 0]   # y_robot = x_bvh
        bvh_positions_converted[..., 2] = bvh_joint_positions[..., 1]   # z_robot = y_bvh
        # bvh_positions_converted = bvh_joint_positions

        # 应用缩放因子
        bvh_positions_converted *= scale_factor



        # 自适应高度调整找到脚趾最低点并调整根节点高度# 自适应高度调整找到脚趾最低点并调整根节点高度
        delta_height1 = min(bvh_positions_converted[:, 4, 2])
        delta_height2 = min(bvh_positions_converted[:, 4, 2])
        delta_height = min(delta_height1, delta_height2)
        print(f"✅ 自适应高度调整: {delta_height:.3f}米")
        bvh_positions_converted[:, :, 2] = bvh_positions_converted[:, :, 2] - delta_height
        # 应用根节点变换（如果需要）
        if bvh_root_trans != [0.0, 0.0]:
            root_trans = torch.zeros(3, device=self.device)
            root_trans[0] = bvh_root_trans[0]  # x方向
            root_trans[2] = bvh_root_trans[1]  # z方向
            bvh_positions_converted += root_trans

        bvh_joint_positions = bvh_positions_converted
        print(f"✅ 完成坐标系转换和缩放，缩放因子: {scale_factor}")

        # 提取目标关节位置
        bvh_target_positions = bvh_joint_positions[:, self.valid_bvh_joint_indices, :]

        # 处理根节点位置和旋转
        p_root = bvh_joint_positions[:, 0, :].clone()


        # 处理根节点旋转 - 使用备份脚本的方式，直接从BVH文件读取原始数据
        print("处理根节点旋转...")

        # 使用已经解包的根节点旋转数据（索引0是根节点）
        bvh_root_euler_degrees = self.bvh_motion.joint_orientation[:, 0, :]  # [num_frames, 3] 欧拉角（度）

        # 检查相邻帧之间的角度跳跃
        angle_diffs = np.abs(np.diff(bvh_root_euler_degrees, axis=0))
        max_diff = np.max(angle_diffs, axis=0)
        print(f"相邻帧最大角度变化: X={max_diff[0]:.3f}°, Y={max_diff[1]:.3f}°, Z={max_diff[2]:.3f}°")

        # 🔧 添加额外的角度连续性检查
        large_jumps = np.any(angle_diffs > 150, axis=1)  # 检测仍然存在的大跳跃
        num_large_jumps = np.sum(large_jumps)

        # 转换为弧度
        bvh_root_euler_radians = np.deg2rad(bvh_root_euler_degrees)

        # 直接转换为轴角表示（避免四元数）
        root_rotations_scipy = sRot.from_euler(self.bvh_motion.euler, bvh_root_euler_radians, degrees=False)
        root_rotations_aa = root_rotations_scipy.as_rotvec()  # 轴角表示

        # 应用坐标变换（如果需要）
        coord_transform = self.skeleton_params.get('coordinate_transform', {})
        if 'transform_matrix' in coord_transform:
            # 如果有坐标变换矩阵，也需要对旋转进行相应变换
            transform_matrix = np.array(coord_transform['transform_matrix'])
            # print(transform_matrix)

            # 🔧 修复：从4x4变换矩阵中提取3x3旋转部分
            rotation_transform = transform_matrix[:3, :3]  # 提取3x3旋转部分

            # 将轴角旋转转换为旋转矩阵
            rotation_matrices = root_rotations_scipy.as_matrix()  # [num_frames, 3, 3]

            # 应用坐标变换: R_new = T * R_old * T^(-1)
            transformed_matrices = np.matmul(np.matmul(rotation_transform, rotation_matrices), rotation_transform.T)

            # 转换回轴角表示
            transformed_rotations = sRot.from_matrix(transformed_matrices)
            root_rotations_aa = transformed_rotations.as_rotvec()

            print("应用了旋转的坐标变换")
        else:
            # 使用默认的坐标系转换：BVH: z前,y上,x左 -> Robot: z上,y前,x右
            print("应用默认的旋转坐标变换（矩阵共轭）…")
            # ① 把 axis–angle 转回旋转矩阵
            rotation_matrices = root_rotations_scipy.as_matrix()    # (T,3,3)

            # ② 构造你的坐标系旋转 R_axes，col-vect 记号
            #    BVH: x←左,y←上,z←前  → Robot: x←前,y←左,z←上
            R_axes = np.array([
                [0, 0, 1],   # x_robot = +z_bvh
                [0, 1, 0],   # y_robot = +y_bvh
                [1, 0, 0],   # z_robot = +x_bvh
            ], dtype=np.float32)

            # ③ 共轭变换：R_new = R_axes @ R_old @ R_axes^T
            R_new = np.einsum('ij,njk,kl->nil', R_axes, rotation_matrices, R_axes.T)

            # ④ 拆回轴–角
            root_rotations_aa = sRot.from_matrix(R_new).as_rotvec()  # (T,3) rad

        r_root = torch.tensor(root_rotations_aa, device=self.device, dtype=torch.float32)

        # 初始化DOF位置
        t_pose_config = self.skeleton_params.get('t_pose_config', {})
        if 'robot_dof_pos' in t_pose_config:
            t_pose_dof = torch.tensor(t_pose_config['robot_dof_pos'], device=self.device, dtype=torch.float32)
            print("✅ 使用配置文件中的T-pose DOF")
        elif hasattr(self.motion_lib_cfg, 'dof_pos') and self.motion_lib_cfg.dof_pos is not None:
            t_pose_dof = self.motion_lib_cfg.dof_pos.to(self.device)
            print("✅ 使用motion_lib_cfg中的T-pose DOF")
        else:
            t_pose_dof = torch.zeros(self.Humanoid_fk.joints_axis.shape[1], device=self.device)
            print("⚠️ 使用零DOF位置")

        # 为所有帧设置初始DOF值
        dof_pos = torch.zeros((1, self.num_frames, self.Humanoid_fk.joints_axis.shape[1], 1), device=self.device)
        if t_pose_dof.dim() == 1:
            t_pose_expanded = t_pose_dof.unsqueeze(0).repeat(self.num_frames, 1)
        else:
            t_pose_expanded = t_pose_dof.repeat(self.num_frames, 1)
        dof_pos[0, :, :, 0] = t_pose_expanded

        print(f"✅ BVH到机器人映射完成，数据已就绪")
        print(f"  - 目标位置形状: {bvh_target_positions.shape}")
        print(f"  - DOF形状: {dof_pos.shape}")
        print(f'{bvh_target_positions}')
        

        return dof_pos, bvh_target_positions, p_root, r_root



    def parse_bvh_motion_data(self):
        """
        解析BVH动作数据，获取所有帧的关节位置

        Returns:
            joint_positions: [num_frames, num_joints, 3] 关节位置
        """
        # 使用BVHMotion类计算前向运动学
        self.bvh_motion.batch_forward_kinematics()

        # 获取全局关节位置
        joint_positions = self.bvh_motion.joint_translation  # [num_frames, num_joints, 3]

        # 转换为torch张量
        joint_positions = torch.tensor(joint_positions, device=self.device, dtype=torch.float32)

        return joint_positions

    def calcAA(self, dof_pos, root_rot):
        """计算轴角表示，参考备份脚本的实现"""
        # 获取关节轴
        axes = self.Humanoid_fk.joints_axis

        # 获取关节角度
        angles = dof_pos

        # 计算所有非根关节的轴角
        joint_aa = angles * axes.unsqueeze(1)

        # 调整根旋转的形状以进行拼接
        root_rot_reshaped = root_rot.view(1, -1, 1, 3)

        # 拼接根旋转和关节轴角
        pose_aa_new = torch.cat([root_rot_reshaped, joint_aa], dim=2).to(self.device)

        return pose_aa_new

    def calcJointPosition(self, fk_return):
        """计算关节位置并应用偏置修正，参考备份脚本的实现"""
        # 获取关节位置
        joint_positions = fk_return['global_translation']

        # 如果有偏置配置，应用偏置修正
        if hasattr(self.motion_lib_cfg, 'joint_pick_bias') and self.motion_lib_cfg.joint_pick_bias:
            # 这里可以添加偏置修正逻辑
            # 暂时直接返回原始位置
            pass

        return joint_positions

    def compute_robot_fk(self, dof_pos, root_rot, root_pos):
        """计算机器人前向运动学 - 使用备份脚本的正确方式"""
        # 计算轴角表示 - 参考备份脚本的实现
        axes = self.Humanoid_fk.joints_axis  # [1, num_dofs, 3]
        angles = dof_pos  # [1, num_frames, num_dofs, 1]

        # 🔧 修复：使用备份脚本的正确计算方式
        # 去掉不必要的维度变换，直接计算轴角
        joint_aa = angles * axes.unsqueeze(1)  # [1, num_frames, num_dofs, 3]

        # 调整根旋转的形状以进行拼接
        # root_rot: [num_frames, 3] -> [1, num_frames, 1, 3]
        root_rot_reshaped = root_rot.view(1, -1, 1, 3)

        # 拼接根旋转和关节轴角
        pose_aa_new = torch.cat([root_rot_reshaped, joint_aa], dim=2).to(self.device)

        # 使用FK计算
        # root_pos: [num_frames, 3] -> [1, num_frames, 3]
        root_pos_batch = root_pos.unsqueeze(0) if root_pos.dim() == 2 else root_pos

        fk_return = self.Humanoid_fk.fk_batch(
            pose_aa_new,
            root_pos_batch,
            return_full=True
        )

        print(fk_return['global_translation'])

        # 返回关节位置
        return fk_return['global_translation']

    def sample_foot_points(self, fk_return, robot_positions):
        """
        在左右脚底周围采样点。
        Args:
            fk_return: FK计算结果字典，需要包含 'global_rotation_mat'
            robot_positions: 机器人关节全局位置 [batch_size, num_frames, num_joints, 3]
        Returns:
            left_foot_samples, right_foot_samples: 左右脚采样点位置
            [batch_size, num_frames, num_sample_points, 3] or (None, None) if indices invalid or data empty
        """
        if self.left_toe_idx == -1 or self.right_toe_idx == -1 or robot_positions.numel() == 0 or fk_return['global_rotation_mat'].numel() == 0:
            # 返回空的 Tensor 或 None，确保调用者能处理
            # print("Warning: Cannot sample foot points due to invalid index or empty FK/joint data.")
            empty_tensor = torch.empty((robot_positions.shape[0], robot_positions.shape[1], 0, 3), device=robot_positions.device, dtype=robot_positions.dtype)
            return empty_tensor, empty_tensor


        batch_size, num_frames, num_joints, _ = robot_positions.shape
        device = robot_positions.device

        # --- 修改：直接使用预定义的局部偏移列表 --- 
        # 从 self.foot_sample_points_local_offsets 获取偏移量
        # offsets_list = self.foot_sample_points_local_offsets 
        # --- 修改：从 motion_lib_cfg 获取偏移列表 --- 
        offsets_list = self.motion_lib_cfg.foot_sample_points_local_offsets
        # --- 结束修改 ---

        offsets = torch.tensor(offsets_list, device=device, dtype=robot_positions.dtype) # [num_sample_points, 3]
        num_sample_points = offsets.shape[0]

        # 获取脚趾关节的全局旋转矩阵和位置
        global_rot_mat = fk_return['global_rotation_mat'] # [B, N, J, 3, 3]
        
        # 安全索引检查
        if self.left_toe_idx >= num_joints or self.right_toe_idx >= num_joints:
             print(f"Error: Toe indices ({self.left_toe_idx}, {self.right_toe_idx}) out of bounds for num_joints ({num_joints}).")
             empty_tensor = torch.empty((batch_size, num_frames, 0, 3), device=device, dtype=robot_positions.dtype)
             return empty_tensor, empty_tensor

        left_toe_rot = global_rot_mat[:, :, self.left_toe_idx, :, :]  # [B, N, 3, 3]
        right_toe_rot = global_rot_mat[:, :, self.right_toe_idx, :, :] # [B, N, 3, 3]
        left_toe_pos = robot_positions[:, :, self.left_toe_idx, :]  # [B, N, 3]
        right_toe_pos = robot_positions[:, :, self.right_toe_idx, :] # [B, N, 3]

        # 扩展偏移量以进行批量计算
        # offsets shape: [num_sample_points, 3] -> [1, 1, num_sample_points, 3, 1]
        expanded_offsets = offsets.view(1, 1, num_sample_points, 3, 1).expand(batch_size, num_frames, -1, -1, -1)

        # 将局部偏移旋转到世界坐标系
        # left_toe_rot: [B, N, 3, 3] -> [B, N, 1, 3, 3]
        # right_toe_rot: [B, N, 3, 3] -> [B, N, 1, 3, 3]
        # rotated_offsets: [B, N, num_sample_points, 3, 1]
        left_rotated_offsets = torch.matmul(left_toe_rot.unsqueeze(2), expanded_offsets).squeeze(-1) # [B, N, num_sample_points, 3]
        right_rotated_offsets = torch.matmul(right_toe_rot.unsqueeze(2), expanded_offsets).squeeze(-1) # [B, N, num_sample_points, 3]

        # 添加到脚趾全局位置
        # left_toe_pos: [B, N, 3] -> [B, N, 1, 3]
        # right_toe_pos: [B, N, 3] -> [B, N, 1, 3]
        left_foot_samples = left_toe_pos.unsqueeze(2) + left_rotated_offsets    # [B, N, num_sample_points, 3]
        right_foot_samples = right_toe_pos.unsqueeze(2) + right_rotated_offsets # [B, N, num_sample_points, 3]

        return left_foot_samples, right_foot_samples

    def calcKeyPointDiffLoss(self, robot_positions, target_positions):
        """计算关键点差异损失"""
        # robot_positions: [1, num_frames, num_joints, 3]
        # target_positions: [num_frames, num_joints, 3]

        # 提取关键点位置
        robot_key_pos = robot_positions[0]  # [num_frames, num_joints, 3]

        # 计算加权损失
        diff = robot_key_pos - target_positions
        weighted_diff = diff * self.joint_weights
        loss = self.mse_loss(weighted_diff, torch.zeros_like(weighted_diff))

        return loss

    def calcSmoothnessLoss(self, robot_positions):
        """计算平滑损失"""
        # robot_joint_positions: [1, num_frames, num_joints, 3]
        positions = robot_positions[0]  # [num_frames, num_joints, 3]

        # 计算一阶差分（速度）
        pos_diff = positions[1:] - positions[:-1]
        pos_diff_norm = torch.norm(pos_diff, p=2, dim=-1)
        total_loss = self.mse_loss(pos_diff_norm, torch.zeros_like(pos_diff_norm))

        return total_loss

    def calcAccelerationLoss(self, robot_positions):
        """计算加速度损失，直接最小化加速度的大小"""
        # robot_joint_positions: [1, num_frames, num_joints, 3]
        positions = robot_positions  # [1, num_frames, num_joints, 3]
        
        if positions.shape[1] <= 2:
            return torch.tensor(0.0, device=self.device)
        
        # 计算速度
        v = positions[:, 1:, :, :] - positions[:, :-1, :, :]
        # 计算加速度
        a = v[:, 1:, :, :] - v[:, :-1, :, :]
        # 计算加速度的范数
        a_norm = torch.norm(a, p=2, dim=-1)  # [1, num_frames-2, num_joints]

        # 使用MSE损失直接最小化加速度
        total_loss = self.mse_loss(a_norm, torch.zeros_like(a_norm))
        return total_loss

    def caclFootSlipLoss(self, robot_positions, contact_sequence):
        """
        计算脚部滑动损失，惩罚接触地面时脚趾在XY平面的移动。
        Args:
            robot_positions: 机器人关节位置 [batch_size, num_frames, num_joints, 3]
            contact_sequence: 接触序列 [num_frames, 2] (左脚, 右脚), 布尔类型
        Returns:
            average_loss: 平均脚部滑动损失张量 (用于优化)
        """
        # 检查脚趾索引和接触序列是否有效
        if self.left_toe_idx == -1 or self.right_toe_idx == -1 or contact_sequence is None:
            num_frames_fallback = robot_positions.shape[1] if robot_positions.numel() > 0 else 0
            empty_per_frame = torch.zeros(num_frames_fallback, device=self.device)
            return torch.tensor(0.0, device=self.device), empty_per_frame

        batch_size, num_frames, _, _ = robot_positions.shape

        # 确保 contact_sequence 是布尔张量
        try:
            contact_mask = torch.as_tensor(contact_sequence, dtype=torch.bool, device=self.device)
        except Exception as e:
            print(f"错误：接触序列格式无法转换为布尔张量: {e}")
            empty_per_frame = torch.zeros(num_frames, device=self.device)
            return torch.tensor(0.0, device=self.device), empty_per_frame

        # 调整 contact_mask 帧数以匹配 robot_positions
        if contact_mask.shape[0] != num_frames:
            if contact_mask.shape[0] > num_frames:
                contact_mask = contact_mask[:num_frames]
            elif contact_mask.shape[0] > 0:
                padding = contact_mask[-1:].repeat(num_frames - contact_mask.shape[0], 1)
                contact_mask = torch.cat([contact_mask, padding], dim=0)
            else:
                contact_mask = torch.zeros((num_frames, 2), dtype=torch.bool, device=self.device)

        # 提取脚趾 XY 位置
        left_toe_xy = robot_positions[:, :, self.left_toe_idx, :2]  # [B, N, 2]
        right_toe_xy = robot_positions[:, :, self.right_toe_idx, :2] # [B, N, 2]

        # 计算相邻帧的位置差
        delta_left_xy = left_toe_xy[:, 1:] - left_toe_xy[:, :-1]   # [B, N-1, 2]
        delta_right_xy = right_toe_xy[:, 1:] - right_toe_xy[:, :-1] # [B, N-1, 2]

        # 获取接触掩码 (需要 t 和 t-1 都接触才计算滑动)
        # contact_mask_t: [N, 2] -> [B, N-1, 2]
        contact_mask_t = contact_mask[1:].unsqueeze(0).expand(batch_size, -1, -1)
        # contact_mask_tm1: [N, 2] -> [B, N-1, 2]
        contact_mask_tm1 = contact_mask[:-1].unsqueeze(0).expand(batch_size, -1, -1)
        
        # 左右脚的持续接触掩码 [B, N-1]
        left_steady_contact = contact_mask_t[:, :, 0] & contact_mask_tm1[:, :, 0]
        right_steady_contact = contact_mask_t[:, :, 1] & contact_mask_tm1[:, :, 1]

        # 初始化损失
        loss_slip_left = torch.zeros_like(delta_left_xy[..., 0])  # [B, N-1]
        loss_slip_right = torch.zeros_like(delta_right_xy[..., 0]) # [B, N-1]

        # 计算平移滑动损失 (平方范数)
        loss_slip_left[left_steady_contact] = torch.norm(delta_left_xy[left_steady_contact], p=2, dim=-1)**2
        loss_slip_right[right_steady_contact] = torch.norm(delta_right_xy[right_steady_contact], p=2, dim=-1)**2

        total_slip_loss = torch.sum(loss_slip_left) + torch.sum(loss_slip_right) # Scalar sum over batch and time
        num_slip_contributions = torch.sum(left_steady_contact) + torch.sum(right_steady_contact) # Scalar total contributing frames/feet

        # 计算平均损失 (用于优化)
        if num_slip_contributions > 0:
            average_loss = total_slip_loss / num_slip_contributions
        else:
            average_loss = torch.tensor(0.0, device=self.device)

        return average_loss

    def calcFootRotationLoss(self, fk_return, contact_sequence):
        """
        计算脚部旋转约束损失，惩罚接触地面时脚部的不自然旋转。
        Args:
            fk_return: FK计算结果字典，需要包含 'global_rotation_mat'
            contact_sequence: 接触序列 [num_frames, 2] (左脚, 右脚), 布尔类型
        Returns:
            average_loss: 平均脚部旋转损失张量 (用于优化)
        """
            
        # 检查脚趾索引和接触序列是否有效
        if self.left_toe_idx == -1 or self.right_toe_idx == -1 or contact_sequence is None:
            # 从fk_return推断帧数
            if fk_return is not None and 'global_rotation_mat' in fk_return:
                num_frames_fallback = fk_return['global_rotation_mat'].shape[1]
            else:
                num_frames_fallback = 0
            empty_per_frame = torch.zeros(num_frames_fallback, device=self.device)
            return torch.tensor(0.0, device=self.device), empty_per_frame

        if fk_return is None or 'global_rotation_mat' not in fk_return:
            print("警告：calcFootRotationLoss需要FK结果中的旋转矩阵")
            return torch.tensor(0.0, device=self.device), torch.zeros(1, device=self.device)

        # 获取维度信息
        global_rot_mat = fk_return['global_rotation_mat']  # [B, N, J, 3, 3]
        batch_size, num_frames, _, _, _ = global_rot_mat.shape

        # 确保 contact_sequence 是布尔张量
        try:
            contact_mask = torch.as_tensor(contact_sequence, dtype=torch.bool, device=self.device)
        except Exception as e:
            print(f"错误：接触序列格式无法转换为布尔张量: {e}")
            empty_per_frame = torch.zeros(num_frames, device=self.device)
            return torch.tensor(0.0, device=self.device), empty_per_frame

        # 调整 contact_mask 帧数以匹配 global_rot_mat
        if contact_mask.shape[0] != num_frames:
            if contact_mask.shape[0] > num_frames:
                contact_mask = contact_mask[:num_frames]
            elif contact_mask.shape[0] > 0:
                padding = contact_mask[-1:].repeat(num_frames - contact_mask.shape[0], 1)
                contact_mask = torch.cat([contact_mask, padding], dim=0)
            else:
                contact_mask = torch.zeros((num_frames, 2), dtype=torch.bool, device=self.device)

        try:
            # 提取脚部旋转矩阵
            left_foot_rot = global_rot_mat[:, :, self.left_toe_idx, :, :]   # [B, N, 3, 3]
            right_foot_rot = global_rot_mat[:, :, self.right_toe_idx, :, :] # [B, N, 3, 3]
            
            # 计算相邻帧的旋转差异
            left_rot_t = left_foot_rot[:, 1:, :, :]    # [B, N-1, 3, 3]
            left_rot_tm1 = left_foot_rot[:, :-1, :, :] # [B, N-1, 3, 3]
            right_rot_t = right_foot_rot[:, 1:, :, :]    # [B, N-1, 3, 3]
            right_rot_tm1 = right_foot_rot[:, :-1, :, :] # [B, N-1, 3, 3]
            
            # 计算相对旋转矩阵 R_rel = R_t^T @ R_{t-1}
            left_rel_rot = torch.matmul(left_rot_t.transpose(-2, -1), left_rot_tm1)    # [B, N-1, 3, 3]
            right_rel_rot = torch.matmul(right_rot_t.transpose(-2, -1), right_rot_tm1) # [B, N-1, 3, 3]
            
            # 计算旋转角度 (测地线距离)
            # theta = acos((Trace(R_rel) - 1) / 2)
            left_trace = torch.diagonal(left_rel_rot, offset=0, dim1=-2, dim2=-1).sum(dim=-1)   # [B, N-1]
            right_trace = torch.diagonal(right_rel_rot, offset=0, dim1=-2, dim2=-1).sum(dim=-1) # [B, N-1]
            
            left_cos_theta = (left_trace - 1.0) / 2.0
            right_cos_theta = (right_trace - 1.0) / 2.0
            
            # 数值稳定性处理
            eps = 1e-7
            left_cos_theta_clamped = torch.clamp(left_cos_theta, -1.0 + eps, 1.0 - eps)
            right_cos_theta_clamped = torch.clamp(right_cos_theta, -1.0 + eps, 1.0 - eps)
            
            left_angle_rad = torch.acos(left_cos_theta_clamped)   # [B, N-1]
            right_angle_rad = torch.acos(right_cos_theta_clamped) # [B, N-1]
            
            # 获取接触掩码 (需要 t 和 t-1 都接触才计算旋转约束)
            contact_mask_t = contact_mask[1:].unsqueeze(0).expand(batch_size, -1, -1)    # [B, N-1, 2]
            contact_mask_tm1 = contact_mask[:-1].unsqueeze(0).expand(batch_size, -1, -1) # [B, N-1, 2]
            
            # 左右脚的持续接触掩码 [B, N-1]
            left_steady_contact = contact_mask_t[:, :, 0] & contact_mask_tm1[:, :, 0]
            right_steady_contact = contact_mask_t[:, :, 1] & contact_mask_tm1[:, :, 1]
            
            # 初始化旋转损失
            loss_rotation_left = torch.zeros_like(left_angle_rad)   # [B, N-1]
            loss_rotation_right = torch.zeros_like(right_angle_rad) # [B, N-1]
            
            # 只对接触状态的帧计算旋转损失
            # 使用线性损失而不是平方损失，确保角度越大损失越大
            loss_rotation_left[left_steady_contact] = left_angle_rad[left_steady_contact]
            loss_rotation_right[right_steady_contact] = right_angle_rad[right_steady_contact]
            
            # 总旋转损失
            total_rotation_loss = torch.sum(loss_rotation_left) + torch.sum(loss_rotation_right)
            num_rotation_contributions = torch.sum(left_steady_contact) + torch.sum(right_steady_contact)
            
            # 计算平均损失 (用于优化)
            if num_rotation_contributions > 0:
                average_loss = total_rotation_loss / num_rotation_contributions
            else:
                average_loss = torch.tensor(0.0, device=self.device)

        except Exception as e:
            # 如果旋转计算失败，返回零损失
            print(f"脚部旋转约束计算错误: {e}")
            average_loss = torch.tensor(0.0, device=self.device)

        return average_loss
    
    def calcRootOptimizationLoss(self, root_trans_offset_new, root_trans_offset, root_rot_new, gt_root_rot):
        """
        计算根节点优化损失，仅当 optimize_root=True 时调用,位于每个机器人的motion_lib_cfg.py中。
        Args:
            root_trans_offset_new: 优化后的根节点平移 [batch_size, 3]
            root_trans_offset: 初始根节点平移 [batch_size, 3]
            root_rot_new: 优化后的根节点旋转 [batch_size, 3]
            gt_root_rot: 初始根节点旋转 [batch_size, 3]
        Returns:
            loss_root_trans: 根节点平移损失
            loss_root_rot: 根节点旋转损失
        """
            
        loss_root_trans = self.mse_loss(root_trans_offset_new, root_trans_offset)

        # 将轴角转换为旋转矩阵
        R_pred = axis_angle_to_matrix(root_rot_new) # [N, 3, 3]
        R_target = axis_angle_to_matrix(gt_root_rot) # [N, 3, 3]

        # 计算测地线距离 (geodesic distance)作为旋转损失
        # 1. 计算相对旋转矩阵 R_rel = R_pred^T @ R_target
        R_rel = torch.matmul(R_pred.transpose(-2, -1), R_target)

        # 2. 计算迹 Trace(R_rel)
        trace_R_rel = torch.diagonal(R_rel, offset=0, dim1=-2, dim2=-1).sum(dim=-1)

        # 3. 计算旋转角度 theta = acos((Trace(R_rel) - 1) / 2)
        #    裁剪参数以确保数值稳定性
        cos_theta = (trace_R_rel - 1.0) / 2.0
        eps = 1e-7 # 防止 acos 的参数超出 [-1, 1]
        cos_theta_clamped = torch.clamp(cos_theta, -1.0 + eps, 1.0 - eps)
        angle_rad = torch.acos(cos_theta_clamped) # 弧度制的测地线距离

        # 4. 使用角度的平均值作为损失
        loss_root_rot = torch.mean(angle_rad)

        return loss_root_trans, loss_root_rot

    def calcContactFootLoss(self, fk_return, robot_positions, contact_sequence):
        """
        计算踝关节姿态约束损失 - 只有当脚部接触且满足平行条件时才计算损失
        
        Args:
            fk_return: FK计算结果字典，需要包含 'global_rotation_mat'
            robot_positions: 机器人关节位置 [batch_size, num_frames, num_joints, 3]
            contact_sequence: 接触序列 [num_frames, 2] (左脚, 右脚), 布尔类型
            max_toe_up_angle: 保持兼容性的参数，实际不使用
            
        Returns:
            average_loss: 平均踝关节姿态损失张量 (用于优化)
            per_frame_ankle_pose_loss: 每帧踝关节姿态损失张量 [num_frames] (用于可视化)
        """
            
        # 检查脚踝索引和接触序列是否有效
        try:
            left_ankle_idx = self.robot_joint_names.index('leg_l6_link')
            right_ankle_idx = self.robot_joint_names.index('leg_r6_link')
        except ValueError:
            # 如果找不到脚踝，使用脚趾索引作为替代
            left_ankle_idx = self.left_toe_idx
            right_ankle_idx = self.right_toe_idx
            
        if left_ankle_idx == -1 or right_ankle_idx == -1 or contact_sequence is None:
            num_frames_fallback = robot_positions.shape[1] if robot_positions.numel() > 0 else 0
            empty_per_frame = torch.zeros(num_frames_fallback, device=self.device)
            return torch.tensor(0.0, device=self.device), empty_per_frame

        batch_size, num_frames, _, _ = robot_positions.shape

        # 确保 contact_sequence 是布尔张量
        try:
            contact_mask = torch.as_tensor(contact_sequence, dtype=torch.bool, device=self.device)
        except Exception as e:
            print(f"错误：接触序列格式无法转换为布尔张量: {e}")
            empty_per_frame = torch.zeros(num_frames, device=self.device)
            return torch.tensor(0.0, device=self.device), empty_per_frame

        # 调整 contact_mask 帧数以匹配 robot_positions
        if contact_mask.shape[0] != num_frames:
            if contact_mask.shape[0] > num_frames:
                contact_mask = contact_mask[:num_frames]
            elif contact_mask.shape[0] > 0:
                padding = contact_mask[-1:].repeat(num_frames - contact_mask.shape[0], 1)
                contact_mask = torch.cat([contact_mask, padding], dim=0)
            else:
                contact_mask = torch.zeros((num_frames, 2), dtype=torch.bool, device=self.device)

        # 获取踝关节的全局旋转矩阵
        global_rot_mat = fk_return['global_rotation_mat']  # [B, N, J, 3, 3]
        left_ankle_rot = global_rot_mat[:, :, left_ankle_idx, :, :]   # [B, N, 3, 3]
        right_ankle_rot = global_rot_mat[:, :, right_ankle_idx, :, :] # [B, N, 3, 3]
        
        # 世界坐标系的基准向量
        world_z = torch.tensor([0.0, 0.0, 1.0], device=self.device)  # 世界Z轴

        # 提取踝关节坐标系的各个轴
        left_ankle_z = left_ankle_rot[:, :, :, 2]   # [B, N, 3] - 踝关节Z轴
        right_ankle_z = right_ankle_rot[:, :, :, 2] # [B, N, 3] - 踝关节Z轴
        
        # 计算平行条件检测
        left_z_world_z_dot = torch.sum(left_ankle_z * world_z, dim=-1)   # [B, N]
        right_z_world_z_dot = torch.sum(right_ankle_z * world_z, dim=-1) # [B, N]
        
        # 平行条件：Y轴与世界Z轴平行，X轴和Z轴与世界Z轴垂直
        parallel_threshold = 0.985# Y轴平行阈值0.985:17.3°,0.995:10°
    
        left_parallel_condition = (left_z_world_z_dot > parallel_threshold)
        right_parallel_condition = (right_z_world_z_dot > parallel_threshold)
        
        # 接触条件
        left_contact_mask_expanded = contact_mask[:, 0].unsqueeze(0).expand(batch_size, -1)  # [B, N]
        right_contact_mask_expanded = contact_mask[:, 1].unsqueeze(0).expand(batch_size, -1) # [B, N]
        
        # 综合条件：接触 AND 平行
        left_valid_condition = left_contact_mask_expanded & left_parallel_condition   # [B, N]
        right_valid_condition = right_contact_mask_expanded & right_parallel_condition # [B, N]
        
        # 计算踝关节姿态损失（只有满足条件的帧才计算）
        # Roll约束：Z轴应该与世界Z轴平行
        left_roll_loss = (1.0 - left_z_world_z_dot) ** 2    # [B, N]
        right_roll_loss = (1.0 - right_z_world_z_dot) ** 2  # [B, N]
        
        left_total_loss = left_roll_loss  * left_valid_condition.float()  # [B, N]
        right_total_loss = right_roll_loss * right_valid_condition.float()  # [B, N]
        # 总损失
        ankle_pose_loss = left_total_loss + right_total_loss  # [B, N]
        
        # 计算有效帧数（用于归一化）
        valid_frames = torch.sum(left_valid_condition.float()) + torch.sum(right_valid_condition.float())
        
        if valid_frames > 0:
            average_loss = torch.sum(ankle_pose_loss) / valid_frames
        else:
            average_loss = torch.tensor(0.0, device=self.device)

        return average_loss
    
    def calcFootHeightLoss(self, left_foot_samples, right_foot_samples, fk_return, contact_sequence):
        """
        策略：
        1. 对接触帧应用强制性地面约束（指数级惩罚严重偏差）
        2. 对非接触帧应用适度的悬空约束（防止过度悬空）
        3. 全局根节点高度优化（大幅度调整能力）
        4. 渐进式惩罚机制（轻微偏差线性惩罚，严重偏差指数惩罚）
        
        Args:
            left_foot_samples: [B, F, N, 3] 左脚底采样点
            right_foot_samples: [B, F, N, 3] 右脚底采样点  
            fk_return: dict, FK计算结果
            contact_sequence: [F, 2] 接触序列，布尔值
        
        Returns:
            total_loss: scalar 总的脚部高度损失
            per_frame_loss: [F] 每帧脚部高度损失 (用于可视化)
        """
        # 检查采样点是否为空
        if (left_foot_samples.numel() == 0 or right_foot_samples.numel() == 0):
            print("calcFootHeightLoss: foot samples are empty, returning zero loss")
            num_frames_fallback = contact_sequence.shape[0] if contact_sequence is not None else 1
            empty_per_frame = torch.zeros(num_frames_fallback, device=self.device)
            return torch.tensor(0.0, device=self.device), empty_per_frame

        # 确保 contact_sequence 是 torch 张量
        if contact_sequence is not None and isinstance(contact_sequence, np.ndarray):
            contact_sequence = torch.from_numpy(contact_sequence).to(self.device)

        batch_size, num_frames, num_samples, _ = left_foot_samples.shape
        self.current_frame_foot_height_loss = torch.zeros(num_frames, device=self.device) if num_frames > 0 else None
        
        # 地面高度设定
        ground_height = 0  # 默认为0.0
        
        # 提取脚底采样点的Z坐标
        left_z = left_foot_samples[..., 2]   # [B, F, N]
        right_z = right_foot_samples[..., 2] # [B, F, N]
        
        # 计算每帧脚部的最低点高度
        left_min_height = left_z.min(dim=-1)[0]   # [B, F] 左脚最低点
        right_min_height = right_z.min(dim=-1)[0] # [B, F] 右脚最低点
        
        # 初始化损失组件
        total_contact_constraint_loss = torch.tensor(0.0, device=self.device)
        total_global_adjustment_loss = torch.tensor(0.0, device=self.device)
        total_non_contact_constraint_loss = torch.tensor(0.0, device=self.device)
        per_frame_loss = torch.zeros(num_frames, device=self.device)
        
        if contact_sequence is not None:
            # 扩展contact_sequence以匹配batch维度
            contact_mask = contact_sequence.unsqueeze(0).expand(batch_size, -1, -1)  # [B, F, 2]
            left_contact_mask = contact_mask[:, :, 0]   # [B, F]
            right_contact_mask = contact_mask[:, :, 1]  # [B, F]
            
            # === 1. 智能接触约束：考虑步态转换 ===
            # 检测接触状态转换，在转换时降低约束强度
            
            # 检测接触状态变化 (从接触到非接触的转换)
            left_transition_mask = self._detect_contact_transitions(left_contact_mask)  # [B, F]
            right_transition_mask = self._detect_contact_transitions(right_contact_mask)  # [B, F]
            
            # 左脚接触约束 - 在转换期间降低约束
            left_contact_expanded = left_contact_mask.unsqueeze(-1).expand(-1, -1, num_samples)  # [B, F, N]
            left_transition_expanded = left_transition_mask.unsqueeze(-1).expand(-1, -1, num_samples)  # [B, F, N]
            left_height_deviation = left_z - ground_height  # [B, F, N] 高度偏差（正值=悬空，负值=穿透）
            
            # 动态权重：转换期间降低权重，稳定接触期间正常权重
            left_dynamic_weight = torch.where(left_transition_expanded, 0.1, 1.0)  # 转换期间降低到10%
            
            left_contact_penalty = torch.where(
                left_contact_expanded,
                left_dynamic_weight * torch.abs(left_height_deviation) ** 2,
                torch.zeros_like(left_height_deviation)
            )
            
            # 右脚接触约束 - 同样的逻辑
            right_contact_expanded = right_contact_mask.unsqueeze(-1).expand(-1, -1, num_samples)  # [B, F, N]
            right_transition_expanded = right_transition_mask.unsqueeze(-1).expand(-1, -1, num_samples)  # [B, F, N]
            right_height_deviation = right_z - ground_height  # [B, F, N]
            
            right_dynamic_weight = torch.where(right_transition_expanded, 0.1, 1.0)
            
            right_contact_penalty = torch.where(
                right_contact_expanded,
                right_dynamic_weight * torch.abs(right_height_deviation) ** 2,
                torch.zeros_like(right_height_deviation)
            )
            
            # 接触约束总损失（高权重，处理微小偏差）
            contact_constraint_weight = 1.0  # 适中权重
            total_contact_constraint_loss = contact_constraint_weight * (
                torch.sum(left_contact_penalty) + torch.sum(right_contact_penalty)
            ) / (torch.sum(left_contact_expanded.float()) + torch.sum(right_contact_expanded.float()) + 1e-8)
            
            # === 2. 全局根节点高度调整（微调用） ===
            # 计算接触帧的平均高度偏差，用于最后的微调
            contact_heights = []
            if torch.any(left_contact_mask):
                left_contact_heights = left_min_height[left_contact_mask]
                contact_heights.append(left_contact_heights)
            if torch.any(right_contact_mask):
                right_contact_heights = right_min_height[right_contact_mask]
                contact_heights.append(right_contact_heights)
                
            if len(contact_heights) > 0:
                all_contact_heights = torch.cat(contact_heights)
                global_height_error = torch.mean(all_contact_heights) - ground_height
                
                # 全局调整损失（微调权重）
                global_adjustment_weight = 0.0  # 保持合理权重
                total_global_adjustment_loss = global_adjustment_weight * (global_height_error ** 2)
            
            # === 3. 非接触脚约束（防止过度悬空） ===
            # 对非接触帧应用适度的高度约束，防止脚部过度悬空
            max_reasonable_height = ground_height + 1.0  # 合理悬空高度
            
            # 左脚非接触约束
            left_non_contact_mask = ~left_contact_mask  # [B, F]
            left_non_contact_expanded = left_non_contact_mask.unsqueeze(-1).expand(-1, -1, num_samples)  # [B, F, N]
            left_excessive_height = torch.relu(left_z - max_reasonable_height)  # 只惩罚过度悬空
            left_non_contact_penalty = torch.where(
                left_non_contact_expanded,
                left_excessive_height ** 2,
                torch.zeros_like(left_excessive_height)
            )
            
            # 右脚非接触约束
            right_non_contact_mask = ~right_contact_mask  # [B, F]
            right_non_contact_expanded = right_non_contact_mask.unsqueeze(-1).expand(-1, -1, num_samples)  # [B, F, N]
            right_excessive_height = torch.relu(right_z - max_reasonable_height)
            right_non_contact_penalty = torch.where(
                right_non_contact_expanded,
                right_excessive_height ** 2,
                torch.zeros_like(right_excessive_height)
            )
            
            # 非接触约束总损失（适度权重）
            non_contact_weight = 0.0  # 适度权重
            total_non_contact_constraint_loss = non_contact_weight * (
                torch.sum(left_non_contact_penalty) + torch.sum(right_non_contact_penalty)
            ) / (torch.sum(left_non_contact_expanded.float()) + torch.sum(right_non_contact_expanded.float()) + 1e-8)
            
            # === 4. 计算每帧损失（用于可视化） ===
            if batch_size == 1:
                # 接触约束每帧损失
                frame_contact_loss = (torch.sum(left_contact_penalty.squeeze(0), dim=-1) + 
                                    torch.sum(right_contact_penalty.squeeze(0), dim=-1))  # [F]
                
                # 非接触约束每帧损失
                frame_non_contact_loss = (torch.sum(left_non_contact_penalty.squeeze(0), dim=-1) + 
                                        torch.sum(right_non_contact_penalty.squeeze(0), dim=-1))  # [F]
                
                # 全局调整损失均匀分布到每帧
                frame_global_loss = torch.full((num_frames,), total_global_adjustment_loss.item() / num_frames, device=self.device)
                
                per_frame_loss = (contact_constraint_weight * frame_contact_loss + 
                                non_contact_weight * frame_non_contact_loss + 
                                frame_global_loss)
            else:
                # 多batch情况，取第一个样本
                frame_contact_loss = (torch.sum(left_contact_penalty[0], dim=-1) + 
                                    torch.sum(right_contact_penalty[0], dim=-1))  # [F]
                frame_non_contact_loss = (torch.sum(left_non_contact_penalty[0], dim=-1) + 
                                        torch.sum(right_non_contact_penalty[0], dim=-1))  # [F]
                frame_global_loss = torch.full((num_frames,), total_global_adjustment_loss.item() / num_frames, device=self.device)
                
                per_frame_loss = (contact_constraint_weight * frame_contact_loss + 
                                non_contact_weight * frame_non_contact_loss + 
                                frame_global_loss)
            
        else:
            # 没有接触序列信息时的退化处理
            print("Warning: No contact sequence provided, applying uniform ground constraint")
            raise ValueError("No contact sequence provided")
        
        # === 5. 总损失 ===
        total_loss = (total_contact_constraint_loss + 
                     total_global_adjustment_loss + 
                     total_non_contact_constraint_loss)
        
        return total_loss
    
    def _detect_contact_transitions(self, contact_mask):
        """
        检测接触状态转换，在转换期间降低约束强度
        
        Args:
            contact_mask: [B, F] 接触掩码
            
        Returns:
            transition_mask: [B, F] 转换期间的掩码
        """
        batch_size, num_frames = contact_mask.shape
        transition_mask = torch.zeros_like(contact_mask, dtype=torch.bool)
        
        if num_frames <= 2:
            return transition_mask
        
        # 检测状态变化：从True到False的转换
        for b in range(batch_size):
            for f in range(num_frames - 1):
                # 从接触到非接触的转换（抬脚过程）
                if contact_mask[b, f] and not contact_mask[b, f + 1]:
                    # 标记转换前后各2帧为转换期间，允许自然的脚后跟抬起
                    start_frame = max(0, f - 8)  # 转换前5帧
                    end_frame = min(num_frames, f + 1)  # 转换后1帧
                    transition_mask[b, start_frame:end_frame] = True
                    
                # 从非接触到接触的转换（着地过程）
                elif not contact_mask[b, f] and contact_mask[b, f + 1]:
                    # 标记转换前后各1帧，允许自然着地
                    start_frame = max(0, f - 1)
                    end_frame = min(num_frames, f + 5)
                    transition_mask[b, start_frame:end_frame] = True
        
        return transition_mask


    def retargetBVHData(self):
        """重定向BVH数据 - 简化版本，专注于dof_pos优化"""
        print("="*50)
        print("开始BVH动作重定向")
        print("="*50)

        # 1. 获取预处理的BVH数据作为目标参考
        dof_pos, bvh_target_positions, root_pos, root_rot = self.caluBVH2Robot()

        # print(root_pos.numpy().tolist())
        # light_visualization([root_pos.numpy().tolist()]*200)

        print(f"目标位置数据形状: {bvh_target_positions.shape}")
        print(f"关节角度数据形状: {dof_pos.shape}")
        print(f"根节点位置形状: {root_pos.shape}")
        print(f"根节点旋转形状: {root_rot.shape}")

        # 2. 更新关节权重以匹配有效关节数量
        num_valid_joints = bvh_target_positions.shape[1]
        self.update_joint_weights_for_valid_joints(num_valid_joints)

        # 使用配置中定义的有效关节索引
        if hasattr(self, 'valid_robot_joint_indices'):
            self.current_robot_joint_pick_idx = self.valid_robot_joint_indices
            print(f"使用配置文件中的机器人关节索引: {len(self.current_robot_joint_pick_idx)} 个")
            print(f"BVH关节索引数量: {len(self.valid_bvh_joint_indices)} 个")

            # 验证数量一致性
            if len(self.current_robot_joint_pick_idx) != len(self.valid_bvh_joint_indices):
                print(f"警告: 机器人关节数({len(self.current_robot_joint_pick_idx)}) != BVH关节数({len(self.valid_bvh_joint_indices)})")
        else:
            # 回退方案
            self.current_robot_joint_pick_idx = self.robot_joint_pick_idx
            print(f"使用默认机器人关节索引: {len(self.current_robot_joint_pick_idx)} 个")

        # 加载接触序列（如果存在）
        self.contact_sequence = None
        if os.path.exists(self.contact_file_path):
            try:
                with open(self.contact_file_path, 'r') as f:
                    contact_data = json.load(f)
                # 验证键
                if all(k in contact_data for k in ['contacts', 'overrides', 'height_threshold', 'velocity_threshold']):
                    self.contact_sequence = np.array(contact_data['contacts'], dtype=bool)
                    print(f"✅ 成功加载接触序列: {self.contact_file_path}")
                    print(f"  - 接触序列形状: {self.contact_sequence.shape}")
                    print(f"  - 左脚接触帧数: {self.contact_sequence[:, 0].sum()}")
                    print(f"  - 右脚接触帧数: {self.contact_sequence[:, 1].sum()}")
                else:
                    print(f"⚠️ 接触文件 {self.contact_file_path} 缺少必要的键，将使用默认接触序列")
            except json.JSONDecodeError:
                print(f"⚠️ 无法解码接触文件 JSON: {self.contact_file_path}，将使用默认接触序列")
            except Exception as e:
                print(f"⚠️ 加载接触文件时出错 {self.contact_file_path}: {e}，将使用默认接触序列")
        else:
            print(f"⚠️ 接触文件不存在: {self.contact_file_path}，将在后续使用默认接触序列")

        # 3. 创建优化变量
        from torch.autograd import Variable

        dof_pos_new = Variable(dof_pos.clone(), requires_grad=True)


        if self.optimize_root:
            print("开启根节点优化")
            root_pos_tensor = torch.as_tensor(root_pos, device=self.device, dtype=torch.float32)
            root_rot_tensor = torch.as_tensor(root_rot, device=self.device, dtype=torch.float32)
            root_pos_new = Variable(root_pos_tensor.clone(), requires_grad=True)
            root_rot_new = Variable(root_rot_tensor.clone(), requires_grad=True)
        else:
            print("关闭根节点优化")
            root_pos_new = torch.as_tensor(root_pos, device=self.device, dtype=torch.float32)
            root_rot_new = torch.as_tensor(root_rot, device=self.device, dtype=torch.float32)

        # 4. 创建优化器 - 参考备份脚本的设置
        optimizer_params = [{'params': dof_pos_new, 'lr': 0.02}]
        if self.optimize_root:
            optimizer_params.extend([
                {'params': root_pos_new, 'lr': 0.005},
                {'params': root_rot_new, 'lr': 0.005}
            ])

        optimizer = torch.optim.Adam(optimizer_params)
        scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=500, gamma=0.5)

        # 5. 优化循环 - 核心任务：调整dof_pos使机器人FK结果匹配BVH目标
        loss_history = []

        print(f"\n开始优化循环: {self.iterations} 次迭代")
        print(f"学习率: {self.learning_rate}")
        print(f"优化目标: 使机器人FK结果匹配预处理的BVH目标")

        # 简化的关节限制信息
        if hasattr(self.Humanoid_fk, 'joints_range') and self.Humanoid_fk.joints_range is not None:
            print(f"\n📐 关节限制信息: {self.Humanoid_fk.joints_range.shape[0]} 个关节")
        else:
            print("⚠️ 警告: 没有找到关节限制信息！")

        for iteration in range(self.iterations):
            optimizer.zero_grad()

            # 限制关节角度范围
            if hasattr(self.Humanoid_fk, 'joints_range') and self.Humanoid_fk.joints_range is not None:
                dof_pos_new.data.clamp_(
                    self.Humanoid_fk.joints_range[:, 0, None],
                    self.Humanoid_fk.joints_range[:, 1, None]
                )

            pose_aa_new = self.calcAA(dof_pos_new, root_rot_new)
            fk_return = self.Humanoid_fk.fk_batch(
                pose_aa_new,
                root_pos_new[None,],
                return_full=True
            )

            # 计算关节位置
            robot_joint_positions = self.calcJointPosition(fk_return)
            robot_key_positions = robot_joint_positions[:, :, self.current_robot_joint_pick_idx]
            # light_visualization(robot_key_positions.detach().numpy()[0])
            # light_visualization(bvh_target_positions)

            left_foot_samples, right_foot_samples = self.sample_foot_points(fk_return, robot_joint_positions)

            # 计算各项损失：机器人FK结果 vs BVH目标
            keypoint_loss = self.calcKeyPointDiffLoss(robot_key_positions, bvh_target_positions)
            # smoothness_loss_total = self.calcSmoothnessLoss(robot_joint_positions)
            # acceleration_loss = self.calcAccelerationLoss(robot_joint_positions)
            # foot_slip_loss = self.caclFootSlipLoss(robot_joint_positions, self.contact_sequence)
            # foot_rotation_loss = self.calcFootRotationLoss(fk_return, self.contact_sequence)
            # root_trans_loss, root_rot_loss = self.calcRootOptimizationLoss(root_pos_new, root_pos, root_rot_new, root_rot)
            # contact_foot_loss = self.calcContactFootLoss(fk_return, robot_joint_positions, self.contact_sequence)
            # foot_height_loss = self.calcFootHeightLoss(left_foot_samples, right_foot_samples, fk_return, self.contact_sequence)

            # 计算关节限制损失
            joint_limit_loss = torch.tensor(0.0, device=self.device)

            # 总损失
            total_loss = (
                self.keypoint_loss_weight * keypoint_loss 
            #     self.smoothness_loss_weight * smoothness_loss_total +
            #     self.acceleration_loss_weight * acceleration_loss +
            #     self.foot_slip_loss_weight * foot_slip_loss +    
            #     self.foot_rotation_loss_weight * foot_rotation_loss +
            #     self.root_trans_loss_weight * root_trans_loss +
            #     self.root_rot_loss_weight * root_rot_loss +
            #     self.contact_foot_loss_weight * contact_foot_loss +
            #     self.foot_height_loss_weight * foot_height_loss
            )

            # 反向传播和优化
            total_loss.backward()
            optimizer.step()
            scheduler.step()



            # 记录损失
            loss_dict = {
                'total': total_loss.item(),
                'keypoint': keypoint_loss.item(),
                # 'smoothness': smoothness_loss_total.item(),
                # 'acceleration': acceleration_loss.item(),
                # 'foot_slip': foot_slip_loss.item(),
                # 'foot_rotation': foot_rotation_loss.item(),
                # 'root_trans': root_trans_loss.item(),
                # 'root_rot': root_rot_loss.item(),
                # 'contact_foot': contact_foot_loss.item(),
                # 'foot_height': foot_height_loss.item()
            }
            loss_history.append(loss_dict)

            # 输出进度
            if iteration % 10 == 0 or iteration == self.iterations - 1:
                print(f"迭代 {iteration:4d}: 总损失={total_loss.item():.6f}, "
                      f"关键点={keypoint_loss.item():.6f}, "
                    #   f"平滑={smoothness_loss_total.item():.6f}, "
                    #   f"加速度={acceleration_loss.item():.6f}, "
                    #   f"脚滑={foot_slip_loss.item():.6f}, "
                    #   f"脚旋={foot_rotation_loss.item():.6f}, "
                    #   f"根位={root_trans_loss.item():.6f}, "
                    #   f"根旋={root_rot_loss.item():.6f}, "
                    #   f"接触={contact_foot_loss.item():.6f}, "
                    #   f"脚高={foot_height_loss.item():.6f}, "
                      f"学习率={scheduler.get_last_lr()[0]:.6f}")

        print("\n优化完成!")

        # 6. 应用平滑滤波（可选）
        if self.args.apply_filter:
            print("应用高斯平滑滤波...")
            try:
                original_shape = dof_pos_new.shape
                dof_reshaped = dof_pos_new.squeeze(-1)
                dof_transposed = dof_reshaped.transpose(1, 2)

                dof_filtered = gaussian_filter_1d_batch(
                    dof_transposed,
                kernel_size=self.kernel_size,
                sigma=self.sigma
            )

                dof_pos_new = dof_filtered.transpose(1, 2).unsqueeze(-1)
                print(f"滤波完成: {original_shape} -> {dof_pos_new.shape}")

            except Exception as e:
                print(f"滤波失败: {e}")
                print("跳过平滑滤波步骤，使用原始优化结果")

        # 7. 计算最终FK结果（避免重复计算）
        print("计算最终FK结果...")
        # final_pose_aa = self.calcAA(dof_pos_new, root_rot_new)
        final_pose_aa = self.calcAA(dof_pos_new, root_rot_new)
        final_fk_return = self.Humanoid_fk.fk_batch(
            final_pose_aa,
            root_pos_new[None,],
            return_full=True
        )
        final_joint_positions = self.calcJointPosition(final_fk_return)

        # light_visualization(final_joint_positions[:, :, self.current_robot_joint_pick_idx].detach().numpy()[0])

        if hasattr(self.Humanoid_fk, 'joints_range') and self.Humanoid_fk.joints_range is not None:
                dof_pos_new.data.clamp_(
                    self.Humanoid_fk.joints_range[:, 0, None],
                    self.Humanoid_fk.joints_range[:, 1, None]
                )

        # 8. 保存结果
        output_path = self.save_results(dof_pos_new, root_pos_new, root_rot_new, loss_history,
                                      final_joint_positions)

        # 9. 播放优化后的动作
        if not self.args.no_vis:
            self.play_optimized_motion(dof_pos_new, root_pos_new, root_rot_new, bvh_target_positions,  # dof_pos_new
                                     final_joint_positions)

        print("="*50)
        print("BVH动作重定向完成!")
        print(f"结果保存在: {output_path}")
        print("="*50)

        return output_path

    def save_results(self, dof_pos, root_pos, root_rot, loss_history, final_joint_positions):
        """保存重定向结果为pkl文件 - 参考grad_rotation_fit_ik.py的格式"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"data/calc_bvh/{self.skeleton_params['metadata']['task_name']}"
        os.makedirs(output_dir, exist_ok=True)

        # 按照grad_rotation_fit_ik.py的格式准备数据
        bvh_key = os.path.splitext(os.path.basename(self.bvh_file))[0]

        # 使用最终的pose_aa用于保存
        final_pose_aa = self.calcAA(dof_pos, root_rot)

        data_dump = {
            bvh_key: {
                "root_trans_offset": root_pos.detach().cpu().numpy(),
                "pose_aa": final_pose_aa.squeeze(0).detach().cpu().numpy(),
                "dof_pos": dof_pos.squeeze(0).detach().cpu().numpy(),
                "root_rot": sRot.from_rotvec(root_rot.detach().cpu().numpy()).as_quat(),
                "global_translation": final_joint_positions.squeeze(0).detach().cpu().numpy(),
                "fps": float(self.fps),
                # BVH特有的元数据
                # "metadata": {
                # 'timestamp': timestamp,
                #     'source_bvh': self.args.optimized_bvh,
                #     'skeleton_params': self.skeleton_params,
                # 'task_name': self.skeleton_params['metadata']['task_name'],
                #     'optimization_version': 'BVH_Retarget_v2.0',
                # 'iterations': self.iterations,
                # 'final_loss': loss_history[-1]['total'] if loss_history else None,
                # 'parameters': {
                #     'learning_rate': self.learning_rate,
                #     'keypoint_loss_weight': self.keypoint_loss_weight,
                #     'smoothness_loss_weight': self.smoothness_loss_weight,
                #     'foot_height_loss_weight': self.foot_height_loss_weight,
                #     'joint_limit_loss_weight': 0.1
                #     }
                # }
            }
        }

        # 保存为pkl文件
        output_path = os.path.join(output_dir, f"bvh_retarget_result_{timestamp}.pkl")
        joblib.dump(data_dump, output_path)

        print(f"重定向结果已保存到: {output_path}")

        return output_path

    def play_optimized_motion(self, dof_pos, root_pos, root_rot, bvh_target_positions, final_joint_positions):
        """使用新的BVH播放器播放优化后的动作"""
        print("开始播放优化后的动作...")

        # 🔧 数据验证
        print(f"📊 数据验证:")
        print(f"  - dof_pos形状: {dof_pos.shape}")
        print(f"  - root_pos形状: {root_pos.shape}")
        print(f"  - root_rot形状: {root_rot.shape}")
        print(f"  - bvh_target_positions形状: {bvh_target_positions.shape}")
        print(f"  - final_joint_positions形状: {final_joint_positions.shape}")

        # 检查数据是否为空
        if bvh_target_positions.numel() == 0:
            print("❌ 错误: bvh_target_positions为空")
            return

        if final_joint_positions.numel() == 0:
            print("❌ 错误: final_joint_positions为空")
            return

        # 使用预计算的FK结果
        with torch.no_grad():
            # 重新计算FK以获得最终的关节位置 - 使用备份脚本的方式
            pose_aa_final = self.calcAA(dof_pos, root_rot)
            fk_return_final = self.Humanoid_fk.fk_batch(
                pose_aa_final,
                root_pos[None,],
                return_full=True
            )

            # 获取关节位置
            robot_joint_positions = self.calcJointPosition(fk_return_final)

            # 计算关键点位置 - 使用有效的机器人关节索引
            if hasattr(self, 'current_robot_joint_pick_idx'):
                robot_joint_indices = self.current_robot_joint_pick_idx
            else:
                robot_joint_indices = self.robot_joint_pick_idx

            # 🔧 验证关节索引
            if not robot_joint_indices or len(robot_joint_indices) == 0:
                print("❌ 错误: robot_joint_indices为空")
                return

            print(f"播放器关节索引验证:")
            print(f"  - 使用的机器人关节索引: {robot_joint_indices}")
            print(f"  - 机器人关节位置形状: {robot_joint_positions.shape}")

            # 🔧 验证关节索引是否在有效范围内
            max_joint_idx = max(robot_joint_indices)
            if max_joint_idx >= robot_joint_positions.shape[2]:
                print(f"❌ 错误: 关节索引超出范围，最大索引 {max_joint_idx} >= 总关节数 {robot_joint_positions.shape[2]}")
                # 过滤掉超出范围的索引
                valid_indices = [idx for idx in robot_joint_indices if idx < robot_joint_positions.shape[2]]
                if not valid_indices:
                    print("❌ 错误: 没有有效的关节索引")
                    return
                print(f"🔧 使用有效的关节索引: {valid_indices}")
                robot_joint_indices = valid_indices

            # 提取关键点位置
            robot_key_pos = robot_joint_positions[:, :, robot_joint_indices]

            print(f"  - 提取的关键点形状: {robot_key_pos.shape}")
            print(f"  - BVH目标位置形状: {bvh_target_positions.shape}")

            # 🔧 确保维度匹配
            if robot_key_pos.shape[2] != bvh_target_positions.shape[1]:
                print(f"⚠️ 警告: 关键点数量不匹配: robot={robot_key_pos.shape[2]}, bvh={bvh_target_positions.shape[1]}")
                # 取最小值进行匹配
                min_joints = min(robot_key_pos.shape[2], bvh_target_positions.shape[1])
                robot_key_pos = robot_key_pos[:, :, :min_joints]
                bvh_target_positions = bvh_target_positions[:, :min_joints, :]
                print(f"🔧 调整后: robot_key_pos={robot_key_pos.shape}, bvh_target_positions={bvh_target_positions.shape}")

            # 🔧 确保帧数匹配
            if robot_key_pos.shape[1] != bvh_target_positions.shape[0]:
                print(f"⚠️ 警告: 帧数不匹配: robot={robot_key_pos.shape[1]}, bvh={bvh_target_positions.shape[0]}")
                min_frames = min(robot_key_pos.shape[1], bvh_target_positions.shape[0])
                robot_key_pos = robot_key_pos[:, :min_frames, :]
                bvh_target_positions = bvh_target_positions[:min_frames, :, :]
                print(f"🔧 调整后: robot_key_pos={robot_key_pos.shape}, bvh_target_positions={bvh_target_positions.shape}")

            # 计算损失
            try:
                final_frame_losses = torch.norm(robot_key_pos - bvh_target_positions, p=2, dim=-1).mean(dim=-1).squeeze().cpu().numpy()
                print(f"  - 损失形状: {final_frame_losses.shape}")
            except Exception as e:
                print(f"❌ 计算损失失败: {e}")
                # 创建默认损失数组
                final_frame_losses = np.zeros(bvh_target_positions.shape[0])

        # 使用预加载的接触序列，确保帧数与数据一致
        expected_frames = bvh_target_positions.shape[0]
        if hasattr(self, 'contact_sequence') and self.contact_sequence is not None:
            contact_sequence = self.contact_sequence
            # 以数据帧数为准，调整接触序列
            if contact_sequence.shape[0] > expected_frames:
                # 接触序列过长，截断
                contact_sequence = contact_sequence[:expected_frames]
            elif contact_sequence.shape[0] < expected_frames:
                # 接触序列过短，补全（重复最后一帧）
                last_frame = contact_sequence[-1:] if len(contact_sequence) > 0 else np.array([[False, False]])
                padding_frames = expected_frames - contact_sequence.shape[0]
                padding = np.repeat(last_frame, padding_frames, axis=0)
                contact_sequence = np.concatenate([contact_sequence, padding], axis=0)
        else:
            # 创建默认接触序列
            contact_sequence = np.zeros((expected_frames, 2), dtype=bool)

        # 计算BVH骨架连接关系
        try:
            self._calculate_bvh_skeleton_connections()
        except Exception as e:
            print(f"⚠️ BVH骨架连接计算失败: {e}")
            self.skeleton_params['bvh_skeleton_connections'] = {}

        # 向skeleton_params添加正确的机器人关节索引信息
        self.skeleton_params['robot_joint_indices'] = robot_joint_indices

        # 🔧 修复：确保MuJoCo可视化器能访问有效的关节索引
        if hasattr(self.mujoco_viewer, 'valid_robot_joint_indices'):
            self.mujoco_viewer.valid_robot_joint_indices = self.valid_robot_joint_indices
        else:
            # 为MuJoCo可视化器添加有效关节索引属性
            setattr(self.mujoco_viewer, 'valid_robot_joint_indices', self.valid_robot_joint_indices)

        if hasattr(self.mujoco_viewer, 'valid_bvh_joint_indices'):
            self.mujoco_viewer.valid_bvh_joint_indices = self.valid_bvh_joint_indices
        else:
            # 为MuJoCo可视化器添加有效BVH关节索引属性
            setattr(self.mujoco_viewer, 'valid_bvh_joint_indices', self.valid_bvh_joint_indices)

        # 🔧 修复：确保MuJoCo可视化器能访问BVH运动数据（用于获取关节名称）
        if hasattr(self.mujoco_viewer, 'bvh_motion'):
            self.mujoco_viewer.bvh_motion = self.bvh_motion
        else:
            setattr(self.mujoco_viewer, 'bvh_motion', self.bvh_motion)

        print(f"[Debug] 已向MuJoCo可视化器传递关节索引:")
        print(f"  - 有效机器人关节索引: {self.valid_robot_joint_indices}")
        print(f"  - 有效BVH关节索引: {self.valid_bvh_joint_indices}")

        # 使用MujocoViwer播放动作
        try:
            # 准备数据以匹配MujocoViwer.visRefPoint的参数格式
            # bvh_joints_scaled: 使用BVH目标位置作为参考关节位置
            bvh_joints_scaled = bvh_target_positions.unsqueeze(0)  # [1, num_frames, num_joints, 3]

            # T1_joint_dump: 机器人原始FK结果（所有关节）
            T1_joint_dump = final_joint_positions  # [1, num_frames, num_joints, 3]

            # T1_joint: 机器人修正后的FK结果（关键关节）
            T1_joint = robot_key_pos  # [1, num_frames, num_key_joints, 3]

            # bvh_key_joints: 关键关节位置（使用BVH目标位置）
            bvh_key_joints = bvh_target_positions.unsqueeze(0)  # [1, num_frames, num_joints, 3]

            # 获取BVH骨架连接关系
            bvh_skeleton_connections = self.skeleton_params.get('bvh_skeleton_connections', {})

            print(f"📊 传递给MujocoViwer的数据:")
            print(f"  - bvh_joints_scaled形状: {bvh_joints_scaled.shape}")
            print(f"  - T1_joint_dump形状: {T1_joint_dump.shape}")
            print(f"  - T1_joint形状: {T1_joint.shape}")
            print(f"  - bvh_key_joints形状: {bvh_key_joints.shape}")
            print(f"  - contact_sequence形状: {contact_sequence.shape}")
            print(f"  - bvh_skeleton_connections数量: {len(bvh_skeleton_connections)}")

            # 🔧 关键修复：确保所有数据的帧数完全一致
            target_frames = bvh_target_positions.shape[0]
            print(f"🔧 数据帧数一致性检查:")
            print(f"  - 目标帧数: {target_frames}")
            
            # 检查并调整所有数据到相同帧数
            data_sources = {
                'final_joint_positions': final_joint_positions.shape[1],
                'robot_key_pos': robot_key_pos.shape[1], 
                'contact_sequence': contact_sequence.shape[0],
                'root_pos': root_pos.shape[0],
                'dof_pos': dof_pos.shape[1]
            }
            
            inconsistent_data = []
            for name, frames in data_sources.items():
                if frames != target_frames:
                    inconsistent_data.append(f"{name}({frames})")
                    
            if inconsistent_data:
                print(f"  ❌ 检测到帧数不一致: {', '.join(inconsistent_data)}")
                print(f"  🔧 将所有数据调整到{target_frames}帧")
                
                # 调整数据到统一帧数
                min_frames = min(final_joint_positions.shape[1], robot_key_pos.shape[1], 
                               contact_sequence.shape[0], root_pos.shape[0], target_frames)
                
                # 截断所有数据到最小帧数
                bvh_joints_scaled = bvh_target_positions[:min_frames].unsqueeze(0)
                T1_joint_dump = final_joint_positions[:, :min_frames, :, :]
                T1_joint = robot_key_pos[:, :min_frames, :, :]
                bvh_key_joints = bvh_target_positions[:min_frames].unsqueeze(0)
                contact_sequence = contact_sequence[:min_frames]
                root_pos = root_pos[:min_frames]
                dof_pos = dof_pos[:, :min_frames, :, :]
                root_rot = root_rot[:min_frames]
                final_frame_losses = final_frame_losses[:min_frames]
                
                print(f"  ✅ 所有数据已调整到{min_frames}帧")
            else:
                print(f"  ✅ 所有数据帧数一致: {target_frames}帧")

            # 调用MujocoViwer的visRefPoint方法
            self.mujoco_viewer.visRefPoint(
                bvh_joints_scaled=bvh_joints_scaled,
                T1_joint_dump=T1_joint_dump,
                T1_joint=T1_joint,
                bvh_key_joints=bvh_key_joints,
                contact_sequence=contact_sequence,
                root_trans_offset=root_pos,
                dof_pos_new=dof_pos,
                root_rot_new=root_rot,
                final_frame_losses=final_frame_losses,
                left_foot_samples=None,
                right_foot_samples=None,
                bvh_skeleton_connections=bvh_skeleton_connections
            )

        except Exception as e:
            print(f"❌ MuJoCo播放错误: {e}")
            import traceback
            traceback.print_exc()

    def _calculate_bvh_skeleton_connections(self):
        """计算BVH骨架连接关系 - 基于选中的关键关节计算正确的连接关系"""
        try:
            # 🔧 修复：基于BVH文件的实际关节父子关系计算连接
            if not hasattr(self, 'bvh_motion') or not hasattr(self.bvh_motion, 'joint_parent'):
                print("⚠️ BVH运动数据或关节父子关系不可用")
                self.skeleton_params['bvh_skeleton_connections'] = {}
                return

            # 获取BVH的全局关节父子关系
            joint_parents = self.bvh_motion.joint_parent  # [num_joints] 数组，包含每个关节的父关节索引
            joint_names = self.bvh_motion.joint_name      # [num_joints] 数组，包含关节名称

            print(f"[Debug] BVH关节总数: {len(joint_names)}")
            print(f"[Debug] 选中的BVH关节索引: {self.valid_bvh_joint_indices}")
            print(f"[Debug] 选中的BVH关节名称: {[joint_names[i] for i in self.valid_bvh_joint_indices]}")

            # 基于选中的关节计算连接关系
            connections = {}
            for i, child_global_idx in enumerate(self.valid_bvh_joint_indices):
                # 获取该关节在全局BVH中的父关节索引
                parent_global_idx = joint_parents[child_global_idx]

                # 检查父关节是否也在选中的关节列表中
                if parent_global_idx >= 0 and parent_global_idx in self.valid_bvh_joint_indices:
                    # 找到父关节在选中关节列表中的局部索引
                    parent_local_idx = self.valid_bvh_joint_indices.index(parent_global_idx)
                    connections[i] = parent_local_idx  # child_local_idx -> parent_local_idx

                    child_name = joint_names[child_global_idx]
                    parent_name = joint_names[parent_global_idx]
                    print(f"[Debug] 连接: [{i}]{child_name} -> [{parent_local_idx}]{parent_name}")

            # 保存连接关系
            self.skeleton_params['bvh_skeleton_connections'] = connections
            print(f"✅ BVH骨架连接关系构建完成: {len(connections)} 个连接")
            print(f"[Debug] 最终连接关系: {connections}")

        except Exception as e:
            print(f"❌ 构建BVH骨架连接失败: {e}")
            import traceback
            traceback.print_exc()
            self.skeleton_params['bvh_skeleton_connections'] = {}


    def run_retargeting(self):
        """运行完整的重定向流程"""
        return self.retargetBVHData()

    
def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="BVH动作重定向 - 使用bone_optimizer_gui.py的输出进行动作重定向",
        epilog="""
            使用示例:
            python bvh_motion_retarget.py --optimized-bvh data/calc_bvh/unitreeG1/walk_forward_optimized.bvh --iterations 1000

            输入要求:
            - 优化后的BVH文件：由bone_optimizer_gui.py生成的BVH文件，包含已经缩放到机器人尺寸的骨骼长度
            - 配置文件：脚本会自动在BVH文件同目录下查找对应的JSON配置文件
            
            工作流程:
            1. 运行bone_optimizer_gui.py优化骨骼结构
            2. 使用本脚本对优化后的BVH文件进行动作重定向
                    """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    # 必需参数
    parser.add_argument("--bvh", type=str, default="data/calc_bvh/unitreeG1/walking_forward_optimized.bvh",
                       help="优化后的BVH文件路径 (由bone_optimizer_gui.py生成)")
    parser.add_argument("--config", type=str, default="data/calc_bvh/unitreeG1/walking_forward.json",
                       help="优化后的BVH文件路径 (由bone_optimizer_gui.py生成)")

    # 优化参数
    parser.add_argument("--iterations", type=int, default=500,
                       help="优化迭代次数 (默认: 500)")
    parser.add_argument("--learning-rate", type=float, default=0.01,
                       help="学习率 (默认: 0.01)")

    # 平滑滤波
    parser.add_argument("--apply-filter", action="store_true", default=True,
                       help="应用高斯平滑滤波 (默认: True)")
    parser.add_argument("--kernel-size", type=int, default=7,
                       help="高斯核大小 (默认: 7)")
    parser.add_argument("--sigma", type=float, default=2.0,
                       help="高斯核标准差 (默认: 2.0)")

    # 其他选项
    parser.add_argument("--optimize-root", action="store_true", default=True,
                       help="优化根节点位置和旋转 (默认: False)")
    parser.add_argument("--contact-file", type=str, default="data/contact_seq/walk_forward_bvh.json",
                        help="接触序列JSON文件路径，如 data/contact_seq/SFU_0005_jogging_contact.json")
    parser.add_argument("--device", type=str, default="cuda", choices=["cpu", "cuda"],
                        help="计算设备 (默认: cpu)")
    parser.add_argument("--no-vis", action="store_true", default=False,
                       help="不显示可视化")
    parser.add_argument("--manual-scale", type=float, default=None,
                       help="手动设置缩放因子，覆盖配置文件中的值 (用于调试骨架缩放问题)")

    args = parser.parse_args()

    # 验证输入文件
    if not os.path.exists(args.bvh):
        print(f"❌ 错误: 优化后的BVH文件不存在: {args.bvh}")
        print("\n💡 使用提示:")
        print("1. 请先运行 bone_optimizer_gui.py 进行骨骼拟合和优化")
        print("2. 确保输入的是优化后的BVH文件路径（通常以_optimized.bvh结尾）")
        print("3. 确保在BVH文件同目录下存在相应的配置JSON文件")
        sys.exit(1)

    print("🚀 BVH动作重定向")
    print("=" * 50)
    print(f"📁 优化后的BVH文件: {args.bvh}")
    print(f"💻 计算设备: {args.device}")
    print(f"🔄 优化迭代次数: {args.iterations}")
    print("=" * 50)

    try:
        # 创建重定向器并运行
        retargeter = BVHMotionRetargeter(args)
        output_path = retargeter.run_retargeting()

        print("\n" + "=" * 50)
        print("✅ 重定向成功完成!")
        print(f"📁 输出文件: {output_path}")
        print("=" * 50)

    except FileNotFoundError as e:
        print(f"\n❌ 文件错误: {e}")
        print("\n💡 解决方案:")
        print("1. 检查BVH文件路径是否正确")
        print("2. 确保已运行bone_optimizer_gui.py生成配置文件")
        print("3. 检查配置文件是否在BVH文件同目录下")
        sys.exit(1)
    
    except Exception as e:
        print(f"\n❌ 重定向失败: {e}")
        import traceback
        print("\n🔍 详细错误信息:")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 