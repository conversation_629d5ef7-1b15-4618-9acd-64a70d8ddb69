# BVH骨骼优化与逆运动学重定向

## 概述

这个项目实现了BVH骨骼参数的优化，并提供了完整的逆运动学重定向流程，将人类动作数据重定向到机器人身上。

## 工作流程

### 1. 骨骼优化阶段

使用GUI工具优化BVH骨骼参数：

```bash
# 基本使用
python bone_optimizer_gui.py --bvh data/bvh/walk_forward.bvh --task unitreeG1_retarget

# 使用scale优化
python bone_optimizer_gui.py --bvh data/bvh/walk_forward.bvh --task unitreeG1_retarget --use-scale

# 启用自适应根节点高度调整
python bone_optimizer_gui.py --bvh data/bvh/walk_forward.bvh --task unitreeG1_retarget --adaptive-root-height
```

### 2. 优化结果

优化完成后会生成以下文件：

- **优化结果JSON文件**: `data/calc_bvh/unitreeG1/bvh_optimization_result.json`
- **优化后的BVH文件**: `bvh_optimization_result_optimized.bvh`

### 3. 逆运动学重定向

使用优化结果进行动作重定向：

```bash
python utils/bvh_retarget_ik.py --result data/calc_bvh/unitreeG1/bvh_optimization_result.json --output retargeted_motion.json
```

## 保存结果格式详解

### 优化结果JSON文件结构

```json
{
  "task_name": "unitreeG1_retarget",
  "timestamp": "2024-01-01T12:00:00",
  "description": "BVH骨骼优化结果，用于后续逆运动学重定向",
  
  // 优化后的骨骼参数
  "optimized_offsets": {
    "Hips": [0.0, 0.0, 0.0],
    "LeftKnee": [0.1, 0.0, 0.0],
    // ... 其他关节的优化偏移量
  },
  "scale": 1.0,
  "root_height_adjustment": [0.05],
  "root_trans": [0.0, 0.0],
  
  // 关节对应关系（关键信息）
  "joint_correspondence": {
    "base_link": "Hips",
    "leg_l4_link": "LeftKnee",
    // ... 机器人关节到BVH关节的映射
  },
  "robot_joint_names": ["base_link", "leg_l4_link", ...],
  "bvh_joint_names": ["Hips", "LeftKnee", ...],
  "robot_joint_pick_idx": [0, 1, ...],
  "bvh_joint_pick_idx": [0, 1, ...],
  
  // T-pose关节位置（用于重定向参考）
  "robot_t_pose_positions": {
    "all_joints": [[x, y, z], ...],
    "selected_joints": [[x, y, z], ...]
  },
  "bvh_t_pose_positions": {
    "all_joints": [[x, y, z], ...],
    "selected_joints": [[x, y, z], ...]
  },
  
  // BVH骨架结构信息
  "bvh_skeleton_info": {
    "Hips": {
      "parent": null,
      "original_offset": [0.0, 0.0, 0.0]
    },
    // ... 其他关节的父子关系
  },
  
  // 坐标系信息
  "coordinate_system": {
    "bvh_to_robot_transform": {
      "description": "BVH坐标系到机器人坐标系的转换",
      "x_robot": "z_bvh",
      "y_robot": "x_bvh", 
      "z_robot": "y_bvh"
    },
    "units": {
      "bvh_file": "centimeters",
      "optimization": "meters",
      "output": "meters"
    }
  },
  
  // 优化参数和配置
  "optimization_params": {
    "joint_loss_weight": 1.0,
    "symmetry_loss_weight": 0.1,
    "endpoint_loss_weight": 0.5,
    "learning_rate": 0.005,
    "optimize_scale": false,
    "adaptive_root_height": true,
    "root_height_weight": 0.1
  },
  
  // 机器人配置信息
  "robot_config": {
    "mjcf_file": "resources/robots/unitreeG1/mjcf/unitree_G1_retarget.xml",
    "joint_names": ["base_link", "leg_l4_link", ...],
    "dof_pos": [[0.0, 0.0, ...]]
  },
  
  // 原始BVH文件信息
  "original_bvh": {
    "file_path": "data/bvh/walk_forward.bvh",
    "frame_count": 120,
    "frame_time": 0.033333,
    "total_channels": 60
  }
}
```

### 重定向结果JSON文件结构

```json
{
  "joint_angles": [
    [0.0, 0.1, 0.2, ...],  // 第1帧的关节角度
    [0.1, 0.2, 0.3, ...],  // 第2帧的关节角度
    // ... 所有帧的关节角度
  ],
  "joint_names": ["base_link", "leg_l4_link", ...],
  "frame_time": 0.033333,
  "num_frames": 120,
  "description": "BVH重定向到机器人的关节角度序列"
}
```

## 关键特性

### 1. 自适应根节点高度调整

当骨骼长度发生变化时，自动调整根节点高度以保持脚部接触地面：

- **骨骼变长**: 根节点高度自动降低
- **骨骼变短**: 根节点高度自动升高
- **动态调整**: 在整个优化过程中持续调整

### 2. 坐标系转换

自动处理BVH和机器人坐标系之间的转换：

- **BVH坐标系**: z向前，y向上，x向左
- **机器人坐标系**: z向上，y向前，x向右
- **自动转换**: 在优化和重定向过程中自动应用

### 3. 关节对应关系

支持灵活的关节映射配置：

```python
bvh_joint_correspondence = {
    'base_link': 'Hips',
    'leg_l4_link': 'LeftKnee', 
    'leg_l6_link': 'LeftAnkle',
    'foot_l1_link': 'LeftFoot',
    'leg_r4_link': 'RightKnee',
    'leg_r6_link': 'RightAnkle',
    'foot_r1_link': 'RightFoot',
    # ... 更多关节映射
}
```

## 使用建议

### 1. 优化阶段

1. **选择合适的任务**: 根据目标机器人选择对应的任务配置
2. **调整优化参数**: 根据具体需求调整损失权重
3. **启用自适应根节点高度**: 建议启用以保持脚部接触地面
4. **使用scale优化**: 如果需要整体缩放，启用scale优化

### 2. 重定向阶段

1. **检查优化结果**: 确保优化结果文件完整且正确
2. **验证关节对应关系**: 确保所有关键关节都有正确的映射
3. **调整IK参数**: 根据机器人特性调整逆运动学求解参数
4. **验证重定向结果**: 检查生成的关节角度序列是否合理

### 3. 常见问题

**Q: 脚部脱离地面怎么办？**
A: 启用自适应根节点高度调整功能

**Q: 动作看起来不自然怎么办？**
A: 调整对称性损失权重和末端位置损失权重

**Q: 优化收敛太慢怎么办？**
A: 调整学习率和迭代次数

**Q: 重定向结果不准确怎么办？**
A: 检查关节对应关系是否正确，调整IK求解参数

## 扩展开发

### 1. 添加新的机器人支持

1. 在`resources/robots/`下添加机器人模型文件
2. 在`humanoid/retarget_motion/`下创建对应的配置文件
3. 更新关节对应关系映射

### 2. 改进IK求解算法

1. 实现更高效的数值优化方法
2. 添加关节限制和约束
3. 支持多目标优化

### 3. 添加更多损失函数

1. 关节速度限制
2. 关节加速度限制
3. 能量消耗优化
4. 稳定性约束

## 技术细节

### 坐标系转换矩阵

```python
# BVH到机器人坐标系的转换
x_robot = z_bvh
y_robot = x_bvh  
z_robot = y_bvh

# 机器人到BVH坐标系的转换
x_bvh = y_robot
y_bvh = z_robot
z_bvh = x_robot
```

### 单位转换

- **BVH文件**: 厘米 (cm)
- **优化过程**: 米 (m) 
- **输出结果**: 米 (m)

### 损失函数组成

```python
total_loss = (
    joint_loss_weight * joint_loss +
    symmetry_loss_weight * symmetry_loss +
    root_height_weight * root_height_loss +
    endpoint_loss_weight * endpoint_loss
)
```

这个完整的流程确保了从BVH动作数据到机器人关节角度的准确重定向。 