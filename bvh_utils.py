from typing import List, Dict, Tuple, Optional

import numpy as np
import torch
import copy
from scipy.spatial.transform import Rotation as R
from scipy.spatial.transform import Slerp
import re
from .bvh_parser import BVHParser


'''
In the comments, N uniformly represents the number of frames, and M represents the number of joints. 
"position, rotation" indicate local translation and rotation
"translation, orientation" indicate global translation and rotation.
'''


class BVHMotion():
    def __init__(self, bvh_file_path=None, order=None) -> None:
        self.count = 0
        self.joints, self.motion_data_list = (BVHParser(bvh_file_path)).parse()  # for debug
        self.bvh_file_path = bvh_file_path
        self.bvh_file_name = bvh_file_path.split('/')[-1].split('.')[0]
        self.euler = order  # 将在load_motion中动态确定

        # 一些 meta data
        self.original_meta_data = []

        self.joint_name = []
        self.end_sites = []
        self.joint_channel = []
        self.joint_parent = []

        # 一些local数据, 对应bvh里的channel, XYZposition和 XYZrotation
        #! 这里我们把没有XYZ position的joint的position设置为offset, 从而进行统一
        self.joint_position = None  # (N,M,3) 的ndarray, 局部平移
        self.joint_rotation = None  # (N,M,4)的ndarray, 用四元数表示的局部旋转
        self.joint_translation = None  # (N,M,3)的ndarray, 全局平移
        self.joint_orientation = None  # (N,M,4)的ndarray, 用四元数表示的全局旋转

        # 骨骼中点的变化比例
        self.marker_fluctuation_rate = None

        if bvh_file_path is not None:
            self.load_motion(bvh_file_path)  # 得到每个关节的位置和旋转

        # self.print_dims()

    def load_meta_data(self, bvh_path):
        with open(bvh_path, 'r') as f:
            channels = []
            joints = []
            joint_parents = []
            joint_offsets = []
            end_sites = []
            parent_stack = [None]
            original_meta_data = []
            rotation_order = None  # 用于存储检测到的旋转顺序
            
            for line in f:
                original_meta_data.append(line)

                if 'ROOT' in line or 'JOINT' in line:
                    joints.append(line.split()[-1])
                    joint_parents.append(parent_stack[-1])
                    channels.append('')
                    joint_offsets.append([0, 0, 0])

                elif 'End Site' in line:
                    end_sites.append(len(joints))
                    joints.append(parent_stack[-1] + '_end')
                    joint_parents.append(parent_stack[-1])
                    channels.append('')
                    joint_offsets.append([0, 0, 0])

                elif '{' in line:
                    parent_stack.append(joints[-1])

                elif '}' in line:
                    parent_stack.pop()

                elif 'OFFSET' in line:
                    joint_offsets[-1] = np.array([float(x)
                                                  for x in line.split()[-3:]]).reshape(1, 3)

                elif 'CHANNELS' in line:
                    trans_order = []
                    rot_order = []
                    tokens = line.split()
                    for token in tokens:
                        if 'position' in token:
                            trans_order.append(token[0])
                        elif 'rotation' in token:
                            rot_order.append(token[0])

                    channels[-1] = ''.join(trans_order) + ''.join(rot_order)
                    
                    # 动态检测欧拉角顺序（只在第一次遇到旋转通道时设置）
                    if rotation_order is None and len(rot_order) == 3:
                        # 将旋转顺序转换为小写（符合scipy的约定）
                        rotation_order = ''.join(rot_order).lower()
                        print(f"检测到BVH欧拉角顺序: {rotation_order.upper()}")

                elif 'Frame Time:' in line:
                    mocap_framerate = int(1 / float(line.split()[-1]))
                    break
        self.end_sites = end_sites

        joint_parents = [-1] + [joints.index(i) for i in joint_parents[1:]]
        channels = [len(i) for i in channels]
        
        return joints, joint_parents, channels, joint_offsets, original_meta_data, mocap_framerate, rotation_order

    def load_motion_data(self, bvh_path):
        with open(bvh_path, 'r') as f:
            lines = f.readlines()
            for i in range(len(lines)):
                if lines[i].startswith('Frame Time'):
                    break
            motion_data = []
            for line in lines[i+1:]:
                data = [float(x) for x in line.split()]
                if len(data) == 0:
                    break
                motion_data.append(np.array(data).reshape(1, -1))
            motion_data = np.concatenate(motion_data, axis=0)


        print('Mocap Framerate:', self.mocap_framerate)
        # print('motion_data.shape:', motion_data.shape)
        return motion_data

    def load_motion(self, bvh_file_path):
        '''
            读取bvh文件，初始化元数据和局部数据
        '''
        self.joint_name, self.joint_parent, self.joint_channel, joint_offset, self.original_meta_data, self.mocap_framerate, detected_euler = \
            self.load_meta_data(bvh_file_path)
        
        # 如果在构造函数中没有指定欧拉角顺序，使用检测到的
        if self.euler is None:
            if detected_euler:
                self.euler = detected_euler
                print(f"检测到BVH欧拉角顺序: {self.euler.upper()}")
            else:
                self.euler = "zyx"
                print(f"未检测到BVH欧拉角顺序，使用默认顺序: {self.euler.upper()}")

        motion_data = self.load_motion_data(bvh_file_path)

        # 把motion_data里的数据分配到joint_position和joint_rotation里
        self.joint_position = np.zeros(
            (motion_data.shape[0], len(self.joint_name), 3))
        # 改为存储欧拉角而不是四元数，保持原始数据
        self.joint_rotation = np.zeros(
            (motion_data.shape[0], len(self.joint_name), 3))

        cur_channel = 0
        for i in range(len(self.joint_name)):
            if self.joint_channel[i] == 0:
                self.joint_position[:, i, :] = joint_offset[i].reshape(1, 3)
                # 没有旋转通道，设为零欧拉角
                self.joint_rotation[:, i, :] = np.zeros((motion_data.shape[0], 3))
                continue
            elif self.joint_channel[i] == 3 and '_end' not in self.joint_name[i]:
                self.joint_position[:, i, :] = joint_offset[i].reshape(1, 3)
                rotation = motion_data[:, cur_channel:cur_channel+3]
            elif self.joint_channel[i] == 3 and '_end' in self.joint_name[i]:
                self.joint_position[:, i, :] = joint_offset[i].reshape(1, 3)
                rotation = np.zeros((motion_data.shape[0], 3))  # End sites没有旋转
                cur_channel -= 3
            elif self.joint_channel[i] == 6:
                self.joint_position[:, i, :] = motion_data[:,
                                                           cur_channel:cur_channel+3]
                rotation = motion_data[:, cur_channel+3:cur_channel+6]

            # 直接保存欧拉角（度），不转换为四元数
            self.joint_rotation[:, i, :] = rotation
            cur_channel += self.joint_channel[i]

        return

    def batch_forward_kinematics(self, joint_position=None, joint_rotation=None):
        self.count += 1
        print('+++++batch_forward_kinematics+++++\n', self.count)

        '''
        @brief: 批量计算全局坐标系下的关节位置和旋转，存储到 self.joint_translation 和 self.joint_orientation
        @param joint_position: (N,M,3)的ndarray, 局部平移
        @param joint_rotation: (N,M,3)的ndarray, 用欧拉角表示的局部旋转（度）
        @return joint_translation: (N,M,3)的ndarray, 全局平移
        @return joint_orientation: (N,M,3)的ndarray, 用欧拉角表示的全局旋转（度）
        '''
        if joint_position is None:
            joint_position = self.joint_position
        if joint_rotation is None:
            joint_rotation = self.joint_rotation

        joint_translation = np.zeros_like(joint_position)
        joint_orientation = np.zeros_like(joint_rotation)  # 现在也是欧拉角格式

        def create_transformation_matrix(euler_angles, offset, degrees=True, channels='XYZ'):
            """
            Create a 4x4 homogeneous transformation matrix.

            Parameters:
              euler_angles: tuple/list of 3 angles (Xrotation, Yrotation, Zrotation)
              offset: tuple/list of 3 offsets (X, Y, Z)
              degrees: whether angles are in degrees (default True)

            Returns:
              4x4 numpy array transformation matrix
            """
            if not channels:
                channels = 'XYZ'
            # Create rotation matrix from Euler angles (XYZ order assumed)
            # print(channels)
            rot = R.from_euler(channels, euler_angles, degrees=degrees)
            rot_matrix = rot.as_matrix()

            # Create 4x4 identity matrix
            transform = np.eye(4)

            # Set rotation part
            transform[:3, :3] = rot_matrix

            # Set translation part (offset)
            transform[:3, 3] = offset

            return transform

        # shape = joint_position.shape
        # # print(shape)  # (1377, 30, 3)
        # # for i in range(shape[0]):
        # #     print(joint_position[i].shape)
        # def create_identity_3d(batch_size=1377, num_joints=30, dim=3):
        #     """
        #     创建 (batch_size, num_joints, dim) 形状的单位阵
        #
        #     Args:
        #         batch_size (int): 批次大小，默认1377
        #         num_joints (int): 关节数量，默认30
        #         dim (int): 维度，默认3
        #
        #     Returns:
        #         numpy.ndarray: 形状为 (batch_size, num_joints, dim) 的数组
        #     """
        #     # 方法1: 使用zeros初始化
        #     result = np.zeros((batch_size, num_joints, dim))
        #
        #     # 设置对角线为1 (对于3D，对角线是 [1,1,1]
        #     result[:, :, 0] = 1  # 第一个维度设为1
        #     result[:, :, 1] = 1  # 第二个维度设为1
        #     result[:, :, 2] = 1  # 第三个维度设为1
        #
        #     return result
        #
        # def create_eye_4d(batch_size=1377, num_joints=30, matrix_size=4):
        #     """
        #     创建 (batch_size, num_joints, matrix_size, matrix_size) 形状的单位阵
        #
        #     Args:
        #         batch_size (int): 批次大小，默认1377
        #         num_joints (int): 关节数量，默认30
        #         matrix_size (int): 矩阵大小，默认4
        #
        #     Returns:
        #         numpy.ndarray: 形状为 (batch_size, num_joints, matrix_size, matrix_size) 的数组
        #     """
        #     # 方法1: 使用tile
        #     identity_4x4 = np.eye(matrix_size)
        #     result = np.tile(identity_4x4, (batch_size, num_joints, 1, 1))
        #
        #     return result
        #
        # # joint_transformation = create_eye_4d()
        # # print(joint_transformation.shape)
        # #
        # self.joint_translation = create_identity_3d(1377, 30, 3)
        # # 一个小hack是root joint的parent是-1, 对应最后一个关节
        # # 计算根节点时最后一个关节还未被计算，刚好是0偏移和单位朝向
        # for i in range(len(self.joint_name)):
        #     pi = self.joint_parent[i]
        #
        #     parent_transformation = joint_transformation[:, pi, :, :]
        #     print(parent_transformation.shape)
        #
        #     current_rotation = R.from_euler(self.euler, joint_rotation[:, i, :], degrees=True).as_matrix()
        #     current_position = self.joint_position[:, i, :]
        #     current_transformation = create_identity_3d(1377, 4, 4)
        #     print(current_transformation[:, :3, :3].shape)
        #     current_transformation[:, :3, :3] = current_rotation
        #     current_transformation[:, 3, :3] = current_position
        #
        #     current_transformation = np.matmul(parent_transformation, current_transformation)
        #
        #     joint_translation = current_transformation[:, 3, :3]


        # for i in range(len(self.joint_name)):
        #     pi = self.joint_parent[i]
        #
        #     # parent_T = create_identity_3d(1377, 4, 4)
        #
        #     # 将父节点的欧拉角转换为旋转矩阵进行计算
        #     parent_orientation = R.from_euler(self.euler, joint_orientation[:, pi, :], degrees=True)
        #     # parent_T[:, :3, :3] = parent_orientation.as_matrix()
        #     # parent_T[:, 3, :3] = joint_translation[:, pi, :]
        #
        #     joint_translation[:, i, :] = joint_translation[:, pi, :] + \
        #         parent_orientation.apply(joint_position[:, i, :])
        #
        #     # 合成旋转：父节点旋转 * 局部旋转
        #     # current_transformation = create_identity_3d(1377, 4, 4)
        #
        #     local_rotation = R.from_euler(self.euler, joint_rotation[:, i, :], degrees=True)
        #     # current_transformation[:, :3, :3] = local_rotation.as_matrix()
        #     # current_transformation[:, 3, :3] = joint_position[:, i, :]
        #
        #     # joint_translation[:, i, :] = np.matmul(parent_T, current_transformation)[:, 3, :3]
        #
        #     global_rotation = parent_orientation.as_matrix() @ local_rotation.as_matrix()
        #     joint_orientation[:, i, :] = R.from_matrix(global_rotation).as_euler(self.euler, degrees=True)

        all_joints_info: List[Dict] = self.joints
        joints_nums: int = len(all_joints_info)
        print('joints_nums:', joints_nums)

        for frame in range(joint_translation.shape[0]):

            sikpped_end_num: int = 0
            current_frame_positions = np.zeros((30, 3))
            name_and_positions: List[Tuple[str, List[List[float]]]] = []
            transformation_matrics: Dict[str, Dict] = {}  # type: ignore
            for single_joint in all_joints_info:
                # print(single_joint)
                transformation_matrics[single_joint['name']] = {
                    'parent': single_joint['parent_name'],
                    'offset': single_joint['offset'],
                    'T': np.eye(4),
                    'finished': False
                }
            current_frame_data = self.motion_data_list[frame]
            for value in transformation_matrics.values():
                value['finished'] = False

            all_finished: bool = False
            while not all_finished:
                for index, single_joint in enumerate(all_joints_info):
                    this_joint_name: str = single_joint['name']
                    if transformation_matrics[this_joint_name]['finished']:
                        continue
                    T_from_parent = np.eye(4)
                    offset: List[float] = transformation_matrics[this_joint_name]['offset']
                    parent_joint_name: Optional[str] = transformation_matrics[this_joint_name]['parent']
                    if parent_joint_name is None:
                        offset = current_frame_data[:3]
                    else:
                        if not transformation_matrics[parent_joint_name]['finished']:
                            continue
                        T_from_parent = transformation_matrics[parent_joint_name]['T']

                    if '_end' in this_joint_name:
                        euler_angles_from_this_joint = [0, 0, 0]
                        sikpped_end_num += 1
                    else:
                        euler_angles_from_this_joint = current_frame_data[(index - sikpped_end_num + 1) * 3:(index - sikpped_end_num + 2) * 3]

                    if parent_joint_name is None:
                        T_from_this_joint = create_transformation_matrix(euler_angles=euler_angles_from_this_joint,
                                                                         offset=offset,
                                                                         channels=single_joint['channels'][3:])
                    else:
                        # print(euler_angles_from_this_joint)
                        T_from_this_joint = create_transformation_matrix(euler_angles=euler_angles_from_this_joint,
                                                                         offset=offset,
                                                                         channels=single_joint['channels'])

                    T = np.matmul(T_from_parent, T_from_this_joint)
                    transformation_matrics[this_joint_name]['T'] = T

                    this_joint_position = T[:3, 3].tolist()
                    parent_joint_position = T_from_parent[:3, 3].tolist()
                    if parent_joint_name is None:
                        name_and_positions.append((this_joint_name, [this_joint_position, this_joint_position]))
                    else:
                        name_and_positions.append((this_joint_name, [this_joint_position, parent_joint_position]))

                    transformation_matrics[this_joint_name]['finished'] = True
                    current_frame_positions[index] = this_joint_position

                for value in transformation_matrics.values():
                    if not value['finished']:
                        all_finished = False
                        break
                    else:
                        all_finished = True

            joint_translation[frame] = current_frame_positions
        #
        # # raise

        self.joint_translation = joint_translation
        # print(self.joint_translation.shape)
        self.joint_orientation = joint_orientation
        return joint_translation, joint_orientation

    def get_T_pose(self):
        translation = np.zeros((len(self.joint_name), 3))
        for i in range(len(self.joint_name)):
            pi = self.joint_parent[i]
            translation[i, :] = translation[pi, :] + \
                self.joint_position[0, i, :]
        return translation

    def adjust_joint_name(self, target_joint_name):
        '''
        调整关节顺序为target_joint_name
        '''
        idx = [self.joint_name.index(joint_name)
               for joint_name in target_joint_name]
        idx_inv = [target_joint_name.index(joint_name)
                   for joint_name in self.joint_name]
        self.joint_name = [self.joint_name[i] for i in idx]
        self.joint_parent = [idx_inv[self.joint_parent[i]] for i in idx]
        self.joint_parent[0] = -1
        self.joint_channel = [self.joint_channel[i] for i in idx]
        self.joint_position = self.joint_position[:, idx, :]
        self.joint_rotation = self.joint_rotation[:, idx, :]
        pass

    def raw_copy(self):
        '''
        返回一个拷贝
        '''
        return copy.deepcopy(self)

    def sub_sequence(self, start, end, step=1):
        '''
        返回一个子序列
        start: 开始帧
        end: 结束帧
        '''
        res = self.raw_copy()
        res.joint_position = res.joint_position[start:end:step, :, :]
        res.joint_rotation = res.joint_rotation[start:end:step, :, :]
        return res

    def append(self, other):
        '''
        在末尾添加另一个动作
        '''
        other = other.raw_copy()
        other.adjust_joint_name(self.joint_name)
        self.joint_position = np.concatenate(
            (self.joint_position, other.joint_position), axis=0)
        self.joint_rotation = np.concatenate(
            (self.joint_rotation, other.joint_rotation), axis=0)
        pass

    def print_dims(self):
        print('joint num:', len(self.joint_name))
        print('end joint:', self.end_sites)
        print('motion length:', self.motion_length)
        print(f'joint_position.shape:{self.joint_position.shape}')
        print(f'joint_rotation.shape:{self.joint_rotation.shape}, note: you may find the end_sites still have rotations, but actually their rotations are 0 and won\'t be outputed if you call \'put_back_bvh()\', it\'s just to occupy a position')
        print("")
        pass

    def put_back_bvh(self, suffix='put_back'):
        '''
        @brief: 把joint_position和joint_rotation写回bvh文件
        @note: 数据来源是 self.joint_position 和 self.joint_rotation ，都是局部坐标系
        @param suffix: 文件名后缀
        @return: None
        '''
        put_back_file_path = '.'.join(self.bvh_file_path.split(
            '.')[:-1]) + '-' + suffix + '.bvh'
        with open(put_back_file_path, 'w') as f:
            for line in self.original_meta_data:
                if 'Frames' in line:
                    f.write('Frames: %d\n' % self.motion_length)
                else:
                    f.write(line)

            for i in range(self.motion_length):
                f.write(
                    ' '.join(['%6f' % x for x in self.joint_position[i, 0, :].flatten()]))
                f.write(' ')
                # 过滤掉end sites的旋转数据
                joint_rotation_without_end_site = np.array([elem for idx, elem in enumerate(
                    self.joint_rotation[i]) if idx not in self.end_sites])
                # 现在joint_rotation已经是欧拉角格式，直接写入
                f.write(
                    ' '.join([' '.join(['%6f' % y for y in x]) for x in joint_rotation_without_end_site]))
                f.write('\n')
        pass

    def get_mid_pos(self, fluctuation_rate=0.):
        '''
        @brief: 计算全局坐标系下的骨骼中点位置
        @note: 前提是已经计算了全局坐标系下的关节位置和旋转
        @param fluctuation_rate: 随机扰动的比例
        @return joint_markers: (N,M,3)的ndarray, 全局骨骼中点
        '''
        joint_markers = np.zeros_like(self.joint_position)
        self.marker_fluctuation_rate = np.random.uniform(-fluctuation_rate, fluctuation_rate, (self.joint_num))

        for i in range(len(self.joint_name)):
            if i == 0:
                continue
            pi = self.joint_parent[i]
            # 现在joint_orientation是欧拉角格式
            parent_orientation = R.from_euler(self.euler, self.joint_orientation[:, pi, :], degrees=True)
            joint_markers[:, i, :] = self.joint_translation[:, pi, :] + \
                parent_orientation.apply(self.joint_position[:, i, :])/2 * \
                (1+self.marker_fluctuation_rate[i])

        print("marker_fluctuation_rate:\n", self.marker_fluctuation_rate)
        print("")
        return joint_markers

    @property
    def motion_length(self):
        return self.joint_position.shape[0]

    @property
    def joint_num(self):
        return len(self.joint_name)
    

class Anim(object):
    """
    A very basic animation object
    """
    def __init__(self, quats, pos, offsets, parents, bones):
        """
        :param quats: local quaternions tensor
        :param pos: local positions tensor
        :param offsets: local joint offsets
        :param parents: bone hierarchy
        :param bones: bone names
        """
        self.quats = quats
        self.pos = pos
        self.offsets = offsets
        self.parents = parents
        self.bones = bones


channelmap = {
    'Xrotation': 'x',
    'Yrotation': 'y',
    'Zrotation': 'z'
}

channelmap_inv = {
    'x': 'Xrotation',
    'y': 'Yrotation',
    'z': 'Zrotation',
}

ordermap = {
    'x': 0,
    'y': 1,
    'z': 2,
}


def read_bvh(filename, start=None, end=None, order=None):
    """
    Reads a BVH file and extracts animation information.

    :param filename: BVh filename
    :param start: start frame
    :param end: end frame
    :param order: order of euler rotations
    :return: A simple Anim object conatining the extracted information.
    """

    f = open(filename, "r")

    i = 0
    active = -1
    end_site = False

    names = []
    orients = np.array([]).reshape((0, 4))
    offsets = np.array([]).reshape((0, 3))
    parents = np.array([], dtype=int)

    # Parse the  file, line by line
    for line in f:

        if "HIERARCHY" in line: continue
        if "MOTION" in line: continue

        rmatch = re.match(r"ROOT (\w+)", line)
        if rmatch:
            names.append(rmatch.group(1))
            offsets = np.append(offsets, np.array([[0, 0, 0]]), axis=0)
            orients = np.append(orients, np.array([[1, 0, 0, 0]]), axis=0)
            parents = np.append(parents, active)
            active = (len(parents) - 1)
            continue

        if "{" in line: continue

        if "}" in line:
            if end_site:
                end_site = False
            else:
                active = parents[active]
            continue

        offmatch = re.match(r"\s*OFFSET\s+([\-\d\.e]+)\s+([\-\d\.e]+)\s+([\-\d\.e]+)", line)
        if offmatch:
            if not end_site:
                offsets[active] = np.array([list(map(float, offmatch.groups()))])
            continue

        chanmatch = re.match(r"\s*CHANNELS\s+(\d+)", line)
        if chanmatch:
            channels = int(chanmatch.group(1))
            if order is None:
                channelis = 0 if channels == 3 else 3
                channelie = 3 if channels == 3 else 6
                parts = line.split()[2 + channelis:2 + channelie]
                if any([p not in channelmap for p in parts]):
                    continue
                order = "".join([channelmap[p] for p in parts])
            continue

        jmatch = re.match("\s*JOINT\s+(\w+)", line)
        if jmatch:
            names.append(jmatch.group(1))
            offsets = np.append(offsets, np.array([[0, 0, 0]]), axis=0)
            orients = np.append(orients, np.array([[1, 0, 0, 0]]), axis=0)
            parents = np.append(parents, active)
            active = (len(parents) - 1)
            continue

        if "End Site" in line:
            end_site = True
            continue

        fmatch = re.match("\s*Frames:\s+(\d+)", line)
        if fmatch:
            if start and end:
                fnum = (end - start) - 1
            else:
                fnum = int(fmatch.group(1))
            positions = offsets[np.newaxis].repeat(fnum, axis=0)
            rotations = np.zeros((fnum, len(orients), 3))
            continue

        fmatch = re.match("\s*Frame Time:\s+([\d\.]+)", line)
        if fmatch:
            frametime = float(fmatch.group(1))
            continue

        if (start and end) and (i < start or i >= end - 1):
            i += 1
            continue

        dmatch = line.strip().split(' ')
        if dmatch:
            data_block = np.array(list(map(float, dmatch)))
            N = len(parents)
            fi = i - start if start else i
            if channels == 3:
                positions[fi, 0:1] = data_block[0:3]
                rotations[fi, :] = data_block[3:].reshape(N, 3)
            elif channels == 6:
                data_block = data_block.reshape(N, 6)
                positions[fi, :] = data_block[:, 0:3]
                rotations[fi, :] = data_block[:, 3:6]
            elif channels == 9:
                positions[fi, 0] = data_block[0:3]
                data_block = data_block[3:].reshape(N - 1, 9)
                rotations[fi, 1:] = data_block[:, 3:6]
                positions[fi, 1:] += data_block[:, 0:3] * data_block[:, 6:9]
            else:
                raise Exception("Too many channels! %i" % channels)

            i += 1

    f.close()

    rotations = euler_to_quat(np.radians(rotations), order=order)
    rotations = remove_quat_discontinuities(rotations)

    return Anim(rotations, positions, offsets, parents, names)


def length(x, axis=-1, keepdims=True):
    """
    Computes vector norm along a tensor axis(axes)

    :param x: tensor
    :param axis: axis(axes) along which to compute the norm
    :param keepdims: indicates if the dimension(s) on axis should be kept
    :return: The length or vector of lengths.
    """
    lgth = np.sqrt(np.sum(x * x, axis=axis, keepdims=keepdims))
    return lgth


def normalize(x, axis=-1, eps=1e-8):
    """
    Normalizes a tensor over some axis (axes)

    :param x: data tensor
    :param axis: axis(axes) along which to compute the norm
    :param eps: epsilon to prevent numerical instabilities
    :return: The normalized tensor
    """
    res = x / (length(x, axis=axis) + eps)
    return res


def quat_normalize(x, eps=1e-8):
    """
    Normalizes a quaternion tensor

    :param x: data tensor
    :param eps: epsilon to prevent numerical instabilities
    :return: The normalized quaternions tensor
    """
    res = normalize(x, eps=eps)
    return res


def angle_axis_to_quat(angle, axis):
    """
    Converts from and angle-axis representation to a quaternion representation

    :param angle: angles tensor
    :param axis: axis tensor
    :return: quaternion tensor
    """
    c = np.cos(angle / 2.0)[..., np.newaxis]
    s = np.sin(angle / 2.0)[..., np.newaxis]
    q = np.concatenate([c, s * axis], axis=-1)
    return q


def euler_to_quat(e, order='zyx'):
    """

    Converts from an euler representation to a quaternion representation

    :param e: euler tensor
    :param order: order of euler rotations
    :return: quaternion tensor
    """
    axis = {
        'x': np.asarray([1, 0, 0], dtype=np.float32),
        'y': np.asarray([0, 1, 0], dtype=np.float32),
        'z': np.asarray([0, 0, 1], dtype=np.float32)}

    q0 = angle_axis_to_quat(e[..., 0], axis[order[0]])
    q1 = angle_axis_to_quat(e[..., 1], axis[order[1]])
    q2 = angle_axis_to_quat(e[..., 2], axis[order[2]])

    return quat_mul(q0, quat_mul(q1, q2))


def quat_inv(q):
    """
    Inverts a tensor of quaternions

    :param q: quaternion tensor
    :return: tensor of inverted quaternions
    """
    res = np.asarray([1, -1, -1, -1], dtype=np.float32) * q
    return res


def quat_fk(lrot, lpos, parents):
    """
    Performs Forward Kinematics (FK) on local quaternions and local positions to retrieve global representations

    :param lrot: tensor of local quaternions with shape (..., Nb of joints, 4)
    :param lpos: tensor of local positions with shape (..., Nb of joints, 3)
    :param parents: list of parents indices
    :return: tuple of tensors of global quaternion, global positions
    """
    gp, gr = [lpos[..., :1, :]], [lrot[..., :1, :]]
    for i in range(1, len(parents)):
        gp.append(quat_mul_vec(gr[parents[i]], lpos[..., i:i+1, :]) + gp[parents[i]])
        gr.append(quat_mul    (gr[parents[i]], lrot[..., i:i+1, :]))

    res = np.concatenate(gr, axis=-2), np.concatenate(gp, axis=-2)
    return res


def quat_ik(grot, gpos, parents):
    """
    Performs Inverse Kinematics (IK) on global quaternions and global positions to retrieve local representations

    :param grot: tensor of global quaternions with shape (..., Nb of joints, 4)
    :param gpos: tensor of global positions with shape (..., Nb of joints, 3)
    :param parents: list of parents indices
    :return: tuple of tensors of local quaternion, local positions
    """
    res = [
        np.concatenate([
            grot[..., :1, :],
            quat_mul(quat_inv(grot[..., parents[1:], :]), grot[..., 1:, :]),
        ], axis=-2),
        np.concatenate([
            gpos[..., :1, :],
            quat_mul_vec(
                quat_inv(grot[..., parents[1:], :]),
                gpos[..., 1:, :] - gpos[..., parents[1:], :]),
        ], axis=-2)
    ]

    return res


def quat_mul(x, y):
    """
    Performs quaternion multiplication on arrays of quaternions

    :param x: tensor of quaternions of shape (..., Nb of joints, 4)
    :param y: tensor of quaternions of shape (..., Nb of joints, 4)
    :return: The resulting quaternions
    """
    x0, x1, x2, x3 = x[..., 0:1], x[..., 1:2], x[..., 2:3], x[..., 3:4]
    y0, y1, y2, y3 = y[..., 0:1], y[..., 1:2], y[..., 2:3], y[..., 3:4]

    res = np.concatenate([
        y0 * x0 - y1 * x1 - y2 * x2 - y3 * x3,
        y0 * x1 + y1 * x0 - y2 * x3 + y3 * x2,
        y0 * x2 + y1 * x3 + y2 * x0 - y3 * x1,
        y0 * x3 - y1 * x2 + y2 * x1 + y3 * x0], axis=-1)

    return res


def quat_mul_vec(q, x):
    """
    Performs multiplication of an array of 3D vectors by an array of quaternions (rotation).

    :param q: tensor of quaternions of shape (..., Nb of joints, 4)
    :param x: tensor of vectors of shape (..., Nb of joints, 3)
    :return: the resulting array of rotated vectors
    """
    t = 2.0 * np.cross(q[..., 1:], x)
    res = x + q[..., 0][..., np.newaxis] * t + np.cross(q[..., 1:], t)

    return res


def quat_slerp(x, y, a):
    """
    Performs spherical linear interpolation (SLERP) between x and y, with proportion a

    :param x: quaternion tensor
    :param y: quaternion tensor
    :param a: indicator (between 0 and 1) of completion of the interpolation.
    :return: tensor of interpolation results
    """
    len = np.sum(x * y, axis=-1)

    neg = len < 0.0
    len[neg] = -len[neg]
    y[neg] = -y[neg]

    a = np.zeros_like(x[..., 0]) + a
    amount0 = np.zeros(a.shape)
    amount1 = np.zeros(a.shape)

    linear = (1.0 - len) < 0.01
    omegas = np.arccos(len[~linear])
    sinoms = np.sin(omegas)

    amount0[linear] = 1.0 - a[linear]
    amount0[~linear] = np.sin((1.0 - a[~linear]) * omegas) / sinoms

    amount1[linear] = a[linear]
    amount1[~linear] = np.sin(a[~linear] * omegas) / sinoms
    res = amount0[..., np.newaxis] * x + amount1[..., np.newaxis] * y

    return res


def quat_between(x, y):
    """
    Quaternion rotations between two 3D-vector arrays

    :param x: tensor of 3D vectors
    :param y: tensor of 3D vetcors
    :return: tensor of quaternions
    """
    res = np.concatenate([
        np.sqrt(np.sum(x * x, axis=-1) * np.sum(y * y, axis=-1))[..., np.newaxis] +
        np.sum(x * y, axis=-1)[..., np.newaxis],
        np.cross(x, y)], axis=-1)
    return res


def interpolate_local(lcl_r_mb, lcl_q_mb, n_past, n_future):
    """
    Performs interpolation between 2 frames of an animation sequence.

    The 2 frames are indirectly specified through n_past and n_future.
    SLERP is performed on the quaternions
    LERP is performed on the root's positions.

    :param lcl_r_mb:  Local/Global root positions (B, T, 1, 3)
    :param lcl_q_mb:  Local quaternions (B, T, J, 4)
    :param n_past:    Number of frames of past context
    :param n_future:  Number of frames of future context
    :return: Interpolated root and quats
    """
    # Extract last past frame and target frame
    start_lcl_r_mb = lcl_r_mb[:, n_past - 1, :, :][:, None, :, :]  # (B, 1, J, 3)
    end_lcl_r_mb = lcl_r_mb[:, -n_future, :, :][:, None, :, :]

    start_lcl_q_mb = lcl_q_mb[:, n_past - 1, :, :]
    end_lcl_q_mb = lcl_q_mb[:, -n_future, :, :]

    # LERP Local Positions:
    n_trans = lcl_r_mb.shape[1] - (n_past + n_future)
    interp_ws = np.linspace(0.0, 1.0, num=n_trans + 2, dtype=np.float32)
    offset = end_lcl_r_mb - start_lcl_r_mb

    const_trans    = np.tile(start_lcl_r_mb, [1, n_trans + 2, 1, 1])
    inter_lcl_r_mb = const_trans + (interp_ws)[None, :, None, None] * offset

    # SLERP Local Quats:
    interp_ws = np.linspace(0.0, 1.0, num=n_trans + 2, dtype=np.float32)
    inter_lcl_q_mb = np.stack(
        [(quat_normalize(quat_slerp(quat_normalize(start_lcl_q_mb), quat_normalize(end_lcl_q_mb), w))) for w in
         interp_ws], axis=1)

    return inter_lcl_r_mb, inter_lcl_q_mb


def remove_quat_discontinuities(rotations):
    """

    Removing quat discontinuities on the time dimension (removing flips)

    :param rotations: Array of quaternions of shape (T, J, 4)
    :return: The processed array without quaternion inversion.
    """
    rots_inv = -rotations

    for i in range(1, rotations.shape[0]):
        # Compare dot products
        replace_mask = np.sum(rotations[i - 1: i] * rotations[i: i + 1], axis=-1) < np.sum(
            rotations[i - 1: i] * rots_inv[i: i + 1], axis=-1)
        replace_mask = replace_mask[..., np.newaxis]
        rotations[i] = replace_mask * rots_inv[i] + (1.0 - replace_mask) * rotations[i]

    return rotations


# Orient the data according to the las past keframe
def rotate_at_frame(X, Q, parents, n_past=10):
    """
    Re-orients the animation data according to the last frame of past context.

    :param X: tensor of local positions of shape (Batchsize, Timesteps, Joints, 3)
    :param Q: tensor of local quaternions (Batchsize, Timesteps, Joints, 4)
    :param parents: list of parents' indices
    :param n_past: number of frames in the past context
    :return: The rotated positions X and quaternions Q
    """
    # Get global quats and global poses (FK)
    global_q, global_x = quat_fk(Q, X, parents)

    key_glob_Q = global_q[:, n_past - 1: n_past, 0:1, :]  # (B, 1, 1, 4)
    forward = np.array([1, 0, 1])[np.newaxis, np.newaxis, np.newaxis, :] \
                 * quat_mul_vec(key_glob_Q, np.array([0, 1, 0])[np.newaxis, np.newaxis, np.newaxis, :])
    forward = normalize(forward)
    yrot = quat_normalize(quat_between(np.array([1, 0, 0]), forward))
    new_glob_Q = quat_mul(quat_inv(yrot), global_q)
    new_glob_X = quat_mul_vec(quat_inv(yrot), global_x)

    # back to local quat-pos
    Q, X = quat_ik(new_glob_Q, new_glob_X, parents)

    return X, Q


def extract_feet_contacts(pos, lfoot_idx, rfoot_idx, velfactor=0.02):
    """
    Extracts binary tensors of feet contacts

    :param pos: tensor of global positions of shape (Timesteps, Joints, 3)
    :param lfoot_idx: indices list of left foot joints
    :param rfoot_idx: indices list of right foot joints
    :param velfactor: velocity threshold to consider a joint moving or not
    :return: binary tensors of left foot contacts and right foot contacts
    """
    lfoot_xyz = (pos[1:, lfoot_idx, :] - pos[:-1, lfoot_idx, :]) ** 2
    contacts_l = (np.sum(lfoot_xyz, axis=-1) < velfactor)

    rfoot_xyz = (pos[1:, rfoot_idx, :] - pos[:-1, rfoot_idx, :]) ** 2
    contacts_r = (np.sum(rfoot_xyz, axis=-1) < velfactor)

    # Duplicate the last frame for shape consistency
    contacts_l = np.concatenate([contacts_l, contacts_l[-1:]], axis=0)
    contacts_r = np.concatenate([contacts_r, contacts_r[-1:]], axis=0)

    return contacts_l, contacts_r


def quat2mat(q: np.ndarray, scalar_last=False, eps=np.finfo(np.float64).eps):
    """
    Convert quaternions to rotation matrices.

    Args:
        q: Quaternions to convert, shape (..., 4).
        scalar_last: If True, the scalar (w) is the last element of q.
    """
    if len(q.shape) == 1:
        q = q.reshape(1, -1)

    batch_size = q.shape[0]
    if scalar_last:
        x, y, z, w = np.split(q, 4, axis=-1)
    else:
        w, x, y, z = np.split(q, 4, axis=-1)

    Nq = w * w + x * x + y * y + z * z
    out = np.zeros((batch_size, 3, 3))
    out[Nq.reshape(batch_size) < eps] = np.eye(3)
    s = 2.0 / Nq
    X = x * s
    Y = y * s
    Z = z * s
    wX = w * X
    wY = w * Y
    wZ = w * Z
    xX = x * X
    xY = x * Y
    xZ = x * Z
    yY = y * Y
    yZ = y * Z
    zZ = z * Z
    out[:, 0, 0] = (1.0 - (yY + zZ)).reshape(-1)
    out[:, 0, 1] = (xY - wZ).reshape(-1)
    out[:, 0, 2] = (xZ + wY).reshape(-1)
    out[:, 1, 0] = (xY + wZ).reshape(-1)
    out[:, 1, 1] = (1.0 - (xX + zZ)).reshape(-1)
    out[:, 1, 2] = (yZ - wX).reshape(-1)
    out[:, 2, 0] = (xZ - wY).reshape(-1)
    out[:, 2, 1] = (yZ + wX).reshape(-1)
    out[:, 2, 2] = (1.0 - (xX + yY)).reshape(-1)
    return out
