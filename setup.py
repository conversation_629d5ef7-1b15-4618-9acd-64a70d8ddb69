from setuptools import setup, find_packages

setup(
    name="smpl_beta_optimizer",
    version="0.1",
    packages=find_packages(),
    install_requires=[
        "numpy>=1.21.0",
        "torch>=1.9.0",
        "PyQt5>=5.15.0",
        "PyQtChart>=5.15.0",
        "smplx>=0.1.28",
        "mujoco>=2.3.0",
        "open3d>=0.15.0",
        "matplotlib>=3.4.0",
        "scipy>=1.7.0",
        "tqdm>=4.62.0",
        "pyyaml>=5.4.0",
        "opencv-python>=4.5.0",
        "tensorboard>=2.7.0",
        "pandas>=1.3.0",
        "scikit-learn>=0.24.0"
    ],
    python_requires=">=3.7",
    author="Bridgedp",
    description="SMPL Beta Optimizer and Motion Retargeting System",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
) 