import os
import numpy as np
from typing import Optional, List, Dict, Tuple
from utils.bvh_parser import B<PERSON><PERSON>ars<PERSON>
from matplotlib import pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from scipy.spatial.transform import Rotation as sRot


def create_transformation_matrix(euler_angles, offset, degrees=True, channels='XYZ'):
    """
    Create a 4x4 homogeneous transformation matrix.
    
    Parameters:
      euler_angles: tuple/list of 3 angles (Xrotation, Yrotation, Zrotation)
      offset: tuple/list of 3 offsets (X, Y, Z)
      degrees: whether angles are in degrees (default True)
      
    Returns:
      4x4 numpy array transformation matrix
    """
    # Create rotation matrix from Euler angles (XYZ order assumed)
    rot = sRot.from_euler(channels, euler_angles, degrees=degrees)
    rot_matrix = rot.as_matrix()
    
    # Create 4x4 identity matrix
    transform = np.eye(4)
    
    # Set rotation part
    transform[:3, :3] = rot_matrix
    
    # Set translation part (offset)
    transform[:3, 3] = offset
    
    return transform


def vis_bvh_files(file_name: Optional[str] = None) -> None:
    """
    BVH File Motion Visualization.
    
    Parameters:
      file_name: bvh file name
      
    Returns:
      None
    """
    test_file: str = f"{os.getcwd()}/data/bvh/data/calc_bvh/unitreeG1_retarget/bvh_retarget_result_20250714_202417.bvh" if file_name is None else file_name
    parser: BVHParser = BVHParser(test_file)
    parser.parse()
    all_joints_info: List[Dict] = parser.joints 
    joints_nums: int = len(all_joints_info)


    # handel the relationship between joints
    transformation_matrics: Dict[str, Dict] = {}  # type: ignore
    for single_joint in all_joints_info:
        transformation_matrics[single_joint['name']] = {
                'parent': single_joint['parent_name'],
                'offset': single_joint['offset'],
                'T': np.eye(4), 
                'finished': False
            }
        
    figure = plt.figure(figsize=(5, 5))
    ax = figure.add_subplot(111, projection='3d')
    ax.set_xlabel('x')
    ax.set_ylabel('y')
    ax.view_init(elev=0, azim=-180, roll=-90)
    plt.ion()
    plt.show(block=False)

    motion_data = parser.motion_data.tolist()  #type: ignore
    # print(motion_data)
    frame_number: int = parser.frame_count
    current_frame: int = 0
    while True:
        ax.clear()
        name_and_positions: List[Tuple[str, List[List[float]]]] = []  #type: ignore

        current_frame_data = motion_data[current_frame % frame_number]

        for value in transformation_matrics.values():
            value['finished'] = False
        
        all_finished: bool = False
        while not all_finished:
            for index, single_joint in enumerate(all_joints_info):
                this_joint_name: str = single_joint['name']
                T_from_parent = np.eye(4)
                offset: List[float] = transformation_matrics[this_joint_name]['offset']
                parent_joint_name: Optional[str] = transformation_matrics[this_joint_name]['parent']
                if parent_joint_name is None:
                    offset = current_frame_data[:3]
                else:
                    if not transformation_matrics[parent_joint_name]['finished']:
                        continue
                    T_from_parent = transformation_matrics[parent_joint_name]['T']

                euler_angles_from_this_joint = current_frame_data[(index + 1) * 3:(index + 2) * 3]

                if parent_joint_name is None:
                    T_from_this_joint = create_transformation_matrix(euler_angles=euler_angles_from_this_joint, offset=offset, channels=single_joint['channels'][3:])
                else:
                    T_from_this_joint = create_transformation_matrix(euler_angles=euler_angles_from_this_joint, offset=offset, channels=single_joint['channels'])
                
                T = np.matmul(T_from_parent, T_from_this_joint)
                transformation_matrics[this_joint_name]['T'] = T

                this_joint_position = T[:3, 3].tolist()
                parent_joint_position = T_from_parent[:3, 3].tolist()
                if parent_joint_name is None:
                    name_and_positions.append((this_joint_name, [this_joint_position, this_joint_position]))
                else:
                    name_and_positions.append((this_joint_name, [this_joint_position, parent_joint_position]))

                transformation_matrics[this_joint_name]['finished'] = True

                
            for value in transformation_matrics.values():
                if not value['finished']:
                    all_finished = False
                    break
                else:
                    all_finished = True
        

        # Drawing starts here
        # key_points
        x = [name_position[-1][0][0] for name_position in name_and_positions]
        y = [name_position[-1][0][1] for name_position in name_and_positions]
        z = [name_position[-1][0][2] for name_position in name_and_positions]

        ax.scatter(x, y, z, c='b', s=100, alpha=.9)  # type: ignore

        # skeleten
        for _, (position, parent_position) in name_and_positions:
            x = [position[0], parent_position[0]]
            y = [position[1], parent_position[1]]
            z = [position[2], parent_position[2]]
            ax.plot(x, y, z, c='r')

        # label
        for name, (position, _) in name_and_positions:
            ax.text(position[0], position[1], position[2], name, fontsize=12)  #type: ignore

        current_frame += 1
        # current_frame = 0
        plt.pause(0.01)
        # plt.pause(0)

    # joint_name_and_its_position: Dict[str, List[List]] = {} # type: ignore for init
    # for single_joint in all_joints_info:  # first loop: search for the ROOT joint
    #     if single_joint['parent_name'] is None: # ROOT joint
    #         joint_name_and_its_position[single_joint['name']] = [single_joint['offset'], single_joint['offset']]
    #         break # break the loop 'cause ROOT has been found
    #     else:
    #         pass
    
    # while len(joint_name_and_its_position) < joints_nums:  # second loop: search for other joints
    #     for single_joint in all_joints_info:  
    #         if single_joint['parent_name'] is None:  # this joint is ROOT
    #             pass
    #         else:  # this joint is not ROOT
    #             its_parent: str = single_joint['parent_name']
    #             if its_parent not in joint_name_and_its_position:  # check whether its parent_joint has been joined the dict or not
    #                 continue
                
    #             joint_position: List[float] = [this_position + parent_position for this_position, parent_position in zip(single_joint['offset'], joint_name_and_its_position[its_parent][0])]
    #             joint_name_and_its_position[single_joint['name']] = [joint_position, joint_name_and_its_position[its_parent][0]]

    # # drawing stars here
    # # we need some ordered type here so we do list
    # joint_name_and_its_position_list: List[Tuple[str, List[List]]] = []  # type: ignore for init
    # for key, value in joint_name_and_its_position.items():
    #     joint_name_and_its_position_list.append((key, value)) # type: ignore
    
    # x = [name_position[-1][0][0] for name_position in joint_name_and_its_position_list]
    # y = [name_position[-1][0][1] for name_position in joint_name_and_its_position_list]
    # z = [name_position[-1][0][2] for name_position in joint_name_and_its_position_list]
    # print(len(x))

    # figure = plt.figure(figsize=(5, 5))
    # ax = figure.add_subplot(111, projection='3d')
    # ax.scatter(x, y, z, c='b', s=100, alpha=.9)  # type: ignore

    # # skeleten
    # for _, (position, parent_position) in joint_name_and_its_position_list:
    #     x = [position[0], parent_position[0]]
    #     y = [position[1], parent_position[1]]
    #     z = [position[2], parent_position[2]]
    #     ax.plot(x, y, z, c='r')

    # # label
    # for name, (position, _) in joint_name_and_its_position_list:
    #     ax.text(position[0], position[1], position[2], name, fontsize=12)  #type: ignore
    
    # ax.set_title('Vis BVH Files Alpha')



    # plt.show()


if __name__ == "__main__":
    # vis_bvh_files('data/calc_bvh/unitreeG1_retarget/bvh_retarget_result_20250714_202417.bvh')
    a = "0.063444 1.039954 0.354956 9.432721 7.083074 -176.960768 -0.768434 0.000000 0.000000 2.469275 0.000000 0.000000 -25.218706 0.000000 0.000000 29.325077 0.000000 0.000000 21.449574 0.000000 0.000000 -9.103373 0.000000 0.000000 -11.814971 0.000000 0.000000 -2.005733 0.000000 0.000000 -26.363697 0.000000 0.000000 29.192183 0.000000 0.000000 15.277788 0.000000 0.000000 15.548805 0.000000 0.000000 -26.355042 0.000000 0.000000 15.965865 0.000000 0.000000 5.527817 0.000000 0.000000 -16.379053 0.000000 0.000000 28.224846 0.000000 0.000000 -24.113459 0.000000 0.000000 54.094788 0.000000 0.000000 35.461689 0.000000 0.000000 -7.061253 0.000000 0.000000 0.000000 0.000000 0.000000 -6.555747 0.000000 0.000000 -38.774105 0.000000 0.000000 27.811163 0.000000 0.000000 39.480564 0.000000 0.000000 -5.889619 0.000000 0.000000 -21.652433 0.000000 0.000000 0.000000 0.000000 0.000000"
    print(len(a.split(' ')))