import argparse
from datetime import datetime
import glob
import os
import sys
import pdb
import os.path as osp
import re
import json

import matplotlib
matplotlib.use('TkAgg') # 将后端更改为 TkAgg
from matplotlib import pyplot as plt
import mujoco

from humanoid.utils.math_tool import But<PERSON><PERSON><PERSON>ilter, LowPassFilter1, butter_lowpass_filter_torch, euler_to_rotMat, rotation_matrix_from_vectors, gaussian_filter_1d_batch
# sys.path.append(os.getcwd())
# from poselib.skeleton.skeleton3d import SkeletonTree, SkeletonMotion, SkeletonState
from torch.nn import functional
from scipy.spatial.transform import Rotation as sRot
import numpy as np
import torch
from humanoid.smpllib.smpl_parser import (
    SMPL_Parser,
    SMPLH_Parser,
    SMPLX_Parser,
    SMPL_BONE_ORDER_NAMES,
)
import joblib
from humanoid.utils.rotation_conversions import axis_angle_to_matrix
from humanoid.utils.torch_humanoid_batch import Humanoid_Batch
from humanoid.retarget_motion.base.motion_lib_cfg import MotionLibCfg
from humanoid.envs import humanoid_batch_register
from torch.autograd import Variable
from tqdm import tqdm

from utils.mujoco_viewer import MujocoViwer

class Retargeting:
    def __init__(self, args):
        self.args = args  # 先设置args
        self.setPrama()
        self.shape_new = None
        self.shape_scale_dict = None
        self.motion_lib_cfg = None # motion_lib_cfg对象，包含机器人配置文件参数
        self.device = None # device
        self.Humanoid_fk = None # 计算机器人轴角的工具
        self.shape_path = args.shape_path if hasattr(args, 'shape_path') and args.shape_path else None # 机器人beta参数文件地址
        # print(f"[Retargeting.__init__] Initial shape_path: {self.shape_path}")  # 调试信息
        self.registryRobot()
        self.robot_joint_pick_bias = None
        self.robot_joint_pick = None
        self.limb_names = None
        self.robot_pos_names = None
        self.robot_joint_names = None
        self.limb_index = None
        # --- 新增：脚趾相关属性 ---
        # self.left_toe_name = 'foot_l1_link' # 假设的左脚趾关节名称
        # self.right_toe_name = 'foot_r1_link'# 假设的右脚趾关节名称
        self.left_toe_idx = -1
        self.right_toe_idx = -1
        # --- 结束新增 ---
        self.initRobotBody()
        # 读取smpl关节信息
        self.smpl_parser_n = None
        self.smpl_joint_pick_idx2 = None
        self.smpl_joint_pick_idx = None
        self.T1_joint_pick_idx = None
        self.initSMPL()
        self.initAmassData()
        
        self.mse_loss = torch.nn.MSELoss()  # 初始化 MSE Loss
        
        # 根据配置创建权重张量
        self.joint_weights = torch.ones(len(self.motion_lib_cfg.joint_pick), device=self.device)
        for i, joint_name in enumerate(self.motion_lib_cfg.joint_pick):
            if joint_name in self.motion_lib_cfg.joint_weights:
                self.joint_weights[i] = self.motion_lib_cfg.joint_weights[joint_name]
        
        # 扩展权重到3D
        self.joint_weights = self.joint_weights.unsqueeze(0).unsqueeze(-1).expand(1, -1, 3)  # (1,12,3)

        # 添加控制是否优化根坐标的属性
        self.optimize_root = self.motion_lib_cfg.optimize_root
        # 添加异常帧检测阈值
        self.anomaly_threshold = 0.05 # 您可以调整这个阈值 - 提高阈值以降低敏感度
        # --- 为新的统一绘图窗口初始化属性 ---
        self.loss_fig = None
        self.total_loss_ax = None
        self.keypoint_loss_ax = None
        self.foot_height_ax = None
        self.contact_loss_ax = None
        self.ankle_target_loss_ax = None
        self.smoothness_loss_ax = None
        self.loss_data = {} # 保留用于showLoss更新进度条
        # --- 修改：为每个帧损失图添加独立的Y轴最大值 ---
        self.keypoint_loss_y_max = 0.1  # 关键点损失的初始Y轴最大值
        self.foot_height_loss_y_max = 0.1 # 脚部高度损失的初始Y轴最大值
        self.contact_loss_y_max = 0.1 # 接触损失的初始Y轴最大值
        self.ankle_target_loss_y_max = 0.1 # 脚踝目标损失的初始Y轴最大值
        self.smoothness_loss_y_max = 0.1 # 平滑损失的初始Y轴最大值
        # --- 新增：安全初始化旧的 figure 变量 --- #
        self.total_loss_fig = None
        self.frame_loss_fig = None
        # 添加接触序列相关属性
        self.contact_sequence = None
        self.mujoco_viewer = MujocoViwer(self.motion_lib_cfg)

    def setPrama(self):
        # 设置可调参数
        self.device = self.args.device
        self.kernel_size = self.args.kernel_size  # Size of the Gaussian kernel
        self.sigma = self.args.sigma  # Standard deviation of the Gaussian kernel
        self.iterations = self.args.iterations # 迭代次数
        self.show_frequence = self.args.show_frequency #可损失函数视化间隔
        self.t_pose_mode = self.args.t_pose_mode  # 添加T-pose模式开关
        self.target_foot_height = self.args.target_foot_height # 新增：目标脚部高度（Z轴）
        self.contact_file_path = self.args.contact_file_path # 新增：接触文件路径

    def getAnkleEulerAngles(self, pose_aa_walk):
        """
        读取SMPL脚踝的旋转欧拉角
        Args:
            pose_aa_walk: SMPL的轴角表示，形状为[num_frames, num_joints*3]
        Returns:
            left_ankle_euler_xyz: 左脚踝的欧拉角，形状为[num_frames, 3]，顺序为[Roll,Pitch,Yaw]
            right_ankle_euler_xyz: 右脚踝的欧拉角，形状为[num_frames, 3]，顺序为[Roll,Pitch,Yaw]
        """
        num_frames = pose_aa_walk.shape[0]
        ankle_names = ["L_Ankle", "R_Ankle"]
        ankle_index = [self.smpl_bone_names.index(i) * 3 + j for i in ankle_names for j in range(3)]
        ankle_pose_aa = pose_aa_walk[:, ankle_index].reshape(num_frames, -1, 3)
        # smpl坐标系的欧拉角 [Z,X,Y] 对应 机器人坐标系 的欧拉角 [X,Y,Z]
        ankle_euler_zxy = (sRot.from_rotvec(ankle_pose_aa.cpu().numpy().reshape(-1, 3))
                          .as_euler('ZXY', degrees=False))
        ankle_euler = (torch.from_numpy(ankle_euler_zxy.reshape(num_frames, -1, 3))
                      .to(pose_aa_walk.device))
        left_ankle_euler_xyz = ankle_euler[:, 0, :]  # 左脚踝 [Roll,Pitch,Yaw]
        right_ankle_euler_xyz = ankle_euler[:, 1, :]  # 右脚踝 [Roll,Pitch,Yaw]
        
        return left_ankle_euler_xyz, right_ankle_euler_xyz

    def setInitRoot(self, pose_aa_walk, smpl_joints_scaled, if_straight=False):
        """
        调整躯干（root节点）姿态
        """
        _num_frames = pose_aa_walk.shape[0]
        _device = pose_aa_walk.device

        # 躯干节点提取
        key_point_names = ["Pelvis", "Torso", "Spine", "Chest", "L_Shoulder", "R_Shoulder"]
        key_point_index = [self.smpl_bone_names.index(i) * 3 + j for i in key_point_names for j in range(3)]
        key_point_pose_aa = pose_aa_walk[:, key_point_index].reshape(_num_frames, -1, 3)
        smpl_body_joint_pick_idx = [SMPL_BONE_ORDER_NAMES.index(j) for j in key_point_names]
        smpl_key_joints_body = torch.tensor(smpl_joints_scaled[:, smpl_body_joint_pick_idx])

        # 创建骨盆、躯干、脊椎、胸腔节点的旋转矩阵
        rotation_matrices_Pelvis = torch.from_numpy(
            sRot.from_rotvec(key_point_pose_aa[:, 0, :].cpu().numpy()).as_matrix()).float().to(pose_aa_walk.device)
        rotation_matrices_Torso = torch.from_numpy(
            sRot.from_rotvec(key_point_pose_aa[:, 1, :].cpu().numpy()).as_matrix()).float().to(pose_aa_walk.device)
        rotation_matrices_Spine = torch.from_numpy(
            sRot.from_rotvec(key_point_pose_aa[:, 2, :].cpu().numpy()).as_matrix()).float().to(pose_aa_walk.device)
        rotation_matrices_Chest = torch.from_numpy(
            sRot.from_rotvec(key_point_pose_aa[:, 3, :].cpu().numpy()).as_matrix()).float().to(pose_aa_walk.device)

        # 坐标系顺序转移矩阵，将z向前，y向上，x向左转换为z向上，y向前，x向右，以符合机器人坐标系的特点
        rotation_matrices_Test = torch.from_numpy(
            sRot.from_euler('ZYX', torch.tensor([torch.pi, 0, torch.pi * 0.5])
                            .unsqueeze(0).repeat(_num_frames, 1).cpu().numpy()).as_matrix()).float().to(pose_aa_walk.device)
        rotation_matrices_Test_inv = torch.from_numpy(
            sRot.from_euler('ZYX', torch.tensor([0, 0, torch.pi * 0.5])
                            .unsqueeze(0).repeat(_num_frames, 1).cpu().numpy()).inv().as_matrix()).float().to(pose_aa_walk.device)

        # rotation_matrices_Body_euler = (sRot.from_matrix(rotation_matrices_upRobot.cpu().numpy())).as_euler('ZYX')

        # 创建坐标矫正后的骨盆节点旋转矩阵
        # rotation_matrices_Pelvis_fix = torch.matmul(rotation_matrices_Test_inv,rotation_matrices_Pelvis)
        rotation_matrices_Pelvis_fix = torch.matmul(rotation_matrices_Pelvis, rotation_matrices_Test_inv)
        rotation_matrices_upRobot = torch.matmul(rotation_matrices_Torso,
                                                 torch.matmul(rotation_matrices_Spine, rotation_matrices_Chest))
        rotation_matrices_Robot = torch.matmul(rotation_matrices_Pelvis_fix, rotation_matrices_upRobot)
        up_body_position = None
        if not if_straight:
            # 选取左右肩膀节点的中点作为躯干的上部端点
            position_shoulder = (smpl_key_joints_body[:, 4, :] + smpl_key_joints_body[:, 5, :]) * 0.5
            up_body_position = position_shoulder - smpl_key_joints_body[:, 0, :]
            # print(up_body_position[200,...])
        else:
            up_body_position = torch.zeros([smpl_key_joints_body.shape[0], 3]).to(smpl_key_joints_body.device)
            up_body_position[:, 2] = 1

        # 计算新世界坐标系下的机器人骨盆的方向向量
        vec = torch.tensor([0, 0, 1.0], device=self.device).unsqueeze(0).repeat(_num_frames, 1)
        vec2 = torch.matmul(rotation_matrices_Pelvis_fix, vec.unsqueeze(-1)).squeeze(-1)
        # vec2 = torch.matmul(vec.unsqueeze(1), rotation_matrices_Pelvis_fix).squeeze(1)
        # up_body_position = torch.matmul(rotation_matrices_Pelvis_fix,up_body_position.unsqueeze(-1))
        # rotation_matrices_Body_euler = (sRot.from_matrix(rotation_matrices_Pelvis.cpu().numpy())).as_euler('ZYX')
        # print("rotation_matrices_Pelvis: ")
        # print(rotation_matrices_Body_euler[200,...])
        # print(vec2[200,...])
        # print(up_body_position[200, ...])

        # 计算骨盆到新躯干方向的旋转矩阵
        # rotation_matrices_Body = rotation_matrix_from_vectors(vec,up_body_position)
        rotation_matrices_Body = rotation_matrix_from_vectors(vec2, up_body_position)
        # rotation_matrices_Body = torch.eyes()
        # rotation_matrices_Body_euler = (sRot.from_matrix(rotation_matrices_Body.cpu().numpy())).as_euler('ZYX')
        # print(rotation_matrices_Body_euler[200,...])
        # print(rotation_matrices_Body_euler[500,...])
        # rotation_matrices_Body_euler[:,[0,1]] = 0 # 可以选择只保留俯仰角(X)偏移，因为躯干是个整体不方便滚转和偏航，选择后会导致姿态失真，但欧氏距离误差会减小
        # rotation_matrices_Body = torch.from_numpy(sRot.from_euler('ZYX',rotation_matrices_Body_euler).as_matrix()).float().to(pose_aa_walk.device)

        # 计算新的根节点旋转矩阵
        rotation_matrices_newRoot = torch.matmul(rotation_matrices_Body, rotation_matrices_Pelvis)
        # rotation_matrices_upRobot_euler = (sRot.from_matrix(rotation_matrices_upRobot.cpu().numpy())).as_euler('ZYX')
        # rotation_matrices_upRobot_euler[:,1:] = 0
        # rotation_matrices_upRobot_z = torch.from_numpy(sRot.from_euler('ZYX',rotation_matrices_upRobot_euler).as_matrix()).float().to(pose_aa_walk.device)
        # #
        # rotation_matrices_Body = torch.matmul(rotation_matrices_Body,rotation_matrices_upRobot_z)
        # rotation_matrices_newRoot = torch.matmul(rotation_matrices_Body,rotation_matrices_Test)
        root_rot = torch.from_numpy(
            (sRot.from_matrix(rotation_matrices_newRoot[:, 0:3, 0:3].cpu().numpy()) * sRot.from_quat([0.5, 0.5, 0.5, 0.5]).inv())
            .as_rotvec()).float().to(pose_aa_walk.device)

        # 计算腿部节点的旋转角度矫正
        key_point_names = ["L_Hip", "R_Hip", "L_Shoulder", "R_Shoulder"]
        key_point_index = [self.smpl_bone_names.index(i) * 3 + j for i in key_point_names for j in range(3)]
        key_point_pose_aa = pose_aa_walk[:, key_point_index].reshape(_num_frames, -1, 3)
        pose_aa_walk_robot = pose_aa_walk.clone()

        # 矫正左胯骨的角度，通过胯骨转动实现躯干姿态变化
        rotation_matrices_L_Hip = torch.from_numpy(
            sRot.from_rotvec(key_point_pose_aa[:, 0, :].cpu().numpy()).as_matrix()).float().to(pose_aa_walk.device)
        rotation_matrices_Body_euler = sRot.from_matrix(rotation_matrices_Body[:, 0:3, 0:3].cpu().numpy()).as_euler('ZYX')
        rotation_matrices_Body_euler[:, [2]] = rotation_matrices_Body_euler[:, [2]] * -1
        tranformation_matrices_Body2Hip_fix = torch.from_numpy(sRot.from_euler('YZX', rotation_matrices_Body_euler[:, [1, 0, 2]]).as_matrix()).float().to(pose_aa_walk.device)
        rotation_L_Hip = torch.from_numpy((
                                                  sRot.from_matrix(rotation_matrices_L_Hip[:, 0:3, 0:3].cpu().numpy()) *
                                                  sRot.from_matrix(tranformation_matrices_Body2Hip_fix[:, 0:3, 0:3].cpu().numpy()))
                                          .as_rotvec()).float().to(pose_aa_walk.device)
        pose_aa_walk_robot[:, key_point_index[0:3]] = rotation_L_Hip

        # 矫正右胯骨的角度，通过胯骨转动实现躯干姿态变化
        rotation_matrices_R_Hip = torch.from_numpy(
            sRot.from_rotvec(key_point_pose_aa[:, 1, :].cpu().numpy()).as_matrix()).float().to(pose_aa_walk.device)
        rotation_matrices_Body_euler = sRot.from_matrix(rotation_matrices_Body[:, 0:3, 0:3].cpu().numpy()).as_euler('ZYX')
        rotation_matrices_Body_euler[:, [2]] = rotation_matrices_Body_euler[:, [2]] * -1
        tranformation_matrices_Body2Hip_fix = torch.from_numpy(sRot.from_euler('YZX', rotation_matrices_Body_euler[:, [1, 0, 2]]).as_matrix()).float().to(pose_aa_walk.device)
        rotation_R_Hip = torch.from_numpy((
                                                  sRot.from_matrix(rotation_matrices_R_Hip[:, 0:3, 0:3].cpu().numpy()) *
                                                  sRot.from_matrix(tranformation_matrices_Body2Hip_fix[:, 0:3, 0:3].cpu().numpy()))
                                          .as_rotvec()).float().to(pose_aa_walk.device)
        pose_aa_walk_robot[:, key_point_index[3:6]] = rotation_R_Hip

        return root_rot, pose_aa_walk_robot

    def registryRobot(self):
        # 注册机器人信息
        task = self.args.task
        # print(f"[registryRobot] Before register_task - shape_path: {self.shape_path}")
        humanoid_batch_register.register_task(task, self.shape_path)
        # print(f"[registryRobot] After register_task - humanoid_batch_register.shape_path: {humanoid_batch_register.shape_path}")
        self.motion_lib_cfg: MotionLibCfg = humanoid_batch_register.get_config(task)
        self.motion_lib_cfg.device = self.device
        # motion_lib_cfg.motion_file = motion_file
        self.Humanoid_fk = Humanoid_Batch(self.motion_lib_cfg.mjcf_file, self.motion_lib_cfg.extend_node_dict, device=self.motion_lib_cfg.device)

    def initRobotBody(self):
        # 读取机器人关节信息
        # 如果用户没有指定shape_path，则使用默认路径
        if self.shape_path is None:
            self.shape_path = humanoid_batch_register.shape_path
            print(f"[initRobotBody] Using default shape_path: {self.shape_path}")
        else:
            print(f"[initRobotBody] Using custom shape_path: {self.shape_path}")
        self.robot_joint_names = self.motion_lib_cfg.joint_names  # 从配置文件中获取机器人关节名称列表，包含所有物理关节
        self.robot_pos_names = self.motion_lib_cfg.pos_names  # 从配置文件中获取需要计算位置的关节名称列表，这些关节用于运动学计算和位置跟踪
        self.limb_names = self.motion_lib_cfg.limb_names  # 从配置文件中获取机器人肢体配置，将关节按肢体（如左腿、右腿、左臂、右臂）分组
        self.robot_joint_pick = self.motion_lib_cfg.joint_pick  # 从配置文件中获取用于运动重定向的关键关节点列表，这些点与SMPL人体模型的关键点对应
        self.robot_joint_pick_bias = self.motion_lib_cfg.joint_pick_bias # 从配置文件中获取用于运动重定向的关键关节点偏移列表

        # 创建关节链条
        self.limb_index = {}
        for key in self.limb_names:
            limb_name = self.limb_names[key]
            self.limb_index[key] = [self.robot_pos_names.index(i) for i in limb_name if i in self.robot_pos_names]

        # --- 新增：获取脚部关节索引 ---
        # --- 修改：统一获取脚趾索引，用于采样、约束和可视化 ---
        try:
            self.left_toe_idx = self.robot_joint_names.index(self.motion_lib_cfg.left_toe_name)
            self.right_toe_idx = self.robot_joint_names.index(self.motion_lib_cfg.right_toe_name)
            print(f"[Info] 脚趾索引初始化成功: Left={self.left_toe_idx} ('{self.motion_lib_cfg.left_toe_name}'), Right={self.right_toe_idx} ('{self.motion_lib_cfg.right_toe_name}')")
        except ValueError:
            print(f"警告：在 robot_joint_names 中找不到脚趾关节 '{self.motion_lib_cfg.left_toe_name}' 或 '{self.motion_lib_cfg.right_toe_name}'。脚底采样和接触相关功能可能受限。")
            self.left_toe_idx = -1
            self.right_toe_idx = -1
        # --- 结束修改 ---

        # --- 结束新增 ---

    def initSMPL(self):
        # 从 bvh_joint_correspondence 中读取关节对应关系
        self.bvh_joint_pick = list(self.motion_lib_cfg.bvh_joint_correspondence.values())
        self.smpl_joint_pick2 = ["Pelvis", "Torso", "Spine", "Chest", "Neck", "Head", "L_Thorax", "R_Thorax",
                            "L_Hip", "L_Knee", "L_Ankle", "L_Toe",
                            "R_Hip", "R_Knee", "R_Ankle", "R_Toe",
                            "L_Shoulder", "L_Elbow", "L_Wrist", "L_Hand",
                            "R_Shoulder", "R_Elbow", "R_Wrist", "R_Hand"]
        self.smpl_bone_names = [
            "Pelvis", "L_Hip", "R_Hip", "Torso", "L_Knee", "R_Knee", "Spine", "L_Ankle", "R_Ankle", "Chest",  # 9
            "L_Toe", "R_Toe", "Neck", "L_Thorax", "R_Thorax", "Head", "L_Shoulder", "R_Shoulder", "L_Elbow",  # 18
            "R_Elbow", "L_Wrist", "R_Wrist", "L_Hand", "R_Hand"]  # 23
        
        # 初始化smpl对象
        self.T1_joint_pick_idx = [self.robot_joint_names.index(j) for j in self.robot_joint_pick]
        self.smpl_joint_pick_idx = [SMPL_BONE_ORDER_NAMES.index(j) for j in self.smpl_joint_pick]
        self.smpl_joint_pick_idx2 = [SMPL_BONE_ORDER_NAMES.index(j) for j in self.smpl_joint_pick2]
        self.smpl_parser_n = SMPL_Parser(model_path="data/smpl", gender="neutral")
        self.smpl_parser_n.to(self.device)

        # 使用json加载json文件
        import json
        print(f"Loading shape_scale_bias from: {self.shape_path}")
        with open(self.shape_path, 'r') as f:
            self.shape_scale_dict = json.load(f)
        self.shape_new = torch.tensor(self.shape_scale_dict["shape"], device=self.device)
        if 'scale' in self.shape_scale_dict:
            self.scale = torch.tensor(self.shape_scale_dict["scale"], device=self.device)
        self.scale_leg = self.scale
        if 'scale_leg' in self.shape_scale_dict:
            self.scale_leg = torch.tensor(self.shape_scale_dict["scale_leg"], device=self.device)
            if self.scale_leg==0:
                self.scale_leg = self.scale
        # 修改bias的处理方式，保持[1, 12, 3]的形状
        self.key_point_bias = torch.tensor(self.shape_scale_dict['bias'], device=self.device).unsqueeze(0)
        self.root_bias = None
        if 'root_bias' in self.shape_scale_dict:
            self.root_bias = torch.tensor(self.shape_scale_dict['root_bias'], device=self.device)

        # --- Start: Define foot indices early --- #
        # --- 修改：优先使用脚趾索引进行可视化 ---
        left_foot_vis_idx = self.left_toe_idx
        right_foot_vis_idx = self.right_toe_idx

        if left_foot_vis_idx == -1 or right_foot_vis_idx == -1:
            print("[visRefPoint Warning] 未找到脚趾索引，将无法显示接触指示器或执行精确的脚底采样。")
            # If toes failed, vis indices remain -1. Contact viz won't work.
        else:
            print(f"[visRefPoint Info] 使用脚趾索引进行接触可视化/采样: Left={left_foot_vis_idx}, Right={right_foot_vis_idx}")
            # --- Added Print --- #
            print(f"[visRefPoint Info] Joint Name for Left Index {left_foot_vis_idx}: {self.robot_joint_names[left_foot_vis_idx]}")
            print(f"[visRefPoint Info] Joint Name for Right Index {right_foot_vis_idx}: {self.robot_joint_names[right_foot_vis_idx]}")
            # --- End Added Print --- #
        # --- End: Define foot indices early --- #

        # 加载机器人模型
        model = mujoco.MjModel.from_xml_path(self.motion_lib_cfg.mjcf_file)

    def initAmassData(self):
        # 导入amass数据
        self.source_data_path = self.args.source_data_path
        self.amass_data = joblib.load(self.source_data_path)  # From humanoid 此处是姿态数据，一个文件就是一个动作
        match = re.search(r'(\d+)hz', self.source_data_path)
        if match:
            number_str = match.group(1)  # 提取到的字符串 '120'
            self.fps = float(number_str)  # 转换为 double（float）
        else:
            self.fps = 30
        data_dump = {}
        data_dump_sysmetric = {}
        data_dump_sysmetric_not_full = {}
        T1_data = {}
        self.pbar = tqdm(self.amass_data.keys())  # 进度条


    def __test_joint_angle__(self,pose_aa_walk):
        # 修改关节转角，帮助你理解amass数据的旋转关系
        # 测试关节转角
        euler_test1 = torch.tensor([0, 0, 1.57]).unsqueeze(0).repeat(self.num_frame, 1)
        # rotation_L_Hip_matrix_test = torch.from_numpy(sRot.from_euler('YXZ',euler_test1.cpu().numpy())
        #                                               .as_matrix()).float().to(pose_aa_walk.device)
        rotation_L_Hip_aa_test = (torch.from_numpy(sRot.from_euler('YZX', euler_test1.cpu().numpy())
                                                   .as_rotvec()).float().to(pose_aa_walk.device))

        key_point_pose_euler_yzx = (sRot.from_rotvec(pose_aa_walk[:, 39:42].cpu().numpy()) * sRot.from_rotvec(pose_aa_walk[:, 48:51].cpu().numpy())
                                    ).as_euler('YZX', degrees=False)
        key_point_pose_euler_yzx[:, 1] = np.maximum(key_point_pose_euler_yzx[:, 1], np.ones_like(key_point_pose_euler_yzx[:, 1]) * (-np.pi * 0.1))
        pose_aa_walk[:, 39:42] = 0
        pose_aa_walk[:, 48:51] = torch.from_numpy(sRot.from_euler('YZX', key_point_pose_euler_yzx)
                                                  .as_rotvec()).float().to(pose_aa_walk.device)
        key_point_pose_euler_yzx = (sRot.from_rotvec(pose_aa_walk[:, 42:45].cpu().numpy()) * sRot.from_rotvec(pose_aa_walk[:, 51:54].cpu().numpy())
                                    ).as_euler('YZX', degrees=False)
        key_point_pose_euler_yzx[:, 1] = np.minimum(key_point_pose_euler_yzx[:, 1], np.ones_like(key_point_pose_euler_yzx[:, 1]) * np.pi * 0.1)
        # key_point_pose_euler_yzx[:, 1] = -np.pi*0.16
        # key_point_pose_euler_yzx[:, :] = 0
        pose_aa_walk[:, 42:45] = 0
        pose_aa_walk[:, 51:54] = torch.from_numpy(sRot.from_euler('YZX', key_point_pose_euler_yzx)
                                                  .as_rotvec()).float().to(pose_aa_walk.device)
        return pose_aa_walk

    def caluSMPL2Robot(self, data_key):
        self.begin = 0
        self.end = self.amass_data[data_key]['trans'].shape[0]
        self.num_frame = self.end - self.begin  # 取前多少帧数ss
        print("帧数：", self.num_frame)

        # rotvec = sRot.from_quat([0.5, 0.5, 0.5, 0.5]).as_rotvec()
        pose_aa_walk = torch.from_numpy(np.concatenate((self.amass_data[data_key]['pose_aa'][self.begin:self.end, :66], np.zeros((self.num_frame, 6))), axis=-1)).float().to(self.device)
        gt_root_rot = torch.from_numpy((sRot.from_rotvec(pose_aa_walk.cpu().numpy()[:, :3]) * sRot.from_quat([0.5, 0.5, 0.5, 0.5]).inv()).as_rotvec()).float().to(self.device)  # 计算真实根部旋转
        # 修改原始smpl动作，防止超限位
        # pose_aa_walk = self.__test_joint_angle__(pose_aa_walk)

        # 计算smpl数据与机器人的根节点高度偏差，以修复穿地悬空脚滑步等问题
        trans = torch.from_numpy(self.amass_data[data_key]['trans'][self.begin:self.end, :]).float().to(self.device)  # 平移项
        origin_beta = torch.from_numpy(self.amass_data[data_key]['beta'])[None, :].float().to(self.device)  # 原始beta
        origin_height = self.calcRootHeight(self.smpl_parser_n, origin_beta)
        new_height = self.calcRootHeight(self.smpl_parser_n, self.shape_new)
        trans[:, :2] = trans[:, :2] - trans[[0], :2]  # x-y平面归一化
        trans = trans * new_height / origin_height  # 高度缩放
        self.t_pose_trans_offset = trans
        smpl_verts, smpl_joints = self.smpl_parser_n.get_joints_verts(pose_aa_walk, self.shape_new, trans)
        root_pos = smpl_joints[:, 0:1, :]

        smpl_root_pos_scaled = root_pos * self.scale_leg
        # 加入scale
        smpl_joints_scaled = smpl_joints - root_pos
        weight = torch.ones_like(smpl_joints_scaled)

        # else:
        #     scale_leg *= 0.99
        weight[:, 0:1, :] *= self.scale
        weight[:, 1:3, :] *= self.scale_leg
        weight[:, 3:4, :] *= self.scale
        weight[:, 4:6, :] *= self.scale_leg
        weight[:, 6:7, :] *= self.scale
        weight[:, 7:9, :] *= self.scale_leg
        weight[:, 9:10, :] *= self.scale
        weight[:, 10:12, :] *= self.scale_leg
        weight[:, 12:, :] *= self.scale
        smpl_joints_scaled = smpl_joints_scaled * weight + smpl_root_pos_scaled
        smpl_key_joints = smpl_joints_scaled[:, self.smpl_joint_pick_idx]

        # 矫正root节点的姿态和位置
        # pose_aa_walk_robot = pose_aa_walk
        self.robot_t_pose_trans_offset = smpl_root_pos_scaled[0:1, 0, :]
        root_trans_offset = smpl_root_pos_scaled[:, 0, :]
        gt_root_rot, pose_aa_walk_robot = self.setInitRoot(pose_aa_walk, smpl_joints_scaled, if_straight=False)
        # 处理机器人自身根节点偏移
        if self.root_bias is not None:
            # 矫正机器人自身root节点的偏移量
            point = self.root_bias.clone().unsqueeze(0).to(self.device)
            self.robot_t_pose_trans_offset = self.robot_t_pose_trans_offset - point
            # 将 gt_root_rot 转换为旋转矩阵，结果为 [N, 3, 3]
            rotation_matrices = torch.from_numpy(sRot.from_rotvec(gt_root_rot.cpu().numpy()).as_matrix()).float().to(self.device)
            # 扩展 point 的维度为 [N, 3] 以便进行批量矩阵乘法
            points_repeated = point.expand(self.num_frame, -1)
            # 为root节点执行批量旋转操作，得到旋转下的root点坐标
            rotated_points = torch.bmm(rotation_matrices, points_repeated.unsqueeze(-1)).squeeze(-1)
            # root_trans_offset = smpl_root_pos_scaled[:, 0, :]
            # smpl_root_pos_scaled[:,0,:] += rotated_points * scale_leg
            root_trans_offset = smpl_root_pos_scaled[:, 0, :] - rotated_points


        # 为关节旋转角度赋初值
        dof_pos = torch.zeros((1, self.num_frame, self.Humanoid_fk.joints_axis.shape[1], 1)).to(self.device)  # 定义初始的自由度变量（DOF），其实是关节旋转值
        # dof_pos = self.setInitPose(dof_pos, pose_aa_walk_robot)  # 赋值
        return dof_pos, pose_aa_walk_robot, gt_root_rot,root_trans_offset,smpl_key_joints,smpl_joints_scaled

    @staticmethod
    def calcRootHeight(smpl_parser_n, beta):
        pose_aa_stand = np.zeros((1, 72))  # (1,24*3)
        rotvec = sRot.from_quat([0.5, 0.5, 0.5, 0.5]).as_rotvec()
        pose_aa_stand[:, :3] = rotvec  # 将计算得到的旋转向量 rotvec 的值赋给 pose_aa_stand 的前 3 个元素（即第一个关节的姿势）
        pose_aa_stand = pose_aa_stand.reshape(-1, 24, 3)
        pose_aa_stand = torch.from_numpy(pose_aa_stand.reshape(-1, 72)).to(beta.device)
        trans = torch.zeros([1, 3]).to(beta.device)  # Smpl的位移
        Smpl_verts, Smpl_joints = smpl_parser_n.get_joints_verts(pose_aa_stand, beta, trans)
        height = min(Smpl_joints[0, [10, 11], 2])  
        return height

    def calcAA(self,dof_pos,gt_root_rot):
        # 计算轴角
        # 记录T1零位时各个关节的姿态旋转矩阵
        # --- 简化：一次性计算所有关节的轴角 ---
        # 获取关节轴 (假设形状为 [num_dofs, 3])
        axes = self.Humanoid_fk.joints_axis
        # 获取关节角度 (形状 [1, num_frames, num_dofs, 1])
        angles = dof_pos
        # 计算所有非根关节的轴角 (利用广播机制)
        # axes reshaped to [1, 1, num_dofs, 3]
        # angles [1, num_frames, num_dofs, 1] * axes [1, 1, num_dofs, 3] -> joint_aa [1, num_frames, num_dofs, 3]
        # --- 修正：使用 unsqueeze 来匹配广播 --- #
        joint_aa = angles * axes.unsqueeze(1) # axes: [1, num_dofs, 3] -> [1, 1, num_dofs, 3]
        # --- 结束修正 --- #

        # 调整根旋转的形状以进行拼接 (形状 [num_frames, 3] -> [1, num_frames, 1, 3])
        root_rot_reshaped = gt_root_rot.view(1, -1, 1, 3)

        # 拼接根旋转和关节轴角
        pose_aa_T1_new = torch.cat([root_rot_reshaped, joint_aa], dim=2).to(self.device)
        # --- 结束简化 ---
        return pose_aa_T1_new

    def calcJointPosition(self, fk_return):
        """计算关节位置并应用偏置修正"""
        # 获取关节位置
        T1_joint = fk_return['global_translation']
        
        # 从输入数据中获取维度信息
        batch_size = fk_return['global_rotation_mat'].shape[0]
        num_frames = fk_return['global_rotation_mat'].shape[1]
        num_joints = fk_return['global_rotation_mat'].shape[2]
        
        # 准备偏置数据
        offset = []
        T1_joint_pick_bias_idx = [self.robot_joint_names.index(j) for j in self.robot_joint_pick_bias]
        
        # 扩展偏置以匹配当前序列长度
        expanded_bias = (self.key_point_bias[:, None]
                         .expand(batch_size, num_frames, len(T1_joint_pick_bias_idx), 3)
                         .to(self.device)
                         .type(fk_return['global_rotation_mat'][:, :, -1].dtype))
        
        # 计算每个关节的偏置
        for i in range(len(T1_joint_pick_bias_idx)):
            if self.motion_lib_cfg.bias_relative_to_parent:
                # 偏置相对于父关节旋转
                parent_idx = self.Humanoid_fk._parents[T1_joint_pick_bias_idx[i]]
                transform_rot = fk_return['global_rotation_mat'][:, :, parent_idx, :, :]
            else:
                # 偏置相对于子关节旋转
                child_idx = T1_joint_pick_bias_idx[i]
                transform_rot = fk_return['global_rotation_mat'][:, :, child_idx, :, :]
                
            rotated_bias = torch.matmul(transform_rot, expanded_bias[:, :, i, :, None]).squeeze(-1)
            offset.append(rotated_bias)

        # 直接应用偏置到各个关节
        for i in range(len(T1_joint_pick_bias_idx)):
            T1_joint[:, :, T1_joint_pick_bias_idx[i]] -= offset[i]
        
        return T1_joint

    def filter(self):
        kernel_size = 5  # Size of the Gaussian kernel
        sigma = 0.75  # Standard deviation of the Gaussian kernel
        sigma_root = 1.25  # Standard deviation of the Gaussian kernel
        cutoff_frequence = 1
        filter_kernel = torch.ones((1, 1, 1, kernel_size), dtype=torch.float32, device=self.device, requires_grad=False) / kernel_size
        filter_kernel = filter_kernel.to(self.device)
        filter_mean = torch.nn.Conv2d(1, 1, (1, kernel_size), padding=(0, kernel_size // 2)).to(self.device)
        filter_mean.weight.data = filter_kernel

        # dof_pos_new = dof_pos_new.permute(0, 3, 2, 1)
        # dof_pos_new = filter_mean(dof_pos_new.data)
        # dof_pos_new = dof_pos_new.permute(0, 3, 2, 1)

    @staticmethod
    def accelerationLoss(T1_key_pos, fps):
        """
        约束机器人关键节点的加速度，抑制机器人的高频抖动 避免太激烈的动作
        """
        # 只算腿的抖动，阈值设置为1.2
        v = T1_key_pos[:, 1:, :, :] - T1_key_pos[:, 0:-1, :, :]
        v = v * fps
        a = v[:, 1:, :, :] - v[:, 0:-1, :, :]
        a = a * fps
        a = torch.norm(a, p=2, dim=-1)
        a = torch.where(a.abs() > 200.0, torch.pow(a.abs()- 200.0, 2), 0)

        sorted_tensor, indices = torch.sort(a, dim=1, descending=True)
        test = sorted_tensor.cpu().detach().numpy()
        a = a.mean()
        # print(torch.max(sorted_tensor))
        return a

    def initVis(self):
        """初始化可视化损失函数"""
        if self.args.no_show:
            return

        # --- 修改：确保只在第一次或窗口关闭后创建新窗口 --- #
        # --- 修改：增加到 7 个子图 (使用 3x3 布局) --- #
        if self.loss_fig is None or not plt.fignum_exists(self.loss_fig.number):
            # --- 修改：改为 3x3 布局 --- #
            self.loss_fig, axes = plt.subplots(3, 3, figsize=(18, 15)) # 3 行 3 列，调整 figsize
            # --- 确保解包 9 个轴 (按行优先顺序)，即使只用 7 --- #
            try:
                axes_flat = axes.flatten() # 展平成一维数组
                self.total_loss_ax = axes_flat[0]
                self.keypoint_loss_ax = axes_flat[1]
                self.foot_height_ax = axes_flat[2]
                self.contact_loss_ax = axes_flat[3]
                self.ankle_target_loss_ax = axes_flat[4]
                self.smoothness_loss_ax = axes_flat[5]
                # --- 新增：分配脚部滑动损失轴 ---
                self.foot_slip_loss_ax = axes_flat[6]
                # --- 结束新增 ---
                # (axes_flat[7] 和 axes_flat[8] 未使用)
                print("创建新的损失图窗口 (3x3 布局)...") # 确认打印信息
            except (ValueError, IndexError) as e: # 捕获可能的解包或索引错误
                 print(f"[Error] 初始化子图轴时出错 (预期 3x3): {e}")
                 # 如果解包失败，将所有轴设为 None，避免后续错误
                 self.total_loss_ax = self.keypoint_loss_ax = self.foot_height_ax = None
                 self.contact_loss_ax = self.ankle_target_loss_ax = self.smoothness_loss_ax = None
                 # --- 新增：将新轴也设为 None ---
                 self.foot_slip_loss_ax = None
                 # --- 结束新增 ---
                 plt.close(self.loss_fig) # 关闭可能创建不完整的图
                 self.loss_fig = None
                 return # 无法继续初始化
            # --- 结束确保 --- #
        else:
            # 如果窗口存在，只需清除子图内容
            for ax in self.loss_fig.get_axes():
                ax.cla()
            print("清除现有的损失图子图...") # Debug print
            # 重新获取轴对象，因为它们可能已被清除
            # 确保获取正确数量的轴
            current_axes = self.loss_fig.get_axes()
            # --- 修改：检查 9 个轴 ---
            if len(current_axes) == 9:
                # --- 修改：按展平顺序重新获取轴 --- #
                self.total_loss_ax = current_axes[0]
                self.keypoint_loss_ax = current_axes[1]
                self.foot_height_ax = current_axes[2]
                self.contact_loss_ax = current_axes[3]
                self.ankle_target_loss_ax = current_axes[4]
                self.smoothness_loss_ax = current_axes[5]
                # --- 新增：获取脚部滑动损失轴 ---
                self.foot_slip_loss_ax = current_axes[6]
                # --- 结束新增 ---
            else: # 如果轴数量不匹配，记录警告并尝试重新创建
                 print(f"[Warning] 窗口存在但轴数量 ({len(current_axes)}) 不匹配预期 (9)。可能需要手动重启程序。")
                 # 尝试将现有轴赋给前 N 个属性，其余设为 None
                 try:
                     # --- 修改：按展平顺序尝试赋值 --- #
                     self.total_loss_ax = current_axes[0]
                     self.keypoint_loss_ax = current_axes[1]
                     self.foot_height_ax = current_axes[2]
                     self.contact_loss_ax = current_axes[3]
                     self.ankle_target_loss_ax = current_axes[4]
                     self.smoothness_loss_ax = current_axes[5]
                     # --- 新增：尝试获取脚部滑动损失轴 ---
                     self.foot_slip_loss_ax = current_axes[6] if len(current_axes) > 6 else None
                     # --- 结束新增 ---
                 except IndexError:
                     # 如果连前几个轴都没有，全部设为 None
                     self.total_loss_ax = self.keypoint_loss_ax = self.foot_height_ax = None
                     self.contact_loss_ax = self.ankle_target_loss_ax = self.smoothness_loss_ax = None
                     # --- 新增：将新轴也设为 None ---
                     self.foot_slip_loss_ax = None
                     # --- 结束新增 ---

        # --- 结束修改 --- #

        # --- 新增：在设置标题/标签前检查轴是否有效 --- #
        if self.total_loss_ax is None: # 检查一个轴就够了
            print("[Error] 无法设置损失图，因为轴对象无效。")
            return # 无法继续设置
        # --- 结束新增 --- 

        # 设置总损失图 (位置 0,0)
        self.total_loss_ax.set_xlabel("Iteration")
        self.total_loss_ax.set_ylabel("Total Loss")
        self.total_loss_ax.set_title('Total Optimization Loss')
        self.total_loss_ax.grid(True)

        # 设置关键点损失图 (位置 0,1)
        self.keypoint_loss_ax.set_xlabel("Frame Index")
        self.keypoint_loss_ax.set_ylabel("Keypoint Loss")
        self.keypoint_loss_ax.set_title('Per-Frame Keypoint Loss Distribution')
        self.keypoint_loss_ax.grid(True)

        # 设置脚部高度损失图
        self.foot_height_ax.set_xlabel("Frame Index")
        self.foot_height_ax.set_ylabel("Foot Height Loss")
        self.foot_height_ax.set_title('Per-Frame Foot Height Loss Distribution')
        self.foot_height_ax.grid(True)

        # --- 新增：设置接触损失图 ---
        self.contact_loss_ax.set_xlabel("Frame Index")
        self.contact_loss_ax.set_ylabel("Contact Loss")
        self.contact_loss_ax.set_title('Per-Frame Contact Loss Distribution')
        self.contact_loss_ax.grid(True)
        # --- 结束新增 ---

        # --- 新增：设置脚踝目标损失图 ---
        self.ankle_target_loss_ax.set_xlabel("Frame Index")
        self.ankle_target_loss_ax.set_ylabel("Ankle Target Loss")
        self.ankle_target_loss_ax.set_title('Per-Frame Ankle Target Loss Distribution')
        self.ankle_target_loss_ax.grid(True)
        # --- 结束新增 ---

        # --- 新增：设置平滑损失图 ---
        # --- 新增：检查 smoothness_loss_ax 是否有效 --- #
        if self.smoothness_loss_ax is not None:
            self.smoothness_loss_ax.set_xlabel("Frame Index")
            self.smoothness_loss_ax.set_ylabel("Smoothness Loss")
            self.smoothness_loss_ax.set_title('Per-Frame Smoothness Loss Distribution')
            self.smoothness_loss_ax.grid(True)
        else:
            print("[Warning] 平滑损失轴无效，无法设置其标签和标题。")
        # --- 结束新增 ---

        # --- 新增：设置脚部滑动损失图 ---
        # --- 新增：检查 foot_slip_loss_ax 是否有效 --- #
        if self.foot_slip_loss_ax is not None:
            self.foot_slip_loss_ax.set_xlabel("Frame Index")
            self.foot_slip_loss_ax.set_ylabel("Foot Slip Loss")
            self.foot_slip_loss_ax.set_title('Per-Frame Foot Slip Loss Distribution')
            self.foot_slip_loss_ax.grid(True)
        else:
            print("[Warning] 脚部滑动损失轴无效，无法设置其标签和标题。")
        # --- 结束新增 ---

        # 调整子图间距
        self.loss_fig.tight_layout()

        # 初始化损失数据存储
        self.loss_data = {}
        # --- 新增：为脚部滑动损失添加 Y 轴最大值属性 ---
        self.foot_slip_loss_y_max = 0.1 # 脚部滑动损失的初始Y轴最大值
        # --- 结束新增 ---

    def showLoss(self, iteration, loss_dict, loss_per_frame_np, foot_height_loss_per_frame_np,
                 contact_loss_per_frame_np, ankle_target_loss_per_frame_np,
                 smoothness_loss_per_frame_np, foot_slip_loss_per_frame_np):
        """
        智能显示和绘制损失函数
        Args:
            iteration: 当前迭代次数
            loss_dict: 包含所有损失项的字典，格式为 {'loss_name': loss_value}
            loss_per_frame_np: 每帧关键点差异损失 (NumPy) [N]
            foot_height_loss_per_frame_np: 每帧脚部高度损失 (NumPy) [N]
            contact_loss_per_frame_np: 每帧接触损失 (NumPy) [N]
            ankle_target_loss_per_frame_np: 每帧脚踝目标损失 (NumPy) [N]
            smoothness_loss_per_frame_np: 每帧平滑度损失 (NumPy) [N-1] (将在函数内填充)
            foot_slip_loss_per_frame_np: 每帧脚部滑动损失 (NumPy) [N]
        """
        if self.args.no_show:
            return

        if iteration % self.show_frequence == 0:
            # 更新损失数据
            for name, value in loss_dict.items():
                if name not in self.loss_data:
                    self.loss_data[name] = []
                self.loss_data[name].append(value.item()) # loss_dict 仍然包含 Tensor

            # 更新进度条显示
            loss_str = " ".join([f"{name}: {value.item():.6f}" for name, value in loss_dict.items()])
            if hasattr(self, 'pbar'): # 确保 pbar 存在
                 self.pbar.set_description_str(f"Iter {iteration} {loss_str}")

            # --- 修改：更新五个子图 --- #
            try:
                if self.loss_fig and plt.fignum_exists(self.loss_fig.number):

                    # 更新总损失图
                    total_loss_data_current = self.loss_data.get('Total', [])
                    iterations_so_far = [i * self.show_frequence for i in range(len(total_loss_data_current))]

                    self.total_loss_ax.cla() # 假设轴是有效的，如果窗口存在
                    self.total_loss_ax.plot(iterations_so_far, total_loss_data_current)
                    self.total_loss_ax.set_xlabel("Iteration")
                    self.total_loss_ax.set_ylabel("Total Loss")
                    # --- 修改：简化标题 --- #
                    self.total_loss_ax.set_title('Total Optimization Loss')
                    # --- 结束修改 --- #
                    self.total_loss_ax.grid(True)

                    # 更新关键点损失和脚部高度损失图
                    # --- 修改：直接使用传入的 NumPy 数组 --- #
                    loss_per_frame_plot = loss_per_frame_np
                    foot_height_loss_plot = foot_height_loss_per_frame_np
                    contact_loss_plot = contact_loss_per_frame_np
                    ankle_target_loss_plot = ankle_target_loss_per_frame_np

                    # 平滑损失需要填充
                    smoothness_loss_plot_raw = smoothness_loss_per_frame_np
                    smoothness_loss_plot = np.pad(smoothness_loss_plot_raw, (1, 0), 'constant')
                    # --- 结束修改 --- #

                    # --- 修改：使用 loss_per_frame_plot 的形状 --- #
                    num_frames_plot = loss_per_frame_plot.shape[0]
                    frame_indices = range(num_frames_plot)

                    # 更新关键点损失图
                    self.keypoint_loss_ax.cla() # 假设轴是有效的
                    self.keypoint_loss_ax.plot(frame_indices, loss_per_frame_plot)
                    self.keypoint_loss_ax.set_xlabel("Frame Index")
                    self.keypoint_loss_ax.set_ylabel("Keypoint Loss")
                    # --- 修改：简化标题 --- #
                    self.keypoint_loss_ax.set_title(f'Per-Frame Keypoint Loss - Iter {iteration}')
                    # --- 结束修改 --- #
                    self.keypoint_loss_ax.grid(True)
                    # --- 修改：使用独立的Y轴最大值 --- #
                    self.keypoint_loss_ax.set_ylim(0, self.keypoint_loss_y_max)
                    # --- 结束修改 --- #

                    # 更新脚部高度损失图 (使用 foot_height_loss_plot)
                    self.foot_height_ax.cla() # 假设轴是有效的
                    self.foot_height_ax.plot(frame_indices, foot_height_loss_plot) # 使用修改后的变量
                    self.foot_height_ax.set_xlabel("Frame Index")
                    self.foot_height_ax.set_ylabel("Foot Height Loss")
                    # --- 修改：简化标题 --- #
                    self.foot_height_ax.set_title(f'Per-Frame Foot Height Loss - Iter {iteration}')
                    # --- 结束修改 --- #
                    self.foot_height_ax.grid(True)
                    # --- 修改：使用独立的Y轴最大值 ---
                    self.foot_height_ax.set_ylim(0, self.foot_height_loss_y_max)
                    # --- 结束修改 ---

                    # --- 新增：更新接触损失图 ---
                    self.contact_loss_ax.cla() # 假设轴是有效的
                    self.contact_loss_ax.plot(frame_indices, contact_loss_plot) # 使用 contact_loss_plot
                    self.contact_loss_ax.set_xlabel("Frame Index")
                    self.contact_loss_ax.set_ylabel("Contact Loss")
                    # --- 修改：简化标题 --- #
                    self.contact_loss_ax.set_title(f'Per-Frame Contact Loss - Iter {iteration}')
                    # --- 结束修改 --- #
                    self.contact_loss_ax.grid(True)
                    self.contact_loss_ax.set_ylim(0, self.contact_loss_y_max)
                    # --- 结束新增 ---

                    # --- 新增：更新脚踝目标损失图 ---
                    self.ankle_target_loss_ax.cla() # 假设轴是有效的
                    self.ankle_target_loss_ax.plot(frame_indices, ankle_target_loss_plot) # 使用 ankle_target_loss_plot
                    self.ankle_target_loss_ax.set_xlabel("Frame Index")
                    self.ankle_target_loss_ax.set_ylabel("Ankle Target Loss")
                    # --- 修改：简化标题 --- #
                    self.ankle_target_loss_ax.set_title(f'Per-Frame Ankle Target Loss - Iter {iteration}')
                    # --- 结束修改 --- #
                    self.ankle_target_loss_ax.grid(True)
                    self.ankle_target_loss_ax.set_ylim(0, self.ankle_target_loss_y_max)
                    # --- 结束新增 ---

                    # --- 新增：更新平滑损失图 ---
                    self.smoothness_loss_ax.cla() # 假设轴是有效的
                    self.smoothness_loss_ax.plot(frame_indices, smoothness_loss_plot) # 使用填充后的 smoothness_loss_plot
                    self.smoothness_loss_ax.set_xlabel("Frame Index")
                    self.smoothness_loss_ax.set_ylabel("Smoothness Loss")
                    # --- 修改：简化标题 --- #
                    self.smoothness_loss_ax.set_title(f'Per-Frame Smoothness Loss - Iter {iteration}')
                    # --- 结束修改 --- #
                    self.smoothness_loss_ax.grid(True)
                    self.smoothness_loss_ax.set_ylim(0, self.smoothness_loss_y_max)
                    # --- 结束新增 ---

                    # --- 新增：更新脚部滑动损失图 ---
                    # --- 修改：检查轴是否有效 --- 
                    if self.foot_slip_loss_ax is not None:
                        self.foot_slip_loss_ax.cla() # 假设轴是有效的
                        # --- 修改：直接使用传入的 foot_slip_loss_per_frame_np (已经是 [N] 形状) ---
                        foot_slip_loss_plot = foot_slip_loss_per_frame_np
                        # --- 结束修改 ---
                        self.foot_slip_loss_ax.plot(frame_indices, foot_slip_loss_plot)
                        self.foot_slip_loss_ax.set_xlabel("Frame Index")
                        self.foot_slip_loss_ax.set_ylabel("Foot Slip Loss")
                        self.foot_slip_loss_ax.set_title(f'Per-Frame Foot Slip Loss - Iter {iteration}')
                        self.foot_slip_loss_ax.grid(True)
                        # --- 修改：使用独立的 Y 轴最大值 ---
                        self.foot_slip_loss_ax.set_ylim(0, self.foot_slip_loss_y_max)
                        # --- 结束修改 --- 
                    # --- 结束新增 ---

                    # 调整子图间距并重绘
                    self.loss_fig.tight_layout()
                    self.loss_fig.canvas.draw_idle()

            except AttributeError as attr_e: # 捕获可能的 AttributeError
                 # 如果仍然出现 AttributeError (例如轴是None), 打印警告但继续运行
                 print(f"\n[Warning] 更新损失图时遇到 AttributeError: {attr_e}. 可能窗口已关闭或轴无效。跳过本次绘图。")
            except Exception as plot_e:
                 print(f"\n更新损失图时出错: {plot_e}")

            # 确保事件循环有机会处理绘图
            plt.pause(0.001)

    def euler_to_quat(self, euler):
        """将旋转向量转换为MuJoCo四元数格式，并应用坐标系修正"""
        from scipy.spatial.transform import Rotation as R
        
        # 可能需要的坐标系修正（取决于您的具体情况）
        # 例如，如果需要在x轴旋转90度来对齐坐标系
        correction = R.from_euler('x', 90, degrees=True)
        
        # 应用旋转向量
        rotation = R.from_rotvec(euler)
        
        # 应用修正（如果需要）
        # rotation = correction * rotation
        
        # 获取四元数并转换为MuJoCo顺序
        quat = rotation.as_quat()  # [x,y,z,w]
        return np.array([quat[3], quat[0], quat[1], quat[2]])  # [w,x,y,z]

    def calcKeyPointDiffLoss(self, T1_key_pos, smpl_key_joints):
        """
        计算关键点位置差异的损失
        Args:
            T1_key_pos: 机器人关键点位置 [batch_size, num_frames, num_key_points, 3]
            smpl_key_joints: SMPL关键点位置 [batch_size, num_frames, num_key_points, 3]
        Returns:
            loss: 关键点位置差异损失
        """
        diff = (T1_key_pos - smpl_key_joints)  # 保持3D差异
        diff = diff * self.joint_weights  # 应用权重
        loss = self.mse_loss(diff, torch.zeros_like(diff))  # 使用MSE Loss
        return loss

    def calcSmoothnessLoss(self, T1_key_pos):
        """
        计算相邻帧之间的关节位置变化损失，用于平滑运动
        Args:
            T1_key_pos: 机器人关键点位置 [batch_size, num_frames, num_key_points, 3]
        Returns:
            total_loss: 平滑性损失 (用于优化)
            per_frame_loss: 每帧的平滑性损失 [num_frames-1] (用于可视化)
        """
        # 计算相邻帧之间的位置差
        pos_diff = T1_key_pos[:, 1:] - T1_key_pos[:, :-1] # Shape [B, N-1, K, 3]
        # 计算位置差的范数
        pos_diff_norm = torch.norm(pos_diff, p=2, dim=-1) # Shape [B, N-1, K]

        # --- 新增：计算每帧损失 (跨关键点取平均) ---
        # 假设 batch_size = 1
        if T1_key_pos.shape[0] == 1:
            per_frame_loss = pos_diff_norm.mean(dim=-1).squeeze(0) # Shape [N-1]
        else:
            # (可选) 处理 batch > 1, 例如只记录第一个样本
            per_frame_loss = pos_diff_norm[0].mean(dim=-1) # Shape [N-1]
            # print("Warning: Batch size > 1 in calcSmoothnessLoss, plotting only first sample's smoothness loss.")
        # --- 结束新增 ---

        # 使用MSE损失计算总的平滑性损失 (用于优化)
        total_loss = self.mse_loss(pos_diff_norm, torch.zeros_like(pos_diff_norm))

        # --- 修改：返回总损失和每帧损失 ---
        return total_loss, per_frame_loss.detach().clone()
        # --- 结束修改 ---

    def calcAccelerationLoss(self, T1_key_pos):
        """
        计算加速度损失，直接最小化加速度的大小
        Args:
            T1_key_pos: 机器人关键点位置 [batch_size, num_frames, num_key_points, 3]
            fps: 帧率
        Returns:
            loss: 加速度损失
        """
        # 计算速度
        v = T1_key_pos[:, 1:, :, :] - T1_key_pos[:, 0:-1, :, :]
        # 计算加速度
        a = v[:, 1:, :, :] - v[:, 0:-1, :, :]
        # 计算加速度的范数
        a_norm = torch.norm(a, p=2, dim=-1)
        # 使用MSE损失直接最小化加速度
        loss = self.mse_loss(a_norm, torch.zeros_like(a_norm))
        return loss

    def calcContactLoss(self, T1_joint, fk_return, contact_sequence,
                        left_foot_samples, right_foot_samples, # <-- 新增参数
                        orientation_loss_weight=0.5):
        """
        计算接触损失。
        惩罚接触帧上脚底最低采样点的高度偏差和脚趾的姿态偏差。
        Args:
            T1_joint: 机器人关节位置 [batch_size, num_frames, num_joints, 3]
            fk_return: FK计算结果字典，需要包含 'global_rotation_mat'
            contact_sequence: 接触序列 [num_frames, 2] (左脚, 右脚), 布尔类型
            left_foot_samples: 左脚采样点位置 [batch, frames, num_samples, 3] # <-- 新增参数
            right_foot_samples: 右脚采样点位置 [batch, frames, num_samples, 3] # <-- 新增参数
            orientation_loss_weight: 脚趾姿态损失的权重
        Returns:
            average_loss: 平均接触损失张量 (用于优化)
            per_frame_contact_loss: 每帧接触损失张量 [num_frames] (用于可视化)
        """
        # 检查脚趾索引是否有效 (姿态损失仍需要)
        # 检查 contact_sequence 是否有效
        # 检查 foot_samples 是否有数据 (高度损失需要)
        # --- 修改：加入采样点数据检查 ---
        if self.left_toe_idx == -1 or self.right_toe_idx == -1 or \
           contact_sequence is None or \
           left_foot_samples.numel() == 0 or right_foot_samples.numel() == 0:
            # --- 修改：如果无效，返回 0 损失和空的每帧损失 ---
            num_frames_fallback = T1_joint.shape[1] if T1_joint.numel() > 0 else 0
            empty_per_frame = torch.zeros(num_frames_fallback, device=self.device)
            return torch.tensor(0.0, device=self.device), empty_per_frame
            # --- 结束修改 ---
            # --- 结束修改 ---

        batch_size, num_frames, _, _ = T1_joint.shape

        # 确保 contact_sequence 是布尔张量
        try:
            contact_mask = torch.as_tensor(contact_sequence, dtype=torch.bool, device=self.device)
        except Exception as e:
            print(f"错误：接触序列格式无法转换为布尔张量: {e}")
            # --- 修改：如果无效，返回 0 损失和空的每帧损失 ---
            empty_per_frame = torch.zeros(num_frames, device=self.device)
            return torch.tensor(0.0, device=self.device), empty_per_frame
            # --- 结束修改 ---

        # 调整 contact_mask 帧数以匹配 T1_joint
        if contact_mask.shape[0] != num_frames:
            if contact_mask.shape[0] > num_frames:
                contact_mask = contact_mask[:num_frames]
            elif contact_mask.shape[0] > 0:
                padding = contact_mask[-1:].repeat(num_frames - contact_mask.shape[0], 1)
                contact_mask = torch.cat([contact_mask, padding], dim=0)
            else:
                contact_mask = torch.zeros((num_frames, 2), dtype=torch.bool, device=self.device)

        # 提取接触掩码
        left_contact_mask = contact_mask[:, 0].unsqueeze(0).expand(batch_size, -1) # [B, N]
        right_contact_mask = contact_mask[:, 1].unsqueeze(0).expand(batch_size, -1)# [B, N]
        combined_contact_mask = left_contact_mask | right_contact_mask           # [B, N]
        num_contact_frames = torch.sum(combined_contact_mask) # Scalar total contact frames across batch and time

        # --- 初始化每帧损失 (即使没有接触帧也要初始化) ---
        per_frame_contact_loss = torch.zeros(num_frames, device=self.device)
        # --- 结束初始化 ---

        # 如果没有接触帧，损失为0
        if num_contact_frames == 0:
            # --- 修改：返回 0 损失和零值的每帧损失 ---
            return torch.tensor(0.0, device=self.device), per_frame_contact_loss
            # --- 结束修改 ---


        # --- 高度损失 (基于最低采样点) --- #
        # --- 修改：使用采样点计算高度损失 ---
        left_z_samples = left_foot_samples[..., 2]   # [B, N, num_samples]
        right_z_samples = right_foot_samples[..., 2] # [B, N, num_samples]

        # 找到每个样本、每帧的最低点Z坐标
        min_left_z, _ = torch.min(left_z_samples, dim=-1)  # [B, N]
        min_right_z, _ = torch.min(right_z_samples, dim=-1) # [B, N]

        loss_h_left = torch.zeros_like(min_left_z)
        loss_h_right = torch.zeros_like(min_right_z)

        # 计算接触帧上最低点的高度平方差损失
        loss_h_left[left_contact_mask] = (min_left_z[left_contact_mask] - self.target_foot_height)**2
        loss_h_right[right_contact_mask] = (min_right_z[right_contact_mask] - self.target_foot_height)**2
        # --- 结束修改 ---

        total_height_loss = torch.sum(loss_h_left) + torch.sum(loss_h_right) # Scalar sum over batch and time

        # --- 姿态损失 (脚趾平行地面 - 保持不变) --- #
        global_rot_mat = fk_return['global_rotation_mat'] # [B, N, J, 3, 3]
        left_toe_rot_mat = global_rot_mat[:, :, self.left_toe_idx, :, :]  # [B, N, 3, 3]
        right_toe_rot_mat = global_rot_mat[:, :, self.right_toe_idx, :, :] # [B, N, 3, 3]

        # 脚趾局部Z轴在世界坐标系下的表示 (旋转矩阵的第三列)
        left_world_z_axis = left_toe_rot_mat[:, :, :, 2]  # [B, N, 3]
        right_world_z_axis = right_toe_rot_mat[:, :, :, 2] # [B, N, 3]

        # 世界坐标系Z轴
        target_world_z = torch.tensor([0.0, 0.0, 1.0], device=self.device).view(1, 1, 3)

        # 计算点积 (即世界Z分量)
        left_z_dot = left_world_z_axis[:, :, 2] # [B, N]
        right_z_dot = right_world_z_axis[:, :, 2]# [B, N]

        # 损失 = (1 - Z分量)^2，鼓励Z分量接近1
        loss_o_left = torch.zeros_like(left_z_dot)
        loss_o_right = torch.zeros_like(right_z_dot)

        loss_o_left[left_contact_mask] = (1.0 - left_z_dot[left_contact_mask])**2
        loss_o_right[right_contact_mask] = (1.0 - right_z_dot[right_contact_mask])**2

        total_orientation_loss = torch.sum(loss_o_left) + torch.sum(loss_o_right) # Scalar sum over batch and time

        # --- 合并损失 --- #
        # 对每个接触帧的损失求平均 (用于优化)
        average_loss = (total_height_loss + orientation_loss_weight * total_orientation_loss) / num_contact_frames

        # --- 计算每帧损失 (用于可视化, 假设 batch_size=1) ---
        if batch_size == 1:
            # --- 修改：使用新的 loss_h_left/right 计算 ---
            per_frame_loss_h = (loss_h_left + loss_h_right).squeeze(0) # [N]
            # --- 结束修改 ---
            per_frame_loss_o = (loss_o_left + loss_o_right).squeeze(0) # [N]
            # 计算总的每帧损失
            per_frame_contact_loss = per_frame_loss_h + orientation_loss_weight * per_frame_loss_o # [N]
        elif batch_size > 1:
            # (可选) 处理 batch > 1 的情况, 例如只记录第一个样本
            # --- 修改：使用新的 loss_h_left/right 计算 ---
            per_frame_loss_h_b0 = (loss_h_left[0] + loss_h_right[0]) # [N]
            # --- 结束修改 ---
            per_frame_loss_o_b0 = (loss_o_left[0] + loss_o_right[0]) # [N]
            per_frame_contact_loss = per_frame_loss_h_b0 + orientation_loss_weight * per_frame_loss_o_b0 # [N]
            # print("Warning: Batch size > 1 in calcContactLoss, plotting only first sample's contact loss.")

        # --- 返回总损失和每帧损失 ---
        return average_loss, per_frame_contact_loss.detach().clone()

    def calcAnkleTargetLoss(self, dof_pos_new, dof_pos_target):
        """
        计算优化后的脚踝关节值与目标值的差异损失
        Returns:
            total_loss: 总的平均 MSE 损失 (用于优化)
            per_frame_loss: 每帧的平均 MSE 损失 [num_frames] (用于可视化)
        """
        # 获取脚踝索引 (确保这些名称与你的配置文件一致)
        try:
            left_ankle_pitch_idx = self.robot_pos_names.index('leg_l5_link')
            left_ankle_roll_idx = self.robot_pos_names.index('leg_l6_link')
            right_ankle_pitch_idx = self.robot_pos_names.index('leg_r5_link')
            right_ankle_roll_idx = self.robot_pos_names.index('leg_r6_link')
        except ValueError as e:
            print(f"错误：在 robot_pos_names 中找不到脚踝关节名称: {e}")
            # --- 修改：返回 0 损失和空的每帧损失 ---
            num_frames_fallback = dof_pos_new.shape[1] if dof_pos_new.numel() > 0 else 0
            empty_per_frame = torch.zeros(num_frames_fallback, device=self.device)
            return torch.tensor(0.0, device=self.device), empty_per_frame
            # --- 结束修改 ---

        # 提取对应的关节值
        ankle_indices = [left_ankle_pitch_idx, left_ankle_roll_idx, right_ankle_pitch_idx, right_ankle_roll_idx]
        
        # 从 dof_pos_new 和 dof_pos_target 中安全地获取数据
        # 确保 ankle_indices 中的所有索引都在有效范围内
        max_dof_index = dof_pos_new.shape[2] - 1
        valid_ankle_indices = [idx for idx in ankle_indices if idx <= max_dof_index]
        
        if not valid_ankle_indices:
             print("警告：没有有效的脚踝索引可用于计算损失。")
             # --- 修改：返回 0 损失和空的每帧损失 ---
             num_frames_fallback = dof_pos_new.shape[1] if dof_pos_new.numel() > 0 else 0
             empty_per_frame = torch.zeros(num_frames_fallback, device=self.device)
             return torch.tensor(0.0, device=self.device), empty_per_frame
             # --- 结束修改 ---

        dof_pos_new_ankles = dof_pos_new[:, :, valid_ankle_indices, :]     # Shape [B, N, num_ankles, 1]
        dof_pos_target_ankles = dof_pos_target[:, :, valid_ankle_indices, :] # Shape [B, N, num_ankles, 1]

        # --- 修改：计算每帧损失和总损失 ---
        # 计算每个元素（每个关节、每帧）的平方误差
        squared_error = (dof_pos_new_ankles - dof_pos_target_ankles)**2 # Shape [B, N, num_ankles, 1]

        # 计算每帧的平均损失 (跨关节和最后一个维度)
        # 假设 batch_size = 1
        if dof_pos_new.shape[0] == 1:
            per_frame_loss = squared_error.mean(dim=(2, 3)).squeeze(0) # Shape [N]
        else:
            # (可选) 处理 batch > 1, 例如只记录第一个样本
            per_frame_loss = squared_error[0].mean(dim=(1, 2)) # Shape [N]
            # print("Warning: Batch size > 1 in calcAnkleTargetLoss, plotting only first sample's ankle target loss.")


        # 计算总的平均损失 (跨所有帧和关节)
        total_loss = squared_error.mean() # Scalar

        # 返回总损失和每帧损失
        return total_loss, per_frame_loss.detach().clone()
        # --- 结束修改 ---

    def calcFootHeightLoss(self, left_foot_samples, right_foot_samples):
        """计算脚底采样点高度损失, 并记录每帧损失用于绘图

        Args:
            left_foot_samples: 左脚采样点位置 [batch, frames, num_samples, 3]
            right_foot_samples: 右脚采样点位置 [batch, frames, num_samples, 3]

        Returns:
            loss: 脚底高度损失值
        """
        # --- 修改：初始化记录属性和获取维度信息 ---
        batch_size = left_foot_samples.shape[0] if left_foot_samples.numel() > 0 else (right_foot_samples.shape[0] if right_foot_samples.numel() > 0 else 0)
        num_frames = left_foot_samples.shape[1] if left_foot_samples.numel() > 0 else (right_foot_samples.shape[1] if right_foot_samples.numel() > 0 else 0)

        loss = torch.tensor(0.0, device=self.device)
        # 初始化用于绘图的每帧损失 (确保在任何情况下都初始化)
        self.current_frame_foot_height_loss = torch.zeros(num_frames, device=self.device) if num_frames > 0 else None
        # --- 结束修改 ---

        if left_foot_samples.numel() > 0 and right_foot_samples.numel() > 0:
            # 提取Z坐标
            left_z = left_foot_samples[..., 2]   # [batch, frames, num_samples]
            right_z = right_foot_samples[..., 2] # [batch, frames, num_samples]

            # 计算低于地面的点的惩罚
            # 使用 ReLU 来只惩罚低于地面的点
            penalty_left = torch.relu(-(left_z - self.target_foot_height))**2
            penalty_right = torch.relu(-(right_z - self.target_foot_height))**2

            # --- 新增：计算并记录每帧的平均脚底高度损失 (用于绘图) ---
            # 假设 batch_size 为 1
            if batch_size == 1:
                per_frame_penalty_left = penalty_left.mean(dim=-1).squeeze(0) # [frames]
                per_frame_penalty_right = penalty_right.mean(dim=-1).squeeze(0) # [frames]
                # 平均左右脚的每帧损失
                per_frame_loss_for_plot = (per_frame_penalty_left + per_frame_penalty_right) / 2.0
                self.current_frame_foot_height_loss = per_frame_loss_for_plot.detach().clone() # 存储分离后的副本
            elif batch_size > 1:
                # (可选) 处理 batch_size > 1 的情况，例如只记录第一个样本
                per_frame_penalty_left_b0 = penalty_left[0].mean(dim=-1) # [frames]
                per_frame_penalty_right_b0 = penalty_right[0].mean(dim=-1) # [frames]
                per_frame_loss_for_plot_b0 = (per_frame_penalty_left_b0 + per_frame_penalty_right_b0) / 2.0
                self.current_frame_foot_height_loss = per_frame_loss_for_plot_b0.detach().clone()
                # print("Warning: Batch size > 1 in calcFootHeightLoss, plotting only first sample's foot height loss.")
            # --- 结束新增 ---

            # 计算总损失 (用于优化) - 对所有采样点求和
            loss = torch.sum(penalty_left) + torch.sum(penalty_right)

            # # 可选：根据点数和帧数进行平均，使损失值更稳定
            num_points = (left_foot_samples.shape[0] * left_foot_samples.shape[1] *
                        left_foot_samples.shape[2] * 2)  # *2 是因为有左右两只脚
            if num_points > 0:
                loss = loss / num_points

        return loss

    def retargetAmassData(self):
        """重定向AMASS数据"""
        # 初始化数据存储字典
        data_dump = {}
        
        if self.args.no_show:
            # 如果不需要显示，跳过所有可视化相关的初始化
            self.loss_fig = None
            self.total_loss_ax = None
            self.keypoint_loss_ax = None
            self.foot_height_ax = None
            self.contact_loss_ax = None
            self.ankle_target_loss_ax = None
            self.smoothness_loss_ax = None
            self.foot_slip_loss_ax = None
            self.vis = None
            self.vis_thread = None
            return

        # ---- ADDED CONTACT LOADING HERE ---- #
        if not os.path.exists(self.contact_file_path):
            raise FileNotFoundError(f"接触文件不存在: {self.contact_file_path}")
            
        try:
            with open(self.contact_file_path, 'r') as f:
                contact_data = json.load(f)
            # 验证键
            if all(k in contact_data for k in ['contacts', 'overrides', 'height_threshold', 'velocity_threshold']):
                self.contact_sequence = np.array(contact_data['contacts'], dtype=bool)
                print(f"成功加载接触序列: {self.contact_file_path}")
            else:
                raise ValueError(f"接触文件 {self.contact_file_path} 缺少必要的键")
        except json.JSONDecodeError:
            raise ValueError(f"无法解码接触文件 JSON: {self.contact_file_path}")
        except Exception as e:
            raise RuntimeError(f"加载接触文件时出错 {self.contact_file_path}: {e}")
        # ---- END CONTACT LOADING ---- #

        for data_key in self.pbar:  # 遍历每个AMASS数据键，处理每个数据项。
            # 保存当前数据键用于绘图
            self.current_data_key = data_key

            # 初始化可视化模块
            self.initVis()
            # 初始化机器人初值和smpl动作位置，
            dof_pos, pose_aa_walk_robot, gt_root_rot,root_trans_offset,smpl_key_joints,smpl_joints_scaled = self.caluSMPL2Robot(data_key)

            # 保存关键点数据用于绘图
            self.smpl_key_joints = smpl_key_joints

            # 获取脚踝欧拉角
            left_ankle_euler_xyz, right_ankle_euler_xyz = self.getAnkleEulerAngles(pose_aa_walk_robot)

            # 使用关节名称来索引，而不是直接使用数字
            # 从T1_retarget_motion_lib_config.py中获取关节名称
            left_ankle_pitch_idx = self.robot_pos_names.index('leg_l5_link')  # 左脚踝pitch
            left_ankle_roll_idx = self.robot_pos_names.index('leg_l6_link')   # 左脚踝roll
            right_ankle_pitch_idx = self.robot_pos_names.index('leg_r5_link') # 右脚踝pitch
            right_ankle_roll_idx = self.robot_pos_names.index('leg_r6_link')  # 右脚踝roll

            # 使用关节名称来初始化脚踝关节
            dof_pos[0, :, left_ankle_pitch_idx, 0] = left_ankle_euler_xyz[:, 1]  # 左脚踝pitch (Y轴旋转)
            dof_pos[0, :, left_ankle_roll_idx, 0] = left_ankle_euler_xyz[:, 0]  # 左脚踝roll (X轴旋转)
            dof_pos[0, :, right_ankle_pitch_idx, 0] = right_ankle_euler_xyz[:, 1]  # 右脚踝pitch (Y轴旋转)
            dof_pos[0, :, right_ankle_roll_idx, 0] = right_ankle_euler_xyz[:, 0]  # 右脚踝roll (X轴旋转)

            # 保存root_trans_offset用于可视化
            # self.root_trans_offset = root_trans_offset # No longer needed as optimization variable

            # 创建优化器
            dof_pos_new = Variable(dof_pos.clone(), requires_grad=True)  # 可优化的自由度变量
            # root_rot_new = gt_root_rot.clone() # Original - no optimization
            # root_rot_new = Variable(gt_root_rot.clone(), requires_grad=True) # Optimize root rotation
            root_rot_new = gt_root_rot.clone() # No optimization for root rotation
            root_trans_offset_new = Variable(root_trans_offset.clone(), requires_grad=True) # Optimize root translation

            # --- 修正优化器参数逻辑 ---
            params_to_optimize = [{'params': dof_pos_new, 'lr': 0.02}]
            if self.optimize_root:
                root_rot_new = Variable(gt_root_rot.clone(), requires_grad=True) # Optimize root rotation
                root_trans_offset_new = Variable(root_trans_offset.clone(), requires_grad=True) # Optimize root translation
                params_to_optimize.extend([
                    {'params': root_rot_new, 'lr': 0.005},
                    {'params': root_trans_offset_new, 'lr': 0.005}
                ])
            else:
                # 如果不优化根坐标，则保持其为固定张量
                root_rot_new = gt_root_rot.clone()
                root_trans_offset_new = root_trans_offset.clone()
            # --- 结束修正 ---

            optimizer_pose = torch.optim.Adam(params_to_optimize)

            # --- 新增：初始化当前 data_key 的总损失数据列表 ---
            total_loss_data_current = []
            self.loss_data = {} # 重置用于 showLoss 文本的数据

            # --- 新增：在循环开始前获取或创建图表窗口 ---
            if not self.args.no_show:
                # --- 修改：改为调用 initVis 来获取或创建窗口 ---
                self.initVis()
                # --- 结束修改 ---

                try:
                    # 计算初始损失范围，并设置独立的 y_max
                    with torch.no_grad():
                        # 计算初始的T1关节位置
                        initial_pose_aa = self.calcAA(dof_pos, gt_root_rot)
                        initial_fk_return = self.Humanoid_fk.fk_batch(initial_pose_aa, root_trans_offset[None,], return_full=True)
                        initial_T1_joint_dump = initial_fk_return['global_translation'][:, :, :].clone()
                        initial_T1_joint = self.calcJointPosition(initial_fk_return)
                        initial_T1_key_pos = initial_T1_joint[:, :, self.T1_joint_pick_idx]

                        # 计算初始关键点损失
                        initial_keypoint_loss = torch.norm(initial_T1_key_pos - smpl_key_joints, p=2, dim=-1).mean(dim=-1).squeeze().cpu().numpy()

                        # 计算初始脚部高度损失
                        initial_left_samples, initial_right_samples = self.sample_foot_points(initial_fk_return, initial_T1_joint_dump)
                        # 需要计算每帧的平均损失以进行可视化
                        num_samples_per_foot = initial_left_samples.shape[2] if initial_left_samples.numel() > 0 else 0
                        if num_samples_per_foot > 0:
                             left_z = initial_left_samples[..., 2]
                             right_z = initial_right_samples[..., 2]
                             penalty_left = torch.relu(-(left_z - self.target_foot_height))**2
                             penalty_right = torch.relu(-(right_z - self.target_foot_height))**2
                             # 平均到每个采样点，然后乘以采样点数量得到每帧总惩罚，再除以2（双脚）得到平均每只脚的惩罚
                             initial_foot_height_loss = (torch.sum(penalty_left, dim=-1) + torch.sum(penalty_right, dim=-1)) / (num_samples_per_foot * 2) # Shape [B, N]
                             initial_foot_height_loss = initial_foot_height_loss.squeeze().cpu().numpy() # Shape [N]
                        else:
                             initial_foot_height_loss = np.zeros_like(initial_keypoint_loss)

                        # --- 新增：计算初始接触损失和脚踝目标损失 (用于确定Y轴范围) ---
                        _, initial_per_frame_contact_loss = self.calcContactLoss(initial_T1_joint_dump, initial_fk_return, self.contact_sequence, initial_left_samples, initial_right_samples)
                        _, initial_per_frame_ankle_target_loss = self.calcAnkleTargetLoss(dof_pos, dof_pos) # 初始时 dof_pos_new = dof_pos

                        initial_per_frame_contact_loss_np = initial_per_frame_contact_loss.cpu().numpy()
                        initial_per_frame_ankle_target_loss_np = initial_per_frame_ankle_target_loss.cpu().numpy()
                        # --- 结束新增 ---

                        # --- 新增：计算初始脚部滑动损失 ---
                        _, initial_per_frame_foot_slip_loss = self.calcFootSlipLoss(initial_T1_joint_dump, self.contact_sequence)
                        initial_per_frame_foot_slip_loss_np = initial_per_frame_foot_slip_loss.cpu().numpy()
                        # --- 结束新增 ---


                        # --- 修改：设置所有独立的 y_max ---
                        self.keypoint_loss_y_max = np.max(initial_keypoint_loss) * 1.1
                        self.foot_height_loss_y_max = np.max(initial_foot_height_loss) * 1.1
                        self.contact_loss_y_max = np.max(initial_per_frame_contact_loss_np) * 1.1
                        self.ankle_target_loss_y_max = np.max(initial_per_frame_ankle_target_loss_np) * 1.1
                        # --- 新增：设置脚部滑动损失的 y_max ---
                        self.smoothness_loss_y_max = 0.1 # Placeholder, need to calculate if desired
                        self.foot_slip_loss_y_max = np.max(initial_per_frame_foot_slip_loss_np) * 1.1
                        # --- 结束新增 ---

                        # --- 新增：避免 y 轴范围为 0 ---
                        if self.keypoint_loss_y_max < 1e-6: self.keypoint_loss_y_max = 0.1
                        if self.foot_height_loss_y_max < 1e-6: self.foot_height_loss_y_max = 0.1
                        if self.contact_loss_y_max < 1e-6: self.contact_loss_y_max = 0.1
                        if self.ankle_target_loss_y_max < 1e-6: self.ankle_target_loss_y_max = 0.1
                        # --- 新增：避免脚部滑动 y 轴为 0 ---
                        if self.foot_slip_loss_y_max < 1e-6: self.foot_slip_loss_y_max = 0.1
                        # --- 结束新增 ---

                    plt.pause(0.1)  # 确保窗口都显示出来

                except Exception as plot_e:
                    print(f"创建或更新图表时出错: {plot_e}")
            # --- 结束：初始化图表 ---

            for iteration in range(self.iterations):
                # 数据后处理，平滑数据，限制关节超限位数据，为选定关键赋值
                dof_pos_new.data.clamp_(self.Humanoid_fk.joints_range[:, 0, None], self.Humanoid_fk.joints_range[:, 1, None])
                # 计算fk
                pose_aa_T1_new = self.calcAA(dof_pos_new, root_rot_new)
                fk_return = self.Humanoid_fk.fk_batch(pose_aa_T1_new, root_trans_offset_new[None,], return_full=True)
                T1_joint_dump = fk_return['global_translation'][:, :, :].clone()
                T1_joint = self.calcJointPosition(fk_return)
                # 取关键点,并计算差异
                T1_key_pos = T1_joint[:, :, self.T1_joint_pick_idx]

                # 保存关键点数据用于绘图
                self.T1_key_pos = T1_key_pos

                # --- 修改：在计算损失前先计算采样点 ---
                left_foot_samples, right_foot_samples = self.sample_foot_points(fk_return, T1_joint_dump)
                # --- 结束修改 ---

                # 计算各项损失
                loss_g = 2.0 * self.calcKeyPointDiffLoss(T1_key_pos, smpl_key_joints)
                # --- 修改：接收每帧平滑度损失 ---
                loss_smooth_total, per_frame_smoothness_loss = self.calcSmoothnessLoss(T1_key_pos)
                loss_smooth = 1.0 * loss_smooth_total # 应用权重到总损失
                # --- 结束修改 ---
                loss_acc = 0.0 * self.calcAccelerationLoss(T1_key_pos)

                # --- 修改：调用 calcContactLoss 时传入采样点 ---
                contact_loss_weight = 0.1 # 可调整接触损失权重
                loss_contact, per_frame_contact_loss = self.calcContactLoss(
                    T1_joint_dump, fk_return, self.contact_sequence,
                    left_foot_samples, right_foot_samples, # <-- 传入采样点
                    orientation_loss_weight=0.1 # <-- 可选：调整姿态权重
                )
                loss_contact *= contact_loss_weight
                # --- 结束修改 ---

                ankle_target_loss_weight = 0.1
                loss_ankle_target, per_frame_ankle_target_loss = self.calcAnkleTargetLoss(dof_pos_new, dof_pos)
                loss_ankle_target *= ankle_target_loss_weight # 应用权重到总损失

                # --- 新增：计算脚底高度损失 ---
                foot_height_loss_weight = 1.0 # 新增：脚底高度损失权重
                loss_foot_height = foot_height_loss_weight * self.calcFootHeightLoss(left_foot_samples, right_foot_samples) # self.current_frame_foot_height_loss 在此函数内更新

                # --- 新增：计算脚部滑动损失 ---
                foot_slip_loss_weight = 0.5 # 新增：脚部滑动损失权重 (可调整)
                loss_foot_slip, per_frame_foot_slip_loss = self.calcFootSlipLoss(T1_joint_dump, self.contact_sequence)
                loss_foot_slip *= foot_slip_loss_weight # 应用权重到总损失
                # --- 结束新增 ---

                # --- 调用函数计算根节点优化损失 ---
                loss_root_trans_w, loss_root_rot_w = self.calcRootOptimizationLoss(
                    root_trans_offset_new, root_trans_offset,
                    root_rot_new, gt_root_rot
                ) if self.optimize_root else (torch.tensor(0.0, device=self.device), torch.tensor(0.0, device=self.device))
                 # --- 结束调用 ---

                # --- 新增：保存其他每帧损失供 showLoss 使用 ---
                self.current_frame_contact_loss = per_frame_contact_loss
                self.current_frame_ankle_target_loss = per_frame_ankle_target_loss
                self.current_frame_smoothness_loss = per_frame_smoothness_loss
                # --- 保存每帧脚部滑动损失 ---
                self.current_frame_foot_slip_loss = per_frame_foot_slip_loss
                # --- 结束新增 ---

                # --- 应用根节点损失权重 ---
                root_trans_weight = 0.1 # 根节点平移损失权重 (可调整)
                root_rot_weight = 0.1   # 根节点旋转损失权重 (可调整)
                loss_root_trans_w = root_trans_weight * loss_root_trans_w
                loss_root_rot_w = root_rot_weight * loss_root_rot_w
                # --- 结束应用权重 ---

                # 总损失 = 关键点差异损失 + 平滑性损失 + 加速度损失 + 接触损失 + 脚踝目标损失 + 脚底高度损失 + 脚部滑动损失 + 根节点损失
                loss = (loss_g + loss_smooth + loss_acc + loss_contact + loss_ankle_target +
                       loss_foot_height + loss_foot_slip +
                       loss_root_trans_w + loss_root_rot_w) # <-- 添加了根节点损失

                # 使用字典形式传递所有损失项 (传递加权后的总损失)
                loss_dict = {
                    'Total': loss,
                    'KeyPoint': loss_g,
                    'Smooth': loss_smooth,
                    'Acceleration': loss_acc,
                    'Contact': loss_contact,
                    'AnkleTarget': loss_ankle_target,
                    'FootHeight': loss_foot_height,
                    'FootSlip': loss_foot_slip, # <-- 添加了 FootSlip
                    'RootTrans': loss_root_trans_w, # <-- 添加了根节点平移损失
                    'RootRot': loss_root_rot_w      # <-- 添加了根节点旋转损失
                }
                # --- 修改：计算每帧损失并转换为 NumPy 传递给 showLoss --- #
                with torch.no_grad():
                    loss_per_frame_for_plot_np = torch.norm(T1_key_pos - smpl_key_joints, p=2, dim=-1).mean(dim=-1).squeeze().cpu().numpy()
                    foot_height_loss_np = self.current_frame_foot_height_loss.cpu().numpy() if self.current_frame_foot_height_loss is not None else np.zeros(self.num_frame) # Handle None case
                    contact_loss_np = self.current_frame_contact_loss.cpu().numpy()
                    ankle_target_loss_np = self.current_frame_ankle_target_loss.cpu().numpy()
                    smoothness_loss_np = self.current_frame_smoothness_loss.cpu().numpy()
                    # --- 新增：转换脚部滑动损失 ---
                    foot_slip_loss_np = self.current_frame_foot_slip_loss.cpu().numpy()
                    # --- 结束新增 ---

                # --- 修改：传递脚部滑动损失给 showLoss ---
                self.showLoss(iteration, loss_dict, loss_per_frame_for_plot_np,
                              foot_height_loss_np,
                              contact_loss_np,
                              ankle_target_loss_np,
                              smoothness_loss_np,
                              foot_slip_loss_np) # <-- 添加了 foot_slip_loss_np
                # --- 结束修改 ---

                # 基于梯度下降的反向传播
                optimizer_pose.zero_grad()
                if self.iterations > 1:
                    loss.backward()
                    optimizer_pose.step()

                # dof_pos_new.data = gaussian_filter_1d_batch(dof_pos_new.data.squeeze().transpose(1, 0)[None,], self.kernel_size, self.sigma).transpose(2, 1)[..., None]
                # root_rot_new.data = gaussian_filter_1d_batch(root_rot_new.data.transpose(1, 0)[None,], self.kernel_size, self.sigma).transpose(2, 1).squeeze(0)
                # dof_pos_new.data[:,:,[2,3,6,8,9,12]] = 0
                # dof_pos_new.data[:,:,[6,12]] = 0
                # dof_pos_new.data[:, :, [0, 1, 6, 7, 5, 11]] = dof_pos[:, :, [0, 1, 6, 7, 5, 11]]
                # 脚踝肉roll直接赋来自smpl的roll角度
                # dof_pos_new.data[:, :, [6, 12]] = dof_pos[:, :, [6, 12]]
                # dof_pos_new.data[:, :, [0]] = dof_pos[:, :, [0]]
                # dof_pos_new.data[:, :, 13:] = dof_pos[:, :, 13:]

            # 保存优化后的结果为类属性，供visRefPoint使用
            self.dof_pos_new = dof_pos_new
            self.root_rot_new = root_rot_new
            # Update self.root_trans_offset with the optimized value for visualization
            self.root_trans_offset = root_trans_offset_new.data if self.optimize_root else root_trans_offset_new

            # 保存T1的源数据
            dof_pos_new.data.clamp_(self.Humanoid_fk.joints_range[:, 0, None], self.Humanoid_fk.joints_range[:, 1, None])  # 限制关节角在合适的范围内
            pose_aa_T1_new = self.calcAA(dof_pos_new, root_rot_new)
            Humanoid_fk_dump = Humanoid_Batch(self.motion_lib_cfg.mjcf_file, self.motion_lib_cfg.extend_node_dict, device=self.device)
            fk_return_dump = Humanoid_fk_dump.fk_batch(pose_aa_T1_new, (root_trans_offset_new.data if self.optimize_root else root_trans_offset_new)[None,], return_full=True)  # Use optimized root_trans_offset_new if available
            T1_joint_dump = fk_return_dump['global_translation'][:, :, :].clone()
            root_trans_offset_dump = (root_trans_offset_new.data if self.optimize_root else root_trans_offset_new).clone() # 保存优化后的根关节位移

            T1_joint = self.calcJointPosition(fk_return_dump).clone()
            # 可视化机器人点云图
            # 计算最终优化后的 T1 关键点位置，以便传递给可视化函数
            final_T1_key_pos = T1_joint[:, :, self.T1_joint_pick_idx]
            # 计算最终每帧损失
            with torch.no_grad():
                self.final_frame_losses = torch.norm(final_T1_key_pos - smpl_key_joints, p=2, dim=-1).mean(dim=-1).squeeze().cpu().numpy()

            # --- 新增：计算脚底采样点 ---
            # 使用最终优化后的 FK 结果和 *原始* 关节位置计算采样点
            left_foot_samples, right_foot_samples = self.sample_foot_points(fk_return_dump, T1_joint_dump)
            # --- 结束新增 ---

            # --- 修改：传递采样点给可视化函数 ---
            # 传递原始位置(dump)、修正后位置(T1_joint)、采样点
            self.mujoco_viewer.visRefPoint(smpl_joints_scaled, T1_joint_dump, T1_joint, smpl_key_joints, 
                                      contact_sequence=self.contact_sequence, root_trans_offset=root_trans_offset_dump, 
                                      dof_pos_new=dof_pos_new, root_rot_new=root_rot_new, 
                                      final_frame_losses=self.final_frame_losses, 
                                      left_foot_samples=left_foot_samples, right_foot_samples=right_foot_samples)
            # --- 结束修改 ---

        data_dump[data_key] = {
            "root_trans_offset": root_trans_offset_dump[:, :].cpu().detach().numpy(),
            "pose_aa": pose_aa_T1_new.squeeze(0)[:, :, :].cpu().detach().numpy(),
            "dof_pos": dof_pos_new.squeeze(0)[:, :, :].detach().cpu().numpy(),
            # "root_rot": sRot.from_rotvec(root_rot_new.data[:, :].cpu().numpy()).as_quat(), # Original, using optimized data
            "root_rot": sRot.from_rotvec((root_rot_new.data if self.optimize_root else root_rot_new)[:, :].cpu().detach().numpy()).as_quat(), # Save optimized root rotation
            "global_translation": T1_joint_dump.squeeze(0)[:, :, :].cpu().detach().numpy(),
            "fps": self.fps,
            'contact_flags': self.contact_sequence
        }

        current_date_time = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
        # 检查文件夹是否存在
        if not os.path.exists(self.args.dataset_path):
            os.makedirs(self.args.dataset_path)
        # 创建保存目录
        save_dir = os.path.join(self.args.dataset_path, self.args.task.split('_')[0])
        os.makedirs(save_dir, exist_ok=True)
        # 构建完整的文件路径
        root_path = os.path.join(save_dir, current_date_time + ".pkl")
        # 保存数据
        joblib.dump(data_dump, root_path)
        # joblib.dump(T1_data,os.path.join(root_path, args.file_name+"_wo_smpl.pkl"))
        print("All data has been saved in :", root_path)

    def calcTPoseData(self):
        """计算T-pose模式下的参考数据"""
        # 创建T-pose的轴角数据
        t_pose_aa = torch.zeros((1, 72), device=self.device)
        rotvec = sRot.from_quat([0.5, 0.5, 0.5, 0.5]).as_rotvec()
        t_pose_aa[:, :3] = torch.tensor(rotvec, device=self.device)

        # 扩展维度以匹配批处理要求
        t_pose_aa = t_pose_aa.unsqueeze(0)  # [1, 1, 72]

        # 计算SMPL T-pose关节位置
        t_pose_verts, t_pose_joints = self.smpl_parser_n.get_joints_verts(
            t_pose_aa.squeeze(0),
            self.shape_new,
            self.t_pose_trans_offset[0].unsqueeze(0)
        )

        # 应用缩放
        t_pose_root_pos = t_pose_joints[:, 0:1, :]
        t_pose_root_pos_scaled = t_pose_root_pos * self.scale_leg
        t_pose_joints_centered = t_pose_joints - t_pose_root_pos

        weight = torch.ones_like(t_pose_joints_centered)
        weight[:, 0:1, :] *= self.scale
        weight[:, 1:3, :] *= self.scale_leg
        weight[:, 3:4, :] *= self.scale
        weight[:, 4:6, :] *= self.scale_leg
        weight[:, 6:7, :] *= self.scale
        weight[:, 7:9, :] *= self.scale_leg
        weight[:, 9:10, :] *= self.scale
        weight[:, 10:12, :] *= self.scale_leg
        weight[:, 12:, :] *= self.scale

        t_pose_joints_scaled = t_pose_joints_centered * weight + t_pose_root_pos_scaled
        smpl_t_pose = t_pose_joints_scaled[:, self.smpl_joint_pick_idx2, :]
        current_smpl_data = smpl_t_pose.cpu().detach().numpy()

        # 创建机器人T-pose数据
        t_pose_dof = torch.zeros((1, 1, self.Humanoid_fk.joints_axis.shape[1], 1), device=self.device)
        t_pose_root_rot = torch.zeros((1, 3), device=self.device)
        t_pose_aa = self.calcAA(t_pose_dof, t_pose_root_rot)
        t_pose_root_trans = self.robot_t_pose_trans_offset.unsqueeze(0)

        # 计算机器人T-pose FK
        Humanoid_fk_t_pose = Humanoid_Batch(
            self.motion_lib_cfg.mjcf_file,
            self.motion_lib_cfg.extend_node_dict,
            device=self.device
        )
        
        fk_return_t_pose = Humanoid_fk_t_pose.fk_batch(
            t_pose_aa,
            t_pose_root_trans,
            return_full=True
        )
        
        current_T1_data = fk_return_t_pose['global_translation'].clone()
        current_T1_data_bias = self.calcJointPosition(fk_return_t_pose).clone()

        current_T1_data = current_T1_data.squeeze(0).cpu().detach().numpy()
        current_T1_data_bias = current_T1_data_bias.squeeze(0).cpu().detach().numpy()
        
        if len(current_smpl_data.shape) == 2:
            current_smpl_data = current_smpl_data.reshape(1, -1, 3)
            
        return current_smpl_data, current_T1_data, current_T1_data_bias

    # --- 新增：脚底采样点计算方法 ---
    def sample_foot_points(self, fk_return, T1_joint):
        """
        在左右脚底周围采样点。
        Args:
            fk_return: FK计算结果字典，需要包含 'global_rotation_mat'
            T1_joint: 机器人关节全局位置 [batch_size, num_frames, num_joints, 3]
        Returns:
            left_foot_samples, right_foot_samples: 左右脚采样点位置
            [batch_size, num_frames, num_sample_points, 3] or (None, None) if indices invalid or data empty
        """
        if self.left_toe_idx == -1 or self.right_toe_idx == -1 or T1_joint.numel() == 0 or fk_return['global_rotation_mat'].numel() == 0:
            # 返回空的 Tensor 或 None，确保调用者能处理
            # print("Warning: Cannot sample foot points due to invalid index or empty FK/joint data.")
            empty_tensor = torch.empty((T1_joint.shape[0], T1_joint.shape[1], 0, 3), device=T1_joint.device, dtype=T1_joint.dtype)
            return empty_tensor, empty_tensor


        batch_size, num_frames, num_joints, _ = T1_joint.shape
        device = T1_joint.device

        # --- 修改：直接使用预定义的局部偏移列表 --- 
        # 从 self.foot_sample_points_local_offsets 获取偏移量
        # offsets_list = self.foot_sample_points_local_offsets 
        # --- 修改：从 motion_lib_cfg 获取偏移列表 --- 
        offsets_list = self.motion_lib_cfg.foot_sample_points_local_offsets
        # --- 结束修改 ---

        offsets = torch.tensor(offsets_list, device=device, dtype=T1_joint.dtype) # [num_sample_points, 3]
        num_sample_points = offsets.shape[0]

        # 获取脚趾关节的全局旋转矩阵和位置
        global_rot_mat = fk_return['global_rotation_mat'] # [B, N, J, 3, 3]
        
        # 安全索引检查
        if self.left_toe_idx >= num_joints or self.right_toe_idx >= num_joints:
             print(f"Error: Toe indices ({self.left_toe_idx}, {self.right_toe_idx}) out of bounds for num_joints ({num_joints}).")
             empty_tensor = torch.empty((batch_size, num_frames, 0, 3), device=device, dtype=T1_joint.dtype)
             return empty_tensor, empty_tensor

        left_toe_rot = global_rot_mat[:, :, self.left_toe_idx, :, :]  # [B, N, 3, 3]
        right_toe_rot = global_rot_mat[:, :, self.right_toe_idx, :, :] # [B, N, 3, 3]
        left_toe_pos = T1_joint[:, :, self.left_toe_idx, :]  # [B, N, 3]
        right_toe_pos = T1_joint[:, :, self.right_toe_idx, :] # [B, N, 3]

        # 扩展偏移量以进行批量计算
        # offsets shape: [num_sample_points, 3] -> [1, 1, num_sample_points, 3, 1]
        expanded_offsets = offsets.view(1, 1, num_sample_points, 3, 1).expand(batch_size, num_frames, -1, -1, -1)

        # 将局部偏移旋转到世界坐标系
        # left_toe_rot: [B, N, 3, 3] -> [B, N, 1, 3, 3]
        # right_toe_rot: [B, N, 3, 3] -> [B, N, 1, 3, 3]
        # rotated_offsets: [B, N, num_sample_points, 3, 1]
        left_rotated_offsets = torch.matmul(left_toe_rot.unsqueeze(2), expanded_offsets).squeeze(-1) # [B, N, num_sample_points, 3]
        right_rotated_offsets = torch.matmul(right_toe_rot.unsqueeze(2), expanded_offsets).squeeze(-1) # [B, N, num_sample_points, 3]

        # 添加到脚趾全局位置
        # left_toe_pos: [B, N, 3] -> [B, N, 1, 3]
        # right_toe_pos: [B, N, 3] -> [B, N, 1, 3]
        left_foot_samples = left_toe_pos.unsqueeze(2) + left_rotated_offsets    # [B, N, num_sample_points, 3]
        right_foot_samples = right_toe_pos.unsqueeze(2) + right_rotated_offsets # [B, N, num_sample_points, 3]

        return left_foot_samples, right_foot_samples
    # --- 结束新增 ---

    def calcFootSlipLoss(self, T1_joint, contact_sequence):
        """
        计算脚部滑动损失，惩罚接触地面时脚趾在XY平面的移动。
        Args:
            T1_joint: 机器人关节位置 [batch_size, num_frames, num_joints, 3]
            contact_sequence: 接触序列 [num_frames, 2] (左脚, 右脚), 布尔类型
        Returns:
            average_loss: 平均脚部滑动损失张量 (用于优化)
            per_frame_slip_loss: 每帧脚部滑动损失张量 [num_frames] (用于可视化)
        """
        # 检查脚趾索引和接触序列是否有效
        if self.left_toe_idx == -1 or self.right_toe_idx == -1 or contact_sequence is None:
            num_frames_fallback = T1_joint.shape[1] if T1_joint.numel() > 0 else 0
            empty_per_frame = torch.zeros(num_frames_fallback, device=self.device)
            return torch.tensor(0.0, device=self.device), empty_per_frame

        batch_size, num_frames, _, _ = T1_joint.shape

        # 确保 contact_sequence 是布尔张量
        try:
            contact_mask = torch.as_tensor(contact_sequence, dtype=torch.bool, device=self.device)
        except Exception as e:
            print(f"错误：接触序列格式无法转换为布尔张量: {e}")
            empty_per_frame = torch.zeros(num_frames, device=self.device)
            return torch.tensor(0.0, device=self.device), empty_per_frame

        # 调整 contact_mask 帧数以匹配 T1_joint
        if contact_mask.shape[0] != num_frames:
            if contact_mask.shape[0] > num_frames:
                contact_mask = contact_mask[:num_frames]
            elif contact_mask.shape[0] > 0:
                padding = contact_mask[-1:].repeat(num_frames - contact_mask.shape[0], 1)
                contact_mask = torch.cat([contact_mask, padding], dim=0)
            else:
                contact_mask = torch.zeros((num_frames, 2), dtype=torch.bool, device=self.device)

        # 提取脚趾 XY 位置
        left_toe_xy = T1_joint[:, :, self.left_toe_idx, :2]  # [B, N, 2]
        right_toe_xy = T1_joint[:, :, self.right_toe_idx, :2] # [B, N, 2]

        # 计算相邻帧的位置差
        delta_left_xy = left_toe_xy[:, 1:] - left_toe_xy[:, :-1]   # [B, N-1, 2]
        delta_right_xy = right_toe_xy[:, 1:] - right_toe_xy[:, :-1] # [B, N-1, 2]

        # 获取接触掩码 (需要 t 和 t-1 都接触才计算滑动)
        # contact_mask_t: [N, 2] -> [B, N-1, 2]
        contact_mask_t = contact_mask[1:].unsqueeze(0).expand(batch_size, -1, -1)
        # contact_mask_tm1: [N, 2] -> [B, N-1, 2]
        contact_mask_tm1 = contact_mask[:-1].unsqueeze(0).expand(batch_size, -1, -1)
        
        # 左右脚的持续接触掩码 [B, N-1]
        left_steady_contact = contact_mask_t[:, :, 0] & contact_mask_tm1[:, :, 0]
        right_steady_contact = contact_mask_t[:, :, 1] & contact_mask_tm1[:, :, 1]

        # 初始化损失
        loss_slip_left = torch.zeros_like(delta_left_xy[..., 0])  # [B, N-1]
        loss_slip_right = torch.zeros_like(delta_right_xy[..., 0]) # [B, N-1]

        # 计算滑动损失 (平方范数)
        # delta_left_xy[left_steady_contact] -> [num_contacting_frames_left, 2]
        # torch.norm(..., p=2, dim=-1)**2 -> [num_contacting_frames_left]
        loss_slip_left[left_steady_contact] = torch.norm(delta_left_xy[left_steady_contact], p=2, dim=-1)**2
        loss_slip_right[right_steady_contact] = torch.norm(delta_right_xy[right_steady_contact], p=2, dim=-1)**2

        total_slip_loss = torch.sum(loss_slip_left) + torch.sum(loss_slip_right) # Scalar sum over batch and time
        num_slip_contributions = torch.sum(left_steady_contact) + torch.sum(right_steady_contact) # Scalar total contributing frames/feet

        # 计算平均损失 (用于优化)
        if num_slip_contributions > 0:
            average_loss = total_slip_loss / num_slip_contributions
        else:
            average_loss = torch.tensor(0.0, device=self.device)

        # --- 计算每帧损失 (用于可视化, 假设 batch_size=1) ---
        per_frame_slip_loss = torch.zeros(num_frames, device=self.device)
        if batch_size == 1:
            # 将 N-1 的损失映射回 N 帧 (损失归属于发生位移的 't' 帧)
            # per_frame_slip_loss[1:] accumulates loss from frame t-1 to t
            per_frame_slip_loss[1:] = loss_slip_left.squeeze(0) + loss_slip_right.squeeze(0) # [N-1] -> [N] (index 0 is zero)
        elif batch_size > 1:
            # (可选) 处理 batch > 1 的情况, 例如只记录第一个样本
            per_frame_slip_loss_b0 = loss_slip_left[0] + loss_slip_right[0] # [N-1]
            per_frame_slip_loss[1:] = per_frame_slip_loss_b0
            # print("Warning: Batch size > 1 in calcFootSlipLoss, plotting only first sample's slip loss.")

        return average_loss, per_frame_slip_loss.detach().clone()

    def calcRootOptimizationLoss(self, root_trans_offset_new, root_trans_offset, root_rot_new, gt_root_rot):
        """
        计算根节点优化损失，仅当 optimize_root=True 时调用。
        Args:
            root_trans_offset_new: 优化后的根节点平移 [batch_size, 3]
            root_trans_offset: 初始根节点平移 [batch_size, 3]
            root_rot_new: 优化后的根节点旋转 [batch_size, 3]
            gt_root_rot: 初始根节点旋转 [batch_size, 3]
        Returns:
            loss_root_trans_w: 根节点平移损失 (加权)
            loss_root_rot_w: 根节点旋转损失 (加权)
        """
        loss_root_trans = self.mse_loss(root_trans_offset_new, root_trans_offset)

        # 将轴角转换为旋转矩阵
        R_pred = axis_angle_to_matrix(root_rot_new) # [N, 3, 3]
        R_target = axis_angle_to_matrix(gt_root_rot) # [N, 3, 3]

        # 计算测地线距离 (geodesic distance)作为旋转损失
        # 1. 计算相对旋转矩阵 R_rel = R_pred^T @ R_target
        R_rel = torch.matmul(R_pred.transpose(-2, -1), R_target)

        # 2. 计算迹 Trace(R_rel)
        trace_R_rel = torch.diagonal(R_rel, offset=0, dim1=-2, dim2=-1).sum(dim=-1)

        # 3. 计算旋转角度 theta = acos((Trace(R_rel) - 1) / 2)
        #    裁剪参数以确保数值稳定性
        cos_theta = (trace_R_rel - 1.0) / 2.0
        eps = 1e-7 # 防止 acos 的参数超出 [-1, 1]
        cos_theta_clamped = torch.clamp(cos_theta, -1.0 + eps, 1.0 - eps)
        angle_rad = torch.acos(cos_theta_clamped) # 弧度制的测地线距离

        # 4. 使用角度的平均值作为损失
        loss_root_rot = torch.mean(angle_rad)

        return loss_root_trans, loss_root_rot

def main():
    # 读取参数
    parser = argparse.ArgumentParser()
    parser.add_argument('--task', type=str, default='unitreeG1_retarget', 
                        choices=["A2_retarget", "T1_retarget", "dobot_retarget", 
                              "unitreeG1_retarget", "x2_retarget", "cdroid_retarget"],
                        help='任务名称')
    parser.add_argument('--shape-path', type=str, default="data/calc_beta/unitreeG1/G1_shape_scale_bias.json", 
                        help='自定义shape_scale_bias文件路径')
    parser.add_argument("--source_data_path", type=str, default="data/processed_data/amass/ACCAD/after_db/Walk_turn_around_poses.pkl",
                        help="源数据路径（文件）")
    parser.add_argument("--dataset_path", type=str, default="data/processed_data/amass/ACCAD/after_gradRotation_fit_ik",
                        help="保存路径（文件夹）")
    parser.add_argument("--no_show", action='store_true', help="禁用可视化显示")
    
    parser.add_argument("--contact_file_path", type=str, default="data/contact_seq/06-18-G1-walk-manual-contact.json",
                      help="接触文件路径")
    # 添加新的参数
    parser.add_argument("--device", type=str, default="cuda:0" if torch.cuda.is_available() else "cpu",
                      help="设备选择 (cuda:0 或 cpu)")
    parser.add_argument("--kernel_size", type=int, default=5,
                      help="高斯核大小")
    parser.add_argument("--sigma", type=float, default=0.75,
                      help="高斯核的标准差")
    parser.add_argument("--iterations", type=int, default=1000,
                      help="迭代次数")
    parser.add_argument("--show_frequency", type=int, default=20,
                      help="损失函数可视化间隔")
    parser.add_argument("--t_pose_mode", action="store_true",
                      help="启用T-pose模式")
    parser.add_argument("--target_foot_height", type=float, default=0.0,
                      help="目标脚部高度(Z轴)")
    
    
    args = parser.parse_args()
    retargeting = Retargeting(args)
    retargeting.retargetAmassData()

if __name__ == "__main__":
    main()
