# BVH动作重定向系统使用指南

这个系统提供了一套完整的基于BVH的动作重定向解决方案，完全摆脱了SMPL模型的依赖，直接使用BVH文件进行骨骼拟合和动作重定向。

## 系统架构

```
1. 骨骼拟合阶段：bone_optimizer_gui.py
   ↓ 生成骨骼参数文件
2. 动作重定向阶段：bvh_motion_retarget.py  
   ↓ 生成重定向结果
3. 可视化验证：utils/vis_mjcf.py
```

## 完整使用流程

### 第一步：骨骼拟合

使用GUI界面拟合BVH骨骼到目标机器人：

```bash
# 基本用法
python bone_optimizer_gui.py --bvh data/bvh/walk_forward.bvh --task unitreeG1_retarget

# 完整参数
python bone_optimizer_gui.py \
    --bvh data/bvh/walk_forward.bvh \
    --task unitreeG1_retarget \
    --device cpu \
    --use-scale \
    --adaptive-root-height \
    --config configs/default_bvh_config.json \
    --save-result data/calc_bvh/unitreeG1/bvh_optimization_result.json
```

**重要参数说明：**
- `--bvh`: BVH文件路径（用于拟合骨骼结构）
- `--task`: 目标机器人任务名称
- `--use-scale`: 启用缩放参数优化
- `--adaptive-root-height`: 启用根节点高度自适应调整

**输出文件：**
- 骨骼参数文件：`data/calc_bvh/{task_name}/bvh_retarget_params_{timestamp}.json`
- 优化后的BVH文件：`{result_name}_optimized.bvh`

### 第二步：动作重定向

使用拟合好的骨骼参数进行动作重定向：

```bash
# 基本用法  
python bvh_motion_retarget.py \
    --skeleton-params data/calc_bvh/unitreeG1/bvh_retarget_params_20241201_143022.json \
    --bvh-motion data/bvh/walking_motion.bvh

# 完整参数
python bvh_motion_retarget.py \
    --skeleton-params data/calc_bvh/unitreeG1/bvh_retarget_params_20241201_143022.json \
    --bvh-motion data/bvh/walking_motion.bvh \
    --iterations 2000 \
    --learning-rate 0.01 \
    --keypoint-loss-weight 1.0 \
    --smoothness-loss-weight 0.1 \
    --foot-height-loss-weight 0.2 \
    --apply-filter \
    --kernel-size 7 \
    --sigma 2.0 \
    --optimize-root \
    --device cpu
```

**参数说明：**
- `--skeleton-params`: 第一步生成的骨骼参数文件
- `--bvh-motion`: 要重定向的BVH动作文件
- `--iterations`: 优化迭代次数
- `--learning-rate`: 学习率
- `--keypoint-loss-weight`: 关键点损失权重
- `--smoothness-loss-weight`: 平滑损失权重
- `--foot-height-loss-weight`: 脚部高度损失权重
- `--apply-filter`: 应用平滑滤波
- `--optimize-root`: 优化根节点位置和旋转

**输出文件：**
- 重定向结果：`data/retargeted_motion/{task_name}/retargeted_motion_{timestamp}.json`

### 第三步：结果可视化

```bash
# 可视化重定向结果
python utils/vis_mjcf.py path/to/generated_robot_model.xml
```

## 支持的机器人

当前支持以下机器人类型：
- `unitreeG1_retarget`: Unitree G1 人形机器人
- `cdroid_retarget`: CDroid 人形机器人  
- `T1_retarget`: T1 人形机器人
- `A2_retarget`: A2 四足机器人
- `x2_retarget`: X2 人形机器人
- `dobot_retarget`: Dobot 机器人

## 文件格式说明

### 骨骼参数文件格式

生成的骨骼参数文件包含以下信息：

```json
{
  "metadata": {
    "task_name": "unitreeG1_retarget",
    "timestamp": "2024-12-01T14:30:22",
    "bvh_file": "walk_forward.bvh",
    "robot_model": "resources/robots/unitreeG1/mjcf/unitree_G1_retarget.xml",
    "optimization_version": "BVH_Direct_v1.0"
  },
  "joint_correspondence": {
    "robot_to_bvh": {"leg_l4_link": "LeftKnee", ...},
    "bvh_to_robot": {"LeftKnee": "leg_l4_link", ...},
    "robot_joint_indices": [4, 6, 8, ...],
    "bvh_joint_indices": [9, 11, 13, ...]
  },
  "skeleton_params": {
    "optimized_offsets": {...},
    "scale_factor": 1.0234,
    "root_height_adjustment": 0.12,
    "bvh_root_trans": [0.0, 0.0]
  },
  "t_pose_config": {
    "robot_dof_pos": [0.0, 0.0, ...],
    "robot_t_pose_positions": [...],
    "bvh_t_pose_positions": [...]
  },
  "coordinate_transform": {
    "bvh_to_robot_description": "BVH: z前,y上,x左 -> Robot: z上,y前,x右",
    "transform_matrix": [[0,0,1], [1,0,0], [0,1,0]]
  }
}
```

### 重定向结果文件格式

```json
{
  "metadata": {
    "timestamp": "2024-12-01T15:45:33",
    "source_bvh": "walking_motion.bvh",
    "skeleton_params": "bvh_retarget_params_20241201_143022.json",
    "task_name": "unitreeG1_retarget",
    "optimization_version": "BVH_Retarget_v1.0"
  },
  "motion_data": {
    "dof_pos": [[0.1, 0.2, ...], ...],  // 关节角度 [num_frames, num_dofs]
    "root_pos": [[0.0, 0.0, 1.0], ...], // 根节点位置 [num_frames, 3]
    "root_rot": [[0.0, 0.0, 0.0], ...], // 根节点旋转 [num_frames, 3]
    "num_frames": 1500,
    "fps": 30.0
  },
  "optimization_stats": {
    "iterations": 2000,
    "final_loss": 0.0123,
    "loss_history": [...],
    "parameters": {...}
  }
}
```

## 优化建议

### 骨骼拟合阶段

1. **选择合适的BVH文件**：使用T-pose或基本姿态的BVH文件进行骨骼拟合
2. **调整损失权重**：
   - 关节损失权重：1.0（主要目标）
   - 对称性损失权重：0.1（保持对称性）
   - 末端位置损失权重：0.5（精确末端控制）
3. **启用缩放优化**：对于尺寸差异较大的情况使用`--use-scale`
4. **高度自适应**：启用`--adaptive-root-height`来自动调整根节点高度

### 动作重定向阶段

1. **迭代次数**：
   - 简单动作：1000-1500次
   - 复杂动作：2000-3000次
2. **学习率调整**：
   - 初始：0.01
   - 如果收敛慢：增加到0.02
   - 如果不稳定：减少到0.005
3. **损失权重平衡**：
   - 关键点损失：1.0（主要目标）
   - 平滑损失：0.1（防止抖动）
   - 脚部高度损失：0.2（防止穿地）
4. **启用平滑滤波**：使用高斯滤波减少噪声

## 故障排除

### 常见错误及解决方案

1. **"关节映射关系不完整"**
   - 检查配置文件中的`bvh_joint_correspondence`
   - 确保所有机器人关节都有对应的BVH关节

2. **"BVH关节名称无效"**
   - 使用`utils/get_bvh_joints.py`查看BVH文件中的关节名称
   - 更新配置文件中的映射关系

3. **"优化不收敛"**
   - 降低学习率
   - 增加迭代次数
   - 调整损失权重

4. **"生成的动作不自然"**
   - 增加平滑损失权重
   - 启用平滑滤波
   - 检查关节限制设置

5. **"脚部穿地"**
   - 增加脚部高度损失权重
   - 启用根节点高度优化
   - 检查坐标系转换

## 系统优势

相比于原有的基于SMPL的方案，这个系统具有以下优势：

1. **直接性**：直接使用BVH数据，无需SMPL中间层
2. **灵活性**：支持任意BVH文件，不限于人体动作
3. **准确性**：直接拟合骨骼长度，避免SMPL形状参数的限制  
4. **简化性**：减少了beta参数的复杂性，流程更加直观
5. **可扩展性**：容易适配新的机器人模型

## 技术细节

### 坐标系转换

系统自动处理BVH和机器人坐标系之间的转换：
- BVH坐标系：z前, y上, x左
- 机器人坐标系：z上, y前, x右

转换矩阵：
```
x_robot = z_bvh
y_robot = x_bvh  
z_robot = y_bvh
```

### 损失函数

1. **关键点损失**：最小化机器人关节位置与目标BVH关节位置的差异
2. **平滑损失**：最小化关节位置的二阶差分，减少运动抖动
3. **脚部高度损失**：防止脚部穿透地面
4. **对称性损失**：保持左右肢体的对称性（在骨骼拟合阶段）

### 优化策略

- 使用Adam优化器进行梯度下降
- 支持根节点位置和旋转的联合优化
- 应用高斯平滑滤波减少噪声
- 渐进式学习率衰减

这个系统为BVH动作重定向提供了一个完整、高效、易用的解决方案。 